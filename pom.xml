<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.nexla</groupId>
    <artifactId>backend-connectors</artifactId>
    <version>3.2.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <distributionManagement>
        <repository>
            <id>codeartifact</id>
            <name>codeartifact</name>
            <url>https://nexla-433433586750.d.codeartifact.us-east-2.amazonaws.com/maven/nexla/</url>
        </repository>
    </distributionManagement>

    <properties>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <surefire.rerunFailingTestsCount>3</surefire.rerunFailingTestsCount>
        <docker.directory>docker</docker.directory>

        <java.version>11</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <nexla-backend-common.version>3.2.1-CONNECTORS-SNAPSHOT</nexla-backend-common.version>
        <nexla-backend-control.version>1.1.0</nexla-backend-control.version>
        <nexla-backend-transform.version>split-commons-af63a94-31-202410030133</nexla-backend-transform.version>

        <spring-boot.version>2.7.8</spring-boot.version>
        <spring-jdbc.version>5.3.18</spring-jdbc.version>
        <spring-web.version>5.3.18</spring-web.version>
        <spring-context.version>5.3.18</spring-context.version>
        <spring-security.version>5.6.9</spring-security.version>
        <spring-boot-email.version>0.6.3</spring-boot-email.version>
        <spring-core.version>5.3.20</spring-core.version>

        <!--spring-kafka.version has kafka-clients dependency which version should match to our kafka.version-->
        <spring-kafka.version>2.7.0</spring-kafka.version>
        <kafka.version>2.8.1</kafka.version>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <confluent.maven.repo>https://packages.confluent.io/maven/</confluent.maven.repo>
        <confluent.version>3.0.0</confluent.version>
        <json-schema-avro.version>0.1.4</json-schema-avro.version>
        <java-json-tools.version>1.2.14</java-json-tools.version>

        <jolt.version>0.1.7</jolt.version>
        <logback.version>1.2.9</logback.version>
        <gson.version>2.8.9</gson.version>

        <jython.version>2.7.4</jython.version>
        <nashorn.version>15.4</nashorn.version>

        <!--it needs to be 20.0 not to conflict with KafkaConnect's guava version-->
        <guava.version>31.1-jre</guava.version>

        <scala.full.version>2.12.14</scala.full.version>
        <scala.short.version>2.12</scala.short.version>

        <antlr4.version>4.8-1</antlr4.version>
        <build.manifest.section>Build Details</build.manifest.section>
        <build.unknown>UNKNOWN</build.unknown>
        <build.branch>${build.unknown}</build.branch>
        <build.number>${build.unknown}</build.number>
        <build.revision>${build.unknown}</build.revision>

        <!--for development only - to regenerate db schema from local database -->
        <jooq.generate.url>********************************</jooq.generate.url>
        <jooq.generate.user>root</jooq.generate.user>
        <jooq.generate.password>123</jooq.generate.password>
        <jooq.generate.input.schema>test</jooq.generate.input.schema>

        <jooq.version>3.14.8</jooq.version>
        <testcontainers.version>1.17.6</testcontainers.version>
        <mockito.version>3.5.10</mockito.version>
        <jackson.version>2.13.5</jackson.version><!--fix classloading with kafka version-->
        <jersey.version>1.9</jersey.version>
        <snappy.version>********</snappy.version>

        <commons-net.version>3.6</commons-net.version>
        <guava-retrying.version>2.0.0</guava-retrying.version>

        <natty.version>0.11</natty.version>
        <aws.sdk.version>1.12.112</aws.sdk.version>
        <commons.io.version>2.11.0</commons.io.version>
        <scalatest.version>3.2.7</scalatest.version>

        <c3p0.version>*******</c3p0.version>
        <wiremock.version>2.10.1</wiremock.version>
        <quartz.version>2.3.2</quartz.version>
        <marathon-client.version>0.6.0</marathon-client.version>
        <springfox.version>2.10.0</springfox.version>
        <logstash.log4j.version>1.7</logstash.log4j.version>
        <joda-time.version>2.9.9</joda-time.version>

        <ehcache.version>2.10.4</ehcache.version>

        <jjwt.version>0.9.0</jjwt.version>

        <log4j2.version>2.17.2</log4j2.version>
        <slf4j.version>1.7.25</slf4j.version>

        <bouncy.gpg.version>2.3.0</bouncy.gpg.version>

        <spray-json.version>1.3.6</spray-json.version>

        <akka.version>2.6.14</akka.version>
        <akka-stream-kafka.version>2.0.5</akka-stream-kafka.version>
        <akka-http.version>10.2.9</akka-http.version>
        <akka-quartz-scheduler.version>1.9.1-akka-2.6.x</akka-quartz-scheduler.version>
        <scala-logging.version>3.9.3</scala-logging.version>

        <github4s.version>0.28.3</github4s.version>
        <commons-compress.version>1.21</commons-compress.version>
        <commons-codec.version>1.13</commons-codec.version>
        <commons-httpclient.version>3.1</commons-httpclient.version>

        <vault.driver.version>3.1.0</vault.driver.version>
        <logstash-logback-encoder.version>7.1.1</logstash-logback-encoder.version>
        <commons-lang3.version>3.10</commons-lang3.version>
        <kafka-statsd-metrics2.version>4f320c8c71</kafka-statsd-metrics2.version>
        <everit.version>1.12.2</everit.version>
        <avro.version>1.11.1</avro.version>
        <curator.version>2.12.0</curator.version>
        <cats-core.version>2.5.0</cats-core.version>
        <scala-maven-plugin.version>4.6.1</scala-maven-plugin.version>
        <scalatest-maven-plugin.version>2.2.0</scalatest-maven-plugin.version>
        <box-java-sdk.version>2.58.0</box-java-sdk.version>
        <elasticsearch.version>7.17.6</elasticsearch.version>
        <vavr.version>0.10.0</vavr.version>
        <jsch.version>0.2.17</jsch.version>

        <mongo.driver.version>3.12.10</mongo.driver.version>
        <firebase-admin.version>8.0.1</firebase-admin.version>
        <jose4j.version>0.6.4</jose4j.version>
        <jongo.version>1.4.1</jongo.version>

        <lettuce-core.version>6.1.10.RELEASE</lettuce-core.version>
        <commons-pool2.version>2.10.0</commons-pool2.version>
        <scala-maven-plugin.use-zinc>true</scala-maven-plugin.use-zinc>
        <pureconfig.version>0.17.2</pureconfig.version>
        <scalaz-core.version>7.3.3</scalaz-core.version>
        <shapeless.version>2.3.3</shapeless.version>
        <json4s-jackson.version>3.6.11</json4s-jackson.version>
        <orc-core.version>1.5.5</orc-core.version>

        <amazon-kinesis-client.version>1.9.1</amazon-kinesis-client.version>
        <debezium.version>2.1.3.Final</debezium.version>
        <javax-validation.version>2.0.1.Final</javax-validation.version>
        <assertj-core.version>3.4.1</assertj-core.version>
        <joor.version>0.9.6</joor.version>
        <lombok.version>1.18.12</lombok.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-shade-plugin.version>3.2.4</maven-shade-plugin.version>
        <scala-collection-compat.version>2.7.0</scala-collection-compat.version>
        <util-eval.version>6.43.0</util-eval.version>
        <akka-http-cors.version>1.1.1</akka-http-cors.version>
        <azure-core.version>1.55.2</azure-core.version>
        <azure-core-http-netty.version>1.15.10</azure-core-http-netty.version>
        <netty.version>4.1.112.Final</netty.version>
        <netty-all.version>4.1.112.Final</netty-all.version>
        <netty-tcnative.version>2.0.44.Final</netty-tcnative.version>
        <azure-storage-blob.version>12.25.1</azure-storage-blob.version>
        <azure-storage-file-datalake.version>12.12.1</azure-storage-file-datalake.version>
        <reactor.version>3.4.23</reactor.version>

        <oracle-ojdbc.version>19.3.0.0</oracle-ojdbc.version>
        <mssql-jdbc.version>12.8.1.jre11</mssql-jdbc.version>
        <amazon.redshift.version>2.1.0.28</amazon.redshift.version>
        <!--  version 8.0.28 does remove TLSv1 and TSLv1.1 versions support.
        We can't go any higher if we still need to support these versions. -->
        <mysql-connector-java.version>8.0.27</mysql-connector-java.version>
        <postgresql.version>42.4.1</postgresql.version>
        <snowflake.jdbc.version>3.13.26</snowflake.jdbc.version>
        <presto.jdbc.version>0.252</presto.jdbc.version>
        <hive.jdbc.version>3.1.3</hive.jdbc.version>
        <jt400.version>10.6</jt400.version>
        <groovy.version>3.0.8</groovy.version>
        <pdfbox.version>2.0.15</pdfbox.version>
        <jsoap.version>1.14.2</jsoap.version>
        <jetty-server.version>9.4.41.v20210516</jetty-server.version>
        <dropwizard-validation.version>1.3.21</dropwizard-validation.version>
        <xerces.version>2.12.2</xerces.version>
        <apache.thrift.version>0.14.0</apache.thrift.version>
        <apache.ant.version>1.10.12</apache.ant.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <jackson-dataformat-cbor.version>2.13.2</jackson-dataformat-cbor.version>
        <zookeeper.version>3.8.0</zookeeper.version>
        <kubernetes.client-java.version>14.0.0</kubernetes.client-java.version>
        <nimbus-jose-jwt.version>9.21</nimbus-jose-jwt.version>
        <firebolt.jdbc.version>1.25</firebolt.jdbc.version>
        <teradata-jdbc.version>16.20.00.13</teradata-jdbc.version>
        <netsuite-jdbc.version>1.0</netsuite-jdbc.version>
        <db2-jdbc.version>11.5.7.0</db2-jdbc.version>
        <sap-hana-jdbc.version>2.11.17</sap-hana-jdbc.version>
        <scalactic.version>3.2.10</scalactic.version>
        <google-cloud-bigquery.version>2.4.0</google-cloud-bigquery.version>
        <jetty.version>9.4.39.v20210325</jetty.version>
        <wildfly-openssl.version>1.1.3.Final</wildfly-openssl.version>
        <protobuf-java.version>3.25.2</protobuf-java.version>
        <derby.version>10.15.2.0</derby.version>
        <sparkJDBC.version>2.6.22.1040</sparkJDBC.version>
        <jersey-media-jaxb.version>2.35</jersey-media-jaxb.version>
        <jetty-webapp.version>11.0.9</jetty-webapp.version>
        <hbase-shaded-jetty.version>4.1.0</hbase-shaded-jetty.version>
        <hbase-shaded-jersey.version>4.1.0</hbase-shaded-jersey.version>
        <okhttp.version>4.11.0</okhttp.version>
        <apache.ftpserver.version>1.1.4</apache.ftpserver.version>
        <junit.version>4.13.1</junit.version>
        <jupiter.version>5.10.0</jupiter.version>
        <netty-transport-native-epoll.version>4.1.77.Final</netty-transport-native-epoll.version>
        <google-oauth-client.version>1.33.3</google-oauth-client.version>
        <sybase-jdbc.version>1.0</sybase-jdbc.version>
        <athenaJDBC.version>2.1.3.1003</athenaJDBC.version>
        <commons-text.version>1.10.0</commons-text.version>
        <jsqlparser.version>4.7</jsqlparser.version>
        <spark.version>3.4.2</spark.version>
        <azure-identity.version>1.15.3</azure-identity.version>
        <microsoft-graph.version>6.7.0</microsoft-graph.version>

        <json-path.vestion>2.2.0</json-path.vestion>

        <!-- delta lake -->
        <delta.lake.version>2.4.0</delta.lake.version>
        <hadoop.version>3.2.4</hadoop.version>
        <mockwebserver.version>4.9.3</mockwebserver.version>
        <!-- testcontainers-scala -->
        <testcontainers.scala.version>0.40.12</testcontainers.scala.version>

        <mock-server.version>5.14.0</mock-server.version>

        <monitoring-interceptors.version>7.0.1</monitoring-interceptors.version>
        <jai-imageio.version>1.4.0</jai-imageio.version>
    </properties>

    <modules>
        <module>base-connector-source-agent</module>
        <module>base-connector-sink-agent</module>
        <module>common-connector</module>
        <module>http-sink</module>
        <module>file-source-sink</module>
        <module>kafka-connect-jdbc-source</module>
        <module>kafka-connect-jdbc-sink</module>
        <module>kafka-connect-documentdb-sink</module>
        <module>kafka-connect-redis-sink</module>
        <module>kafka-connect-rest-source</module>
        <module>kafka-connect-rest-sink</module>
        <module>kafka-connect-soap-source</module>
        <module>kafka-connect-soap-sink</module>
<!--        <module>kafka-connect-kinesis-sink</module>-->
<!--        <module>kafka-connect-kinesis-source</module>-->
        <module>kafka-connect-bigquery-source</module>
        <module>kafka-connect-bigquery-sink</module>
        <module>kafka-connect-spreadsheets-sink</module>
        <module>kafka-connect-file-source</module>
        <module>kafka-connect-file-sink</module>
        <module>kafka-connect-documentdb-source</module>
        <module>kafka-connect-iceberg-source</module>
        <module>kafka-connect-iceberg-sink</module>
        <module>kafka-connect-vectordb-sink</module>
        <module>kafka-connect-vectordb-source</module>

        <module>probe-http</module>
        <module>s3-probe-service</module>
        <module>gcs-probe-service</module>
        <module>webdav-probe-service</module>
        <module>sql-probe-service</module>
        <module>redis-probe-service</module>
        <module>box-service</module>
        <module>bigquery-probe-service</module>
        <module>dropbox-probe-service</module>
        <module>ftp-probe-service</module>
        <module>http-probe-service</module>
        <module>kafka-probe-service</module>
        <module>azure-blob-probe-service</module>
        <module>delta-lake-service</module>
        <module>documentdb-service</module>
        <module>vectordb-service</module>
        <module>gdrive-probe-service</module>
        <module>file-service</module>
        <module>file-service-utils</module>
        <module>iceberg-probe</module>
        <module>kafka-connect-api-streams-source</module>
        <module>one-drive-probe-service</module>
        <module>sharepoint-probe-service</module>
        <module>inmemory-connector-common</module>
        <module>inmemory-connector</module>
        <module>connector-properties</module>
        <module>connector-test</module>
        <module>parsers</module>
        <module>parser-properties</module>
        <module>replication-connector</module>

        <module>cloud-job-connector</module>
        <module>nexla-spark-agent</module>
        <module>ingestion-service</module>
    </modules>

    <repositories>

        <repository>
            <id>spring-milestones</id>
            <url>https://repo.spring.io/milestone</url>
        </repository>

        <repository>
            <id>confluent</id>
            <name>Confluent</name>
            <url>${confluent.maven.repo}</url>
        </repository>

        <!-- https://docs.aws.amazon.com/redshift/latest/mgmt/configure-jdbc-connection-with-maven.html
             To connect using SSL -->
        <repository>
            <id>redshift</id>
            <url>https://s3.amazonaws.com/redshift-maven-repository/release</url>
        </repository>

        <!-- https://docs.aws.amazon.com/emr/latest/ReleaseGuide/emr-artifact-repository.html
             To connect using SSL -->
        <repository>
            <id>emr</id>
            <url>https://s3.us-west-1.amazonaws.com/us-west-1-emr-artifacts/emr-5.18.0/repos/maven/</url>
        </repository>


        <repository>
            <id>jitpack.io</id>
            <url>https://jitpack.io</url>
        </repository>

            <repository>
                <id>local-repo</id>
                <name>Local m2 repository</name>
                <url>file:///${user.home}/.m2/repository</url>
            </repository>
    </repositories>

    <dependencies>

        <dependency>
            <groupId>org.apache.htrace</groupId>
            <artifactId>htrace-core4</artifactId>
            <version>4.2.0-incubating</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${scala.short.version}</artifactId>
            <version>${scalatest.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest-core_${scala.short.version}</artifactId>
            <version>${scalatest.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.javatuples</groupId>
            <artifactId>javatuples</artifactId>
            <version>1.2</version>
        </dependency>

        <dependency>
            <groupId>one.util</groupId>
            <artifactId>streamex</artifactId>
            <version>0.8.1</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>${protobuf-java.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bazaarvoice.jolt</groupId>
            <artifactId>jolt-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bazaarvoice.jolt</groupId>
            <artifactId>json-utils</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>

            <!-- informational, don't uncomment -->

            <!--<dependency>-->
            <!--&lt;!&ndash; Import dependency management from Spring Boot &ndash;&gt;-->
            <!--<groupId>org.springframework.boot</groupId>-->
            <!--<artifactId>spring-boot-dependencies</artifactId>-->
            <!--<version>${spring-boot.version}</version>-->
            <!--<type>pom</type>-->
            <!--<scope>import</scope>-->
            <!--</dependency>-->

            <dependency>
                <groupId>com.google.oauth-client</groupId>
                <artifactId>google-oauth-client</artifactId>
                <version>${google-oauth-client.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.ant</groupId>
                <artifactId>ant</artifactId>
                <version>${apache.ant.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.hbase.thirdparty</groupId>
                <artifactId>hbase-shaded-jetty</artifactId>
                <version>${hbase-shaded-jetty.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.hbase.thirdparty</groupId>
                <artifactId>hbase-shaded-jersey</artifactId>
                <version>${hbase-shaded-jersey.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>

            <dependency>
                <groupId>com.nexla</groupId>
                <artifactId>common</artifactId>
                <version>${nexla-backend-common.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${javax-validation.version}</version>
            </dependency>

            <dependency>
                <groupId>io.debezium</groupId>
                <artifactId>debezium-embedded</artifactId>
                <version>${debezium.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.reflections</groupId>
                        <artifactId>reflections</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>connect-runtime</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.debezium</groupId>
                <artifactId>debezium-api</artifactId>
                <version>${debezium.version}</version>
            </dependency>

            <dependency>
                <groupId>io.debezium</groupId>
                <artifactId>debezium-connector-postgres</artifactId>
                <version>${debezium.version}</version>
            </dependency>

            <dependency>
                <groupId>io.debezium</groupId>
                <artifactId>debezium-connector-mysql</artifactId>
                <version>${debezium.version}</version>
            </dependency>

            <dependency>
                <groupId>io.debezium</groupId>
                <artifactId>debezium-connector-sqlserver</artifactId>
                <version>${debezium.version}</version>
            </dependency>

            <dependency>
                <groupId>io.debezium</groupId>
                <artifactId>debezium-connector-oracle</artifactId>
                <version>${debezium.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>libraries-bom</artifactId>
                <version>23.1.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>io.vavr</groupId>
                <artifactId>vavr</artifactId>
                <version>${vavr.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jooq</groupId>
                <artifactId>jooq</artifactId>
                <version>${jooq.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jooq</groupId>
                <artifactId>jooq-meta</artifactId>
                <version>${jooq.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jooq</groupId>
                <artifactId>jooq-codegen</artifactId>
                <version>${jooq.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.orc</groupId>
                <artifactId>orc-tools</artifactId>
                <version>${orc-core.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.xml.bind</groupId>
                        <artifactId>jaxb-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hadoop</groupId>
                        <artifactId>hadoop-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hadoop</groupId>
                        <artifactId>hadoop-hdfs</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-aws</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.amazonaws</groupId>
                        <artifactId>aws-java-sdk-bundle</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.orc</groupId>
                <artifactId>orc-core</artifactId>
                <version>${orc-core.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.xml.bind</groupId>
                        <artifactId>jaxb-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hadoop</groupId>
                        <artifactId>hadoop-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hadoop</groupId>
                        <artifactId>hadoop-hdfs</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool2.version}</version>
            </dependency>

            <dependency>
                <groupId>org.scalaz</groupId>
                <artifactId>scalaz-core_${scala.short.version}</artifactId>
                <version>${scalaz-core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chuusai</groupId>
                <artifactId>shapeless_${scala.short.version}</artifactId>
                <version>${shapeless.version}</version>
            </dependency>

            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>${lettuce-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver</artifactId>
                <version>${mongo.driver.version}</version>
            </dependency>

            <dependency>
                <groupId>com.box</groupId>
                <artifactId>box-java-sdk</artifactId>
                <version>${box-java-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>org.typelevel</groupId>
                <artifactId>cats-core_${scala.short.version}</artifactId>
                <version>${cats-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.typelevel</groupId>
                <artifactId>cats-effect_${scala.short.version}</artifactId>
                <version>3.0.1</version>
            </dependency>

            <dependency>
                <groupId>io.confluent</groupId>
                <artifactId>kafka-connect-avro-converter</artifactId>
                <version>${confluent.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-client</artifactId>
                <version>${curator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-stream-kafka_${scala.short.version}</artifactId>
                <version>${akka-stream-kafka.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro</artifactId>
                <version>${avro.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.codehaus.jackson</groupId>
                        <artifactId>jackson-mapper-asl</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-library</artifactId>
                <version>${scala.full.version}</version>
            </dependency>

            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-reflect</artifactId>
                <version>${scala.full.version}</version>
            </dependency>

            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-compiler</artifactId>
                <version>${scala.full.version}</version>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jdk8</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-csv</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j-core</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j-slf4j-impl</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>org.mockito</artifactId>
                        <groupId>mockito-core</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring-jdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring-web.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring-context.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-freemarker</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.13</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>4.4.13</version>
            </dependency>

            <dependency>
                <groupId>net.sf.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>${ehcache.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-security</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>5.0.4.RELEASE</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj-core.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-test</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring-security.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-config</artifactId>
                <version>${spring-security.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-web</artifactId>
                <version>${spring-security.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring-kafka.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>kafka-clients</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>kafka-streams</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>ch.megard</groupId>
                <artifactId>akka-http-cors_${scala.short.version}</artifactId>
                <version>${akka-http-cors.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP-java6</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.mchange</groupId>
                <artifactId>c3p0</artifactId>
                <version>${c3p0.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons-net.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.rholder</groupId>
                <artifactId>guava-retrying</artifactId>
                <version>${guava-retrying.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.fge</groupId>
                <artifactId>json-schema-avro</artifactId>
                <version>${json-schema-avro.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.avro</groupId>
                        <artifactId>avro</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.github.java-json-tools</groupId>
                <artifactId>json-schema-core</artifactId>
                <version>${java-json-tools.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-scala_${scala.short.version}</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-api</artifactId>
                <version>${kafka.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-runtime</artifactId>
                <version>${kafka.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-core</artifactId>
                <version>${jersey.version}</version>
            </dependency>

            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback-encoder.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${jupiter.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-library</artifactId>
                <version>1.3</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.scalatestplus</groupId>
                <artifactId>mockito-3-4_${scala.short.version}</artifactId>
                <version>3.2.7.0</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.bazaarvoice.jolt</groupId>
                <artifactId>jolt-core</artifactId>
                <version>${jolt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bazaarvoice.jolt</groupId>
                <artifactId>json-utils</artifactId>
                <version>${jolt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazon.redshift</groupId>
                <artifactId>redshift-jdbc42</artifactId>
                <version>${amazon.redshift.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-redshift</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${mssql-jdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>net.snowflake</groupId>
                <artifactId>snowflake-jdbc</artifactId>
                <version>${snowflake.jdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facebook.presto</groupId>
                <artifactId>presto-jdbc</artifactId>
                <version>${presto.jdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.hive</groupId>
                <artifactId>hive-jdbc</artifactId>
                <version>${hive.jdbc.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.derby</groupId>
                        <artifactId>derby</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-http</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-runner</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hadoop</groupId>
                        <artifactId>hadoop-yarn-server-resourcemanager</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.glassfish</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.derby</groupId>
                <artifactId>derby</artifactId>
                <version>${derby.version}</version>
            </dependency>

            <dependency>
                <groupId>com.teradata.jdbc</groupId>
                <artifactId>terajdbc4</artifactId>
                <version>${teradata-jdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ibm.db2</groupId>
                <artifactId>jcc</artifactId>
                <version>${db2-jdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sap.cloud.db.jdbc</groupId>
                <artifactId>ngdbc</artifactId>
                <version>${sap-hana-jdbc.version}</version>
            </dependency>

            <!--tests-->

            <!-- https://mvnrepository.com/artifact/org.testcontainers/kafka -->
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers</artifactId>
                <version>${testcontainers.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.jetbrains</groupId>
                        <artifactId>annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>kafka</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>mssqlserver</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>mongodb</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>2.8.1-hack</version>
            </dependency>

            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>postgresql</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>oracle-xe</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>mysql</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>jdbc</artifactId>
                <version>${testcontainers.version}</version>
            </dependency>

            <!-- scala testcontainers -->
            <dependency>
                <groupId>com.dimafeng</groupId>
                <artifactId>testcontainers-scala-scalatest_${scala.short.version}</artifactId>
                <version>${testcontainers.scala.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.dimafeng</groupId>
                <artifactId>testcontainers-scala-postgresql_${scala.short.version}</artifactId>
                <version>${testcontainers.scala.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.dimafeng</groupId>
                <artifactId>testcontainers-scala-kafka_${scala.short.version}</artifactId>
                <version>${testcontainers.scala.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.dimafeng</groupId>
                <artifactId>testcontainers-scala-mongodb_${scala.short.version}</artifactId>
                <version>${testcontainers.scala.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.dimafeng</groupId>
                <artifactId>testcontainers-scala-mysql_${scala.short.version}</artifactId>
                <version>${testcontainers.scala.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>20201115</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security.oauth</groupId>
                <artifactId>spring-security-oauth2</artifactId>
                <version>2.5.1.RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>5.3.19</version>
            </dependency>

            <dependency>
                <groupId>com.nexla</groupId>
                <artifactId>admin-api-client</artifactId>
                <version>${nexla-backend-common.version}</version>
            </dependency>

            <dependency>
                <groupId>net.minidev</groupId>
                <artifactId>json-smart</artifactId>
                <version>2.4.8</version>
            </dependency>

            <dependency>
                <groupId>com.joestelmach</groupId>
                <artifactId>natty</artifactId>
                <version>${natty.version}</version>
            </dependency>

            <dependency>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest_${scala.short.version}</artifactId>
                <version>${scalatest.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>

            <dependency>
                <groupId>io.firebolt</groupId>
                <artifactId>firebolt-jdbc</artifactId>
                <version>${firebolt.jdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>net.sf.jt400</groupId>
                <artifactId>jt400-jdk9</artifactId>
                <version>${jt400.version}</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${springfox.version}</version>
            </dependency>

            <dependency>
                <groupId>net.logstash.log4j</groupId>
                <artifactId>jsonevent-layout</artifactId>
                <version>${logstash.log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>

            <dependency>
                <groupId>com.typesafe.scala-logging</groupId>
                <artifactId>scala-logging_${scala.short.version}</artifactId>
                <version>${scala-logging.version}</version>
            </dependency>

            <dependency>
                <groupId>com.enragedginger</groupId>
                <artifactId>akka-quartz-scheduler_${scala.short.version}</artifactId>
                <version>${akka-quartz-scheduler.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>c3p0</groupId>
                        <artifactId>c3p0</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-stream_${scala.short.version}</artifactId>
                <version>${akka.version}</version>
            </dependency>

            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-actor_${scala.short.version}</artifactId>
                <version>${akka.version}</version>
            </dependency>

            <dependency>
                <groupId>io.spray</groupId>
                <artifactId>spray-json_${scala.short.version}</artifactId>
                <version>${spray-json.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.scala-lang.modules</groupId>
                        <artifactId>scala-xml_${scala.short.version}</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-http-spray-json_${scala.short.version}</artifactId>
                <version>${akka-http.version}</version>
            </dependency>

            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-http-testkit_${scala.short.version}</artifactId>
                <version>${akka-http.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-stream-testkit_${scala.short.version}</artifactId>
                <version>${akka.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-http_${scala.short.version}</artifactId>
                <version>${akka-http.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cronutils</groupId>
                <artifactId>cron-utils</artifactId>
                <version>9.1.6</version>
            </dependency>

            <dependency>
                <groupId>org.jooq</groupId>
                <artifactId>joor</artifactId>
                <version>${joor.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.airbnb</groupId>
                <artifactId>kafka-statsd-metrics2</artifactId>
                <version>${kafka-statsd-metrics2.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>kafka_2.10</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.indeed</groupId>
                        <artifactId>java-dogstatsd-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>oauth.signpost</groupId>
                <artifactId>signpost-core</artifactId>
                <version>2.1.1</version>
            </dependency>

            <dependency>
                <groupId>oauth.signpost</groupId>
                <artifactId>signpost-commonshttp4</artifactId>
                <version>2.1.1</version>
            </dependency>

            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>${json-path.vestion}</version>
            </dependency>

            <dependency>
                <groupId>com.github.nexla</groupId>
                <artifactId>soa-model</artifactId>
                <version>206075c5fb</version>
            </dependency>

            <dependency>
                <groupId>com.oracle.ojdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${oracle-ojdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>amazon-kinesis-client</artifactId>
                <version>${amazon-kinesis-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.typesafe</groupId>
                <artifactId>config</artifactId>
                <version>1.3.3</version>
            </dependency>

            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-core_${scala.short.version}</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.spark</groupId>
                        <artifactId>spark-network-common_2.12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--  kafka connect-runtime:jar:2.8.1 depends on jersey 2.34 - spark 2.4.3 pulls in jersey 2.36 which breaks kafka connect -->
            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-server</artifactId>
                <version>2.34</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-client</artifactId>
                <version>2.34</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-servlet-core</artifactId>
                <version>2.34</version>
            </dependency>

            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-sql_${scala.short.version}</artifactId>
                <version>${spark.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.lookfirst</groupId>
                <artifactId>sardine</artifactId>
                <version>5.10</version>
            </dependency>

            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>${protobuf-java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf-java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>google-cloud-bigquery</artifactId>
                <version>${google-cloud-bigquery.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty-all.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-network-common_2.12</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.2.0</version>
                <dependencies>
                    <!-- Dependency on 'common' module for custom checker -->
                    <dependency>
                        <groupId>com.nexla</groupId>
                        <artifactId>common-core</artifactId>
                        <version>${nexla-backend-common.version}</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <!-- Ensure this path is correct -->
                    <configLocation>nexla-checkstyle.xml</configLocation>
                    <encoding>UTF-8</encoding>
                    <consoleOutput>true</consoleOutput>
                    <failsOnError>true</failsOnError>
                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    <includeTestResources>true</includeTestResources>
                </configuration>
                <executions>
                    <execution>
                        <id>checkstyle-validation</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <version>${scala-maven-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <recompileMode>incremental</recompileMode>
                    <args>
                        <arg>-unchecked</arg>
                        <arg>-deprecation</arg>
                        <arg>-explaintypes</arg>
                        <arg>-feature</arg>
                        <!--<arg>-Ywarn-unused</arg>-->
                        <!--<arg>-Xlint:unused</arg>-->
                        <arg>-target:jvm-1.8</arg>
                    </args>
                    <jvmArgs>
                        <jvmArg>-Xmx2g</jvmArg>
                    </jvmArgs>
                </configuration>
                <executions>
                    <execution>
                        <id>scala-compile-first</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>add-source</goal>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>scala-test-compile</id>
                        <phase>process-test-resources</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>org.scalatest</groupId>
                        <artifactId>scalatest_${scala.short.version}</artifactId>
                        <version>${scalatest.version}</version>
                    </dependency>
                </dependencies>
                <version>${scalatest-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>test</id>
                        <goals>
                            <goal>test</goal>
                        </goals>
                        <configuration>
                            <tagsToInclude>${scalatestTagsInclude}</tagsToInclude>
                            <tagsToExclude>${scalatestTagsExclude}</tagsToExclude>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
                <version>9.0.1</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                        <phase>initialize</phase>
                    </execution>
                </executions>
                <configuration>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <generateGitPropertiesFilename>${project.build.outputDirectory}/git.properties</generateGitPropertiesFilename>
                    <skipPoms>false</skipPoms>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>0.4.13</version>
                <configuration>
                    <serverId>docker-hub</serverId>
                    <imageName>nexla/${project.name}</imageName>
                    <dockerDirectory>${docker.directory}</dockerDirectory>
                    <imageTags>
                        <imageTag>${build.branch}-${build.number}-${build.revision}</imageTag>
                        <imageTag>latest</imageTag>
                    </imageTags>
                    <resources>
                        <resource>
                            <targetPath>/target</targetPath>
                            <directory>${project.build.directory}</directory>
                            <include>${project.build.finalName}.jar</include>
                        </resource>
                        <resource>
                            <targetPath>/</targetPath>
                            <directory>${project.basedir}</directory>
                            <include>/patch/</include>
                        </resource>
                        <resource>
                            <targetPath>/target</targetPath>
                            <directory>${project.build.directory}</directory>
                            <include>/classes/</include>
                        </resource>
                    </resources>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <version>3.1.1</version>
                <inherited>false</inherited>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.11</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <!-- attached to Maven test phase -->
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
        </plugins>

        <pluginManagement>

            <plugins>

                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.22.2</version>
                    <configuration>
                        <groups>${testcase.groups}</groups>
                        <excludedGroups>${testcase.excluded.groups}</excludedGroups>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <pluginRepositories>
        <pluginRepository>
            <id>repository.spring.release</id>
            <name>Spring GA Repository</name>
            <url>https://repo.spring.io/plugins-release/</url>
        </pluginRepository>

        <pluginRepository>
            <id>codeartifact</id>
            <name>codeartifact</name>
            <url>https://nexla-433433586750.d.codeartifact.us-east-2.amazonaws.com/maven/nexla/</url>
        </pluginRepository>
    </pluginRepositories>

    <profiles>
        <profile>
            <id>integrationTestOnly</id>
            <properties>
                <testcase.excluded.groups>com.nexla.test.UnitTests</testcase.excluded.groups>
                <scalatestTagsExclude>com.nexla.test.ScalaUnitTests</scalatestTagsExclude>
            </properties>
        </profile>
        <profile>
            <id>unitTestOnly</id>
            <properties>
                <testcase.excluded.groups>com.nexla.test.IntegrationTests</testcase.excluded.groups>
                <scalatestTagsExclude>com.nexla.test.ScalaIntegrationTests</scalatestTagsExclude>
            </properties>
        </profile>
    </profiles>

</project>
