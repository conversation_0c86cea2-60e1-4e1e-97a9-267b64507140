<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:ns="http://fedex.com/ws/track/v16" xmlns:s1="http://schemas.xmlsoap.org/wsdl/soap/" targetNamespace="http://fedex.com/ws/track/v16" name="TrackServiceDefinitions">
  <types>
    <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://fedex.com/ws/track/v16">
      <xs:element name="GetTrackingDocumentsReply" type="ns:GetTrackingDocumentsReply"/>
      <xs:element name="GetTrackingDocumentsRequest" type="ns:GetTrackingDocumentsRequest"/>
      <xs:element name="SendNotificationsReply" type="ns:SendNotificationsReply"/>
      <xs:element name="SendNotificationsRequest" type="ns:SendNotificationsRequest"/>
      <xs:element name="TrackReply" type="ns:TrackReply"/>
      <xs:element name="TrackRequest" type="ns:TrackRequest"/>
      <xs:complexType name="Address">
        <xs:annotation>
          <xs:documentation>Descriptive data for a physical location. May be used as an actual physical address (place to which one could go), or as a container of "address parts" which should be handled as a unit (such as a city-state-ZIP combination within the US).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="StreetLines" type="xs:string" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Combination of number, street name, etc. At least one line is required for a valid physical address; empty lines should not be included.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="City" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Name of city, town, etc.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="StateOrProvinceCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifying abbreviation for US state, Canada province, etc. Format and presence of this field will vary, depending on country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PostalCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identification of a region (usually small) for mail/package delivery. Format and presence of this field will vary, depending on country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="UrbanizationCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Relevant only to addresses in Puerto Rico.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CountryCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The two-letter code used to identify a country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CountryName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The fully spelt out name of a country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Residential" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates whether this address residential (as opposed to commercial).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="GeographicCoordinates" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The geographic coordinates cooresponding to this address.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="AppointmentDetail">
        <xs:annotation>
          <xs:documentation>Specifies the different appointment times on a specific date.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Date" type="xs:date" minOccurs="0"/>
          <xs:element name="WindowDetails" type="ns:AppointmentTimeDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Different appointment time windows on the date specified.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="AppointmentTimeDetail">
        <xs:annotation>
          <xs:documentation>Specifies the details about the appointment time window.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Type" type="ns:AppointmentWindowType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The description that FedEx Ground uses for the appointment window being specified.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Window" type="ns:LocalTimeRange" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the window of time for an appointment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="AppointmentWindowType">
        <xs:annotation>
          <xs:documentation>The description that FedEx uses for a given appointment window.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="AFTERNOON"/>
          <xs:enumeration value="LATE_AFTERNOON"/>
          <xs:enumeration value="MID_DAY"/>
          <xs:enumeration value="MORNING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ArrivalLocationType">
        <xs:annotation>
          <xs:documentation>Identifies where a tracking event occurs.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="AIRPORT"/>
          <xs:enumeration value="CUSTOMER"/>
          <xs:enumeration value="CUSTOMS_BROKER"/>
          <xs:enumeration value="DELIVERY_LOCATION"/>
          <xs:enumeration value="DESTINATION_AIRPORT"/>
          <xs:enumeration value="DESTINATION_FEDEX_FACILITY"/>
          <xs:enumeration value="DROP_BOX"/>
          <xs:enumeration value="ENROUTE"/>
          <xs:enumeration value="FEDEX_FACILITY"/>
          <xs:enumeration value="FEDEX_OFFICE_LOCATION"/>
          <xs:enumeration value="INTERLINE_CARRIER"/>
          <xs:enumeration value="NON_FEDEX_FACILITY"/>
          <xs:enumeration value="ORIGIN_AIRPORT"/>
          <xs:enumeration value="ORIGIN_FEDEX_FACILITY"/>
          <xs:enumeration value="PICKUP_LOCATION"/>
          <xs:enumeration value="PLANE"/>
          <xs:enumeration value="PORT_OF_ENTRY"/>
          <xs:enumeration value="SHIP_AND_GET_LOCATION"/>
          <xs:enumeration value="SORT_FACILITY"/>
          <xs:enumeration value="TURNPOINT"/>
          <xs:enumeration value="VEHICLE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="AvailableImageType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BILL_OF_LADING"/>
          <xs:enumeration value="SIGNATURE_PROOF_OF_DELIVERY"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="AvailableImagesDetail">
        <xs:sequence>
          <xs:element name="Type" type="ns:AvailableImageType" minOccurs="0"/>
          <xs:element name="Size" type="ns:ImageSizeType" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CarrierCodeType">
        <xs:annotation>
          <xs:documentation>Identification of a FedEx operating company (transportation).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FDXC"/>
          <xs:enumeration value="FDXE"/>
          <xs:enumeration value="FDXG"/>
          <xs:enumeration value="FXCC"/>
          <xs:enumeration value="FXFR"/>
          <xs:enumeration value="FXSP"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ClientDetail">
        <xs:annotation>
          <xs:documentation>Descriptive data for the client submitting a transaction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="AccountNumber" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The FedEx account number associated with this transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MeterNumber" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>This number is assigned by FedEx and identifies the unique device from which the request is originating</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="IntegratorId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used in transactions which require identification of the FedEx Office integrator.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Localization" type="ns:Localization" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The language to be used for human-readable Notification.localizedMessages in responses to the request containing this ClientDetail object. Different requests from the same client may contain different Localization data. (Contrast with TransactionDetail.localization, which governs data payload language/translation.)</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Commodity">
        <xs:sequence>
          <xs:element name="CommodityId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Value used to identify a commodity description; must be unique within the containing shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Name" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>FedEx internal commodity identifier</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NumberOfPieces" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A free-form description of the commodity, which could be used for customs clearance documentation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Purpose" type="ns:CommodityPurposeType" minOccurs="0"/>
          <xs:element name="CountryOfManufacture" type="xs:string" minOccurs="0"/>
          <xs:element name="HarmonizedCode" type="xs:string" minOccurs="0"/>
          <xs:element name="Weight" type="ns:Weight" minOccurs="0"/>
          <xs:element name="Quantity" type="xs:decimal" minOccurs="0"/>
          <xs:element name="QuantityUnits" type="xs:string" minOccurs="0"/>
          <xs:element name="AdditionalMeasures" type="ns:Measure" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Contains only additional quantitative information other than weight and quantity to calculate duties and taxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="UnitPrice" type="ns:Money" minOccurs="0"/>
          <xs:element name="CustomsValue" type="ns:Money" minOccurs="0"/>
          <xs:element name="ExciseConditions" type="ns:EdtExciseCondition" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Defines additional characteristic of commodity used to calculate duties and taxes</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExportLicenseNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="ExportLicenseExpirationDate" type="xs:date" minOccurs="0"/>
          <xs:element name="CIMarksAndNumbers" type="xs:string" minOccurs="0"/>
          <xs:element name="PartNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="NaftaDetail" type="ns:NaftaCommodityDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>All data required for this commodity in NAFTA Certificate of Origin.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CommodityPurposeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BUSINESS"/>
          <xs:enumeration value="CONSUMER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CompletedTrackDetail">
        <xs:sequence>
          <xs:element name="HighestSeverity" type="ns:NotificationSeverityType" minOccurs="0"/>
          <xs:element name="Notifications" type="ns:Notification" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="DuplicateWaybill" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>True if duplicate packages (more than one package with the same tracking number) have been found, and only limited data will be provided for each one.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MoreData" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>True if additional packages remain to be retrieved.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PagingToken" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Value that must be passed in a TrackNotification request to retrieve the next set of packages (when MoreDataAvailable = true).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TrackDetailsCount" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the total number of available track details across all pages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TrackDetails" type="ns:TrackDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Contains detailed tracking information for the requested packages(s).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Contact">
        <xs:annotation>
          <xs:documentation>The descriptive data for a point-of-contact person.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PersonName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the contact person's name.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Title" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the contact person's title.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CompanyName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the company this contact is associated with.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PhoneNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the phone number associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PhoneExtension" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the phone extension associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TollFreePhoneNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies a toll free number, if any, associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PagerNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the pager number associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FaxNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the fax number associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EMailAddress" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the email address associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ContactAndAddress">
        <xs:sequence>
          <xs:element name="Contact" type="ns:Contact" minOccurs="1"/>
          <xs:element name="Address" type="ns:Address" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ContentRecord">
        <xs:sequence>
          <xs:element name="PartNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="ItemNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="ReceivedQuantity" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomerExceptionRequestDetail">
        <xs:sequence>
          <xs:element name="Id" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Unique identifier for the customer exception request.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="StatusCode" type="xs:string" minOccurs="0"/>
          <xs:element name="StatusDescription" type="xs:string" minOccurs="0"/>
          <xs:element name="CreateTime" type="xs:dateTime" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomsOptionDetail">
        <xs:sequence>
          <xs:element name="Type" type="ns:CustomsOptionType" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies additional description about customs options. This is a required field when the customs options type is "OTHER".</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomsOptionType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="COURTESY_RETURN_LABEL"/>
          <xs:enumeration value="EXHIBITION_TRADE_SHOW"/>
          <xs:enumeration value="FAULTY_ITEM"/>
          <xs:enumeration value="FOLLOWING_REPAIR"/>
          <xs:enumeration value="FOR_REPAIR"/>
          <xs:enumeration value="ITEM_FOR_LOAN"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="REJECTED"/>
          <xs:enumeration value="REPLACEMENT"/>
          <xs:enumeration value="TRIAL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DateRange">
        <xs:sequence>
          <xs:element name="Begins" type="xs:date" minOccurs="0"/>
          <xs:element name="Ends" type="xs:date" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="DeliveryOptionEligibilityDetail">
        <xs:annotation>
          <xs:documentation>Details about the eligibility for a delivery option.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Option" type="ns:DeliveryOptionType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Type of delivery option.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Eligibility" type="ns:EligibilityType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Eligibility of the customer for the specific delivery option.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DeliveryOptionType">
        <xs:annotation>
          <xs:documentation>Specifies the different option types for delivery.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="INDIRECT_SIGNATURE_RELEASE"/>
          <xs:enumeration value="REDIRECT_TO_HOLD_AT_LOCATION"/>
          <xs:enumeration value="REROUTE"/>
          <xs:enumeration value="RESCHEDULE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Dimensions">
        <xs:sequence>
          <xs:element name="Length" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="Width" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="Height" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="Units" type="ns:LinearUnits" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Distance">
        <xs:annotation>
          <xs:documentation>Driving or other transportation distances, distinct from dimension measurements.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Value" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the distance quantity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Units" type="ns:DistanceUnits" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the unit of measure for the distance value.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DistanceUnits">
        <xs:restriction base="xs:string">
          <xs:enumeration value="KM"/>
          <xs:enumeration value="MI"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DocumentPart">
        <xs:annotation>
          <xs:documentation>Successive parts of the document (only one, for PDF documents).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="SequenceNumber" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The one-origin position of this part within a document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Content" type="xs:base64Binary" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Graphic or printer commands for this image within a document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="EMailDetail">
        <xs:sequence>
          <xs:element name="EmailAddress" type="xs:string" minOccurs="0"/>
          <xs:element name="Name" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the name associated with the email address.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="EdtExciseCondition">
        <xs:sequence>
          <xs:element name="Category" type="xs:string" minOccurs="0"/>
          <xs:element name="Value" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customer-declared value, with data type and legal values depending on excise condition, used in defining the taxable value of the item.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="EligibilityType">
        <xs:annotation>
          <xs:documentation>Specifies different values of eligibility status</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ELIGIBLE"/>
          <xs:enumeration value="INELIGIBLE"/>
          <xs:enumeration value="POSSIBLY_ELIGIBLE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FaxDetail">
        <xs:sequence>
          <xs:element name="PhoneNumber" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FedExLocationType">
        <xs:annotation>
          <xs:documentation>Identifies a kind of FedEx facility.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FEDEX_AUTHORIZED_SHIP_CENTER"/>
          <xs:enumeration value="FEDEX_EXPRESS_STATION"/>
          <xs:enumeration value="FEDEX_FACILITY"/>
          <xs:enumeration value="FEDEX_FREIGHT_SERVICE_CENTER"/>
          <xs:enumeration value="FEDEX_GROUND_TERMINAL"/>
          <xs:enumeration value="FEDEX_HOME_DELIVERY_STATION"/>
          <xs:enumeration value="FEDEX_OFFICE"/>
          <xs:enumeration value="FEDEX_ONSITE"/>
          <xs:enumeration value="FEDEX_SELF_SERVICE_LOCATION"/>
          <xs:enumeration value="FEDEX_SHIPSITE"/>
          <xs:enumeration value="FEDEX_SHIP_AND_GET"/>
          <xs:enumeration value="FEDEX_SMART_POST_HUB"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="GetTrackingDocumentsReply">
        <xs:sequence>
          <xs:element name="HighestSeverity" type="ns:NotificationSeverityType" minOccurs="1"/>
          <xs:element name="Notifications" type="ns:Notification" minOccurs="1" maxOccurs="unbounded"/>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0"/>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1"/>
          <xs:element name="Documents" type="ns:TrackingDocument" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="GetTrackingDocumentsRequest">
        <xs:sequence>
          <xs:element name="WebAuthenticationDetail" type="ns:WebAuthenticationDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data to be used in authentication of the sender's identity (and right to use FedEx web services).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClientDetail" type="ns:ClientDetail" minOccurs="1"/>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0"/>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1"/>
          <xs:element name="SelectionDetails" type="ns:TrackSelectionDetail" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="TrackingDocumentSpecification" type="ns:TrackingDocumentSpecification" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ImageSizeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="LARGE"/>
          <xs:enumeration value="SMALL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="LinearUnits">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CM"/>
          <xs:enumeration value="IN"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="LocalTimeRange">
        <xs:annotation>
          <xs:documentation>Time Range specified in local time.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Begins" type="xs:string" minOccurs="0"/>
          <xs:element name="Ends" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Localization">
        <xs:annotation>
          <xs:documentation>Identifies the representation of human-readable text.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="LanguageCode" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Two-letter code for language (e.g. EN, FR, etc.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocaleCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Two-letter code for the region (e.g. us, ca, etc..).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Measure">
        <xs:sequence>
          <xs:element name="Quantity" type="xs:decimal" minOccurs="0"/>
          <xs:element name="Units" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Money">
        <xs:sequence>
          <xs:element name="Currency" type="xs:string" minOccurs="0"/>
          <xs:element name="Amount" type="xs:decimal" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="NaftaCommodityDetail">
        <xs:sequence>
          <xs:element name="PreferenceCriterion" type="ns:NaftaPreferenceCriterionCode" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Defined by NAFTA regulations.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ProducerDetermination" type="ns:NaftaProducerDeterminationCode" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Defined by NAFTA regulations.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ProducerId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identification of which producer is associated with this commodity (if multiple producers are used in a single shipment).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NetCostMethod" type="ns:NaftaNetCostMethodCode" minOccurs="0"/>
          <xs:element name="NetCostDateRange" type="ns:DateRange" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Date range over which RVC net cost was calculated.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="NaftaNetCostMethodCode">
        <xs:restriction base="xs:string">
          <xs:enumeration value="NC"/>
          <xs:enumeration value="NO"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="NaftaPreferenceCriterionCode">
        <xs:annotation>
          <xs:documentation>See instructions for NAFTA Certificate of Origin for code definitions.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="A"/>
          <xs:enumeration value="B"/>
          <xs:enumeration value="C"/>
          <xs:enumeration value="D"/>
          <xs:enumeration value="E"/>
          <xs:enumeration value="F"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="NaftaProducerDeterminationCode">
        <xs:annotation>
          <xs:documentation>See instructions for NAFTA Certificate of Origin for code definitions.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="NO_1"/>
          <xs:enumeration value="NO_2"/>
          <xs:enumeration value="NO_3"/>
          <xs:enumeration value="YES"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Notification">
        <xs:annotation>
          <xs:documentation>The descriptive data regarding the result of the submitted transaction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Severity" type="ns:NotificationSeverityType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The severity of this notification. This can indicate success or failure or some other information about the request. The values that can be returned are SUCCESS - Your transaction succeeded with no other applicable information. NOTE - Additional information that may be of interest to you about your transaction. WARNING - Additional information that you need to know about your transaction that you may need to take action on. ERROR - Information about an error that occurred while processing your transaction. FAILURE - FedEx was unable to process your transaction at this time due to a system failure. Please try again later</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Source" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Indicates the source of this notification. Combined with the Code it uniquely identifies this notification</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Code" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A code that represents this notification. Combined with the Source it uniquely identifies this notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Message" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Human-readable text that explains this notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocalizedMessage" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The translated message. The language and locale specified in the ClientDetail. Localization are used to determine the representation. Currently only supported in a TrackReply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MessageParameters" type="ns:NotificationParameter" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>A collection of name/value pairs that provide specific data to help the client determine the nature of an error (or warning, etc.) without having to parse the message string.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="NotificationDetail">
        <xs:sequence>
          <xs:element name="NotificationType" type="ns:NotificationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the type of notification that will be sent.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EmailDetail" type="ns:EMailDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the email notification details.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Localization" type="ns:Localization" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the localization for this notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="NotificationEventType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ON_DELIVERY"/>
          <xs:enumeration value="ON_ESTIMATED_DELIVERY"/>
          <xs:enumeration value="ON_EXCEPTION"/>
          <xs:enumeration value="ON_SHIPMENT"/>
          <xs:enumeration value="ON_TENDER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="NotificationFormatType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="HTML"/>
          <xs:enumeration value="TEXT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="NotificationParameter">
        <xs:sequence>
          <xs:element name="Id" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the type of data contained in Value (e.g. SERVICE_TYPE, PACKAGE_SEQUENCE, etc..).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Value" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The value of the parameter (e.g. PRIORITY_OVERNIGHT, 2, etc..).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="NotificationSeverityType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ERROR"/>
          <xs:enumeration value="FAILURE"/>
          <xs:enumeration value="NOTE"/>
          <xs:enumeration value="SUCCESS"/>
          <xs:enumeration value="WARNING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="NotificationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EMAIL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="OfficeOrderDeliveryMethodType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="COURIER"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="PICKUP"/>
          <xs:enumeration value="SHIPMENT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="OperatingCompanyType">
        <xs:annotation>
          <xs:documentation>Identification for a FedEx operating company (transportation and non-transportation).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FEDEX_CARGO"/>
          <xs:enumeration value="FEDEX_CORPORATE_SERVICES"/>
          <xs:enumeration value="FEDEX_CORPORATION"/>
          <xs:enumeration value="FEDEX_CUSTOMER_INFORMATION_SYSTEMS"/>
          <xs:enumeration value="FEDEX_CUSTOM_CRITICAL"/>
          <xs:enumeration value="FEDEX_EXPRESS"/>
          <xs:enumeration value="FEDEX_FREIGHT"/>
          <xs:enumeration value="FEDEX_GROUND"/>
          <xs:enumeration value="FEDEX_KINKOS"/>
          <xs:enumeration value="FEDEX_OFFICE"/>
          <xs:enumeration value="FEDEX_SERVICES"/>
          <xs:enumeration value="FEDEX_TRADE_NETWORKS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="PackagingType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="FEDEX_10KG_BOX"/>
          <xs:enumeration value="FEDEX_25KG_BOX"/>
          <xs:enumeration value="FEDEX_BOX"/>
          <xs:enumeration value="FEDEX_ENVELOPE"/>
          <xs:enumeration value="FEDEX_EXTRA_LARGE_BOX"/>
          <xs:enumeration value="FEDEX_LARGE_BOX"/>
          <xs:enumeration value="FEDEX_MEDIUM_BOX"/>
          <xs:enumeration value="FEDEX_PAK"/>
          <xs:enumeration value="FEDEX_SMALL_BOX"/>
          <xs:enumeration value="FEDEX_TUBE"/>
          <xs:enumeration value="YOUR_PACKAGING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PagingDetail">
        <xs:sequence>
          <xs:element name="PagingToken" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>When the MoreData field = true in a TrackReply the PagingToken must be sent in the subsequent TrackRequest to retrieve the next page of data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NumberOfResultsPerPage" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the number of results to display per page when the there is more than one page in the subsequent TrackReply.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PhysicalPackagingType">
        <xs:annotation>
          <xs:documentation>This enumeration rationalizes the former FedEx Express international "admissibility package" types (based on ANSI X.12) and the FedEx Freight packaging types. The values represented are those common to both carriers.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BAG"/>
          <xs:enumeration value="BARREL"/>
          <xs:enumeration value="BASKET"/>
          <xs:enumeration value="BOX"/>
          <xs:enumeration value="BUCKET"/>
          <xs:enumeration value="BUNDLE"/>
          <xs:enumeration value="CAGE"/>
          <xs:enumeration value="CARTON"/>
          <xs:enumeration value="CASE"/>
          <xs:enumeration value="CHEST"/>
          <xs:enumeration value="CONTAINER"/>
          <xs:enumeration value="CRATE"/>
          <xs:enumeration value="CYLINDER"/>
          <xs:enumeration value="DRUM"/>
          <xs:enumeration value="ENVELOPE"/>
          <xs:enumeration value="HAMPER"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="PACKAGE"/>
          <xs:enumeration value="PAIL"/>
          <xs:enumeration value="PALLET"/>
          <xs:enumeration value="PARCEL"/>
          <xs:enumeration value="PIECE"/>
          <xs:enumeration value="REEL"/>
          <xs:enumeration value="ROLL"/>
          <xs:enumeration value="SACK"/>
          <xs:enumeration value="SHRINK_WRAPPED"/>
          <xs:enumeration value="SKID"/>
          <xs:enumeration value="TANK"/>
          <xs:enumeration value="TOTE_BIN"/>
          <xs:enumeration value="TUBE"/>
          <xs:enumeration value="UNIT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="PieceCountLocationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="DESTINATION"/>
          <xs:enumeration value="ORIGIN"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PieceCountVerificationDetail">
        <xs:sequence>
          <xs:element name="CountLocationType" type="ns:PieceCountLocationType" minOccurs="0"/>
          <xs:element name="Count" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="SendNotificationsReply">
        <xs:sequence>
          <xs:element name="HighestSeverity" type="ns:NotificationSeverityType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>This contains the severity type of the most severe Notification in the Notifications array.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Notifications" type="ns:Notification" minOccurs="1" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Information about the request/reply such was the transaction successful or not, and any additional information relevant to the request and/or reply. There may be multiple Notifications in a reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contains the CustomerTransactionDetail that is echoed back to the caller for matching requests and replies and a Localization element for defining the language/translation used in the reply data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Contains the version of the reply being used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DuplicateWaybill" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>True if duplicate packages (more than one package with the same tracking number) have been found, the packages array contains information about each duplicate. Use this information to determine which of the tracking numbers is the one you need and resend your request using the tracking number and TrackingNumberUniqueIdentifier for that package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MoreDataAvailable" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>True if additional packages remain to be retrieved.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PagingToken" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Value that must be passed in a TrackNotification request to retrieve the next set of packages (when MoreDataAvailable = true).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Packages" type="ns:TrackNotificationPackage" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Information about the notifications that are available for this tracking number. If there are duplicates the ship date and destination address information is returned for determining which TrackingNumberUniqueIdentifier to use on a subsequent request.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="SendNotificationsRequest">
        <xs:sequence>
          <xs:element name="WebAuthenticationDetail" type="ns:WebAuthenticationDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data to be used in authentication of the sender's identity (and right to use FedEx web services).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClientDetail" type="ns:ClientDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the client submitting the transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contains a free form field that is echoed back in the reply to match requests with replies and data that governs the data payload language/translations</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1"/>
          <xs:element name="TrackingNumber" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The tracking number to which the notifications will be triggered from.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MultiPiece" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates whether to return tracking information for all associated packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PagingToken" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>When the MoreDataAvailable field is true in a TrackNotificationReply the PagingToken must be sent in the subsequent TrackNotificationRequest to retrieve the next page of data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TrackingNumberUniqueId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Use this field when your original request informs you that there are duplicates of this tracking number. If you get duplicates you will also receive some information about each of the duplicate tracking numbers to enable you to chose one and resend that number along with the TrackingNumberUniqueId to get notifications for that tracking number.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipDateRangeBegin" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>To narrow the search to a period in time the ShipDateRangeBegin and ShipDateRangeEnd can be used to help eliminate duplicates.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipDateRangeEnd" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>To narrow the search to a period in time the ShipDateRangeBegin and ShipDateRangeEnd can be used to help eliminate duplicates.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SenderEMailAddress" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Included in the email notification identifying the requester of this notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SenderContactName" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Included in the email notification identifying the requester of this notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EventNotificationDetail" type="ns:ShipmentEventNotificationDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This replaces eMailNotificationDetail</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ServiceType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EUROPE_FIRST_INTERNATIONAL_PRIORITY"/>
          <xs:enumeration value="FEDEX_1_DAY_FREIGHT"/>
          <xs:enumeration value="FEDEX_2_DAY"/>
          <xs:enumeration value="FEDEX_2_DAY_AM"/>
          <xs:enumeration value="FEDEX_2_DAY_FREIGHT"/>
          <xs:enumeration value="FEDEX_3_DAY_FREIGHT"/>
          <xs:enumeration value="FEDEX_CARGO_AIRPORT_TO_AIRPORT"/>
          <xs:enumeration value="FEDEX_CARGO_FREIGHT_FORWARDING"/>
          <xs:enumeration value="FEDEX_CARGO_INTERNATIONAL_EXPRESS_FREIGHT"/>
          <xs:enumeration value="FEDEX_CARGO_INTERNATIONAL_PREMIUM"/>
          <xs:enumeration value="FEDEX_CARGO_MAIL"/>
          <xs:enumeration value="FEDEX_CARGO_REGISTERED_MAIL"/>
          <xs:enumeration value="FEDEX_CARGO_SURFACE_MAIL"/>
          <xs:enumeration value="FEDEX_CUSTOM_CRITICAL_AIR_EXPEDITE"/>
          <xs:enumeration value="FEDEX_CUSTOM_CRITICAL_AIR_EXPEDITE_EXCLUSIVE_USE"/>
          <xs:enumeration value="FEDEX_CUSTOM_CRITICAL_AIR_EXPEDITE_NETWORK"/>
          <xs:enumeration value="FEDEX_CUSTOM_CRITICAL_CHARTER_AIR"/>
          <xs:enumeration value="FEDEX_CUSTOM_CRITICAL_POINT_TO_POINT"/>
          <xs:enumeration value="FEDEX_CUSTOM_CRITICAL_SURFACE_EXPEDITE"/>
          <xs:enumeration value="FEDEX_CUSTOM_CRITICAL_SURFACE_EXPEDITE_EXCLUSIVE_USE"/>
          <xs:enumeration value="FEDEX_CUSTOM_CRITICAL_TEMP_ASSURE_AIR"/>
          <xs:enumeration value="FEDEX_CUSTOM_CRITICAL_TEMP_ASSURE_VALIDATED_AIR"/>
          <xs:enumeration value="FEDEX_CUSTOM_CRITICAL_WHITE_GLOVE_SERVICES"/>
          <xs:enumeration value="FEDEX_DISTANCE_DEFERRED"/>
          <xs:enumeration value="FEDEX_EXPRESS_SAVER"/>
          <xs:enumeration value="FEDEX_FIRST_FREIGHT"/>
          <xs:enumeration value="FEDEX_FREIGHT_ECONOMY"/>
          <xs:enumeration value="FEDEX_FREIGHT_PRIORITY"/>
          <xs:enumeration value="FEDEX_GROUND"/>
          <xs:enumeration value="FEDEX_NEXT_DAY_AFTERNOON"/>
          <xs:enumeration value="FEDEX_NEXT_DAY_EARLY_MORNING"/>
          <xs:enumeration value="FEDEX_NEXT_DAY_END_OF_DAY"/>
          <xs:enumeration value="FEDEX_NEXT_DAY_FREIGHT"/>
          <xs:enumeration value="FEDEX_NEXT_DAY_MID_MORNING"/>
          <xs:enumeration value="FIRST_OVERNIGHT"/>
          <xs:enumeration value="GROUND_HOME_DELIVERY"/>
          <xs:enumeration value="INTERNATIONAL_DISTRIBUTION_FREIGHT"/>
          <xs:enumeration value="INTERNATIONAL_ECONOMY"/>
          <xs:enumeration value="INTERNATIONAL_ECONOMY_DISTRIBUTION"/>
          <xs:enumeration value="INTERNATIONAL_ECONOMY_FREIGHT"/>
          <xs:enumeration value="INTERNATIONAL_FIRST"/>
          <xs:enumeration value="INTERNATIONAL_PRIORITY"/>
          <xs:enumeration value="INTERNATIONAL_PRIORITY_DISTRIBUTION"/>
          <xs:enumeration value="INTERNATIONAL_PRIORITY_EXPRESS"/>
          <xs:enumeration value="INTERNATIONAL_PRIORITY_FREIGHT"/>
          <xs:enumeration value="PRIORITY_OVERNIGHT"/>
          <xs:enumeration value="SAME_DAY"/>
          <xs:enumeration value="SAME_DAY_CITY"/>
          <xs:enumeration value="SMART_POST"/>
          <xs:enumeration value="STANDARD_OVERNIGHT"/>
          <xs:enumeration value="TRANSBORDER_DISTRIBUTION_CONSOLIDATION"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShipmentEventNotificationDetail">
        <xs:sequence>
          <xs:element name="AggregationType" type="ns:ShipmentNotificationAggregationType" minOccurs="0"/>
          <xs:element name="PersonalMessage" type="xs:string" minOccurs="0"/>
          <xs:element name="EventNotifications" type="ns:ShipmentEventNotificationSpecification" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipmentEventNotificationSpecification">
        <xs:sequence>
          <xs:element name="Role" type="ns:ShipmentNotificationRoleType" minOccurs="0"/>
          <xs:element name="Events" type="ns:NotificationEventType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="NotificationDetail" type="ns:NotificationDetail" minOccurs="0"/>
          <xs:element name="FormatSpecification" type="ns:ShipmentNotificationFormatSpecification" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShipmentNotificationAggregationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="PER_PACKAGE"/>
          <xs:enumeration value="PER_SHIPMENT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShipmentNotificationFormatSpecification">
        <xs:sequence>
          <xs:element name="Type" type="ns:NotificationFormatType" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShipmentNotificationRoleType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="RECIPIENT"/>
          <xs:enumeration value="SHIPPER"/>
          <xs:enumeration value="THIRD_PARTY"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="SignatureImageDetail">
        <xs:sequence>
          <xs:element name="Image" type="xs:base64Binary" minOccurs="0"/>
          <xs:element name="Notifications" type="ns:Notification" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="SpecialInstructionStatusDetail">
        <xs:sequence>
          <xs:element name="Status" type="ns:SpecialInstructionsStatusCode" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the status of the track special instructions requested.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="StatusCreateTime" type="xs:dateTime" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Time when the status was changed.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="SpecialInstructionsStatusCode">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACCEPTED"/>
          <xs:enumeration value="CANCELLED"/>
          <xs:enumeration value="DENIED"/>
          <xs:enumeration value="HELD"/>
          <xs:enumeration value="MODIFIED"/>
          <xs:enumeration value="RELINQUISHED"/>
          <xs:enumeration value="REQUESTED"/>
          <xs:enumeration value="SET"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="StringBarcode">
        <xs:annotation>
          <xs:documentation>Each instance of this data type represents a barcode whose content must be represented as ASCII text (i.e. not binary data).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Type" type="ns:StringBarcodeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The kind of barcode data in this instance.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Value" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The data content of this instance.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="StringBarcodeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADDRESS"/>
          <xs:enumeration value="ASTRA"/>
          <xs:enumeration value="FEDEX_1D"/>
          <xs:enumeration value="GROUND"/>
          <xs:enumeration value="POSTAL"/>
          <xs:enumeration value="USPS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackAdvanceNotificationDetail">
        <xs:sequence>
          <xs:element name="EstimatedTimeOfArrival" type="xs:dateTime" minOccurs="0"/>
          <xs:element name="Reason" type="xs:string" minOccurs="0"/>
          <xs:element name="Status" type="ns:TrackAdvanceNotificationStatusType" minOccurs="0"/>
          <xs:element name="StatusDescription" type="xs:string" minOccurs="0"/>
          <xs:element name="StatusTime" type="xs:dateTime" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackAdvanceNotificationStatusType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BACK_ON_TRACK"/>
          <xs:enumeration value="FAIL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackChargeDetail">
        <xs:sequence>
          <xs:element name="Type" type="ns:TrackChargeDetailType" minOccurs="0"/>
          <xs:element name="ChargeAmount" type="ns:Money" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackChargeDetailType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ORIGINAL_CHARGES"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="TrackChargesPaymentClassificationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="DUTIES_AND_TAXES"/>
          <xs:enumeration value="TRANSPORTATION"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="TrackDeliveryLocationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="APARTMENT_OFFICE"/>
          <xs:enumeration value="FEDEX_LOCATION"/>
          <xs:enumeration value="GATE_HOUSE"/>
          <xs:enumeration value="GUARD_OR_SECURITY_STATION"/>
          <xs:enumeration value="IN_BOND_OR_CAGE"/>
          <xs:enumeration value="LEASING_OFFICE"/>
          <xs:enumeration value="MAILROOM"/>
          <xs:enumeration value="MAIN_OFFICE"/>
          <xs:enumeration value="MANAGER_OFFICE"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="PHARMACY"/>
          <xs:enumeration value="RECEPTIONIST_OR_FRONT_DESK"/>
          <xs:enumeration value="RENTAL_OFFICE"/>
          <xs:enumeration value="RESIDENCE"/>
          <xs:enumeration value="SHIPPING_RECEIVING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="TrackDeliveryOptionType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="APPOINTMENT"/>
          <xs:enumeration value="DATE_CERTAIN"/>
          <xs:enumeration value="ELECTRONIC_SIGNATURE_RELEASE"/>
          <xs:enumeration value="EVENING"/>
          <xs:enumeration value="REDIRECT_TO_HOLD_AT_LOCATION"/>
          <xs:enumeration value="REROUTE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackDetail">
        <xs:sequence>
          <xs:element name="Notification" type="ns:Notification" minOccurs="0">
            <xs:annotation>
              <xs:documentation>To report soft error on an individual track detail.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TrackingNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The FedEx package identifier.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Barcode" type="ns:StringBarcode" minOccurs="0"/>
          <xs:element name="TrackingNumberUniqueIdentifier" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>When duplicate tracking numbers exist this data is returned with summary information for each of the duplicates. The summary information is used to determine which of the duplicates the intended tracking number is. This identifier is used on a subsequent track request to retrieve the tracking data for the desired tracking number.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="StatusDetail" type="ns:TrackStatusDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies details about the status of the shipment being tracked.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="InformationNotes" type="ns:TrackInformationNoteDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Notifications to the end user that provide additional information relevant to the tracked shipment. For example, a notification may indicate that a change in behavior has occurred.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerExceptionRequests" type="ns:CustomerExceptionRequestDetail" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="Reconciliation" type="ns:TrackReconciliation" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used to report the status of a piece of a multiple piece shipment which is no longer traveling with the rest of the packages in the shipment or has not been accounted for.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ServiceCommitMessage" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used to convey information such as. 1. FedEx has received information about a package but has not yet taken possession of it. 2. FedEx has handed the package off to a third party for final delivery. 3. The package delivery has been cancelled</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DestinationServiceArea" type="xs:string" minOccurs="0"/>
          <xs:element name="DestinationServiceAreaDescription" type="xs:string" minOccurs="0"/>
          <xs:element name="CarrierCode" type="ns:CarrierCodeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies a FedEx operating company (transportation).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OperatingCompany" type="ns:OperatingCompanyType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies operating transportation company that is the specific to the carrier code.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OperatingCompanyOrCarrierDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies a detailed description about the carrier or the operating company.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CartageAgentCompanyName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>If the package was interlined to a cartage agent, this is the name of the cartage agent. (Returned for CSR SL only.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ProductionLocationContactAndAddress" type="ns:ContactAndAddress" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the FXO production centre contact and address.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OtherIdentifiers" type="ns:TrackOtherIdentifierDetail" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="FormId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>(Returned for CSR SL only.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Service" type="ns:TrackServiceDescriptionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies details about service such as service description and type.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackageWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The weight of this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackageDimensions" type="ns:Dimensions" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Physical dimensions of the package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackageDimensionalWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The dimensional weight of the package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipmentWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The weight of the entire shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Packaging" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Retained for legacy compatibility only.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackagingType" type="ns:PackagingType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Strict representation of the Packaging type (e.g. FEDEX_BOX, YOUR_PACKAGING).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PhysicalPackagingType" type="ns:PhysicalPackagingType" minOccurs="0"/>
          <xs:element name="PackageSequenceNumber" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The sequence number of this package in a shipment. This would be 2 if it was package number 2 of 4.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackageCount" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The number of packages in this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CreatorSoftwareId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>FOR FEDEX INTERNAL USE ONLY: Specifies the software id of the device that was used to create this tracked shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Charges" type="ns:TrackChargeDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the details about the SPOC details.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NickName" type="xs:string" minOccurs="0"/>
          <xs:element name="Notes" type="xs:string" minOccurs="0"/>
          <xs:element name="Attributes" type="ns:TrackDetailAttributeType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="ShipmentContents" type="ns:ContentRecord" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="PackageContents" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="ClearanceLocationCode" type="xs:string" minOccurs="0"/>       
          <xs:element name="Commodities" type="ns:Commodity" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="ReturnDetail" type="ns:TrackReturnDetail" minOccurs="0"/>
          <xs:element name="CustomsOptionDetails" type="ns:CustomsOptionDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the reason for return.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AdvanceNotificationDetail" type="ns:TrackAdvanceNotificationDetail" minOccurs="0"/>
          <xs:element name="SpecialHandlings" type="ns:TrackSpecialHandling" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>List of special handlings that applied to this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Payments" type="ns:TrackPayment" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the details about the payments for the shipment being tracked.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Shipper" type="ns:Contact" minOccurs="0">
            <xs:annotation>
              <xs:documentation>(Returned for CSR SL only.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PossessionStatus" type="ns:TrackPossessionStatusType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates last-known possession of package (Returned for CSR SL only.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipperAddress" type="ns:Address" minOccurs="0"/>
          <xs:element name="OriginLocationAddress" type="ns:Address" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The address of the FedEx pickup location/facility.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OriginStationId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>(Returned for CSR SL only.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DatesOrTimes" type="ns:TrackingDateOrTimestamp" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="TotalTransitDistance" type="ns:Distance" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The distance from the origin to the destination. Returned for Custom Critical shipments.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DistanceToDestination" type="ns:Distance" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total distance package still has to travel. Returned for Custom Critical shipments.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SpecialInstructions" type="ns:TrackSpecialInstruction" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Provides additional details about package delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Recipient" type="ns:Contact" minOccurs="0">
            <xs:annotation>
              <xs:documentation>(Returned for CSR SL only.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LastUpdatedDestinationAddress" type="ns:Address" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This is the latest updated destination address.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DestinationAddress" type="ns:Address" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The address this package is to be (or has been) delivered.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="HoldAtLocationContact" type="ns:Contact" minOccurs="0"/>
          <xs:element name="HoldAtLocationAddress" type="ns:Address" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The address this package is requested to placed on hold.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DestinationStationId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>(Returned for CSR SL only.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DestinationLocationAddress" type="ns:Address" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The address of the FedEx delivery location/facility.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DestinationLocationType" type="ns:FedExLocationType" minOccurs="0"/>
          <xs:element name="DestinationLocationTimeZoneOffset" type="xs:string" minOccurs="0"/>
          <xs:element name="ActualDeliveryAddress" type="ns:Address" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Actual address where package was delivered. Differs from destinationAddress, which indicates where the package was to be delivered; This field tells where delivery actually occurred (next door, at station, etc.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OfficeOrderDeliveryMethod" type="ns:OfficeOrderDeliveryMethodType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the method of office order delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeliveryLocationType" type="ns:TrackDeliveryLocationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Strict text indicating the delivery location at the delivered to address.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeliveryLocationDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>User/screen friendly representation of the DeliveryLocationType (delivery location at the delivered to address). Can be returned in localized text.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeliveryAttempts" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the number of delivery attempts made to deliver the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeliverySignatureName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This is either the name of the person that signed for the package or "Signature not requested" or "Signature on file".</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PieceCountVerificationDetails" type="ns:PieceCountVerificationDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the details about the count of the packages delivered at the delivery location and the count of the packages at the origin.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalUniqueAddressCountInConsolidation" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the total number of unique addresses on the CRNs in a consolidation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AvailableImages" type="ns:AvailableImagesDetail" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="Signature" type="ns:SignatureImageDetail" minOccurs="0"/>
          <xs:element name="NotificationEventsAvailable" type="ns:NotificationEventType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="SplitShipmentParts" type="ns:TrackSplitShipmentPart" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Returned for cargo shipments only when they are currently split across vehicles.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeliveryOptionEligibilityDetails" type="ns:DeliveryOptionEligibilityDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the details about the eligibility for different delivery options.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Events" type="ns:TrackEvent" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Event information for a tracking number.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackDetailAttributeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="INCLUDED_IN_WATCHLIST"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackEvent">
        <xs:annotation>
          <xs:documentation>FedEx scanning information about a package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Timestamp" type="xs:dateTime" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The time this event occurred.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EventType" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Carrier's scan code. Pairs with EventDescription.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EventDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Literal description that pairs with the EventType.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="StatusExceptionCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Further defines the Scan Type code's specific type (e.g., DEX08 business closed). Pairs with StatusExceptionDescription.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="StatusExceptionDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Literal description that pairs with the StatusExceptionCode.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Address" type="ns:Address" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Address information of the station that is responsible for the scan.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="StationId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>FedEx location ID where the scan took place. (Returned for CSR SL only.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ArrivalLocation" type="ns:ArrivalLocationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates where the arrival actually occurred.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackIdentifierType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BILL_OF_LADING"/>
          <xs:enumeration value="COD_RETURN_TRACKING_NUMBER"/>
          <xs:enumeration value="CUSTOMER_AUTHORIZATION_NUMBER"/>
          <xs:enumeration value="CUSTOMER_REFERENCE"/>
          <xs:enumeration value="DEPARTMENT"/>
          <xs:enumeration value="DOCUMENT_AIRWAY_BILL"/>
          <xs:enumeration value="FREE_FORM_REFERENCE"/>
          <xs:enumeration value="GROUND_INTERNATIONAL"/>
          <xs:enumeration value="GROUND_SHIPMENT_ID"/>
          <xs:enumeration value="GROUP_MPS"/>
          <xs:enumeration value="INVOICE"/>
          <xs:enumeration value="JOB_GLOBAL_TRACKING_NUMBER"/>
          <xs:enumeration value="ORDER_GLOBAL_TRACKING_NUMBER"/>
          <xs:enumeration value="ORDER_TO_PAY_NUMBER"/>
          <xs:enumeration value="OUTBOUND_LINK_TO_RETURN"/>
          <xs:enumeration value="PARTNER_CARRIER_NUMBER"/>
          <xs:enumeration value="PART_NUMBER"/>
          <xs:enumeration value="PURCHASE_ORDER"/>
          <xs:enumeration value="REROUTE_TRACKING_NUMBER"/>
          <xs:enumeration value="RETURNED_TO_SHIPPER_TRACKING_NUMBER"/>
          <xs:enumeration value="RETURN_MATERIALS_AUTHORIZATION"/>
          <xs:enumeration value="SHIPPER_REFERENCE"/>
          <xs:enumeration value="STANDARD_MPS"/>
          <xs:enumeration value="TRACKING_NUMBER_OR_DOORTAG"/>
          <xs:enumeration value="TRANSPORTATION_CONTROL_NUMBER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackInformationNoteDetail">
        <xs:sequence>
          <xs:element name="Code" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A code that designates the type of informational message being returned.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The informational message in human readable form.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackNotificationPackage">
        <xs:sequence>
          <xs:element name="TrackingNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>FedEx assigned identifier for a package/shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TrackingNumberUniqueIdentifiers" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>When duplicate tracking numbers exist this data is returned with summary information for each of the duplicates. The summary information is used to determine which of the duplicates the intended tracking number is. This identifier is used on a subsequent track request to retrieve the tracking data for the desired tracking number.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CarrierCode" type="ns:CarrierCodeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identification of a FedEx operating company (transportation).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipDate" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The date the package was shipped (tendered to FedEx).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Destination" type="ns:Address" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The destination address of this package. Only city, state/province, and country are returned.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RecipientDetails" type="ns:TrackNotificationRecipientDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Options available for a tracking notification recipient.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackNotificationRecipientDetail">
        <xs:annotation>
          <xs:documentation>Options available for a tracking notification recipient.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="NotificationEventsAvailable" type="ns:NotificationEventType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackOtherIdentifierDetail">
        <xs:sequence>
          <xs:element name="PackageIdentifier" type="ns:TrackPackageIdentifier" minOccurs="0"/>
          <xs:element name="TrackingNumberUniqueIdentifier" type="xs:string" minOccurs="0"/>
          <xs:element name="CarrierCode" type="ns:CarrierCodeType" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackPackageIdentifier">
        <xs:annotation>
          <xs:documentation>The type and value of the package identifier that is to be used to retrieve the tracking information for a package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Type" type="ns:TrackIdentifierType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The type of the Value to be used to retrieve tracking information for a package (e.g. SHIPPER_REFERENCE, PURCHASE_ORDER, TRACKING_NUMBER_OR_DOORTAG, etc..) .</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Value" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The value to be used to retrieve tracking information for a package.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackPayment">
        <xs:sequence>
          <xs:element name="Classification" type="ns:TrackChargesPaymentClassificationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the classification of the charges being paid.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Type" type="ns:TrackPaymentType" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackPaymentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CASH_OR_CHECK_AT_DESTINATION"/>
          <xs:enumeration value="CASH_OR_CHECK_AT_ORIGIN"/>
          <xs:enumeration value="CREDIT_CARD_AT_DESTINATION"/>
          <xs:enumeration value="CREDIT_CARD_AT_ORIGIN"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="RECIPIENT_ACCOUNT"/>
          <xs:enumeration value="SHIPPER_ACCOUNT"/>
          <xs:enumeration value="THIRD_PARTY_ACCOUNT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="TrackPossessionStatusType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER"/>
          <xs:enumeration value="CARRIER"/>
          <xs:enumeration value="CUSTOMS"/>
          <xs:enumeration value="RECIPIENT"/>
          <xs:enumeration value="SHIPPER"/>
          <xs:enumeration value="SPLIT_STATUS"/>
          <xs:enumeration value="TRANSFER_PARTNER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackReconciliation">
        <xs:annotation>
          <xs:documentation>Used to report the status of a piece of a multiple piece shipment which is no longer traveling with the rest of the packages in the shipment or has not been accounted for.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Status" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>An identifier for this type of status.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>A human-readable description of this status.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackReply">
        <xs:sequence>
          <xs:element name="HighestSeverity" type="ns:NotificationSeverityType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>This contains the severity type of the most severe Notification in the Notifications array.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Notifications" type="ns:Notification" minOccurs="1" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Information about the request/reply such was the transaction successful or not, and any additional information relevant to the request and/or reply. There may be multiple Notifications in a reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contains the CustomerTransactionDetail that is echoed back to the caller for matching requests and replies and a Localization element for defining the language/translation used in the reply data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Contains the version of the reply being used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CompletedTrackDetails" type="ns:CompletedTrackDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Contains detailed tracking entity information.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackRequest">
        <xs:sequence>
          <xs:element name="WebAuthenticationDetail" type="ns:WebAuthenticationDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data to be used in authentication of the sender's identity (and right to use FedEx web services).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClientDetail" type="ns:ClientDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the client submitting the transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contains a free form field that is echoed back in the reply to match requests with replies and data that governs the data payload language/translations.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The version of the request being used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SelectionDetails" type="ns:TrackSelectionDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the details needed to select the shipment being requested to be tracked.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionTimeOutValueInMilliseconds" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The customer can specify a desired time out value for this particular transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ProcessingOptions" type="ns:TrackRequestProcessingOptionType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackRequestProcessingOptionType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="INCLUDE_DETAILED_SCANS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackReturnDetail">
        <xs:sequence>
          <xs:element name="MovementStatus" type="ns:TrackReturnMovementStatusType" minOccurs="0"/>
          <xs:element name="LabelType" type="ns:TrackReturnLabelType" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="AuthorizationName" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackReturnLabelType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EMAIL"/>
          <xs:enumeration value="PRINT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="TrackReturnMovementStatusType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="MOVEMENT_OCCURRED"/>
          <xs:enumeration value="NO_MOVEMENT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackSelectionDetail">
        <xs:sequence>
          <xs:element name="CarrierCode" type="ns:CarrierCodeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The FedEx operating company (transportation) used for this package's delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OperatingCompany" type="ns:OperatingCompanyType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies operating transportation company that is the specific to the carrier code.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackageIdentifier" type="ns:TrackPackageIdentifier" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The type and value of the package identifier that is to be used to retrieve the tracking information for a package or group of packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TrackingNumberUniqueIdentifier" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used to distinguish duplicate FedEx tracking numbers.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipDateRangeBegin" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>To narrow the search to a period in time the ShipDateRangeBegin and ShipDateRangeEnd can be used to help eliminate duplicates.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipDateRangeEnd" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>To narrow the search to a period in time the ShipDateRangeBegin and ShipDateRangeEnd can be used to help eliminate duplicates.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipmentAccountNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For tracking by references information either the account number or destination postal code and country must be provided.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SecureSpodAccount" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the SPOD account number for the shipment being tracked.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Destination" type="ns:Address" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For tracking by references information either the account number or destination postal code and country must be provided.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PagingDetail" type="ns:PagingDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the details about how to retrieve the subsequent pages when there is more than one page in the TrackReply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerSpecifiedTimeOutValueInMilliseconds" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The customer can specify a desired time out value for this particular tracking number.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackServiceDescriptionDetail">
        <xs:sequence>
          <xs:element name="Type" type="ns:ServiceType" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="ShortDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies a shorter description for the service that is calculated per the service code.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackSpecialHandling">
        <xs:sequence>
          <xs:element name="Type" type="ns:TrackSpecialHandlingType" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="PaymentType" type="ns:TrackPaymentType" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackSpecialHandlingType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACCESSIBLE_DANGEROUS_GOODS"/>
          <xs:enumeration value="ADULT_SIGNATURE_OPTION"/>
          <xs:enumeration value="AIRBILL_AUTOMATION"/>
          <xs:enumeration value="AIRBILL_DELIVERY"/>
          <xs:enumeration value="ALCOHOL"/>
          <xs:enumeration value="AM_DELIVERY_GUARANTEE"/>
          <xs:enumeration value="APPOINTMENT_DELIVERY"/>
          <xs:enumeration value="BATTERY"/>
          <xs:enumeration value="BILL_RECIPIENT"/>
          <xs:enumeration value="BROKER_SELECT_OPTION"/>
          <xs:enumeration value="CALL_BEFORE_DELIVERY"/>
          <xs:enumeration value="CALL_TAG"/>
          <xs:enumeration value="CALL_TAG_DAMAGE"/>
          <xs:enumeration value="CHARGEABLE_CODE"/>
          <xs:enumeration value="COD"/>
          <xs:enumeration value="COLLECT"/>
          <xs:enumeration value="CONSOLIDATION"/>
          <xs:enumeration value="CONSOLIDATION_SMALLS_BAG"/>
          <xs:enumeration value="CURRENCY"/>
          <xs:enumeration value="CUT_FLOWERS"/>
          <xs:enumeration value="DATE_CERTAIN_DELIVERY"/>
          <xs:enumeration value="DELIVERY_ON_INVOICE_ACCEPTANCE"/>
          <xs:enumeration value="DELIVERY_REATTEMPT"/>
          <xs:enumeration value="DELIVERY_RECEIPT"/>
          <xs:enumeration value="DELIVER_WEEKDAY"/>
          <xs:enumeration value="DIRECT_SIGNATURE_OPTION"/>
          <xs:enumeration value="DOMESTIC"/>
          <xs:enumeration value="DO_NOT_BREAK_DOWN_PALLETS"/>
          <xs:enumeration value="DO_NOT_STACK_PALLETS"/>
          <xs:enumeration value="DRY_ICE"/>
          <xs:enumeration value="DRY_ICE_ADDED"/>
          <xs:enumeration value="EAST_COAST_SPECIAL"/>
          <xs:enumeration value="ELECTRONIC_COD"/>
          <xs:enumeration value="ELECTRONIC_DOCUMENTS_WITH_ORIGINALS"/>
          <xs:enumeration value="ELECTRONIC_SIGNATURE_SERVICE"/>
          <xs:enumeration value="ELECTRONIC_TRADE_DOCUMENTS"/>
          <xs:enumeration value="EVENING_DELIVERY"/>
          <xs:enumeration value="EXCLUSIVE_USE"/>
          <xs:enumeration value="EXTENDED_DELIVERY"/>
          <xs:enumeration value="EXTENDED_PICKUP"/>
          <xs:enumeration value="EXTRA_LABOR"/>
          <xs:enumeration value="EXTREME_LENGTH"/>
          <xs:enumeration value="FOOD"/>
          <xs:enumeration value="FREIGHT_ON_VALUE_CARRIER_RISK"/>
          <xs:enumeration value="FREIGHT_ON_VALUE_OWN_RISK"/>
          <xs:enumeration value="FREIGHT_TO_COLLECT"/>
          <xs:enumeration value="FULLY_REGULATED_DANGEROUS_GOODS"/>
          <xs:enumeration value="GEL_PACKS_ADDED_OR_REPLACED"/>
          <xs:enumeration value="GROUND_SUPPORT_FOR_SMARTPOST"/>
          <xs:enumeration value="GUARANTEED_FUNDS"/>
          <xs:enumeration value="HAZMAT"/>
          <xs:enumeration value="HIGH_FLOOR"/>
          <xs:enumeration value="HOLD_AT_LOCATION"/>
          <xs:enumeration value="HOLIDAY_DELIVERY"/>
          <xs:enumeration value="INACCESSIBLE_DANGEROUS_GOODS"/>
          <xs:enumeration value="INDIRECT_SIGNATURE_OPTION"/>
          <xs:enumeration value="INSIDE_DELIVERY"/>
          <xs:enumeration value="INSIDE_PICKUP"/>
          <xs:enumeration value="INTERNATIONAL"/>
          <xs:enumeration value="INTERNATIONAL_CONTROLLED_EXPORT"/>
          <xs:enumeration value="INTERNATIONAL_MAIL_SERVICE"/>
          <xs:enumeration value="INTERNATIONAL_TRAFFIC_IN_ARMS_REGULATIONS"/>
          <xs:enumeration value="LIFTGATE"/>
          <xs:enumeration value="LIFTGATE_DELIVERY"/>
          <xs:enumeration value="LIFTGATE_PICKUP"/>
          <xs:enumeration value="LIMITED_ACCESS_DELIVERY"/>
          <xs:enumeration value="LIMITED_ACCESS_PICKUP"/>
          <xs:enumeration value="LIMITED_QUANTITIES_DANGEROUS_GOODS"/>
          <xs:enumeration value="MARKING_OR_TAGGING"/>
          <xs:enumeration value="NET_RETURN"/>
          <xs:enumeration value="NON_BUSINESS_TIME"/>
          <xs:enumeration value="NON_STANDARD_CONTAINER"/>
          <xs:enumeration value="NO_SIGNATURE_REQUIRED_SIGNATURE_OPTION"/>
          <xs:enumeration value="ORDER_NOTIFY"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="OTHER_REGULATED_MATERIAL_DOMESTIC"/>
          <xs:enumeration value="OVER_LENGTH"/>
          <xs:enumeration value="PACKAGE_RETURN_PROGRAM"/>
          <xs:enumeration value="PIECE_COUNT_VERIFICATION"/>
          <xs:enumeration value="POISON"/>
          <xs:enumeration value="PREPAID"/>
          <xs:enumeration value="PRIORITY_ALERT"/>
          <xs:enumeration value="PRIORITY_ALERT_PLUS"/>
          <xs:enumeration value="PROTECTION_FROM_FREEZING"/>
          <xs:enumeration value="RAIL_MODE"/>
          <xs:enumeration value="RECONSIGNMENT_CHARGES"/>
          <xs:enumeration value="REROUTE_CROSS_COUNTRY_DEFERRED"/>
          <xs:enumeration value="REROUTE_CROSS_COUNTRY_EXPEDITED"/>
          <xs:enumeration value="REROUTE_LOCAL"/>
          <xs:enumeration value="RESIDENTIAL_DELIVERY"/>
          <xs:enumeration value="RESIDENTIAL_PICKUP"/>
          <xs:enumeration value="RETURNS_CLEARANCE"/>
          <xs:enumeration value="RETURNS_CLEARANCE_SPECIAL_ROUTING_REQUIRED"/>
          <xs:enumeration value="RETURN_MANAGER"/>
          <xs:enumeration value="SATURDAY_DELIVERY"/>
          <xs:enumeration value="SHIPMENT_PLACED_IN_COLD_STORAGE"/>
          <xs:enumeration value="SINGLE_SHIPMENT"/>
          <xs:enumeration value="SMALL_QUANTITY_EXCEPTION"/>
          <xs:enumeration value="SORT_AND_SEGREGATE"/>
          <xs:enumeration value="SPECIAL_DELIVERY"/>
          <xs:enumeration value="SPECIAL_EQUIPMENT"/>
          <xs:enumeration value="STANDARD_GROUND_SERVICE"/>
          <xs:enumeration value="STORAGE"/>
          <xs:enumeration value="SUNDAY_DELIVERY"/>
          <xs:enumeration value="THIRD_PARTY_BILLING"/>
          <xs:enumeration value="THIRD_PARTY_CONSIGNEE"/>
          <xs:enumeration value="TOP_LOAD"/>
          <xs:enumeration value="WEEKEND_DELIVERY"/>
          <xs:enumeration value="WEEKEND_PICKUP"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackSpecialInstruction">
        <xs:sequence>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="DeliveryOption" type="ns:TrackDeliveryOptionType" minOccurs="0"/>
          <xs:element name="StatusDetail" type="ns:SpecialInstructionStatusDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the status and status update time of the track special instructions.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OriginalEstimatedDeliveryTimestamp" type="xs:dateTime" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the estimated delivery time that was originally estimated when the shipment was shipped.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OriginalRequestTime" type="xs:dateTime" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the time the customer requested a change to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestedAppointmentTime" type="ns:AppointmentDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The requested appointment time for delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackSplitShipmentPart">
        <xs:annotation>
          <xs:documentation>Used when a cargo shipment is split across vehicles. This is used to give the status of each part of the shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PieceCount" type="xs:positiveInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The number of pieces in this part.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Timestamp" type="xs:dateTime" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The date and time this status began.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="StatusCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A code that identifies this type of status.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="StatusDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A human-readable description of this status.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackStatusAncillaryDetail">
        <xs:sequence>
          <xs:element name="Reason" type="xs:string" minOccurs="0"/>
          <xs:element name="ReasonDescription" type="xs:string" minOccurs="0"/>
          <xs:element name="Action" type="xs:string" minOccurs="0"/>
          <xs:element name="ActionDescription" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackStatusDetail">
        <xs:annotation>
          <xs:documentation>Specifies the details about the status of the track information for the shipments being tracked.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="CreationTime" type="xs:dateTime" minOccurs="0"/>
          <xs:element name="Code" type="xs:string" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="Location" type="ns:Address" minOccurs="0"/>
          <xs:element name="AncillaryDetails" type="ns:TrackStatusAncillaryDetail" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackingBillOfLadingDocumentDetail">
        <xs:sequence>
          <xs:element name="DocumentFormat" type="ns:TrackingDocumentFormat" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackingDateOrTimestamp">
        <xs:sequence>
          <xs:element name="Type" type="ns:TrackingDateOrTimestampType" minOccurs="0"/>
          <xs:element name="DateOrTimestamp" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackingDateOrTimestampType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACTUAL_DELIVERY"/>
          <xs:enumeration value="ACTUAL_PICKUP"/>
          <xs:enumeration value="ACTUAL_TENDER"/>
          <xs:enumeration value="ANTICIPATED_TENDER"/>
          <xs:enumeration value="APPOINTMENT_DELIVERY"/>
          <xs:enumeration value="ESTIMATED_DELIVERY"/>
          <xs:enumeration value="ESTIMATED_PICKUP"/>
          <xs:enumeration value="ESTIMATED_RETURN_TO_STATION"/>
          <xs:enumeration value="SHIP"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackingDocument">
        <xs:sequence>
          <xs:element name="Type" type="ns:TrackingDocumentType" minOccurs="0"/>
          <xs:element name="Localizations" type="ns:Localization" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="ImageType" type="ns:TrackingDocumentImageType" minOccurs="0"/>
          <xs:element name="Resolution" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="Parts" type="ns:DocumentPart" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackingDocumentDispositionDetail">
        <xs:sequence>
          <xs:element name="DispositionType" type="ns:TrackingDocumentDispositionType" minOccurs="0"/>
          <xs:element name="EMailDetail" type="ns:TrackingDocumentEmailDetail" minOccurs="0"/>
          <xs:element name="FaxDetails" type="ns:FaxDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the information used to fax the document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackingDocumentDispositionType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EMAIL"/>
          <xs:enumeration value="FAX"/>
          <xs:enumeration value="RETURN"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackingDocumentEmailDetail">
        <xs:sequence>
          <xs:element name="Recipients" type="ns:EMailDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the recipients of the email.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Sender" type="ns:EMailDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the person initiating the email.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Localization" type="ns:Localization" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This is the localization of the email content.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PersonalMessage" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A message included in the body of the email.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackingDocumentFormat">
        <xs:sequence>
          <xs:element name="Dispositions" type="ns:TrackingDocumentDispositionDetail" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="Grouping" type="ns:TrackingDocumentGroupingType" minOccurs="0"/>
          <xs:element name="ImageType" type="ns:TrackingDocumentImageType" minOccurs="0"/>
          <xs:element name="Localization" type="ns:Localization" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The localization for the generated document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackingDocumentGroupingType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CONSOLIDATED_BY_DOCUMENT_TYPE"/>
          <xs:enumeration value="INDIVIDUAL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="TrackingDocumentImageType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="PDF"/>
          <xs:enumeration value="PNG"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackingDocumentSpecification">
        <xs:sequence>
          <xs:element name="DocumentTypes" type="ns:TrackingDocumentType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="BillOfLadingDocumentDetail" type="ns:TrackingBillOfLadingDocumentDetail" minOccurs="0"/>
          <xs:element name="FreightBillingDocumentDetail" type="ns:TrackingFreightBillingDocumentDetail" minOccurs="0"/>
          <xs:element name="SignatureProofOfDeliveryDetail" type="ns:TrackingSignatureProofOfDeliveryDetail" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackingDocumentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BILL_OF_LADING"/>
          <xs:enumeration value="FREIGHT_BILLING_DOCUMENT"/>
          <xs:enumeration value="SIGNATURE_PROOF_OF_DELIVERY"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackingFreightBillingDocumentDetail">
        <xs:sequence>
          <xs:element name="DocumentFormat" type="ns:TrackingDocumentFormat" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TrackingSignatureProofOfDeliveryDetail">
        <xs:sequence>
          <xs:element name="DocumentFormat" type="ns:TrackingDocumentFormat" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TransactionDetail">
        <xs:sequence>
          <xs:element name="CustomerTransactionId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Free form text to be echoed back in the reply. Used to match requests and replies.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Localization" type="ns:Localization" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Governs data payload language/translations (contrasted with ClientDetail.localization, which governs Notification.localizedMessage language selection).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Weight">
        <xs:annotation>
          <xs:documentation>The descriptive data for the heaviness of an object.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Units" type="ns:WeightUnits" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the unit of measure associated with a weight value.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Value" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the weight value of a package/shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="WeightUnits">
        <xs:restriction base="xs:string">
          <xs:enumeration value="KG"/>
          <xs:enumeration value="LB"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="WebAuthenticationDetail">
        <xs:annotation>
          <xs:documentation>Used in authentication of the sender's identity.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ParentCredential" type="ns:WebAuthenticationCredential" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This was renamed from cspCredential.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="UserCredential" type="ns:WebAuthenticationCredential" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Credential used to authenticate a specific software application. This value is provided by FedEx after registration.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="WebAuthenticationCredential">
        <xs:annotation>
          <xs:documentation>Two part authentication string used for the sender's identity</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Key" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifying part of authentication credential. This value is provided by FedEx after registration</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Password" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Secret part of authentication key. This value is provided by FedEx after registration.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="VersionId">
        <xs:annotation>
          <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ServiceId" type="xs:string" fixed="trck" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies a system or sub-system which performs an operation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Major" type="xs:int" fixed="16" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the service business level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Intermediate" type="xs:int" fixed="0" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the service interface level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Minor" type="xs:int" fixed="0" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the service code level.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
    </xs:schema>
  </types>
  <message name="SendNotificationsReply">
    <part name="SendNotificationsReply" element="ns:SendNotificationsReply"/>
  </message>
  <message name="TrackRequest">
    <part name="TrackRequest" element="ns:TrackRequest"/>
  </message>
  <message name="GetTrackingDocumentsRequest">
    <part name="GetTrackingDocumentsRequest" element="ns:GetTrackingDocumentsRequest"/>
  </message>
  <message name="SendNotificationsRequest">
    <part name="SendNotificationsRequest" element="ns:SendNotificationsRequest"/>
  </message>
  <message name="TrackReply">
    <part name="TrackReply" element="ns:TrackReply"/>
  </message>
  <message name="GetTrackingDocumentsReply">
    <part name="GetTrackingDocumentsReply" element="ns:GetTrackingDocumentsReply"/>
  </message>
  <portType name="TrackPortType">
    <operation name="track" parameterOrder="TrackRequest">
      <input message="ns:TrackRequest"/>
      <output message="ns:TrackReply"/>
    </operation>
    <operation name="getTrackingDocuments" parameterOrder="GetTrackingDocumentsRequest">
      <input message="ns:GetTrackingDocumentsRequest"/>
      <output message="ns:GetTrackingDocumentsReply"/>
    </operation>
    <operation name="sendNotifications" parameterOrder="SendNotificationsRequest">
      <input message="ns:SendNotificationsRequest"/>
      <output message="ns:SendNotificationsReply"/>
    </operation>
  </portType>
  <binding name="TrackServiceSoapBinding" type="ns:TrackPortType">
    <s1:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="track">
      <s1:operation soapAction="http://fedex.com/ws/track/v16/track" style="document"/>
      <input>
        <s1:body use="literal"/>
      </input>
      <output>
        <s1:body use="literal"/>
      </output>
    </operation>
    <operation name="getTrackingDocuments">
      <s1:operation soapAction="http://fedex.com/ws/track/v16/getTrackingDocuments" style="document"/>
      <input>
        <s1:body use="literal"/>
      </input>
      <output>
        <s1:body use="literal"/>
      </output>
    </operation>
    <operation name="sendNotifications">
      <s1:operation soapAction="http://fedex.com/ws/track/v16/sendNotifications" style="document"/>
      <input>
        <s1:body use="literal"/>
      </input>
      <output>
        <s1:body use="literal"/>
      </output>
    </operation>
  </binding>
  <service name="TrackService">
    <port name="TrackServicePort" binding="ns:TrackServiceSoapBinding">
      <s1:address location="###WS_ADDRESS###"/>
    </port>
  </service>
</definitions>
