<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/">
    <SOAP-ENV:Header/>
    <SOAP-ENV:Body>
        <TrackReply xmlns="http://fedex.com/ws/track/v16">
            <HighestSeverity>SUCCESS</HighestSeverity>
            <Notifications>
                <Severity>SUCCESS</Severity>
                <Source>trck</Source>
                <Code>0</Code>
                <Message>Request was successfully processed.</Message>
                <LocalizedMessage>Request was successfully processed.</LocalizedMessage>
            </Notifications>
            <TransactionDetail>
                <CustomerTransactionId>Track By Number_v16</CustomerTransactionId>
                <Localization>
                    <LanguageCode>EN</LanguageCode>
                    <LocaleCode>US</LocaleCode>
                </Localization>
            </TransactionDetail>
            <Version>
                <ServiceId>trck</ServiceId>
                <Major>16</Major>
                <Intermediate>0</Intermediate>
                <Minor>0</Minor>
            </Version>
            <CompletedTrackDetails>
                <HighestSeverity>SUCCESS</HighestSeverity>
                <Notifications>
                    <Severity>SUCCESS</Severity>
                    <Source>trck</Source>
                    <Code>0</Code>
                    <Message>Request was successfully processed.</Message>
                    <LocalizedMessage>Request was successfully processed.</LocalizedMessage>
                </Notifications>
                <DuplicateWaybill>false</DuplicateWaybill>
                <MoreData>false</MoreData>
                <TrackDetailsCount>0</TrackDetailsCount>
                <TrackDetails>
                    <Notification>
                        <Severity>SUCCESS</Severity>
                        <Source>trck</Source>
                        <Code>0</Code>
                        <Message>Request was successfully processed.</Message>
                        <LocalizedMessage>Request was successfully processed.</LocalizedMessage>
                    </Notification>
                    <TrackingNumber>782483162957</TrackingNumber>
                    <TrackingNumberUniqueIdentifier>12018~782483162957~FDEG</TrackingNumberUniqueIdentifier>
                    <StatusDetail>
                        <CreationTime>2018-08-31T00:00:00</CreationTime>
                        <Code>DL</Code>
                        <Description>Delivered</Description>
                        <Location>
                            <City>Mountain View</City>
                            <StateOrProvinceCode>CA</StateOrProvinceCode>
                            <CountryCode>US</CountryCode>
                            <CountryName>United States</CountryName>
                            <Residential>false</Residential>
                        </Location>
                        <AncillaryDetails>
                            <Reason>014</Reason>
                            <ReasonDescription>Left at front door.Signature Service not requested.</ReasonDescription>
                        </AncillaryDetails>
                    </StatusDetail>
                    <CarrierCode>FDXG</CarrierCode>
                    <OperatingCompanyOrCarrierDescription>FedEx Ground</OperatingCompanyOrCarrierDescription>
                    <Service>
                        <Type>GROUND_HOME_DELIVERY</Type>
                        <Description>FedEx Home Delivery</Description>
                        <ShortDescription>HD</ShortDescription>
                    </Service>
                    <PackageWeight>
                        <Units>LB</Units>
                        <Value>7.0</Value>
                    </PackageWeight>
                    <PackageDimensions>
                        <Length>14</Length>
                        <Width>13</Width>
                        <Height>5</Height>
                        <Units>IN</Units>
                    </PackageDimensions>
                    <Packaging>Package</Packaging>
                    <PackagingType>YOUR_PACKAGING</PackagingType>
                    <PhysicalPackagingType>PACKAGE</PhysicalPackagingType>
                    <PackageSequenceNumber>1</PackageSequenceNumber>
                    <PackageCount>1</PackageCount>
                    <Payments>
                        <Classification>TRANSPORTATION</Classification>
                        <Type>SHIPPER_ACCOUNT</Type>
                        <Description>Shipper</Description>
                    </Payments>
                    <ShipperAddress>
                        <City>Tampa</City>
                        <StateOrProvinceCode>FL</StateOrProvinceCode>
                        <CountryCode>US</CountryCode>
                        <CountryName>United States</CountryName>
                        <Residential>false</Residential>
                    </ShipperAddress>
                    <OriginLocationAddress>
                        <City>TAMPA</City>
                        <StateOrProvinceCode>FL</StateOrProvinceCode>
                        <CountryCode>US</CountryCode>
                        <CountryName>United States</CountryName>
                        <Residential>false</Residential>
                    </OriginLocationAddress>
                    <DatesOrTimes>
                        <Type>ACTUAL_DELIVERY</Type>
                        <DateOrTimestamp>2018-08-31T12:06:23-07:00</DateOrTimestamp>
                    </DatesOrTimes>
                    <DatesOrTimes>
                        <Type>ACTUAL_PICKUP</Type>
                        <DateOrTimestamp>2018-08-27T00:00:00</DateOrTimestamp>
                    </DatesOrTimes>
                    <DatesOrTimes>
                        <Type>SHIP</Type>
                        <DateOrTimestamp>2018-08-27T00:00:00</DateOrTimestamp>
                    </DatesOrTimes>
                    <DatesOrTimes>
                        <Type>ACTUAL_TENDER</Type>
                        <DateOrTimestamp>2018-08-27T00:00:00</DateOrTimestamp>
                    </DatesOrTimes>
                    <DatesOrTimes>
                        <Type>ANTICIPATED_TENDER</Type>
                        <DateOrTimestamp>2018-08-27T00:00:00</DateOrTimestamp>
                    </DatesOrTimes>
                    <DestinationAddress>
                        <City>MOUNTAIN VIEW</City>
                        <StateOrProvinceCode>CA</StateOrProvinceCode>
                        <CountryCode>US</CountryCode>
                        <CountryName>United States</CountryName>
                        <Residential>false</Residential>
                    </DestinationAddress>
                    <ActualDeliveryAddress>
                        <City>Mountain View</City>
                        <StateOrProvinceCode>CA</StateOrProvinceCode>
                        <CountryCode>US</CountryCode>
                        <CountryName>United States</CountryName>
                        <Residential>false</Residential>
                    </ActualDeliveryAddress>
                    <DeliveryAttempts>0</DeliveryAttempts>
                    <DeliverySignatureName>Signature not required</DeliverySignatureName>
                    <TotalUniqueAddressCountInConsolidation>0</TotalUniqueAddressCountInConsolidation>
                    <NotificationEventsAvailable>ON_DELIVERY</NotificationEventsAvailable>
                    <DeliveryOptionEligibilityDetails>
                        <Option>INDIRECT_SIGNATURE_RELEASE</Option>
                        <Eligibility>INELIGIBLE</Eligibility>
                    </DeliveryOptionEligibilityDetails>
                    <DeliveryOptionEligibilityDetails>
                        <Option>REDIRECT_TO_HOLD_AT_LOCATION</Option>
                        <Eligibility>INELIGIBLE</Eligibility>
                    </DeliveryOptionEligibilityDetails>
                    <DeliveryOptionEligibilityDetails>
                        <Option>REROUTE</Option>
                        <Eligibility>INELIGIBLE</Eligibility>
                    </DeliveryOptionEligibilityDetails>
                    <DeliveryOptionEligibilityDetails>
                        <Option>RESCHEDULE</Option>
                        <Eligibility>INELIGIBLE</Eligibility>
                    </DeliveryOptionEligibilityDetails>
                    <Events>
                        <Timestamp>2018-08-31T12:06:23-07:00</Timestamp>
                        <EventType>DL</EventType>
                        <EventDescription>Delivered</EventDescription>
                        <StatusExceptionCode>014</StatusExceptionCode>
                        <StatusExceptionDescription>Left at front door. Signature Service not requested.
                        </StatusExceptionDescription>
                        <Address>
                            <City>Mountain View</City>
                            <StateOrProvinceCode>CA</StateOrProvinceCode>
                            <PostalCode>94040</PostalCode>
                            <CountryCode>US</CountryCode>
                            <CountryName>United States</CountryName>
                            <Residential>false</Residential>
                        </Address>
                        <ArrivalLocation>DELIVERY_LOCATION</ArrivalLocation>
                    </Events>
                </TrackDetails>
            </CompletedTrackDetails>
        </TrackReply>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>