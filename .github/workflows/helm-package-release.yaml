name: '[HELM] Package and Release'

on:
  workflow_dispatch:
    inputs:
      force-scan-paths:
        type: string
        description: >
          He<PERSON> chart relative directory paths to trigger release, separated by spaces.
          Example: charts/sinks
        required: true
  push:
    branches:
      - unify-src-sink-imgs
    paths:
      - 'charts/**/Chart.yaml'

permissions:
  contents: read
  packages: write

env:
  REGISTRY: ghcr.io

jobs:
  package-and-release:
    name: Package and Release Helm Charts
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 50 # Fetch last 50 commits only

      - name: Set Up Helm
        uses: azure/setup-helm@v3
        with:
          version: v3.10.3

      - name: Get Changed Directories
        id: changed-dirs
        uses: tj-actions/changed-files@v41.0.0
        with:
          dir_names: "true"
          dir_names_exclude_current_dir: "true"
          dir_names_max_depth: 2
          files: "charts/**/Chart.yaml"

      - name: Determine Helm Charts to Release
        id: determine-charts
        run: |
          set -euo pipefail

          # Extract changed directories from the previous step
          changed_dirs=(${{ steps.changed-dirs.outputs.all_changed_files }})

          # Extract forced scan paths from workflow_dispatch input
          force_scan_paths=(${{ github.event.inputs.force-scan-paths }})

          # Initialize an empty array for scan paths
          scan_paths=()

          if [ "${#force_scan_paths[@]}" -gt 0 ]; then
            echo "INFO: Performing build and release for specified Helm chart(s): ${force_scan_paths[*]}"
            scan_paths=("${force_scan_paths[@]}")
          else
            echo "INFO: Performing build and release based on changed Helm chart(s): ${changed_dirs[*]}"
            scan_paths=("${changed_dirs[@]}")
          fi

          # Output the scan paths for subsequent steps
          echo "scan_paths=${scan_paths[*]}" >> $GITHUB_OUTPUT

      - name: Log Helm Charts to be Released
        run: |
          echo "Helm Charts to be packaged and released: ${{ steps.determine-charts.outputs.scan_paths }}"

      - name: Helm Registry Login
        env:
          HELM_REGISTRY_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          echo "${HELM_REGISTRY_TOKEN}" | helm registry login -u 'gh-action-bot' --password-stdin ${REGISTRY}

      - name: Package and Push Helm Charts
        run: |
          set -xeuo pipefail

          # Convert scan_paths string to an array
          IFS=' ' read -r -a paths <<< "${{ steps.determine-charts.outputs.scan_paths }}"

          for path in "${paths[@]}"; do
            (
              cd "${path}"
              if [ -f "Chart.yaml" ]; then
                echo "📦 Packaging Helm chart in ${path}"

                # Update chart dependencies
                helm dependency update

                # Package the Helm chart
                helm package . --destination ./packages

                # Push the packaged chart to the OCI registry
                chart_package=$(ls ./packages/*.tgz | head -n 1)
                if [ -z "${chart_package}" ]; then
                  echo "❌ No packaged chart found in ${path}/packages/. Skipping push."
                  exit 1
                fi

                echo "🚀 Pushing ${chart_package} to OCI registry at ${REGISTRY}/${{ github.repository }}"
                helm push "${chart_package}" oci://${REGISTRY}/${{ github.repository }}
              else
                echo "⚠️ Chart.yaml not found in ${path}. Skipping..."
              fi
            )
          done
