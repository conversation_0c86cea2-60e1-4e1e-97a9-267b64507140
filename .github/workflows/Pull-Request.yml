name: Pull-Request Checks

on:
  pull_request:
  workflow_dispatch:
    inputs:
      branch:
        type: string
        description: The branch, tag or SHA to checkout
        required: true

jobs:

  Detect-Changed-Modules:
    runs-on: nexla-dind-runners
    outputs:
      modules: ${{ steps.detect.outputs.modules }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.head_ref }}

      - name: Fetch target branch
        run: git fetch origin ${{ github.base_ref }}

      - name: Detect changed modules
        id: detect
        env:
          CHANGE_TARGET: ${{ github.base_ref }}
        shell: bash
        run: |
          set -euo pipefail
          
          get_module_for_file() {
            local file_path="$1"
            if [[ "$file_path" == "pom.xml" ]]; then
              echo "ROOT_POM"
              return
            fi
            if [[ "$file_path" != */* ]]; then
              echo ""
              return
            fi
            local dir="${file_path%%/*}"
            if [[ -f "$dir/pom.xml" ]]; then
              echo "$dir"
              return
            fi
            echo ""
          }

          get_all_modules() {
            find . -maxdepth 1 -mindepth 1 -type d -exec test -f '{}/pom.xml' \; -print | sed 's|^\./||'
          }

          get_modules_from_pom() {
            sed -e ':a;N;$!ba;s/<!--.*?-->//g' pom.xml | grep -oP '(?<=<module>).*?(?=</module>)'
          }

          filter_valid_modules() {
            local -n input_modules=$1
            local -a valid_modules=($(get_modules_from_pom))
            local -a filtered=()
            for mod in "${input_modules[@]}"; do
              if [[ " ${valid_modules[*]} " == *" $mod "* ]]; then
                filtered+=("$mod")
              fi
            done
            echo "${filtered[*]}"
          }

          get_modules_for_changed_files() {
            local -a changed_files=("$@")
            declare -A module_map
            for file in "${changed_files[@]}"; do
              local module
              module=$(get_module_for_file "$file")
              if [[ "$module" == "ROOT_POM" ]]; then
                echo "The root pom was changed."
                local all_modules
                readarray -t all_modules < <(get_all_modules)
                for mod in "${all_modules[@]}"; do
                    module_map["$mod"]=1
                done
              elif [[ -n "$module" ]]; then
                module_map["$module"]=1
              fi
            done

            echo "${!module_map[@]}"
          }

          # === Main execution starts here ===
          target_branch="origin/${CHANGE_TARGET:-main}"
          changed_files=()
          while IFS= read -r line; do
            changed_files+=("$line")
          done < <(git diff --name-only "${target_branch}...HEAD")

          if [[ "${#changed_files[@]}" -eq 0 ]]; then
            echo "No changed files detected."
            echo "modules=" >> $GITHUB_OUTPUT
            exit 0
          fi

          echo "Changed files: ${changed_files[*]}"

          read -a all_changed_modules <<< "$(get_modules_for_changed_files "${changed_files[@]}")"
          read -a filtered_modules <<< "$(filter_valid_modules all_changed_modules)"

          echo "Valid changed modules: ${filtered_modules[*]}"
          echo "modules=$(IFS=','; echo "${filtered_modules[*]}")" >> $GITHUB_OUTPUT

  Unit-Tests:
    runs-on: nexla-dind-runners
    needs: Detect-Changed-Modules
    if: always()
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Code Artifact Login
        uses: nexla/cloud-actions/actions/code-artifact-login@v1

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: 11
          distribution: corretto

      - uses: sdkman/sdkman-action@master
        id: sdkman
        with:
          candidate: scala
          version: 2.12.14

      - name: Set up Maven
        uses: stCarolas/setup-maven@v5
        with:
          maven-version: 3.9.9

      - name: Restore Maven cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.m2/repository
          key: maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            maven-

      - name: Build with Maven
        env:
          MAVEN_OPTS: -Xmx8g -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/root/.m2/heapdump
          changedModules: ${{ needs.Detect-Changed-Modules.outputs.modules }}
        shell: bash
        run: |
          if [ -z "$changedModules" ]; then
            echo "No modules changed — marking as successful without testing."
            exit 0
          fi

          echo "Running unit tests on modules: ${changedModules}"
          mvn verify --settings ./.mvn/local-settings.xml -T 1 -P unitTestOnly --no-transfer-progress -pl "${changedModules}" -am

      - name: Add coverage to PR
        id: jacoco
        if: ${{ env.changedModules && env.changedModules != '' }}
        uses: madrapps/jacoco-report@v1.7.2
        with:
          paths: |
            ${{ github.workspace }}/coverage/target/site/jacoco-aggregate/jacoco.xml
          token: ${{ secrets.GITHUB_TOKEN }}
          title: Code Coverage Report
          update-comment: true

  Integration-Tests:
    runs-on: nexla-dind-runners
    needs: Detect-Changed-Modules
    if: always()
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Code Artifact Login
        uses: nexla/cloud-actions/actions/code-artifact-login@v1

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: 11
          distribution: corretto

      - uses: sdkman/sdkman-action@master
        id: sdkman
        with:
          candidate: scala
          version: 2.12.14

      - name: Set up Maven
        uses: stCarolas/setup-maven@v5
        with:
          maven-version: 3.9.9

      - name: Restore Maven cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.m2/repository
          key: maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            maven-

      - name: Build with Maven
        env:
          MAVEN_OPTS: -Xmx8g -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/root/.m2/heapdump
          changedModules: ${{ needs.Detect-Changed-Modules.outputs.modules }}
        shell: bash
        run: |
          if [ -z "$changedModules" ]; then
            echo "No modules changed — marking as successful without testing."
            exit 0
          fi

          echo "Running integration tests on modules: ${changedModules}"
          mvn package --settings ./.mvn/local-settings.xml -T 1 -P integrationTestOnly --no-transfer-progress -pl "${changedModules}" -am