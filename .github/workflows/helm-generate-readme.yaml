# name: '[HELM] Update README'

# on:
#   pull_request:
#     branches:
#       - main
#     types:
#       - opened
#       - edited
#       - reopened
#       - ready_for_review
#       - synchronize
#     paths:
#       - 'charts/**/values.yaml'

# permissions:
#   contents: write

# jobs:
#   update-readme:
#     name: Update README.md in Helm Charts
#     runs-on: ubuntu-latest

#     steps:
#       - name: Checkout Repository
#         uses: actions/checkout@v4
#         with:
#           fetch-depth: 50 # Fetch last 50 commits only

#       - name: Set Up Helm
#         uses: azure/setup-helm@v3
#         with:
#           version: v3.10.3

#       - name: Cache NPM Global Modules
#         uses: actions/cache@v3
#         with:
#           path: ~/.npm
#           key: ${{ runner.os }}-npm-${{ hashFiles('**/package-lock.json') }}
#           restore-keys: |
#             ${{ runner.os }}-npm-

#       - name: Install Readme Generator for Helm
#         run: |
#           npm install -g @bitnami/readme-generator-for-helm@2.6.0

#       - name: Get Changed Directories
#         id: changed-dirs
#         uses: tj-actions/changed-files@v41.0.0
#         with:
#           dir_names: "true"
#           dir_names_exclude_current_dir: "true"
#           dir_names_max_depth: 2
#           files: "charts/**/values.yaml"

#       - name: Execute Readme Generator
#         run: |
#           set -euo pipefail

#           # Extract directories with changes
#           IFS=$'\n' read -rd '' -a dirs <<< "${{ steps.changed-dirs.outputs.all_changed_files }}"

#           # Iterate over each changed directory to update README.md
#           for dir in "${dirs[@]}"; do
#             echo "Processing directory: $dir"
#             if [[ -f "$dir/values.yaml" && -f "$dir/README.md" ]]; then
#               (
#                 cd "$dir"
#                 echo "Updating README.md in $dir"
#                 readme-generator --values "./values.yaml" \
#                                  --readme "./README.md" \
#                                  --schema "/tmp/schema.json"
#               )
#             else
#               echo "Skipping $dir: Required files not found."
#             fi
#           done

#       - name: Commit and Push Changes
#         if: github.event_name == 'pull_request' && github.event.action != 'closed'
#         uses: stefanzweifel/git-auto-commit-action@v5
#         with:
#           commit_message: "chore: Update README.md with readme-generator-for-helm"
#           commit_user_name: "GitHub Action"
#           commit_user_email: "<EMAIL>"
# <AUTHOR> <EMAIL>"
#           branch: ${{ github.head_ref }}
