package com.nexla.probe.dynamodb

import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.amazonaws.services.dynamodbv2.document.{DynamoDB, Item, ItemUtils, TableWriteItems}
import com.amazonaws.services.dynamodbv2.model.{AttributeValue, ScanRequest, ScanResult}
import com.bazaarvoice.jolt.JsonUtils.toJsonString
import com.fasterxml.jackson.databind.{MapperFeature, ObjectMapper}
import com.nexla.common.MetricUtils.calcBytes
import com.nexla.common.logging.{NexlaLog<PERSON>ey, NexlaLogger}
import com.nexla.common.metrics.RecordMetric
import com.nexla.common.metrics.RecordMetric.quarantineMessage
import com.nexla.common.{NexlaBucket, NexlaFile, ResourceType}
import com.nexla.connector.ConnectorService.AuthResponse.{SUCCESS, authError}
import com.nexla.connector.config.documentdb.dynamodb.{DynamoDBQueryConfig, DynamoDbAuthConfig}
import com.nexla.connector.config.documentdb.{DocumentDbSinkConnectorConfig, DocumentDbSourceConnectorConfig, DocumentDbTreeService}
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.connector.{ConnectorService, NexlaMessageContext}
import com.nexla.probe.documentdb.DocumentDbConnectorService
import com.nexla.probe.documentdb.DocumentDbConnectorService.{Document, LazyDocument}
import com.nexla.probe.dynamodb.DynamoDbService.MAX_BATCH_SIZE
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import com.nexla.sc.util.StrictNexlaLogging
import one.util.streamex.StreamEx
import org.apache.kafka.common.config.AbstractConfig
import org.slf4j.LoggerFactory

import java.util
import java.util.Optional
import scala.collection.JavaConverters._
import scala.collection.mutable
import scala.util.{Failure, Success, Try}

class DynamoDbService
  extends DocumentDbConnectorService[BaseAuthConfig]
    with StrictNexlaLogging
    with WithLogging
    with DocumentDbTreeService[DocumentDbSourceConnectorConfig] {

  type ErrorMessage = String

  private var nexlaLogger = LoggerFactory.getLogger(classOf[DynamoDbService])
  private var objectMapper = new ObjectMapper()
  objectMapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true)

  override def initLogger(resourceType: ResourceType, resourceId: Integer, taskId: Optional[Integer]): Unit = {
    this.nexlaLogger = new NexlaLogger(nexlaLogger, new NexlaLogKey(resourceType, resourceId, taskId))
  }

  override def authenticate(authConfig: BaseAuthConfig): ConnectorService.AuthResponse = {
    withClient(authConfig) {
      dynamoDb => dynamoDb.listTables()
    } match {
      case Success(_) => SUCCESS
      case Failure(e) => {
        logger.error(s"[creds-${authConfig.getCredsId}] Exception while authenticating", e)
        authError(e)
      }
    }
  }

  def bulkWrite(config: DocumentDbSinkConnectorConfig, messageList: mutable.Buffer[NexlaMessageContext])(implicit recordMetric: RecordMetric): Try[Unit] = {
    
    val (successes, failures) = messageList
      .map(m => m -> toDynamoDbMessage(m))
      .toList
      .partition(_._2.isSuccess)

    updateFailureMetrics(failures.map { case (m, failure) => m -> failure.failed.get.getMessage })

    withClient(config.authConfig) {
      dynamoDB => {
        val client = new DynamoDB(dynamoDB)
        successes
          .grouped(MAX_BATCH_SIZE)
          .foreach(batch => Try {
            val items = batch.flatMap(_._2.toOption)
            val tableWriteItems = new TableWriteItems(config.collection).withItemsToPut(items.asJava)
            client.batchWriteItem(tableWriteItems)
          } match {
            case Success(_) =>
              updateSuccessMetrics(batch)
            case Failure(e) =>
              processFailure(e, batch.map(_._1).toArray)
          })
      }
    }
  }

  override def listBuckets(config: AbstractConfig): StreamEx[NexlaBucket] = throw new UnsupportedOperationException("not supported")

  override def listBucketContents(config: AbstractConfig): StreamEx[NexlaFile] = throw new UnsupportedOperationException("not supported")

  override def checkWriteAccess(config: AbstractConfig): Boolean = throw new UnsupportedOperationException("not supported")

  override def listDatabases(config: DocumentDbSourceConnectorConfig): StreamEx[String] = throw new UnsupportedOperationException("not supported")

  override def readWholeCollection(c: DocumentDbSourceConnectorConfig): StreamEx[_ <: Document] = {
    withClient(c.authConfig) { client =>
      val scanResult = client.scan(new ScanRequest().withTableName(c.collection))
      extractDocuments(scanResult)
    }.toOption.getOrElse(StreamEx.empty())
  }

  override def readBoundedCollection(config: DocumentDbSourceConnectorConfig, key: String, fromInclusive: Option[Long], toExclusive: Option[Long]): StreamEx[_ <: Document] = {
    withClient(config.authConfig) { client =>
      val scanResult = client.scan(scanRequest(config, Option(key), fromInclusive, toExclusive))
      extractDocuments(scanResult)
    }.toOption.getOrElse(StreamEx.empty())
  }

  override def query(config: DocumentDbSourceConnectorConfig): StreamEx[_ <: Document] = {
    withClient(config.authConfig) { client =>
      val scanResult = client.scan(queryRequest(config))
      extractDocuments(scanResult)
    }.toOption.getOrElse(StreamEx.empty())
  }

  override def listCollections(database: String, config: DocumentDbSourceConnectorConfig): StreamEx[String] = {
    withClient(config.authConfig) {
      client =>
        val tables = client
          .listTables()
          .getTableNames
        StreamEx.of(tables)
    }
  }.get

  private def withClient[T](authConfig: BaseAuthConfig)
                           (fn: AmazonDynamoDB => T): Try[T] = tryOp(s"creds-${authConfig.getCredsId}") { () =>
    Try(DynamoDbUtils.buildClient(authConfig.asInstanceOf[DynamoDbAuthConfig]))
      .map { dynamoDb => fn(dynamoDb) }
  }

  private def extractDocuments(scanResult: ScanResult) = {
    StreamEx.of(ItemUtils.toItemList(scanResult.getItems))
      .map[LazyDocument] { item => new LazyDocument(new util.LinkedHashMap(item.asMap())) }
  }

  private def toDynamoDbMessage(message: NexlaMessageContext) = {
    for {
      record <- Try(toJsonString(message.mapped.getRawMessage))
    } yield Item.fromJSON(record)
  }

  private def updateFailureMetrics(failures: List[(NexlaMessageContext, ErrorMessage)])
                                  (implicit metric: RecordMetric) = {
    failures.foreach { case (message, errorMessage) =>
      metric.errorRecords.incrementAndGet()
      metric.quarantineMessages.add(quarantineMessage(message.original, errorMessage))
    }
  }

  private def updateSuccessMetrics(successes: List[(NexlaMessageContext, Try[Item])])
                                  (implicit metric: RecordMetric) = {
    successes.foreach { success =>
      metric.sentRecordsTotal.incrementAndGet()
      metric.sentBytesTotal.addAndGet(calcBytes(success._2.get))
    }
  }

  private def processFailure(e: Throwable,
                             sentMessages: Array[NexlaMessageContext])
                            (implicit metric: RecordMetric) = e match {
    case ex => Try {
      logger.error("DynamoDb bulk write failed", ex)
      val errorsFromDynamoDb = sentMessages
        .map(m => m -> e.getMessage)
        .toList
      updateFailureMetrics(errorsFromDynamoDb)
    }
  }

  private def scanRequest(config: DocumentDbSourceConnectorConfig, dateField: Option[String], from: Option[Long], to: Option[Long]): ScanRequest = {
    val attributeNames = new util.HashMap[String, String]
    val attributeValues = new util.HashMap[String, AttributeValue]
    val conditions = scala.collection.mutable.ListBuffer[String]()

    val request = new ScanRequest()
      .withTableName(config.collection)

    // Add all possible optional filter criteria
    if (dateField.nonEmpty) {
      attributeNames.put("#dateField", dateField.get)
      request.withExpressionAttributeNames(attributeNames)
    }
    if (from.nonEmpty) {
      attributeValues.put(":from", new AttributeValue().withN(from.get.toString))
      conditions += "#dateField >= :from"
    }
    if (to.nonEmpty) {
      attributeValues.put(":to", new AttributeValue().withN(to.get.toString))
      conditions += "#dateField < :to"
    }

    if (conditions.nonEmpty) {
      request.withFilterExpression(conditions.mkString(" and "))
      request.withExpressionAttributeValues(attributeValues)
    }
    request
  }

  private def queryRequest(config: DocumentDbSourceConnectorConfig): ScanRequest = {
    val attributeNames = new util.HashMap[String, String]
    val attributeValues = new util.HashMap[String, AttributeValue]

    Option(config.documentDbQueryConfig.get()) match {
      case option: Some[DynamoDBQueryConfig] =>
        val queryConfig = option.get
        val request = new ScanRequest()
          .withTableName(config.collection)
          .withFilterExpression(queryConfig.filterExpression)

        if (Option(queryConfig.attributesNames).exists(_.trim.nonEmpty)) {
          val namesMap = objectMapper.readValue(queryConfig.attributesNames, classOf[java.util.Map[String, String]])
          attributeNames.putAll(namesMap)
          request.withExpressionAttributeNames(attributeNames)
        }
        if (Option(queryConfig.attributesValues).exists(_.trim.nonEmpty)) {
          val valuesMap = objectMapper.readValue(queryConfig.attributesValues, classOf[java.util.Map[String, AttributeValue]])
          attributeValues.putAll(valuesMap)
          request.withExpressionAttributeValues(attributeValues)
        }
        request
      case _ => new ScanRequest().withTableName(config.collection)
    }
  }

  def isEmpty(x: String) = x == null || x.isEmpty
}

object DynamoDbService {
  val MAX_BATCH_SIZE = 25
}
