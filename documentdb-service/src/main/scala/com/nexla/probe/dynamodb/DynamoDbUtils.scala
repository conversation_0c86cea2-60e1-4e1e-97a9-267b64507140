package com.nexla.probe.dynamodb

import com.amazonaws.auth.{AWSStaticCredentialsProvider, BasicAWSCredentials}
import com.amazonaws.client.builder.AwsClientBuilder
import com.amazonaws.regions.Regions
import com.amazonaws.services.dynamodbv2.{AmazonDynamoDB, AmazonDynamoDBClientBuilder}
import com.nexla.connector.config.documentdb.dynamodb.DynamoDbAuthConfig
import org.apache.commons.lang3.StringUtils.isNotEmpty

object DynamoDbUtils {

  def buildClient(config: DynamoDbAuthConfig): AmazonDynamoDB = {
    val configuration = new AwsClientBuilder.EndpointConfiguration(config.endpoint, null)
    val builder = AmazonDynamoDBClientBuilder
      .standard
      .withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials(config.accessKey, config.secretKEy)))
    if (isNotEmpty(config.endpoint)) builder.withEndpointConfiguration(configuration)
    else builder.withRegion(Regions.fromName(config.region))
   builder.build
  }

}
