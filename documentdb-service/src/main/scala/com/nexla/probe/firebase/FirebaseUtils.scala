package com.nexla.probe.firebase

import com.google.auth.oauth2.GoogleCredentials
import com.google.cloud.firestore.{CollectionReference, Firestore, Query => FirestoreQuery}
import com.google.firebase.cloud.FirestoreClient
import com.google.firebase.{FirebaseApp, FirebaseOptions}
import com.nexla.connector.config.documentdb.firebase.FirebaseAuthConfig
import com.nexla.probe.firebase.querymode.QueryConvertions._
import com.nexla.probe.firebase.querymode.{FirebaseQueryTypes, Query}

import java.io.ByteArrayInputStream
import scala.collection.JavaConverters._
import scala.language.implicitConversions

object FirebaseUtils extends FirebaseQueryTypes {

  def buildClient(config: FirebaseAuthConfig): Firestore = {
    val serviceAccount = new ByteArrayInputStream(config.jsonCreds.getBytes)
    val projectId = config.projectId
    val options = FirebaseOptions.builder
      .setCredentials(GoogleCredentials.fromStream(serviceAccount))
      .setProjectId(projectId)
      .build

    val firebaseApp =
      if (FirebaseApp.getApps.stream().anyMatch(_.getName == projectId))
        FirebaseApp.getInstance(projectId)
      else
        FirebaseApp.initializeApp(options, projectId)

    FirestoreClient.getFirestore(firebaseApp)
  }

  def resolveQueryDataType(fieldType: String, stringValue: String): Any = {
    fieldType match {
      case STRING => stringValue
      case STRING_SEQUENCE => strToSeq(stringValue).asJava
      case BOOLEAN => stringValue.toBoolean
      case BOOLEAN_SEQUENCE => strToSeq(stringValue).map(_.toBoolean).asJava
      case NUMBER => stringValue.toDouble
      case NUMBER_SEQUENCE => strToSeq(stringValue).map(_.toDouble).asJava
      case _ => throw new IllegalArgumentException("Invalid query data type provided")
    }
  }

  def resolveQuery(collection: CollectionReference, query: Query): FirestoreQuery = {
    val filterQueryResult = query.filters.foldLeft(collection.asInstanceOf[FirestoreQuery]) {
      (accumulator, fieldFilter) => {
        accumulator.resolveQueryParams(accumulator, fieldFilter.operator,
          fieldFilter.fieldName, resolveQueryDataType(fieldFilter.fieldType, fieldFilter.stringValue).asInstanceOf[Object])
      }
    }
    val orderedQuery = query.orderBy match {
      case Some(orderByClause) => orderByClause.foldLeft(filterQueryResult) {
        (accumulator, orderBy) => {
          accumulator.resolveSorting(accumulator, orderBy.fieldName, orderBy.direction)
        }
      }
      case None => filterQueryResult
    }

    query.limit match {
      case Some(queryLimit) => orderedQuery.limit(queryLimit)
      case None => orderedQuery
    }
  }

  def strToSeq(stringValue: String): List[String] = {
    stringValue.split(SEQUENCE_DELIMITER, -1).toList
  }

}
