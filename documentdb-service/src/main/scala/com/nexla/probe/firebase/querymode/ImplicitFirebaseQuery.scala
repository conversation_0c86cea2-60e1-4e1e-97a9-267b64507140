package com.nexla.probe.firebase.querymode

import com.google.cloud.firestore.Query.Direction
import com.google.cloud.firestore.{Query => FirestoreQuery}

import java.util.List

class ImplicitFirebaseQuery(query: FirestoreQuery) extends FirebaseQueryTypes {

  def resolveQueryParams(collection: FirestoreQuery, operator: String, field: String, value: Object): FirestoreQuery = {
    operator match {
      case LESS_THAN => collection.whereLessThan(field, value)
      case LESS_THAN_OR_EQUAL_TO => collection.whereLessThanOrEqualTo(field, value)
      case EQUAL_TO => collection.whereEqualTo(field, value)
      case GREATER_THAN => collection.where<PERSON>reater<PERSON>han(field, value)
      case GREATER_THAN_OR_EQUAL_TO => collection.whereGreaterThanOrEqualTo(field, value)
      case NOT_EQUAL_TO => collection.whereNotEqualTo(field, value)
      case ARRAY_CONTAINS => collection.whereArrayContains(field, value)
      case IN => collection.whereIn(field, value.asInstanceOf[List[Object]])
      case NOT_IN => collection.whereNotIn(field, value.asInstanceOf[List[Object]])
      case _ => throw new IllegalArgumentException("Invalid operator provided")
    }
  }

  def resolveSorting(collection: FirestoreQuery, fieldName: String, order: String): FirestoreQuery = {
    order match {
      case DIRECTION_ASCENDING => collection.orderBy(fieldName, Direction.ASCENDING)
      case DIRECTION_DESCENDING => collection.orderBy(fieldName, Direction.DESCENDING)
      case _ => throw new IllegalArgumentException("Direction needs to be provided for query results")
    }
  }
}

object QueryConvertions {
  implicit def queryToCustomFirebaseQuery(originalQuery: FirestoreQuery) = {
    new ImplicitFirebaseQuery(originalQuery)
  }
}

