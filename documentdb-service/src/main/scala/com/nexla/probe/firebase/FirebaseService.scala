package com.nexla.probe.firebase

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import com.bazaarvoice.jolt.JsonUtils.toJsonString
import com.google.cloud.firestore.Firestore
import com.google.gson.Gson
import com.nexla.common.MetricUtils.calcBytes
import com.nexla.common.logging.{<PERSON>exlaLog<PERSON>ey, NexlaLogger}
import com.nexla.common.metrics.RecordMetric
import com.nexla.common.metrics.RecordMetric.quarantineMessage
import com.nexla.common.{NexlaBucket, NexlaFile, ResourceType}
import com.nexla.connector.ConnectorService.AuthResponse.{SUCCESS, authError}
import com.nexla.connector.config.documentdb.firebase.{FirebaseAuthConfig, FirebaseQueryConfig}
import com.nexla.connector.config.documentdb.{DocumentDbSinkConnectorConfig, DocumentDbSourceConnectorConfig, DocumentDbTreeService}
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.connector.{ConnectorService, NexlaMessageContext}
import com.nexla.probe.documentdb.DocumentDbConnectorService
import com.nexla.probe.documentdb.DocumentDbConnectorService.{Document, LazyDocument}
import com.nexla.probe.firebase.querymode.{FirebaseQueryMarshalling, Query}
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import com.nexla.sc.util.StrictNexlaLogging
import one.util.streamex.{EntryStream, StreamEx}
import org.apache.kafka.common.config.AbstractConfig
import org.jose4j.json.internal.json_simple.JSONObject
import org.slf4j.LoggerFactory
import spray.json.{DefaultJsonProtocol, _}

import java.nio.charset.StandardCharsets
import java.util
import java.util.Optional
import java.util.stream.Collectors
import scala.collection.mutable
import scala.jdk.CollectionConverters._
import scala.util.{Failure, Success, Try}

class FirebaseService extends DocumentDbConnectorService[BaseAuthConfig]
  with StrictNexlaLogging
  with WithLogging
  with DocumentDbTreeService[DocumentDbSourceConnectorConfig]
  with FirebaseQueryMarshalling
  with DefaultJsonProtocol
  with SprayJsonSupport {

  private var log = LoggerFactory.getLogger(classOf[FirebaseService])
  type ErrorMessage = String

  override def initLogger(resourceType: ResourceType, resourceId: Integer, taskId: Optional[Integer]): Unit = {
    this.log = new NexlaLogger(log, new NexlaLogKey(resourceType, resourceId, taskId))
  }

  override def authenticate(authConfig: BaseAuthConfig): ConnectorService.AuthResponse = {
    withClient(authConfig) {
      firestore => firestore.listCollections()
    } match {
      case Success(_) => SUCCESS
      case Failure(e) => {
        logger.error(s"[creds-${authConfig.getCredsId}] Exception while authenticating", e)
        authError(e)
      }
    }
  }

  override def bulkWrite(config: DocumentDbSinkConnectorConfig, messageList: mutable.Buffer[NexlaMessageContext])(implicit recordMetric: RecordMetric): Try[Unit] = {
    
    val (successes, failures) = messageList
      .map(m => m -> toFirebaseMessage(m))
      .toList
      .partition(_._2.isSuccess)

    updateFailureMetrics(failures.map { case (m, failure) => m -> failure.failed.get.getMessage })

    withClient(config.authConfig) {
      firebase => Try {
        val batch = firebase.batch()
        messageList
          .map(m => toFirebaseMessage(m))
          .foreach(m => {
          val docRef = firebase.collection(config.collection).document()
          batch.set(docRef, m.get)
        })
        batch.commit
      } match {
        case Success(_) =>
          updateSuccessMetrics(successes)
          Success(())
        case Failure(e) =>
          processFailure(e, successes.map(_._1).toArray)
      }
    }
  }

  override def listCollections(database: String, config: DocumentDbSourceConnectorConfig): StreamEx[String] = {
    withClient(config.authConfig) {
      firebase =>
        val tables = firebase
          .listCollections()
          .asScala
          .map(_.getId)
        StreamEx.of(tables.toList.asJava)
    }.get
  }

  override def listDatabases(config: DocumentDbSourceConnectorConfig): StreamEx[String] = StreamEx.of[String](config.authConfig.asInstanceOf[FirebaseAuthConfig].projectId)

  override def listBuckets(c: AbstractConfig): StreamEx[NexlaBucket] = {
    val (_, authConfig) = configs(c)

    StreamEx.of[NexlaBucket](new NexlaBucket(authConfig.projectId))
  }

  override def listBucketContents(c: AbstractConfig): StreamEx[NexlaFile] = {
   val (config, authConfig) = configs(c)

    listCollections(authConfig.projectId, config)
      .map(collection => new NexlaFile(collection, null, authConfig.projectId, null, null, null, null))
  }

  override def checkWriteAccess(config: AbstractConfig): Boolean = throw new UnsupportedOperationException("not supported")

  private def withClient[T](authConfig: BaseAuthConfig)
                           (fn: Firestore => T): Try[T] = tryOp(s"creds-${authConfig.getCredsId}") { () =>
    Try(FirebaseUtils.buildClient(authConfig.asInstanceOf[FirebaseAuthConfig]))
      .flatMap { firebase =>
        val result = Try(fn(firebase))
        result
      }
  }

  private def configs(c: AbstractConfig): (DocumentDbSourceConnectorConfig, FirebaseAuthConfig) = {
    val config = c.asInstanceOf[DocumentDbSourceConnectorConfig]
    val authConfig = config.authConfig.asInstanceOf[FirebaseAuthConfig]

    (config, authConfig)
  }

  private def toFirebaseMessage(message: NexlaMessageContext) = {
    for {
      record <- Try {
        val stringData = toJsonString(message.mapped.getRawMessage)
        val record = new Gson().fromJson(stringData, classOf[JSONObject])
        record
      }
    } yield record
  }

  override def readBoundedCollection(c: DocumentDbSourceConnectorConfig, key: String, fromInclusive: Option[Long], toExclusive: Option[Long]): StreamEx[_ <: DocumentDbConnectorService.Document] = {
    withClient(c.authConfig) { db =>
      val collection = db.collection(c.collection).get

      var q = collection.get.getQuery

      q = fromInclusive.map(from => q.whereGreaterThanOrEqualTo(key, from)).getOrElse(q)
      q = toExclusive.map(to => q.whereLessThan(key, to)).getOrElse(q)
      q = q.orderBy(key)

      val snapshot = q.get()

      StreamEx.of(snapshot.get().iterator())
        .map[LazyDocument] { document =>
          new LazyDocument(new util.LinkedHashMap(convert(document.getData)))
        }
    }.toOption.getOrElse(StreamEx.empty())
  }

  override def readWholeCollection(c: DocumentDbSourceConnectorConfig): StreamEx[_ <: Document] = {
    withClient(c.authConfig) { db =>
      val collection = db.collection(c.collection).get

      StreamEx.of(
        collection.get
          .getDocuments
          .stream()
          .map[LazyDocument] { document =>
            new LazyDocument(new util.LinkedHashMap(convert(document.getData)))
          }
      )
    }.toOption.getOrElse(StreamEx.empty())
  }

  override def query(config: DocumentDbSourceConnectorConfig): StreamEx[_ <: Document] = {
    withClient(config.authConfig) { db =>
      val collection = db.collection(config.collection)
      val queryConfig = config.documentDbQueryConfig.get().asInstanceOf[FirebaseQueryConfig]
      val marshalledQueryObj = queryConfig.query.parseJson.convertTo[Query]

      val executedQuery = FirebaseUtils.resolveQuery(collection, marshalledQueryObj)

      StreamEx.of(
        executedQuery.get().get
          .getDocuments
          .stream()
          .map[LazyDocument] { document =>
            new LazyDocument(new util.LinkedHashMap(convert(document.getData)))
          }
      )
    }.toOption.getOrElse(StreamEx.empty())

  }
  
  private def updateFailureMetrics(failures: List[(NexlaMessageContext, ErrorMessage)])
                                  (implicit metric: RecordMetric) = {
    failures.foreach { case (message, errorMessage) =>
      metric.errorRecords.incrementAndGet()
      metric.quarantineMessages.add(quarantineMessage(message.original, errorMessage))
    }
  }

  private def updateSuccessMetrics(successes: List[(NexlaMessageContext, Try[JSONObject])])
                                  (implicit metric: RecordMetric) = {
    successes.foreach { success =>
      metric.sentRecordsTotal.incrementAndGet()
      metric.sentBytesTotal.addAndGet(calcBytes(success._2.get))
    }
  }

  private def processFailure(e: Throwable,
                             sentMessages: Array[NexlaMessageContext])
                            (implicit metric: RecordMetric) = e match {
    case ex => Try {
      logger.error("Firebase bulk write failed", ex)
      val errorsFromFirebase = sentMessages
        .map(m => m -> e.getMessage)
        .toList
      updateFailureMetrics(errorsFromFirebase)
    }
  }
  

  def convert(doc: java.util.Map[String, AnyRef]): java.util.LinkedHashMap[String, AnyRef] = {
    EntryStream.of(doc)
      .mapValues[AnyRef](v => convert(v))
      .toCustomMap(() => new util.LinkedHashMap[String, AnyRef]())
  }

  def convert(v: AnyRef): AnyRef = v match {
    case t: com.google.cloud.Timestamp => t.toSqlTimestamp
    case t: com.google.cloud.firestore.GeoPoint => Map("lat" -> t.getLatitude, "lon" -> t.getLongitude).asJava
    case t: com.google.cloud.firestore.DocumentReference => t.getPath // todo add option to resolve refs
    case t: java.util.Map[String, AnyRef] => convert(t)
    case t: java.util.List[AnyRef] => t.stream().map(v => convert(v)).collect(Collectors.toList[Any])
    case other => String.valueOf(other)
  }

}
