package com.nexla.connector.mongodb.sink.processor.id.strategy;

import com.mongodb.DBCollection;
import com.nexla.common.NexlaMessage;
import com.nexla.connector.mongodb.sink.converter.SinkDocument;
import org.apache.kafka.connect.errors.DataException;
import org.bson.BsonArray;
import org.bson.BsonNull;
import org.bson.BsonValue;

public class ProvidedStrategy implements IdStrategy {

	@Override
	public BsonValue generateId(SinkDocument doc, NexlaMessage orig) {

		BsonValue _id = doc.valueDoc.get(DBCollection.ID_FIELD_NAME);

		if (_id instanceof BsonNull) {
			throw new DataException("error: provided id strategy used but the document structure contained an _id of type BsonNull");
		} else if (_id instanceof BsonArray) {
			throw new DataException("error: ket cannot be array: " + _id);
		} else {
			return _id;
		}
	}
}
