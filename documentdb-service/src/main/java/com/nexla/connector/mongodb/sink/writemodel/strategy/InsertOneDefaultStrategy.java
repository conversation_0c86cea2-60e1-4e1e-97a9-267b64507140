package com.nexla.connector.mongodb.sink.writemodel.strategy;

import com.mongodb.client.model.InsertOneModel;
import com.mongodb.client.model.WriteModel;
import com.nexla.connector.mongodb.sink.converter.SinkDocument;
import org.bson.BsonDocument;

public class InsertOneDefaultStrategy implements WriteModelStrategy {

	@Override
	public WriteModel<BsonDocument> createWriteModel(SinkDocument document) {
		return new InsertOneModel<>(document.valueDoc);
	}
}
