package com.nexla.connector.mongodb.sink.processor.id.strategy;

import com.nexla.common.NexlaMessage;
import com.nexla.connector.mongodb.sink.converter.SinkDocument;
import org.bson.BsonString;
import org.bson.BsonValue;

import java.util.UUID;

public class UuidStrategy implements IdStrategy {

	@Override
	public BsonValue generateId(SinkDocument doc, NexlaMessage orig) {
		return new BsonString(UUID.randomUUID().toString());
	}

}
