package com.nexla.connector.mongodb.sink.writemodel.strategy;

import com.mongodb.DBCollection;
import com.mongodb.client.model.UpdateOneModel;
import com.mongodb.client.model.UpdateOptions;
import com.mongodb.client.model.WriteModel;
import com.nexla.connector.mongodb.sink.converter.SinkDocument;
import org.bson.BsonDateTime;
import org.bson.BsonDocument;
import org.bson.BsonElement;

import java.time.Instant;

import static java.util.Arrays.asList;

public class UpsertStrategy implements WriteModelStrategy {

	public static final String FIELDNAME_MODIFIED_TS = "_modifiedTS";
	public static final String FIELDNAME_INSERTED_TS = "_insertedTS";

	private static final UpdateOptions UPDATE_OPTIONS = new UpdateOptions().upsert(true);

	@Override
	public WriteModel<BsonDocument> createWriteModel(SinkDocument document) {

		BsonDocument vd = document.valueDoc;
		BsonDateTime dateTime = new BsonDateTime(Instant.now().toEpochMilli());

		return new UpdateOneModel<>(
			new BsonDocument(
				DBCollection.ID_FIELD_NAME,
				vd.get(DBCollection.ID_FIELD_NAME)),

			new BsonDocument(asList(
				new BsonElement("$set", vd.append(FIELDNAME_MODIFIED_TS, dateTime)),
				new BsonElement("$setOnInsert", new BsonDocument(FIELDNAME_INSERTED_TS, dateTime))
			)),
			UPDATE_OPTIONS);

	}
}
