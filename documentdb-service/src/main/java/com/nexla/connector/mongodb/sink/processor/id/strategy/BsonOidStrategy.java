package com.nexla.connector.mongodb.sink.processor.id.strategy;

import com.nexla.common.NexlaMessage;
import com.nexla.connector.mongodb.sink.converter.SinkDocument;
import org.bson.BsonObjectId;
import org.bson.BsonValue;
import org.bson.types.ObjectId;

public class BsonOidStrategy implements IdStrategy {

	@Override
	public BsonValue generateId(SinkDocument doc, NexlaMessage orig) {
		return new BsonObjectId(ObjectId.get());
	}

}
