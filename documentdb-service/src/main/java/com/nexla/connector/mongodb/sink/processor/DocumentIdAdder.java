package com.nexla.connector.mongodb.sink.processor;

import com.mongodb.DBCollection;
import com.nexla.common.NexlaMessage;
import com.nexla.connector.mongodb.sink.converter.SinkDocument;
import com.nexla.connector.mongodb.sink.processor.id.strategy.IdStrategy;

public class DocumentIdAdder extends PostProcessor {

	protected final IdStrategy idStrategy;

	public DocumentIdAdder(IdStrategy idStrategy) {
		this.idStrategy = idStrategy;
	}

	public void process(SinkDocument doc, NexlaMessage orig) {
		doc.valueDoc.append(DBCollection.ID_FIELD_NAME, idStrategy.generateId(doc, orig));
	}

}
