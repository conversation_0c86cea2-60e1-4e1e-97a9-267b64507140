package com.nexla.connector.mongodb.sink.processor;

import com.nexla.common.NexlaMessage;
import com.nexla.connector.mongodb.sink.converter.SinkDocument;

import java.util.Optional;

public abstract class PostProcessor {

	private Optional<PostProcessor> next = Optional.empty();

	public PostProcessor chain(PostProcessor next) {
		// intentionally throws NPE here if someone
		// tries to be 'smart' by chaining with null
		this.next = Optional.of(next);
		return this.next.get();
	}

	public abstract void process(SinkDocument doc, NexlaMessage orig);

	public Optional<PostProcessor> getNext() {
		return this.next;
	}

}
