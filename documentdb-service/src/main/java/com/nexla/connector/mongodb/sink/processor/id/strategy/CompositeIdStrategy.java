package com.nexla.connector.mongodb.sink.processor.id.strategy;

import com.nexla.common.NexlaMessage;
import com.nexla.connector.mongodb.sink.converter.SinkDocument;
import com.nexla.connector.mongodb.sink.processor.field.projection.FieldProjector;
import org.bson.BsonValue;

public class CompositeIdStrategy implements IdStrategy {

	private FieldProjector fieldProjector;

	public CompositeIdStrategy(FieldProjector fieldProjector) {
		this.fieldProjector = fieldProjector;
	}

	@Override
	public BsonValue generateId(SinkDocument doc, NexlaMessage orig) {

		//NOTE: this has to operate on a clone because
		//otherwise it would interfere with further projections
		//happening later in the chain e.g. for value fields
		SinkDocument clone = doc.clone();
		fieldProjector.process(clone, orig);
		//NOTE: If there is no key doc present the strategy
		//simply returns an empty BSON document per default.
		return clone.valueDoc;

	}

}
