package com.nexla.connector.mongodb.sink.processor.id;

import com.nexla.common.NexlaMessage;
import com.nexla.connector.mongodb.sink.converter.SinkDocument;
import com.nexla.connector.mongodb.sink.processor.field.projection.WhitelistProjector;

import java.util.Set;

public class WhitelistValueProjector extends WhitelistProjector {

	public WhitelistValueProjector(Set<String> fields) {
		super(fields);
	}

	@Override
	public void process(SinkDocument doc, NexlaMessage orig) {
		doProjection("", doc.valueDoc);
		getNext().ifPresent(pp -> pp.process(doc, orig));
	}

}
