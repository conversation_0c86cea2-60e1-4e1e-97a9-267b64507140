package com.nexla.connector.mongodb.sink.processor.field.projection;

import com.nexla.connector.mongodb.sink.processor.PostProcessor;
import org.bson.BsonDocument;

import java.util.Set;

public abstract class FieldProjector extends PostProcessor {

	public static final String SINGLE_WILDCARD = "*";
	public static final String DOUBLE_WILDCARD = "**";
	public static final String SUB_FIELD_DOT_SEPARATOR = ".";

	protected final Set<String> fields;

	protected FieldProjector(Set<String> fields) {
		this.fields = fields;
	}

	protected abstract void doProjection(String field, BsonDocument doc);

}
