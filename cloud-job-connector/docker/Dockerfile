FROM nexla/base-jdk11:release-v2.16.1-206-b410f057d6746bbe86bf4ac1986b2917d0d70b0e
MAINTAINER Avinash "<EMAIL>"

ARG GIT_HASH
ENV GIT_HASH=$GIT_HASH

ADD target/cloud-job-connector*.jar app.jar
# copy spark-agent distribution to docker container
ADD target/nexla-spark-agent.jar nexla-spark-agent.jar

RUN md5sum nexla-spark-agent.jar > nexla-spark-agent.jar.md5
RUN sha1sum nexla-spark-agent.jar > nexla-spark-agent.jar.sha1
RUN sha256sum nexla-spark-agent.jar > nexla-spark-agent.jar.sha256

EXPOSE 2001

ENTRYPOINT exec java $JAVA_OPTS \
                     -Dcom.sun.management.jmxremote \
                     -Dcom.sun.management.jmxremote.authenticate=false \
                     -Dcom.sun.management.jmxremote.ssl=false \
                     -Dcom.sun.management.jmxremote.local.only=false \
                     -Dcom.sun.management.jmxremote.port=2000 \
                     -Dcom.sun.management.jmxremote.rmi.port=2000 \
                     -Djava.rmi.server.hostname=127.0.0.1 \
                     -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:2001 \
                     -jar /app.jar
