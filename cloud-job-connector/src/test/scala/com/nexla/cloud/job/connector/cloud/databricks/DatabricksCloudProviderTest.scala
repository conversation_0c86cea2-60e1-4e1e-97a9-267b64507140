package com.nexla.cloud.job.connector.cloud.databricks

import com.databricks.sdk.WorkspaceClient
import com.databricks.sdk.core.{DatabricksConfig, DatabricksException}
import com.databricks.sdk.mixin.{ClustersExt, DbfsExt, SecretsExt}
import com.databricks.sdk.service.compute.{ClusterDetails, CreateCluster, CreateClusterResponse, Library, SparkNode}
import com.databricks.sdk.service.files
import com.databricks.sdk.service.files.{AddBlock, Create, DbfsAPI, FileInfo}
import com.databricks.sdk.service.jobs._
import com.databricks.sdk.support.{Wait, WaitStarter}
import com.nexla.admin.client.{DataSink, DataSource}
import com.nexla.cloud.job.connector.MetricsHelper
import com.nexla.cloud.job.connector.cloud.{DatabricksCloudSpecificCfg, DatabricksOidcAuth, DatabricksTokenAuth}
import com.nexla.common.ConnectionType
import com.nexla.connector.config.file.AWSAuthConfig
import com.nexla.connector.config.rest.BaseAuthConfig
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.{any, anyLong, anyString}
import org.mockito.Mockito.{doNothing, mock, verify, when}
import org.scalatest.funsuite.AnyFunSuite

import java.{time, util}
import java.util.Collections
import java.util.function.Consumer
import scala.collection.JavaConverters._

class DatabricksCloudProviderTest extends AnyFunSuite {

  val logTag = s"${getClass.getCanonicalName}-unittest"
  val decryptKey = "key"

  val mockAuthCfg: BaseAuthConfig = mock(classOf[BaseAuthConfig])
  when(mockAuthCfg.asAWS()).thenReturn(mock(classOf[AWSAuthConfig]))
  val srcCfg: Map[String, AnyRef] = java.util.Map.of(
    "cloud.jar.location", "s3://mybucket/my.jar",
    "cloud.databricks.token", "token",
    "cloud.databricks.workspace.host", "https://example.net",
    "access_key_id", "blah",
    "secret_key", "not-blah"
  ).asScala.toMap
  val dstCfg: Map[String, AnyRef] = java.util.Map.of(
    "insert.mode", "INSERT",
    "sink_type", "databricks",
    "table", "evan_nex_14752",
    "database", "nexla"
  ).asScala.toMap
  when(mockAuthCfg.originals()).thenReturn(srcCfg.asJava)
  val src = new DataSource
  src.setConnectionType(ConnectionType.DATABRICKS)
  src.setSourceConfig(srcCfg.asJava)
  def prepareInstance(mh: MetricsHelper, mockBricksClient: WorkspaceClient, lt: String = logTag): DatabricksCloudProvider = {
    new DatabricksCloudProvider(mh, lt, mockAuthCfg, src, hwProvider = "aws", storageProvider = ConnectionType.S3, "stub", mockBricksClient)
  }

  test("spawn a cluster") {
    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])
    val mockJobs = mock(classOf[JobsAPI])
    when(mockJobs.list(any[ListJobsRequest]())).thenReturn(Collections.emptyList[BaseJob]())

    val clusterName = "unit-test-cluster"
    val mockClusters = mock(classOf[ClustersExt])
    val stubDetails = new ClusterDetails()
    stubDetails.setNumWorkers(1)
    stubDetails.setClusterName(clusterName)
    val driver = new SparkNode()
    driver.setNodeId("node1")
    driver.setInstanceId("instance")
    driver.setHostPrivateIp("*******")
    stubDetails.setDriver(driver)
    stubDetails.setExecutors(java.util.Collections.singletonList(driver))
    stubDetails.setClusterId(clusterName)

    val stubCreationResponse = new CreateClusterResponse()
    stubCreationResponse.setClusterId(clusterName)

    val waitImpl = new WaitStarter[ClusterDetails]{
      override def apply(timeout: time.Duration, progress: Consumer[ClusterDetails]): ClusterDetails = {
        new ClusterDetails()
      }
    }
    when(mockClusters.create(any[CreateCluster]())).thenReturn(new Wait[ClusterDetails, CreateClusterResponse](waitImpl, stubCreationResponse))
    when(mockClusters.waitGetClusterRunning(anyString())).thenReturn(stubDetails)

    val client = mock(classOf[WorkspaceClient])
    when(client.clusters()).thenReturn(mockClusters)
    when(client.jobs()).thenReturn(mockJobs)
    val mockCfg = mock(classOf[DatabricksConfig])
    when(mockCfg.setHost(anyString())).thenReturn(mockCfg)
    when(mockCfg.setToken(anyString())).thenReturn(mockCfg)
    when(client.config()).thenReturn(mockCfg)

    val mockSecrets = mock(classOf[SecretsExt])
    when(client.secrets()).thenReturn(mockSecrets)

    val instance = prepareInstance(mockMetricsHelper, client)

    val response = instance.createCluster()
    assert(response.jobs.isEmpty)
    assert(response.clusterId.equals(clusterName))
    assert(response.mainNodes.nonEmpty)
    assert(response.workerNodes.nonEmpty)
    verify(mockMetricsHelper).incrementClusterSpawns()
  }

  test("track down the job until terminal state") {
    val jobId = "12342"
    val clusterId = "another-cluster"
    val mockCfg = mock(classOf[DatabricksConfig])
    when(mockCfg.setHost(anyString())).thenReturn(mockCfg)
    when(mockCfg.setToken(anyString())).thenReturn(mockCfg)
    val mockBricks = mock(classOf[WorkspaceClient])
    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])

    // these are set by the cloud job connector
    val sinkId = "42"
    val nexlaRunId = "9121321"
    val partitions = "s3://bucket/p=1/"
    val apiEnv = "test"
    // and this is set on databricks side after we launch
    val ourDatabricksRunId = 123

    val jobIsPending = new BaseRun
    jobIsPending.setJobId(jobId.toLong)
    jobIsPending.setRunId(ourDatabricksRunId)
    val taskPending = new RunTask
    val sparkJarPending = new SparkJarTask
    sparkJarPending.setParameters(java.util.List.of(sinkId, partitions, apiEnv, nexlaRunId))
    taskPending.setSparkJarTask(sparkJarPending)
    jobIsPending.setTasks(Collections.singletonList(taskPending))

    val jobIsRunning = new BaseRun
    jobIsRunning.setJobId(jobId.toLong)
    jobIsRunning.setRunId(ourDatabricksRunId)
    val taskRunning = new RunTask
    val sparkJarRunning = new SparkJarTask
    sparkJarRunning.setParameters(java.util.List.of(sinkId, partitions, apiEnv, nexlaRunId))
    taskRunning.setSparkJarTask(sparkJarRunning)
    jobIsRunning.setTasks(Collections.singletonList(taskRunning))

    val jobIsFinished = new BaseRun
    jobIsFinished.setJobId(jobId.toLong)
    jobIsFinished.setRunId(ourDatabricksRunId)
    val taskFinished = new RunTask
    val sparkJarFinished = new SparkJarTask
    sparkJarFinished.setParameters(java.util.List.of(sinkId, partitions, apiEnv, nexlaRunId))
    taskFinished.setSparkJarTask(sparkJarFinished)
    jobIsFinished.setTasks(Collections.singletonList(taskFinished))

    val mockJobs = mock(classOf[JobsAPI])
    when(mockJobs.listRuns(any[ListRunsRequest]()))
      .thenReturn(Collections.singletonList(jobIsPending))
      .thenReturn(Collections.singletonList(jobIsRunning))
      .thenReturn(Collections.singletonList(jobIsFinished))

    val stubRun = new Run
    stubRun.setTasks(Collections.singletonList(taskFinished))
    val finishedState = new RunState
    finishedState.setLifeCycleState(RunLifeCycleState.TERMINATED)
    finishedState.setResultState(RunResultState.SUCCESS)
    stubRun.setState(finishedState)
    when(mockJobs.getRun(ourDatabricksRunId)).thenReturn(stubRun)

    when(mockBricks.config()).thenReturn(mockCfg)
    when(mockBricks.jobs()).thenReturn(mockJobs)
    val instance = prepareInstance(mockMetricsHelper, mockBricks, "trackjobtest|||")

    val finalDefinition = instance.trackUntilTerminalState(jobId, clusterId)

    assert(finalDefinition.jobId.equals(jobId))
    assert(finalDefinition.status.equals("Succeeded"))
    verify(mockMetricsHelper).gaugeTaskRunTime(anyLong())
  }

  test("terminate cluster uses correct API methods and meters the calls") {
    val mh = mock(classOf[MetricsHelper])
    val mockBricks = mock(classOf[WorkspaceClient])
    val mockCfg = mock(classOf[DatabricksConfig])
    when(mockCfg.setHost(anyString())).thenReturn(mockCfg)
    when(mockCfg.setToken(anyString())).thenReturn(mockCfg)
    when(mockBricks.config()).thenReturn(mockCfg)
    val mockClustersExt = mock(classOf[ClustersExt])
    when(mockBricks.clusters()).thenReturn(mockClustersExt)

    val instance = prepareInstance(mh, mockBricks)
    instance.stopCluster("stopme")

    verify(mockClustersExt).waitGetClusterTerminated("stopme")
    verify(mh).incrementClusterTerminates()
  }

  test("run submit step uses correct API methods and meters the calls, jar upload") {
    val mh = mock(classOf[MetricsHelper])
    val mockBricks = mock(classOf[WorkspaceClient])
    val mockCfg = mock(classOf[DatabricksConfig])
    when(mockCfg.setHost(anyString())).thenReturn(mockCfg)
    when(mockCfg.setToken(anyString())).thenReturn(mockCfg)
    when(mockBricks.config()).thenReturn(mockCfg)
    val mockClustersExt = mock(classOf[ClustersExt])
    val mockJobs = mock(classOf[JobsAPI])
    val mockDbfs = mock(classOf[DbfsExt])
    val stubJob = new BaseJob
    stubJob.setJobId(32131L)
    val stubSettings = new JobSettings
    val stubTask = new Task
    val stubSparkTask = new SparkJarTask
    val theParams = java.util.List.of("1", "s3://abc/bcd=1/", "test", "123")
    stubSparkTask.setParameters(theParams)
    val lib = new Library()
    lib.setJar("s3://mybucket/my.jar")
    stubTask.setLibraries(Collections.singletonList(lib))
    val stubRun = new BaseRun()
    stubRun.setStartTime(System.currentTimeMillis())
    val stubState = new RunState()
    stubState.setLifeCycleState(RunLifeCycleState.TERMINATED)
    stubState.setResultState(RunResultState.SUCCESS)
    stubRun.setState(stubState)
    stubRun.setJobId(32131L)
    stubRun.setStartTime(System.currentTimeMillis())
    val stubRunTask = new RunTask
    stubRunTask.setSparkJarTask(stubSparkTask)
    stubRun.setTasks(Collections.singletonList(stubRunTask))
    val stubRunParams = new RunParameters
    stubRunParams.setJarParams(theParams)
    stubRun.setOverridingParameters(stubRunParams)
    stubTask.setSparkJarTask(stubSparkTask)
    stubTask.setExistingClusterId("stub")
    stubSettings.setTasks(Collections.singletonList(stubTask))
    stubJob.setSettings(stubSettings)
    stubJob.setJobId(32131L)

    val mockSecrets = mock(classOf[SecretsExt])
    when(mockBricks.secrets()).thenReturn(mockSecrets)

    val createResponse = new files.CreateResponse().setHandle(1L)

    when(mockJobs.list(any[ListJobsRequest]())).thenReturn(Collections.singletonList(stubJob))
    when(mockJobs.listRuns(any[ListRunsRequest])).thenReturn(Collections.singletonList(stubRun))
    when(mockDbfs.create(any[String])).thenReturn(createResponse)
    when(mockDbfs.getStatus(any[String])).thenThrow(new DatabricksException("Resource not found"))
    doNothing().when(mockDbfs).addBlock(any[AddBlock])
    doNothing().when(mockDbfs).close(1L)
    when(mockBricks.clusters()).thenReturn(mockClustersExt)
    when(mockBricks.jobs()).thenReturn(mockJobs)
    when(mockBricks.dbfs()).thenReturn(mockDbfs)
    val instance = prepareInstance(mh, mockBricks)
    val jobs = instance.runSparkSubmitStep("stub", 1, "s3://abc/bcd=1/", "test", 123L)

    assert(jobs.nonEmpty)
    verify(mh).gaugeTaskAssignment(ArgumentMatchers.eq(instance.cloudName), anyLong())
  }

  test("run submit step uses correct API methods and meters the calls, jar present") {
    val mh = mock(classOf[MetricsHelper])
    val mockBricks = mock(classOf[WorkspaceClient])
    val mockCfg = mock(classOf[DatabricksConfig])
    when(mockCfg.setHost(anyString())).thenReturn(mockCfg)
    when(mockCfg.setToken(anyString())).thenReturn(mockCfg)
    when(mockBricks.config()).thenReturn(mockCfg)
    val mockClustersExt = mock(classOf[ClustersExt])
    val mockJobs = mock(classOf[JobsAPI])
    val mockDbfs = mock(classOf[DbfsExt])
    val stubJob = new BaseJob
    stubJob.setJobId(32131L)
    val stubSettings = new JobSettings
    val stubTask = new Task
    val stubSparkTask = new SparkJarTask
    val theParams = java.util.List.of("1", "s3://abc/bcd=1/", "test", "123")
    stubSparkTask.setParameters(theParams)
    val lib = new Library()
    lib.setJar("s3://mybucket/my.jar")
    stubTask.setLibraries(Collections.singletonList(lib))
    val stubRun = new BaseRun()
    stubRun.setStartTime(System.currentTimeMillis())
    val stubState = new RunState()
    stubState.setLifeCycleState(RunLifeCycleState.TERMINATED)
    stubState.setResultState(RunResultState.SUCCESS)
    stubRun.setState(stubState)
    stubRun.setJobId(32131L)
    stubRun.setStartTime(System.currentTimeMillis())
    val stubRunTask = new RunTask
    stubRunTask.setSparkJarTask(stubSparkTask)
    stubRun.setTasks(Collections.singletonList(stubRunTask))
    val stubRunParams = new RunParameters
    stubRunParams.setJarParams(theParams)
    stubRun.setOverridingParameters(stubRunParams)
    stubTask.setSparkJarTask(stubSparkTask)
    stubTask.setExistingClusterId("stub")
    stubSettings.setTasks(Collections.singletonList(stubTask))
    stubJob.setSettings(stubSettings)
    stubJob.setJobId(32131L)

    val mockSecrets = mock(classOf[SecretsExt])
    when(mockBricks.secrets()).thenReturn(mockSecrets)

    val createResponse = new files.CreateResponse().setHandle(1L)

    when(mockJobs.list(any[ListJobsRequest]())).thenReturn(Collections.singletonList(stubJob))
    when(mockJobs.listRuns(any[ListRunsRequest])).thenReturn(Collections.singletonList(stubRun))
    when(mockDbfs.create(any[String])).thenReturn(createResponse)
    when(mockDbfs.getStatus(any[String])).thenReturn(new FileInfo().setFileSize(633100052L).setPath("/FileStore/jars/my.jar"))
    doNothing().when(mockDbfs).addBlock(any[AddBlock])
    doNothing().when(mockDbfs).close(1L)
    when(mockBricks.clusters()).thenReturn(mockClustersExt)
    when(mockBricks.jobs()).thenReturn(mockJobs)
    when(mockBricks.dbfs()).thenReturn(mockDbfs)
    val instance = prepareInstance(mh, mockBricks)
    val jobs = instance.runSparkSubmitStep("stub", 1, "s3://abc/bcd=1/", "test", 123L)

    assert(jobs.nonEmpty)
    verify(mh).gaugeTaskAssignment(ArgumentMatchers.eq(instance.cloudName), anyLong())
  }

  test("verify job casting") {
    val baseJob = new BaseJob
    baseJob.setCreatedTime(1709250125721L)
    baseJob.setCreatorUserName("<EMAIL>")
    baseJob.setJobId(168724509575172L)
    val emailNotifications = new JobEmailNotifications
    emailNotifications.setNoAlertForSkippedRuns(false)
    val tasks = new util.ArrayList[RunTask]()
    val task = new RunTask
    task.setDescription("Nexla Spark Job")
    task.setExistingClusterId("0311-215318-j045dn1a")
    val lib = new Library
    lib.setJar("s3://nexla-emr-test-bucket/akotliar/emr_jars/nexla-spark-agent-develop-886a2bcd1fb508abcf3421247a01725263a59d51.jar")
    task.setLibraries(Collections.singletonList(lib))
    val tns = new TaskNotificationSettings
    tns.setAlertOnLastAttempt(false)
    tns.setNoAlertForCanceledRuns(false)
    tns.setNoAlertForSkippedRuns(false)
    task.setRunIf(RunIf.ALL_SUCCESS)
    val sjt = new SparkJarTask
    sjt.setJarUri("")
    sjt.setMainClassName("com.nexla.spark_agent.SparkAgentApp")
    sjt.setParameters(java.util.List.of("9815", "s3://qa.nexla.com/akotliar/sparksource_partitioned/partition=3/", "https://test.nexla.com/admin-api", "1710519132492"))
    task.setSparkJarTask(sjt)
    task.setTaskKey("Nexla_Spark_Job_sink-9815")
    tasks.add(task)

    val mockCfg = mock(classOf[DatabricksConfig])
    when(mockCfg.setHost(anyString())).thenReturn(mockCfg)
    when(mockCfg.setToken(anyString())).thenReturn(mockCfg)
    val mockBricks = mock(classOf[WorkspaceClient])
    when(mockBricks.config()).thenReturn(mockCfg)
    val mockJobs = mock(classOf[JobsAPI])

    val stubSettings = new JobSettings
    val stubJob = new BaseJob
    stubJob.setJobId(32131L)
    val stubTask = new Task
    stubTask.setSparkJarTask(sjt)
    stubTask.setExistingClusterId("stub")
    stubSettings.setTasks(Collections.singletonList(stubTask))
    stubJob.setSettings(stubSettings)
    stubJob.setJobId(32131L)
    val stubRun = new BaseRun()
    stubRun.setStartTime(System.currentTimeMillis())
    val stubState = new RunState()
    stubState.setLifeCycleState(RunLifeCycleState.TERMINATED)
    stubState.setResultState(RunResultState.SUCCESS)
    stubRun.setState(stubState)
    stubRun.setJobId(32131L)
    stubRun.setStartTime(System.currentTimeMillis())
    val stubRunTask = new RunTask
    stubRunTask.setSparkJarTask(sjt)
    stubRun.setTasks(Collections.singletonList(stubRunTask))
    val stubRunParams = new RunParameters
    val theParams = java.util.List.of("1", "s3://abc/bcd=1/", "test", "123")
    stubRunParams.setJarParams(theParams)
    stubRun.setOverridingParameters(stubRunParams)

    when(mockJobs.list(any[ListJobsRequest]())).thenReturn(Collections.singletonList(stubJob))
    when(mockJobs.listRuns(any[ListRunsRequest])).thenReturn(Collections.singletonList(stubRun))
    when(mockBricks.jobs()).thenReturn(mockJobs)
    val mh = mock(classOf[MetricsHelper])
    val instance = prepareInstance(mh, mockBricks)

    val baseRun = new BaseRun()
    baseRun.setTasks(tasks)

    val jd = instance.databricksToNexlaJobDef(baseRun)
    assert(jd != null)
  }

  test("assumes token auth if auth-type is not specified") {
    val tokenCfg: Map[String, AnyRef] = java.util.Map.of(
      "cloud.jar.location", "s3://mybucket/my.jar",
      "cloud.databricks.token", "token",
      "cloud.databricks.workspace.host", "https://example.net",
      "access_key_id", "blah",
      "secret_key", "not-blah"
    ).asScala.toMap
    val dbricksCfg: DatabricksCloudSpecificCfg = DatabricksCloudSpecificCfg.apply(tokenCfg)

    assert(dbricksCfg.auth.isInstanceOf[DatabricksTokenAuth])
  }

  test("verifies all oauth parameters present") {
    val oidcCfgWithoutSecret: Map[String, AnyRef] = java.util.Map.of(
      "cloud.jar.location", "s3://mybucket/my.jar",
      "cloud.databricks.auth-type", "oidc",
      "cloud.databricks.workspace.host", "https://example.net",
      "cloud.databricks.client-id", "client"
    ).asScala.toMap
    val oidcCfgWithoutClientId: Map[String, AnyRef] = java.util.Map.of(
      "cloud.jar.location", "s3://mybucket/my.jar",
      "cloud.databricks.auth-type", "oidc",
      "cloud.databricks.workspace.host", "https://example.net",
      "cloud.databricks.secret", "s3cr37"
    ).asScala.toMap

    assertThrows[IllegalArgumentException](DatabricksCloudSpecificCfg.apply(oidcCfgWithoutSecret))
    assertThrows[IllegalArgumentException](DatabricksCloudSpecificCfg.apply(oidcCfgWithoutClientId))
  }
}
