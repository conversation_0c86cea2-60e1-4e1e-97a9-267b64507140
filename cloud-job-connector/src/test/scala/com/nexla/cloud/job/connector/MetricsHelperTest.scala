package com.nexla.cloud.job.connector

import com.nexla.telemetry.Telemetry
import com.typesafe.scalalogging.LazyLogging
import org.mockito.Mockito.{atLeast, mock, verify}
import org.scalatest.funsuite.AnyFunSuite

class MetricsHelperTest extends AnyFunSuite with LazyLogging {
  test("make sure gauges and counters don't intersect") {
    val mockTelemetry = mock(classOf[Telemetry])
    val metricsHelper = MetricsHelper(mockTelemetry, Integer.MAX_VALUE, Integer.MAX_VALUE)

    metricsHelper.incrementClusterSpawns()
    metricsHelper.incrementClusterTerminates()
    metricsHelper.incrementTaskAssignments()
    // "direct" mgmt of the underlying impl
    metricsHelper.reportAndResetGauges().run()

    verify(mockTelemetry, atLeast(1)).recordCounter(metricsHelper.CLUSTER_SPAWNS_NUMBER_COUNTER)
    verify(mockTelemetry, atLeast(1)).recordCounter(metricsHelper.CLUSTER_TERMINATES_NUMBER_COUNTER)
    verify(mockTelemetry, atLeast(1)).recordCounter(metricsHelper.TASK_ASSIGNMENTS_NUMBER_COUNTER)
  }
}
