package com.nexla.cloud.job.connector

import com.nexla.admin.client.{DataCredentials, DataSink, DataSource}
import com.nexla.cloud.job.connector.cloud.{CloudSpecificCfg, ClusterConfig}
import com.nexla.cloud.job.connector.cloud.aws.AWSCloudProvider
import com.nexla.cloud.job.connector.compat.PipelineContext
import com.nexla.common.ConnectionType
import com.nexla.sc.client.listing.{FileSourceTypes, FileStatuses, ListedFile}
import com.nexla.sc.config.DbConf
import com.typesafe.scalalogging.LazyLogging
import org.mockito.Mockito.{mock, never, verify, when}
import org.scalatest.TagAnnotation
import org.scalatest.funsuite.AnyFunSuite

import java.time.LocalDateTime
import java.util.Collections
import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class UtilsTest extends AnyFunSuite with LazyLogging {

  def getMockProps: AppProps = {
    val instance = mock(classOf[AppProps])
    when(instance.decryptKey).thenReturn("6aea52fdf667ad08ed9a7ab2c18de555")
    when(instance.apiCredentialsServer).thenReturn("mock-admin-api")
    when(instance.apiAccessKey).thenReturn("mock-admin-api-key")
    when(instance.listingDb).thenReturn(mock(classOf[DbConf]))
    instance
  }

  test("mapping for s3 should work") {
    val ctx = mock(classOf[PipelineContext])
    val mockMetrics: MetricsHelper = mock(classOf[MetricsHelper])

    when(ctx.readLog).thenReturn("[unittest][from-123][to-456]")
    val stubCfg = Map(
      "cloud.region" -> "no",
      "cloud.role.with.cluster.access" -> "yes",
      "cloud.cluster.entity.role" -> "yes",
      "cloud.cluster.service.role" -> "yes",
      "cloud.cluster.vm.subnet" -> "yes",
      "cloud.jar.location" -> "no"
    ).asJava

    val stubSrc = new DataSource
    stubSrc.setName("test")
    stubSrc.setConnectionType(ConnectionType.S3)
    stubSrc.setSourceConfig(stubCfg.asInstanceOf[java.util.Map[String, AnyRef]])
    val stubCreds = new DataCredentials
    stubCreds.setId(123)
    stubSrc.setDataCredentials(stubCreds)
    val stubSink = new DataSink
    stubSink.setId(0)
    stubSink.setName("test-sink")
    stubSink.setConnectionType(ConnectionType.S3)
    stubSink.setSinkConfig(stubCfg.asInstanceOf[java.util.Map[String, AnyRef]])

    when(ctx.dataSource).thenReturn(stubSrc)
    when(ctx.dataSinks).thenReturn(Map(0 -> stubSink))

    val result = Utils.getCloudProvider(mockMetrics, stubCfg, stubCfg, ctx.dataSource, "logtag", getMockProps)
    assert(result.isInstanceOf[AWSCloudProvider])
    assert(result.cloudName.equals("AWS"))
    verify(mockMetrics).incrementCloudAPIConnections() // make sure metrics are incremented
  }

  test("unsupported types should throw exception") {
    val mockMetrics: MetricsHelper = mock(classOf[MetricsHelper])

    val ctx = mock(classOf[PipelineContext])
    val stubSrc = new DataSource
    stubSrc.setName("test")
    stubSrc.setConnectionType(ConnectionType.AS400)
    stubSrc.setSourceConfig(java.util.Map.of())
    val stubCreds = new DataCredentials
    stubCreds.setId(123)
    stubSrc.setDataCredentials(stubCreds)
    val stubSink = new DataSink
    stubSink.setId(0)
    stubSink.setName("test-sink")
    stubSink.setConnectionType(ConnectionType.S3)

    when(ctx.dataSource).thenReturn(stubSrc)
    when(ctx.dataSinks).thenReturn(Map(0 -> stubSink))

    assertThrows[IllegalArgumentException] {
      Utils.getCloudProvider(mockMetrics, Map("a" ->"b").asJava, Map("a" ->"b").asJava, ctx.dataSource, "logTag", getMockProps)
    }
    verify(mockMetrics, never()).incrementCloudAPIConnections() // this should not be called as we weren't able to create cloud provider
  }

  test("prepare simple s3 path") {
    val md = Option(Map("hive.partition" -> Option("true")))
    val lf = ListedFile(3456L, "someOtherDir/someName=123", None, None, None, None,
      FileSourceTypes.Listing, FileStatuses.New, None, md, LocalDateTime.now())

    val result = Utils.preparePath(ConnectionType.S3, "bucketA/someDir/someOtherDir", lf)
    assert(result.equals("3456:s3://bucketA/someOtherDir/someName=123/"))
  }

  test("fail path preparation if there's only the bucket root") {
    assertThrows[IllegalArgumentException] {
      val lf = ListedFile(1237L,"someOtherDir/someName=123", None, None, None, None,
        FileSourceTypes.Listing, FileStatuses.New, None, None, LocalDateTime.now())
      Utils.preparePath(ConnectionType.S3,  "justBucketA", lf)
    }
  }

  test("assemble correct cluster config from the cloud cfg even if it's missing smth") {
    val srcCfg: Map[String, AnyRef] = java.util.Map.of("cloud.jar.location", "b").asScala.toMap
    val mockCfg = new CloudSpecificCfg(srcCfg)
    val clusterCfg: ClusterConfig = Utils.getClusterConfig("logTag", mockCfg, srcCfg)
    assert(clusterCfg.mainNodeVCPU == 2)
    assert(clusterCfg.mainNodes == 1)
    assert(clusterCfg.workerNodeVCPU == 2)
  }

  test("assemble correct cluster config from the cloud cfg parameters") {
    val srcCfg: Map[String, AnyRef] = java.util.Map.of("cloud.cluster.main.node.vcpu", "32",
      "cloud.jar.location", "meh",
      "cloud.cluster.worker.node.vcpu", "64",
      "cloud.cluster.worker.nodes", "3").asScala.toMap
    val mockCfg = new CloudSpecificCfg(srcCfg)
    val clusterCfg: ClusterConfig = Utils.getClusterConfig("logTag", mockCfg, srcCfg)
    assert(clusterCfg.mainNodeVCPU == 32)
    assert(clusterCfg.workerNodes == 3)
    assert(clusterCfg.workerNodeVCPU == 64)
  }
}
