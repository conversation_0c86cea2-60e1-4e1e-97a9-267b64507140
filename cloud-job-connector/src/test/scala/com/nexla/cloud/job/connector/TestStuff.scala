package com.nexla.cloud.job.connector

import com.amazonaws.services.elasticmapreduce.model.{Cluster, ClusterState, ClusterStatus, DescribeClusterResult, DescribeStepResult, HadoopStepConfig, ListStepsResult, RunJobFlowResult, Step, StepState, StepStatus, StepSummary}
import com.nexla.cloud.job.connector.cloud.{ClusterNode, ClusterResponse, JobDefinition}

import java.util
import java.util.UUID
import scala.collection.JavaConverters._

object TestStuff {

  def getExampleSourceConfig: util.HashMap[String, Object] = {
    val stubCfgMap = new util.HashMap[String, Object]()
    stubCfgMap.put("path", "bucketA/someDir/someOtherDir")
    stubCfgMap.put("cloud.region", "my-region")
    stubCfgMap.put("cloud.cluster.id", "my-external-cluster")
    stubCfgMap.put("cloud.cluster.vm.subnet", "my-subnet")
    stubCfgMap.put("cloud.role.with.cluster.access", "stub-role-1")
    stubCfgMap.put("cloud.cluster.service.role", "stub-role-2")
    stubCfgMap.put("cloud.cluster.entity.role", "stub-role-3")
    stubCfgMap.put("cloud.jar.location", "s3://foo/bar/baz.jar")
    stubCfgMap.put("cloud.credentials.id", "42")
    stubCfgMap
  }


  def stubRawClusterResponse(clusterName: String): DescribeClusterResult = {
    val res = new DescribeClusterResult()
    val cluster = new Cluster()
    val status = new ClusterStatus()
    status.setState(ClusterState.WAITING)
    cluster.setStatus(status)
    cluster.setMasterPublicDnsName(s"${UUID.randomUUID().toString}")
    cluster.setId(s"${UUID.randomUUID().toString}")
    cluster.setName(clusterName)
    res.setCluster(cluster)
    res
  }

  def stubRawRunJobFlowResponse(clusterName: String): RunJobFlowResult = {
    val res = new RunJobFlowResult()
    res.setJobFlowId(clusterName)
    res.setClusterArn(clusterName)
    res
  }

  def stubRawStepResponse(stepName: String, sinkId: Int, runId: Long, partition: String): ListStepsResult = {
    val stepSummary = new StepSummary
    stepSummary.setId(stepName)
    stepSummary.setName(stepName)
    val cfg = new HadoopStepConfig
    cfg.setArgs(List(String.valueOf(sinkId), partition, "3", String.valueOf(runId)).asJava)
    stepSummary.setConfig(cfg)
    val stat = new StepStatus
    stat.setState(StepState.RUNNING)
    stepSummary.setStatus(stat)
    val steps = List(stepSummary).asJava
    val res = new ListStepsResult()
    res.setSteps(steps)
    res
  }

  def stubClusterResponse(clusterName: String): ClusterResponse = ClusterResponse(
    clusterId = clusterName,
    List(ClusterNode("1", "master-host", "master-address")),
    List(ClusterNode("2", "data-host", "data-address")),
    List(ClusterNode("3", "worker-host", "worker-address")),
    List(
      JobDefinition("reusable-job-id", 1122, 1232131231123L, List("partition-processed"), "PENDING"),
      JobDefinition("job-id-2", 3344, 118818181121L, List("another-partition"), "STOPPED"),
    ),
  )

  def stubRunningJobDef(jobName: String): JobDefinition = {
    JobDefinition(jobName, 1122, 1232131231123L, List("partition-processed"), "RUNNING")
  }

  def stubStoppedJobDef(jobName: String): JobDefinition = {
    JobDefinition(jobName, 1122, 1232131231123L, List("partition-processed"), "STOPPED")
  }

  def stubRawDescribeStepResult(stepName: String, stepState: StepState, sinkId: Int, partition: String, runId: Long): DescribeStepResult = {
    val res = new DescribeStepResult
    val stp = new Step()
    val cfg = new HadoopStepConfig
    cfg.setArgs(List(String.valueOf(sinkId), partition, "3", String.valueOf(runId)).asJava)
    stp.setName(stepName)
    stp.setConfig(cfg)
    stp.setId(stepName)
    val stat = new StepStatus
    stat.setState(stepState)
    stp.setStatus(stat)
    res.setStep(stp)
    res
  }
}
