package com.nexla.cloud.job.connector.cloud.aws

import com.amazonaws.services.elasticmapreduce.AmazonElasticMapReduceClient
import com.amazonaws.services.elasticmapreduce.model.{Cluster, ClusterState, ClusterStatus, DescribeClusterRequest, DescribeClusterResult, HadoopStepConfig, ListStepsResult, RunJobFlowResult, StepState, StepStatus, StepSummary, TerminateJobFlowsRequest}
import com.amazonaws.services.s3.AmazonS3Client
import com.amazonaws.services.secretsmanager.AWSSecretsManager
import com.amazonaws.services.secretsmanager.model.{GetSecretValueRequest, GetSecretValueResult}
import com.nexla.cloud.job.connector.MetricsHelper
import com.nexla.connector.config.rest.BaseAuthConfig
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.{any, anyLong, anyString}
import org.mockito.Mockito._
import org.scalatest.TagAnnotation
import org.scalatest.funsuite.AnyFunSuite

import scala.collection.JavaConverters._
import java.util

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class AWSCloudProviderTest extends AnyFunSuite {
  val logTag = "AWSCloudProviderTest"
  val baseAuthCfg: BaseAuthConfig = {
    val emptyMap = new util.HashMap[String, String]()
    new BaseAuthConfig(BaseAuthConfig.baseAuthConfigDef, emptyMap, 1)
  }

  val mockEmrClient: AmazonElasticMapReduceClient = mock(classOf[AmazonElasticMapReduceClient])
  val stubCfgMap: Map[String, String] = Map(
    "cloud.region" -> "example",
    "cloud.cluster.vm.subnet" -> "my-subnet",
    "cloud.role.with.cluster.access" -> "stub",
    "cloud.cluster.service.role" -> "stub2",
    "cloud.cluster.entity.role" -> "stub3",
    "cloud.jar.location" -> "s3://foo/bar/baz.jar"
  )

  test("cluster termination") {
    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])
    val awsCloudProvider: AWSCloudProvider = AWSCloudProvider(mockMetricsHelper, logTag,
      baseAuthCfg,
      stubCfgMap,
      stubCfgMap,
      "stubKey",
      Option(mockEmrClient)
    )
    val clusterName = "mock-cluster"
    val expectedReq = new TerminateJobFlowsRequest()
    expectedReq.setJobFlowIds(List(clusterName).asJava)

    awsCloudProvider.stopCluster(clusterName)
    verify(mockEmrClient).terminateJobFlows(expectedReq)
    verify(mockMetricsHelper).incrementClusterTerminates()
  }

  test("cluster creation") {
    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])
    val secrets = mock(classOf[AWSSecretsManager])
    val stubResult = new GetSecretValueResult().withSecretString("stub")
    when(secrets.getSecretValue(ArgumentMatchers.any[GetSecretValueRequest]())).thenReturn(stubResult)
    val mockS3 = mock(classOf[AmazonS3Client])
    val awsCloudProvider: AWSCloudProvider = AWSCloudProvider(mockMetricsHelper, logTag,
      baseAuthCfg,
      stubCfgMap,
      stubCfgMap,
      "stubKey",
      Option(mockEmrClient),
      Option(secrets),
      Option(mockS3)
    )

    val response = new RunJobFlowResult()
    response.setJobFlowId("creating-cluster")
    when(mockEmrClient.runJobFlow(any())).thenReturn(response)
    val describeClusterResult = new DescribeClusterResult()
    val cluster = new Cluster()
    val clusterStatus = new ClusterStatus()
    clusterStatus.setState(ClusterState.WAITING)
    cluster.setStatus(clusterStatus)
    describeClusterResult.setCluster(cluster)
    when(mockEmrClient.describeCluster(any())).thenReturn(describeClusterResult)

    awsCloudProvider.createCluster()
    verify(mockEmrClient).runJobFlow(any())
    verify(mockMetricsHelper).incrementClusterSpawns()
    verify(mockMetricsHelper).gaugeClusterCreation(anyString(), anyLong())
  }

  test("check live cluster state") {
    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])
    val awsCloudProvider: AWSCloudProvider = AWSCloudProvider(mockMetricsHelper, logTag,
      baseAuthCfg,
      stubCfgMap,
      stubCfgMap,
      "stubKey",
      Option(mockEmrClient)
    )

    val clusterName = "live-cluster"
    val descrReq = new DescribeClusterRequest()
    descrReq.setClusterId(clusterName)
    val result = new DescribeClusterResult()
    val cluster = new Cluster()
    cluster.setName(clusterName)
    val clusterStatus = new ClusterStatus()
    clusterStatus.setState(ClusterState.WAITING)
    cluster.setStatus(clusterStatus)
    result.setCluster(cluster)
    val listStepResults = new ListStepsResult()
    val stepSummary = new StepSummary()
    val stepStatus = new StepStatus()
    stepStatus.setState(StepState.RUNNING)
    stepSummary.setStatus(stepStatus)
    stepSummary.setName("mock-step")
    val hadoopStepConfig = new HadoopStepConfig()
    hadoopStepConfig.setArgs(List("9999", "a-partition", "env", "2131231").asJavaCollection)
    stepSummary.setConfig(hadoopStepConfig)
    listStepResults.setSteps(List(stepSummary).asJava)
    when(mockEmrClient.listSteps(any())).thenReturn(listStepResults)
    when(mockEmrClient.describeCluster(descrReq)).thenReturn(result)
    val checkResult = awsCloudProvider.checkExistingCluster(clusterName)
    verify(mockEmrClient, atLeast(1)).describeCluster(any())
    assert(checkResult.jobs.size == 1)
    assert(checkResult.jobs.head.status.equals("RUNNING"))
    assert(checkResult.jobs.head.sinkId.equals(9999))
    assert(checkResult.jobs.head.partitionInfo.equals(List("a-partition")))
    assert(checkResult.jobs.head.runId.equals(2131231L))
  }

  test("check dead cluster state") {
    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])
    val awsCloudProvider: AWSCloudProvider = AWSCloudProvider(mockMetricsHelper, logTag,
      baseAuthCfg,
      stubCfgMap,
      stubCfgMap,
      "stubKey",
      Option(mockEmrClient)
    )
    val clusterName = "dead-cluster"
    val descrReq = new DescribeClusterRequest()
    descrReq.setClusterId(clusterName)
    val result = new DescribeClusterResult()
    val cluster = new Cluster()
    cluster.setName(clusterName)
    val clusterStatus = new ClusterStatus()
    clusterStatus.setState(ClusterState.TERMINATED_WITH_ERRORS)
    cluster.setStatus(clusterStatus)
    result.setCluster(cluster)
    when(mockEmrClient.describeCluster(descrReq)).thenReturn(result)
    val caught = intercept[IllegalArgumentException] {
      awsCloudProvider.checkExistingCluster(clusterName)
    }
    assert(caught.getMessage.toLowerCase.contains("unusable"))
  }

}
