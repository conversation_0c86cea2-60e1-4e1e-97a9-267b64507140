
package com.nexla.cloud.job.connector.pipeline

import akka.actor.ActorSystem
import akka.stream.Materializer
import com.amazonaws.services.elasticmapreduce.AmazonElasticMapReduce
import com.amazonaws.services.elasticmapreduce.model._
import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.model.ObjectMetadata
import com.amazonaws.services.secretsmanager.AWSSecretsManager
import com.amazonaws.services.secretsmanager.model.{GetSecretValueRequest, GetSecretValueResult}
import com.nexla.Fixtures
import com.nexla.admin.client.{AdminApiClient, DataCredentials, ResourceStatus}
import com.nexla.cloud.job.connector.cloud.aws.AWSCloudProvider
import com.nexla.cloud.job.connector.compat.{BasicPipelineRegistry, Pipeline}
import com.nexla.cloud.job.connector.infra.ClusterTracker
import com.nexla.cloud.job.connector.job.JobSender
import com.nexla.cloud.job.connector.{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TestStuff}
import com.nexla.common.ConnectionType
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.connector.config.PipelineTaskType
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.sc.client.job_scheduler.TaskRequest.TaskId
import com.nexla.sc.client.job_scheduler.{NodeTaskResponseElem, PipelineTaskMeta}
import com.nexla.sc.client.listing.{FileSourceTypes, FileStatuses, ListedFile, ListingAppClient, ListingResult}
import com.nexla.sc.config.DbConf
import com.nexla.sc.util.Async
import com.nexla.scalatest.Repeated
import com.typesafe.scalalogging.LazyLogging
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.{any, anyInt, anyLong, anyString}
import org.mockito.Mockito._
import org.scalatest.{BeforeAndAfter, Ignore, TagAnnotation}
import org.scalatest.funsuite.AnyFunSuite

import java.io.File
import java.time.LocalDateTime
import java.util
import java.util.Optional
import java.util.concurrent.Executors
import scala.collection.JavaConverters._
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.reflect.io.Directory

@TagAnnotation("com.nexla.test.ScalaUnitTests")
@Ignore
// FIXME: failures due to decryption, creds are encrypted with prod key
class CloudJobPipelineTest extends AnyFunSuite with BeforeAndAfter with LazyLogging with Repeated {

  before {
    val directory = new Directory(new File("/tmp/trackedClusters"))
    directory.deleteRecursively()
  }

  implicit lazy val system: ActorSystem = ActorSystem("cloud-job-pipeline-test")
  implicit lazy val materializer: Materializer = Materializer(system)
  implicit lazy val executionContext: ExecutionContext = Async.ioExecutorContext

  val logTag = "CloudJobPipelineTest"

  def getMockListing(sourceId: Int, files: List[String]): ListingAppClient = {
    val instance = mock(classOf[ListingAppClient])

    if (files.isEmpty) {
      when(instance.takeFile(sourceId)).thenReturn(Future { ListingResult(None, inProgress = false) } )
      instance
    } else {
      val futures = files.map { fileName =>
        Future {
          ListingResult(Option(ListedFile(sourceId, fileName, None, None, None, None, FileSourceTypes.Listing, FileStatuses.New, None, None, LocalDateTime.now())), inProgress = false)
        }
      }

      when(instance.takeFile(sourceId)).thenReturn(futures.head, futures.tail: _*)
      instance
    }
  }

  def getMockProps: AppProps = {
    val instance = mock(classOf[AppProps])
    when(instance.decryptKey).thenReturn("6aea52fdf667ad08ed9a7ab2c18de555")
    when(instance.apiCredentialsServer).thenReturn("mock-admin-api")
    when(instance.listingDb).thenReturn(mock(classOf[DbConf]))
    instance
  }

  def getMockEmr(desiredClusterName: Option[String], jobToRun: Option[String], jobStatuses: List[StepState], sinkId: Int, runId: Long, partition: String): AmazonElasticMapReduce = {
    val instance = mock(classOf[AmazonElasticMapReduce])

    if (desiredClusterName.isDefined) {
      when(instance.describeCluster(ArgumentMatchers.any[DescribeClusterRequest]())).thenReturn(
        TestStuff.stubRawClusterResponse(desiredClusterName.get)
      )
    }

    if (jobToRun.isDefined) {
      when(instance.listSteps(ArgumentMatchers.any[ListStepsRequest]())).thenReturn(
        TestStuff.stubRawStepResponse(jobToRun.get, sinkId, runId, partition)
      )
      val jobStatusUpdates = jobStatuses.map(js => TestStuff.stubRawDescribeStepResult(jobToRun.get, js, sinkId, partition, runId))
      when(instance.describeStep(ArgumentMatchers.any[DescribeStepRequest]())).thenReturn(jobStatusUpdates.head, jobStatusUpdates.tail: _*)
    }
    instance
  }

  def getMockApi(sinkId: Int, sourceId: Int, cfg: util.Map[String, Object]): AdminApiClient = {
    val instance = Fixtures.adminApiWithSourceCfgAndSpecificSinkStatus(sourceId, sinkId, ResourceStatus.ACTIVE, cfg, ConnectionType.S3, ConnectionType.S3)
    val creds = new DataCredentials
    creds.setId(42)
    creds.setCredentialsVersion("1")
    creds.setCredentialsEncIv("vX8sfDZNyOe8TVac")
    creds.setCredentialsEnc("IkBs/BhFHRQm162xGXIazMhaq6U/QXJJdoKzDAU6MZSZwDbBX5JgypJYhKYwHBJ8XSR7ChgVknNySL+IaAk8bTGQ0TvEqDBkACCgZU75AVdgrpdOg2a6Mm2ZGzXDwmUjbWrWsX2LfU9CU9wNjaLvx/guAfaChq0T0M6VyDntfQx9eZ/6p/AlkmM3kEc7Rnu+f3SmCazNAZ1oZxHUKnl8JE91EIPZC8GmqBu9xF7HdkkigbvXg/WBeQBZ4emBcir0+/Ik0RSz/t35eZiw8unST2IcBQ==")
    when(instance.getDataCredentials(42)).thenReturn(Optional.of(creds))
    instance
  }

  test("normal flow, external cluster, successful run") {
    val sourceId = 1
    val sinkId = 321
    val runId = 123L

    val mockS3: AmazonS3 = mock(classOf[AmazonS3])
    when(mockS3.doesObjectExist(ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(true)
    val stubMd = new ObjectMetadata()
    stubMd.setRequesterCharged(true)
    val userMd = new util.HashMap[String, String]()
    userMd.put("a", "b")
    stubMd.setUserMetadata(userMd)
    when(mockS3.getObjectMetadata(ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(stubMd)

    val secrets = mock(classOf[AWSSecretsManager])
    val stubResult = new GetSecretValueResult().withSecretString("stub")
    when(secrets.getSecretValue(ArgumentMatchers.any[GetSecretValueRequest]())).thenReturn(stubResult)

    val stubCfgMap: util.HashMap[TaskId, Object] = TestStuff.getExampleSourceConfig
    val taskId = s"spark-$sinkId"
    val partitionToHandle = "s3://bucketA/some-path/"
    val mockListing = getMockListing(sourceId, List("some-path"))
    val mockProps = getMockProps
    val mockEmr = getMockEmr(Option("my-external-cluster"), Option("blah-job-id"),
      List(StepState.RUNNING, StepState.COMPLETED),
      sinkId, runId, partitionToHandle
    )
    val stubRes = new RunJobFlowResult
    stubRes.setJobFlowId("stib")
    when(mockEmr.runJobFlow(ArgumentMatchers.any[RunJobFlowRequest]())).thenReturn(stubRes)
    val mockMetrics: MetricsHelper = mock(classOf[MetricsHelper])
    val clusterTracker: ClusterTracker = ClusterTracker(
      Integer.MAX_VALUE, // check clusters every milli
      Integer.MAX_VALUE, // maximum idle time is 5 seconds
      "local",
      getMockProps,
      mockMetrics
    )
    val stubCloud = new AWSCloudProvider(mockMetrics, logTag, mock(classOf[BaseAuthConfig]),
      stubCfgMap.asScala.toMap,
      stubCfgMap.asScala.toMap,
      Option(mockEmr),
      Option(secrets),
      Option(mockS3), 1, 256, "stub")

    val mockApi = getMockApi(sinkId, sourceId, stubCfgMap)
    val configuredSender = JobSender(stubCloud, s"$taskId", mockMetrics)

    val stubRegistry: BasicPipelineRegistry = new BasicPipelineRegistry() {
      implicit val executionContext: ExecutionContext = ExecutionContext.fromExecutorService(Executors.newSingleThreadExecutor())
      override def createPipeline(resourceId: Int, runId: Long, taskId: TaskId): Pipeline = {
        new CloudJobPipeline(taskId, runId, sourceId, sinkId, mock(classOf[BaseAuthConfig]), mockApi, mockListing, mock(classOf[NexlaMessageProducer]), stubCloud, configuredSender, clusterTracker, mockMetrics, mockProps)
      }
    }

    val x = NodeTaskResponseElem(
      taskId,
      PipelineTaskType.SPARK,
      Some(PipelineTaskMeta(sinkId = Some(sinkId), runId = Some(runId))),
      restart = false,
      None)
    Await.result(stubRegistry.startPipeline(x, Set[Int](), Set[Int]()), Duration.Inf)
    // assert listing was called, so that processing is successful
    verify(mockListing).setFileStatus(anyInt(), anyLong(), ArgumentMatchers.eq(FileStatuses.Done), any[Option[Long]](), any())
  }

  test("no-hive flow, external cluster, successful run") {
    val sourceId = 2
    val sinkId = 322
    val filesAtOnce = 7
    val runId = 124L

    val mockS3: AmazonS3 = mock(classOf[AmazonS3])
    when(mockS3.doesObjectExist(ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(true)
    val stubMd = new ObjectMetadata()
    stubMd.setRequesterCharged(true)
    val userMd = new util.HashMap[String, String]()
    userMd.put("a", "b")
    stubMd.setUserMetadata(userMd)
    when(mockS3.getObjectMetadata(ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(stubMd)

    val stubCfgMap: util.HashMap[TaskId, Object] = TestStuff.getExampleSourceConfig
    val taskId = s"spark-$sinkId"
    val mockListing = getMockListing(sourceId, List("some-path"))
    val mockProps = getMockProps
    val mockEmr = getMockEmr(Option("my-external-cluster"), Option("blah-job-id"),
      List(StepState.RUNNING, StepState.COMPLETED),
      sinkId, runId, String.valueOf(filesAtOnce)
    )
    val stubRes = new RunJobFlowResult
    stubRes.setJobFlowId("stib")
    when(mockEmr.runJobFlow(ArgumentMatchers.any[RunJobFlowRequest]())).thenReturn(stubRes)
    val secrets = mock(classOf[AWSSecretsManager])
    val stubResult = new GetSecretValueResult().withSecretString("stub")
    when(secrets.getSecretValue(ArgumentMatchers.any[GetSecretValueRequest]())).thenReturn(stubResult)
    val mockMetrics: MetricsHelper = mock(classOf[MetricsHelper])
    val clusterTracker: ClusterTracker = ClusterTracker(
      1, // check clusters every milli
      5 * 1000, // maximum idle time is 5 seconds
      "local",
      getMockProps,
      mockMetrics
    )
    val stubCloud = new AWSCloudProvider(mockMetrics, logTag, mock(classOf[BaseAuthConfig]),
      stubCfgMap.asScala.toMap,
      stubCfgMap.asScala.toMap,
      Option(mockEmr), Option(secrets), Option(mockS3), 1, 256, "stub")

    val mockApi = getMockApi(sinkId, sourceId, stubCfgMap)
    val updatedSrcWithNewCfg = mockApi.getDataSource(sourceId)
    val itsCfg = updatedSrcWithNewCfg.get().getSourceConfig
    itsCfg.put("hive", "false")
    itsCfg.put("files.in.batch", String.valueOf(filesAtOnce))
    updatedSrcWithNewCfg.get().setSourceConfig(itsCfg)
    when(mockApi.getDataSource(sourceId)).thenReturn(updatedSrcWithNewCfg)

    val configuredSender = spy(JobSender(stubCloud, s"$taskId", mockMetrics))

    val stubRegistry: BasicPipelineRegistry = new BasicPipelineRegistry() {
      implicit val executionContext: ExecutionContext = ExecutionContext.fromExecutorService(Executors.newSingleThreadExecutor())

      override def createPipeline(resourceId: Int, runId: Long, taskId: TaskId): Pipeline = {
        new CloudJobPipeline(taskId, runId, sourceId, sinkId, mock(classOf[BaseAuthConfig]), mockApi, mockListing, mock(classOf[NexlaMessageProducer]), stubCloud, configuredSender, clusterTracker, mockMetrics, mockProps)
      }
    }

    val x = NodeTaskResponseElem(
      taskId,
      PipelineTaskType.SPARK,
      Some(PipelineTaskMeta(sinkId = Some(sinkId), runId = Some(runId))),
      restart = false,
      None)
    Await.result(stubRegistry.startPipeline(x, Set[Int](), Set[Int]()), Duration.Inf)
    // make sure that we launched spark job with maxFiles in arguments, instead of the partition.
    // the rest of the things, including listing interaction, is done from the spark app.
    // example: cluster-id 321 7 mockadminapi 1024
    // where first is cluster id, second is sink id, third is the maxFiles per run, fourth is the necessary admin api alias
    // and last is the run id
    verify(configuredSender).sendJob(anyString(), ArgumentMatchers.eq(sinkId),
      ArgumentMatchers.eq(String.valueOf(filesAtOnce)), ArgumentMatchers.eq("mock-admin-api"),
      ArgumentMatchers.eq(runId))
  }

  test("managed cluster, AWS hangs the cluster creation - timeout error expected, files not marked as processed") {
    val sourceId = 3
    val sinkId = 457
    val runId = 789L
    val taskId = s"spark-$sinkId"

    val mockS3: AmazonS3 = mock(classOf[AmazonS3])
    when(mockS3.doesObjectExist(ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(true)
    val stubMd = new ObjectMetadata()
    stubMd.setRequesterCharged(true)
    val userMd = new util.HashMap[String, String]()
    userMd.put("a", "b")
    stubMd.setUserMetadata(userMd)
    when(mockS3.getObjectMetadata(ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(stubMd)

    val stubCfgMap: util.HashMap[TaskId, Object] = TestStuff.getExampleSourceConfig
    val partitionToHandle = "s3://bucketA/some-other-path/"
    val mockListing = getMockListing(sourceId, List("some-other-path"))
    val mockProps = getMockProps
    val mockEmr = getMockEmr(None, None,
      List(StepState.RUNNING, StepState.COMPLETED),
      sinkId, runId, partitionToHandle
    )

    val mockMetrics: MetricsHelper = mock(classOf[MetricsHelper])
    val clusterTracker: ClusterTracker = ClusterTracker(
      1, // check clusters every milli
      5 * 1000, // maximum idle time is 5 seconds
      "local",
      getMockProps,
      mockMetrics
    )
    val secrets = mock(classOf[AWSSecretsManager])
    val stubResult = new GetSecretValueResult().withSecretString("stub")
    when(secrets.getSecretValue(ArgumentMatchers.any[GetSecretValueRequest]())).thenReturn(stubResult)
    val stubCloud = new AWSCloudProvider(mockMetrics, logTag, mock(classOf[BaseAuthConfig]),
      stubCfgMap.asScala.toMap,
      stubCfgMap.asScala.toMap,
      Option(mockEmr), Option(secrets), Option(mockS3), 1, 256, "stub")
    // remove the external cluster id
    val cfgWithNoExtCluster = stubCfgMap
    cfgWithNoExtCluster.remove("cloud.cluster.id")

    val mockApi = getMockApi(sinkId, sourceId, cfgWithNoExtCluster)
    when(mockEmr.runJobFlow(any[RunJobFlowRequest]())).thenThrow(new InternalServerErrorException("boom"))
    val configuredSender = JobSender(stubCloud, s"$taskId", mockMetrics)

    val stubRegistry: BasicPipelineRegistry = new BasicPipelineRegistry() {
      implicit val executionContext: ExecutionContext = ExecutionContext.fromExecutorService(Executors.newSingleThreadExecutor())

      override def createPipeline(sinkId: Int, runId: Long, taskId: TaskId): Pipeline = {
        new CloudJobPipeline(taskId, runId, sourceId, sinkId, mock(classOf[BaseAuthConfig]), mockApi, mockListing, mock(classOf[NexlaMessageProducer]), stubCloud, configuredSender, clusterTracker, mockMetrics, mockProps)
      }
    }

    val x = NodeTaskResponseElem(
      taskId,
      PipelineTaskType.SPARK,
      Some(PipelineTaskMeta(sinkId = Some(sinkId), runId = Some(runId))),
      restart = false,
      None)
    Await.result(stubRegistry.startPipeline(x, Set[Int](), Set[Int]()), Duration.Inf)
    // there was an attempt
    verify(mockMetrics).gaugeClusterCreation(ArgumentMatchers.eq("AWS"), anyLong())
    verify(mockMetrics).incrementFailedJobRuns()
    // assert we didn't set the file as processed
    verify(mockListing, never()).setFileStatus(anyInt(), anyLong(), ArgumentMatchers.eq(FileStatuses.Done), any[Option[Long]](), any())
  }

  test("managed cluster, no files from listing, make sure nothing is run on cloud provider side") {
    val sourceId = 4
    val sinkId = 6346
    val runId = 32432L
    val taskId = s"spark-$sinkId"

    val mockS3: AmazonS3 = mock(classOf[AmazonS3])
    when(mockS3.doesObjectExist(ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(true)
    val stubMd = new ObjectMetadata()
    stubMd.setRequesterCharged(true)
    val userMd = new util.HashMap[String, String]()
    userMd.put("a", "b")
    stubMd.setUserMetadata(userMd)
    when(mockS3.getObjectMetadata(ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(stubMd)

    val stubCfgMap: util.HashMap[TaskId, Object] = TestStuff.getExampleSourceConfig
    val partitionToHandle = "s3://bucketA/some-path/"
    val mockListing = getMockListing(sourceId, List())
    val mockProps = getMockProps
    val mockEmr = getMockEmr(None, None,
      List(StepState.RUNNING, StepState.COMPLETED),
      sinkId, runId, partitionToHandle
    )
    val mockMetrics: MetricsHelper = mock(classOf[MetricsHelper])
    val clusterTracker: ClusterTracker = ClusterTracker(
      1, // check clusters every milli
      5 * 1000, // maximum idle time is 5 seconds
      "local",
      getMockProps,
      mockMetrics
    )
    val secrets = mock(classOf[AWSSecretsManager])
    val stubResult = new GetSecretValueResult().withSecretString("stub")
    when(secrets.getSecretValue(ArgumentMatchers.any[GetSecretValueRequest]())).thenReturn(stubResult)
    val stubCloud = new AWSCloudProvider(mockMetrics, logTag, mock(classOf[BaseAuthConfig]),
      stubCfgMap.asScala.toMap,
      stubCfgMap.asScala.toMap,
      Option(mockEmr), Option(secrets), Option(mockS3), 1, 256, "stub")
    // remove the external cluster id
    val cfgWithNoExtCluster = stubCfgMap
    cfgWithNoExtCluster.remove("cloud.cluster.id")

    val mockApi = getMockApi(sinkId, sourceId, cfgWithNoExtCluster)
    when(mockEmr.runJobFlow(any[RunJobFlowRequest]())).thenThrow(new InternalServerErrorException("boom"))
    val configuredSender = JobSender(stubCloud, s"$taskId", mockMetrics)

    val stubRegistry: BasicPipelineRegistry = new BasicPipelineRegistry() {
      implicit val executionContext: ExecutionContext = ExecutionContext.fromExecutorService(Executors.newSingleThreadExecutor())

      override def createPipeline(sinkId: Int, runId: Long, taskId: TaskId): Pipeline = {
        new CloudJobPipeline(taskId, runId, sourceId, sinkId, mock(classOf[BaseAuthConfig]), mockApi, mockListing, mock(classOf[NexlaMessageProducer]), stubCloud, configuredSender, clusterTracker, mockMetrics, mockProps)
      }
    }

    val x = NodeTaskResponseElem(
      taskId,
      PipelineTaskType.SPARK,
      Some(PipelineTaskMeta(sinkId = Some(sinkId), runId = Some(runId))),
      restart = false,
      None)
    Await.result(stubRegistry.startPipeline(x, Set[Int](), Set[Int]()), Duration.Inf)

    // there was empty job run - and nothing was assigned to the cluster
    verify(mockMetrics, never()).incrementFailedJobRuns()
    verify(mockMetrics, never()).incrementSuccessfulJobRuns()
    verify(mockMetrics).incrementEmptyJobRuns()
    verify(mockMetrics, never()).incrementTaskAssignments()

    // assert we didn't set the file as processed
    verify(mockListing, never()).setFileStatus(anyInt(), anyLong(), ArgumentMatchers.eq(FileStatuses.Done), any[Option[Long]](), any())
  }

  test("managed cluster, spark agent jar failed, job restarted, cluster reused") {
    val sourceId = 343
    val sinkId = 65241
    val runId = 23411L
    val taskId = s"spark-$sinkId"

    val mockS3: AmazonS3 = mock(classOf[AmazonS3])
    when(mockS3.doesObjectExist(ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(true)
    val stubMd = new ObjectMetadata()
    stubMd.setRequesterCharged(true)
    val userMd = new util.HashMap[String, String]()
    userMd.put("a", "b")
    stubMd.setUserMetadata(userMd)
    when(mockS3.getObjectMetadata(ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(stubMd)

    val stubCfgMap: util.HashMap[TaskId, Object] = TestStuff.getExampleSourceConfig
    val partitionToHandle = "s3://bucketA/totally-other-path/"
    val clusterName = "a-new-managed-cluster"
    val mockListing = getMockListing(sourceId, List("totally-other-path"))
    val mockProps = getMockProps

    val mockMetrics: MetricsHelper = mock(classOf[MetricsHelper])
    val clusterTracker: ClusterTracker = ClusterTracker(
      1, // check clusters every milli
      5 * 1000, // maximum idle time is 5 seconds
      "local",
      getMockProps,
      mockMetrics
    )
    when(mockProps.idleClusterTtlMilli).thenReturn(Option(600000))

    val mockEmr = getMockEmr(Option(clusterName), Option("blah-job-id"),
      List(StepState.RUNNING, StepState.FAILED),
      sinkId, runId, partitionToHandle
    )
    val secrets = mock(classOf[AWSSecretsManager])
    val stubResult = new GetSecretValueResult().withSecretString("stub")
    when(secrets.getSecretValue(ArgumentMatchers.any[GetSecretValueRequest]())).thenReturn(stubResult)
    val stubCloud = new AWSCloudProvider(mockMetrics, logTag, mock(classOf[BaseAuthConfig]),
      stubCfgMap.asScala.toMap,
      stubCfgMap.asScala.toMap,
      Option(mockEmr), Option(secrets), Option(mockS3), 1, 256, "stub")
    // remove the external cluster id
    val cfgWithNoExtCluster = stubCfgMap
    cfgWithNoExtCluster.remove("cloud.cluster.id")

    val mockApi = getMockApi(sinkId, sourceId, cfgWithNoExtCluster)
    when(mockEmr.runJobFlow(any[RunJobFlowRequest]())).thenReturn(TestStuff.stubRawRunJobFlowResponse(clusterName))
    val configuredSender = JobSender(stubCloud, s"$taskId", mockMetrics)

    val stubRegistry: BasicPipelineRegistry = new BasicPipelineRegistry() {
      implicit val executionContext: ExecutionContext = ExecutionContext.fromExecutorService(Executors.newSingleThreadExecutor())

      override def createPipeline(sinkId: Int, runId: Long, taskId: TaskId): Pipeline = {
        new CloudJobPipeline(taskId, runId, sourceId, sinkId, mock(classOf[BaseAuthConfig]), mockApi, mockListing, mock(classOf[NexlaMessageProducer]), stubCloud, configuredSender, clusterTracker, mockMetrics, mockProps)
      }
    }

    val x = NodeTaskResponseElem(
      taskId,
      PipelineTaskType.SPARK,
      Some(PipelineTaskMeta(sinkId = Some(sinkId), runId = Some(runId))),
      restart = false,
      Option(true))

    Await.result(stubRegistry.startPipeline(x, Set[Int](), Set[Int]()), Duration.Inf)
    Await.result(stubRegistry.startPipeline(x.copy(restart = true), Set[Int](), Set[Int]()), Duration.Inf)
    // assert we didn't set the file as processed
    verify(mockListing, never()).setFileStatus(anyInt(), anyLong(), ArgumentMatchers.eq(FileStatuses.Done), any[Option[Long]](), any())
    // cluster must be reused, ttl should keep us from doing pointless restarts
    verify(mockEmr, atMostOnce()).runJobFlow(any[RunJobFlowRequest]())

    verify(mockMetrics).gaugeClusterCreation(ArgumentMatchers.eq("AWS"), anyLong())
    verify(mockMetrics, times(2)).incrementFailedJobRuns()
    // it started then finished then started again
    verify(mockMetrics, atLeast(1)).incrementActivePipelines()
    verify(mockMetrics, atLeast(1)).decrementActivePipelines()
  }

}
