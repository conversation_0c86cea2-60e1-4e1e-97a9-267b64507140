# Extra logging related to initialization of Log4j
# Set to debug or trace if log4j initialization is failing
status = info
# Name of the configuration
name = NexlaConnectorLogConfig

# Console appender configuration
appender.console.type = Console
appender.console.name = consoleLogger
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = [%date{yyyy-MM-dd HH:mm:ss.SSS}] [${sys:git_hash}] %highlight{%-5level} [%20.20thread] %style{%-40logger}{cyan} - %msg%n%throwable

# Root logger
rootLogger.level = debug
rootLogger.appenderRef.stdout.ref = consoleLogger

logger.nexla.name = com.nexla
logger.nexla.additivity = false
logger.nexla.level = debug
logger.nexla.appenderRef.stdout.ref = consoleLogger

logger.joestelmach.name = com.joestelmach.natty
logger.joestelmach.additivity = false
logger.joestelmach.level = warn
logger.joestelmach.appenderRef.stdout.ref = consoleLogger

logger.kafka.name = org.apache.kafka
logger.kafka.additivity = false
logger.kafka.level = info
logger.kafka.appenderRef.stdout.ref = consoleLogger

logger.kafkaconnect.name = org.apache.kafka.connect
logger.kafkaconnect.additivity = false
logger.kafkaconnect.level = info
logger.kafkaconnect.appenderRef.stdout.ref = consoleLogger

logger.mchange.name = com.mchange.v2
logger.mchange.additivity = false
logger.mchange.level = warn
logger.mchange.appenderRef.rolling.ref = fileLogger
logger.mchange.appenderRef.console.ref = consoleLogger
