package com.nexla.cloud.job.connector.cloud.azure

import com.nexla.sc.util.StrictNexlaLogging

object AzureTools extends StrictNexlaLogging {

  def toVmType(workerNodeVCPU: Int): String = {
    workerNodeVCPU match {
      case 2 => "B2ps v2"
      case 4 => "B4ps v2"
      case 8 => "B8ps v2"
      case 16 => "B16ps v2"
      case _ =>
        logger.warn(s"Unknown Azure VM type for $workerNodeVCPU requested VCPU, assuming smallest")
        "B2ps v2"
    }
  }

}
