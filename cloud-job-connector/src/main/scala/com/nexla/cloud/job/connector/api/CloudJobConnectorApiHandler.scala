package com.nexla.cloud.job.connector.api

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import akka.http.scaladsl.model.StatusCodes.OK
import akka.http.scaladsl.server.{Directives, Route}
import com.nexla.cloud.job.connector.MetricsHelper
import com.nexla.cloud.job.connector.infra.CloudJobMarshalling._
import com.nexla.cloud.job.connector.pipeline.CloudJobPipelineRegistry
import com.nexla.sc.util.{StrictNexlaLogging, _}
import fr.davit.akka.http.metrics.core.scaladsl.server.HttpMetricsDirectives._
import spray.json.DefaultJsonProtocol

import scala.concurrent.{ExecutionContext, Future}

class CloudJobConnectorApiHandler(jobsRepo: CloudJobPipelineRegistry, metricsHelper: MetricsHelper)
  (implicit ec: ExecutionContext)
  extends Directives
    with SprayJsonSupport
    with DefaultJsonProtocol
    with StrictNexlaLogging
    with WithLogging {

  val listRunningJobs: Route =
    (get
      & pathPrefixLabeled("jobs" / "running", "jobs/running") & end) {
        onSuccess {
          Future {
            val response = jobsRepo.getRunningPipelines
            metricsHelper.incrementServedListJobsRequests()
            response
          }
        } { jobs =>
          complete(OK -> jobs)
        }
    }

}
