package com.nexla.cloud.job.connector

import com.nexla.telemetry.Telemetry
import com.typesafe.scalalogging.LazyLogging

import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.{Executors, ScheduledExecutorService, TimeUnit}

class MetricsHelper(telemetry: Telemetry, initialDelayMillis: Int, periodMillis: Int) extends LazyLogging {
  private val gaugeDropper: ScheduledExecutorService = Executors.newScheduledThreadPool(1)

  // these 3 include both gauges (values during last X seconds) + counters (total values for entire uptime of a container)
  val CLUSTER_SPAWNS_NUMBER = "nexla-cluster-spawns-number"
  val CLUSTER_SPAWNS_NUMBER_COUNTER = "nexla-cluster-spawns-total"
  val CLUSTER_TERMINATES_NUMBER = "nexla-cluster-terminates-number"
  val CLUSTER_TERMINATES_NUMBER_COUNTER = "nexla-cluster-terminates-total"
  val TASK_ASSIGNMENTS_NUMBER = "nexla-task-sends-number"
  val TASK_ASSIGNMENTS_NUMBER_COUNTER = "nexla-task-sends-total"

  private val clusterSpawns: AtomicInteger = new AtomicInteger(0)
  private val clusterTerminates: AtomicInteger = new AtomicInteger(0)
  private val taskAssignments: AtomicInteger = new AtomicInteger(0)

  private val currentlyTrackedClusters = new AtomicInteger(0)
  private val currentlyTrackedAPIClients = new AtomicInteger(0)
  private val activePipelines = new AtomicInteger(0)

  // every X seconds, we report and then reset gauges for these events to have the correct representation on the charts
  gaugeDropper.scheduleAtFixedRate(reportAndResetGauges(), initialDelayMillis, periodMillis, TimeUnit.MILLISECONDS)

  val CURRENT_TRACKED_CLUSTERS_NUMBER = "nexla-current-tracked-clusters-number"
  val CURRENT_CLOUD_PROVIDER_API_CONNECTIONS_GAUGE = "nexla-current-cloud-provider-api-connections-number"
  val CURRENT_LISTING_DB_CONNECTIONS_GAUGE = "nexla-current-db-connections-number"
  val LATENCY_CLOUD_PROVIDER_API_CONNECTION = "nexla-latency-cloud-provider-api"                           // + per cloud provider
  val LATENCY_LISTING_API = "nexla-latency-listing-app"
  val LATENCY_ADMIN_API = "nexla-latency-admin-api"
  val REQUESTS_CURRENTLY_IN_FLIGHT_CLOUD_PROVIDERS = "nexla-requests-in-flight-cloud-provider"
  val CLOUD_PROVIDER_4xx_ERRORS = "nexla-cloud-provider-api-4xx-errors-number"
  val CLOUD_PROVIDER_5xx_ERRORS = "nexla-cloud-provider-api-5xx-errors-number"
  val LISTING_APP_4xx_ERRORS = "nexla-listing-app-4xx-errors-number"
  val LISTING_APP_5xx_ERRORS = "nexla-listing-app-5xx-errors-number"
  val ADMIN_API_4xx_ERRORS = "nexla-admin-api-4xx-errors-number"
  val ADMIN_API_5xx_ERRORS = "nexla-admin-api-5xx-errors-number"
  val LIST_RUNNING_JOBS_REQUESTS_SERVED = "nexla-list-running-jobs-requests"
  val DURATION_OF_CLUSTER_CREATION = "nexla-duration-of-cluster-creation"
  val DURATION_OF_CLUSTER_TERMINATION = "nexla-duration-of-cluster-termination"
  val DURATION_OF_TASK_ASSIGNMENT = "nexla-duration-of-cluster-task-assignment"
  val REMOTE_JOB_DURATION = "nexla-remote-job-duration"
  val SUCCESSFUL_JOB_RUNS = "nexla-successful-job-runs"
  val FAILED_JOB_RUNS = "nexla-failed-job-runs"
  val EMPTY_JOB_RUNS = "nexla-empty-job-runs"
  val ACTIVE_CLOUD_JOB_PIPELINES = "nexla-active-cloud-job-pipelines"

  def reportAndResetGauges(): Runnable = new Runnable {
    override def run(): Unit = {
      logger.trace("Running report-and-reset gauges wherever necessary")
      telemetry.recordGauge(CLUSTER_SPAWNS_NUMBER, clusterSpawns.get())
      clusterSpawns.set(0)
      telemetry.recordGauge(CLUSTER_TERMINATES_NUMBER, clusterTerminates.get())
      clusterTerminates.set(0)
      telemetry.recordGauge(TASK_ASSIGNMENTS_NUMBER, taskAssignments.get())
      taskAssignments.set(0)

      telemetry.recordGauge(CURRENT_TRACKED_CLUSTERS_NUMBER, currentlyTrackedClusters.get())
      telemetry.recordGauge(CURRENT_CLOUD_PROVIDER_API_CONNECTIONS_GAUGE, currentlyTrackedAPIClients.get())
      telemetry.recordGauge(ACTIVE_CLOUD_JOB_PIPELINES, activePipelines.get())
    }
  }

  def incrementClusterSpawns(): Unit = {
    clusterSpawns.incrementAndGet()
    telemetry.recordCounter(CLUSTER_SPAWNS_NUMBER_COUNTER)
  }

  def incrementClusterTerminates(): Unit = {
    clusterTerminates.incrementAndGet()
    telemetry.recordCounter(CLUSTER_TERMINATES_NUMBER_COUNTER)
  }

  def incrementTaskAssignments(): Unit = {
    taskAssignments.incrementAndGet()
    telemetry.recordCounter(TASK_ASSIGNMENTS_NUMBER_COUNTER)
  }

  def incrementTrackedClusters(): Unit = {
    currentlyTrackedClusters.incrementAndGet()
  }

  def decrementTrackedClusters(): Unit = {
    currentlyTrackedClusters.decrementAndGet()
  }

  def incrementCloudAPIConnections(): Unit = {
    currentlyTrackedAPIClients.incrementAndGet()
  }

  def decrementCloudAPIConnections(): Unit = {
    currentlyTrackedAPIClients.decrementAndGet()
  }

  def incrementActivePipelines(): Unit = {
    activePipelines.incrementAndGet()
  }

  def decrementActivePipelines(): Unit = {
    activePipelines.decrementAndGet()
  }

  def incrementServedListJobsRequests(): Unit = {
    telemetry.recordCounter(LIST_RUNNING_JOBS_REQUESTS_SERVED)
  }

  def gaugeClusterCreation(cloudName: String, durationMillis: Long): Unit = {
    telemetry.recordHistogram(DURATION_OF_CLUSTER_CREATION + "-" + cloudName, durationMillis)
  }

  def gaugeClusterTermination(cloudName: String, durationMillis: Long): Unit = {
    telemetry.recordHistogram(DURATION_OF_CLUSTER_TERMINATION + "-" + cloudName, durationMillis)
  }

  def gaugeTaskAssignment(cloudName: String, durationMillis: Long): Unit = {
    telemetry.recordHistogram(DURATION_OF_TASK_ASSIGNMENT + "-" + cloudName, durationMillis)
  }

  def gaugeTaskRunTime(durationMillis: Long): Unit = {
    telemetry.recordHistogram(REMOTE_JOB_DURATION, durationMillis)
  }

  def incrementSuccessfulJobRuns(): Unit = {
    telemetry.recordCounter(SUCCESSFUL_JOB_RUNS)
  }

  def incrementFailedJobRuns(): Unit = {
    telemetry.recordCounter(FAILED_JOB_RUNS)
  }

  def incrementEmptyJobRuns(): Unit = {
    telemetry.recordCounter(EMPTY_JOB_RUNS)
  }
}

object MetricsHelper {
  private val DEFAULT_PERIOD_MILLIS: Int = 15 * 1000
  def apply(t: Telemetry, initialDelaySeconds: Int = DEFAULT_PERIOD_MILLIS, periodSeconds: Int = DEFAULT_PERIOD_MILLIS): MetricsHelper = {
    new MetricsHelper(t, initialDelaySeconds, periodSeconds)
  }
}
