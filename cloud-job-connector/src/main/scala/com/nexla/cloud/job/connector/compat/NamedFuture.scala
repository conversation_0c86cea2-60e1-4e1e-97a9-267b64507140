package com.nexla.cloud.job.connector.compat

import com.nexla.sc.util.StrictNexlaLogging

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}

object NamedFuture extends StrictNexlaLogging {
  private def now(): Long = System.currentTimeMillis()

  def apply[T](name: String)(code: => T)(implicit ec: ExecutionContext): Future[T] = {
    val start = now()
    val logTag = name + "_" + UUID.randomUUID().toString.split("-").last
    val f = Future {
      logger.info(s"Future [$logTag] is now starting")
      code
    }
    f.onComplete(_ => logger.info(s"Future [$logTag] took ${now() - start} ms"))
    f
  }

}
