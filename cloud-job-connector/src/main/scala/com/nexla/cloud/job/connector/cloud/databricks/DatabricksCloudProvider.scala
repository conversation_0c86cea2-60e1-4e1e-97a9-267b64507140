package com.nexla.cloud.job.connector.cloud.databricks

import com.amazonaws.regions.Regions
import com.amazonaws.services.s3.AmazonS3URI
import com.amazonaws.services.s3.model.PutObjectRequest
import com.databricks.sdk.WorkspaceClient
import com.databricks.sdk.core.{Config<PERSON><PERSON><PERSON>, DatabricksConfig}
import com.databricks.sdk.core.commons.CommonsHttpClient
import com.databricks.sdk.core.http.HttpClient
import com.databricks.sdk.service.compute._
import com.databricks.sdk.service.files.AddBlock
import com.databricks.sdk.service.jobs._
import com.databricks.sdk.service.workspace.{PutSecret, SecretScope}
import com.google.common.hash.Hashing
import com.mchange.util.Base64Encoder
import com.nexla.admin.client.DataSource
import com.nexla.cloud.job.connector.cloud._
import com.nexla.cloud.job.connector.cloud.aws.AWSTools
import com.nexla.cloud.job.connector.cloud.aws.AWSTools.{buildFromNexlaCreds, getS3Client}
import com.nexla.cloud.job.connector.cloud.azure.AzureTools
import com.nexla.cloud.job.connector.cloud.gcp.GCPTools
import com.nexla.cloud.job.connector.{MetricsHelper, Utils}
import com.nexla.common.{ConnectionType, NexlaDataCredentials}
import com.nexla.connector.config.databricks.DatabricksAuthConfig
import com.nexla.connector.config.file.AWSAuthConfig
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.sc.util.StrictNexlaLogging
import okhttp3.{MediaType, OkHttpClient, Request, RequestBody}
import org.apache.commons.lang3.StringUtils
import org.apache.http.impl.client.HttpClientBuilder

import java.io.{BufferedInputStream, File}
import java.nio.file.{Files, Paths}
import java.util.Base64
import java.util.concurrent.atomic.AtomicReference
import scala.collection.JavaConverters._
import scala.compat.java8.OptionConverters.RichOptionalGeneric
import scala.concurrent.duration.Duration
import scala.util.{Failure, Success, Try}

class DatabricksCloudProvider(metricsHelper: MetricsHelper, logTag: String,
                              cfg: BaseAuthConfig,
                              src: DataSource,
                              hwProvider: String,
                              storageProvider: ConnectionType,
                              nexlaAdminApiKey: String,
                              databricksClient: WorkspaceClient = new WorkspaceClient())
  extends CloudProvider(cfg) with StrictNexlaLogging {

  val srcCfg: Map[String, AnyRef] = src.getSourceConfig.asScala.toMap

  override val cloudName: String = "Databricks"

  private val MaxDbfsChunkSize: Int = 1024 * 1024

  private val LOCAL_JAR_FILE_PATH: String = if (Utils.devMode() || Utils.isUnitTest) {
    System.getProperty("user.dir") + "/target/nexla-spark-agent.jar"
  } else {
    "/nexla-spark-agent.jar"
  }
  private val cloudCfg: DatabricksCloudSpecificCfg = DatabricksCloudSpecificCfg(cfg.originals().asScala.toMap)

  // if only databricks, then we cannot use s3 and must use dbfs instead
  val jarLocation: String = if (src.getConnectionType.equals(ConnectionType.DATABRICKS)) {
    logger.info("databricks source, using dbfs for upload")

    if (cloudCfg.cloudJarLocation.contains("/")) {
      // so a path
      val justFileName = cloudCfg.cloudJarLocation.substring(
        cloudCfg.cloudJarLocation.lastIndexOf("/") + 1, cloudCfg.cloudJarLocation.length
      )

      val result = "/FileStore/jars/" + justFileName
      result
    } else {
      // not even a pth. maybe just jar file name
      logger.warn("cloud jar location is not a path. using it as a jar name")
      "/FileStore/jars/" + cloudCfg.cloudJarLocation
    }
  } else {
    val loc = cloudCfg.cloudJarLocation
    logger.info(s"not a databricks source, using $loc as a runtime jar location")
    loc
  }

  val jarDestination: AtomicReference[String] = new AtomicReference[String]()

  def databricksToNexlaNode(node: SparkNode): ClusterNode = {
    if (node == null) {
      ClusterNode("N/A", "N/A", "N/A")
    } else {
      ClusterNode(
        Option(node.getNodeId).getOrElse("N/A"),
        Option(node.getInstanceId).getOrElse("N/A"),
        Option(node.getHostPrivateIp).getOrElse("N/A")
      )
    }
  }

  def databricksToNexlaJobDef(baseRun: BaseRun): JobDefinition = {
    // we create just a single task job for spark-runner
    val task = baseRun.getTasks.asScala.toList.headOption.map(_.getSparkJarTask)
    val taskArguments = task.map(_.getParameters.asScala.toList)
    // ["9821","s3://qa.nexla.com/akotliar/sparksource_partitioned/partition=2/","test","123"]
    // sinkId, partitions, admin api env, run id
    val sinkId = taskArguments.map(args => args(0).toInt).getOrElse(0)
    val lastRunsForThisSink = databricksClient.jobs().listRuns(new ListRunsRequest().setJobId(baseRun.getJobId).setExpandTasks(true))
      .asScala.toList.filter(a => a.getTasks.asScala.head.getSparkJarTask.getParameters.contains(String.valueOf(sinkId)))
    val currentLastRun = lastRunsForThisSink.maxBy(_.getStartTime)

    val lastKnownStatus: String = if (currentLastRun.getState.getLifeCycleState.equals(RunLifeCycleState.TERMINATED) &&
      currentLastRun.getState.getResultState.equals(RunResultState.SUCCESS)) {
      "Succeeded"
    } else if (currentLastRun.getState.getLifeCycleState.equals(RunLifeCycleState.RUNNING)) {
      "In progress"
    } else {
      "Failed"
    }
    val lastRunParams = currentLastRun.getOverridingParameters.getJarParams.asScala.toList

    val runId = lastRunParams(3).toLong
    val partitions = lastRunParams(1).split(",").toList

    JobDefinition(
      String.valueOf(baseRun.getJobId),
      sinkId,
      runId,
      partitions,
      lastKnownStatus
    )
  }

  def toProviderVMType(hwProvider: String, nodeVCPU: Int): String = {
    hwProvider match {
      case "aws" => AWSTools.toAmazonVmType(nodeVCPU)
      case "gcp" => GCPTools.toVmType(nodeVCPU)
      case "azure" => AzureTools.toVmType(nodeVCPU)
      case _ =>
        throw new IllegalArgumentException(s"Don't know the mappings for Databricks cloud provider [$hwProvider]")
    }
  }

  override def createCluster(): ClusterResponse = {
    val clusterConfig = Utils.getClusterConfig(logTag, cloudCfg, srcCfg)

    val generatedName = s"Nexla Spark Job"
    val finalClusterName = clusterConfig.name.getOrElse(generatedName)

    val createClusterTemplate = new CreateCluster()
      .setClusterName(finalClusterName)
      .setSparkVersion("12.2.x-scala2.12") // spark 3.3.2 LTS
      .setNodeTypeId(toProviderVMType(hwProvider, clusterConfig.workerNodeVCPU))
      .setDriverNodeTypeId(toProviderVMType(hwProvider, clusterConfig.mainNodeVCPU))
      .setEnableElasticDisk(true)
      .setAutoterminationMinutes(15L)
      .setNumWorkers(clusterConfig.workerNodes.toLong)

    val createCluster = if (storageProvider.equals(ConnectionType.S3)) {
      createClusterTemplate.setSparkEnvVars(java.util.Map.of(
        "AWS_SECRET_ACCESS_KEY", s"{{secrets/$finalClusterName/aws_secret_access_key}}",
        "AWS_ACCESS_KEY_ID", s"{{secrets/$finalClusterName/aws_access_key_id}}",
        "JNAME", "zulu11-ca-amd64"
      ))
    } else {
      createClusterTemplate.setSparkEnvVars(java.util.Map.of(
        "JNAME", "zulu11-ca-amd64"
      ))
    }

    if (Utils.unityCatalogEnabled(srcCfg) || Utils.unityCatalogInCredentials(cfg.originals().asScala.toMap)) {
      // necessary for enabling the Unity Catalog. otherwise don't bother with it
      logger.info("Unity Catalog enabled for the source/flow, enabling it on the cluster as well.")
      createCluster.setDataSecurityMode(DataSecurityMode.SINGLE_USER)
    } else {
      logger.info("Unity Catalog is not enabled for the source/flow, skipping.")
    }

    val clusterResponse = databricksClient.clusters().create(createCluster).getResponse
    metricsHelper.incrementClusterSpawns()

    val start = System.currentTimeMillis()
    // will take a while
    val detailedClusterResponse = try {
      databricksClient.clusters().waitGetClusterRunning(clusterResponse.getClusterId)
    } catch {
      case e: Exception =>
        logger.error("Error during cluster creation", e)
        throw e
    } finally {
      val end = System.currentTimeMillis()
      metricsHelper.gaugeClusterCreation(cloudName, end - start)
    }

    val clusterJobs = databricksClient.jobs().listRuns(new ListRunsRequest().setExpandTasks(true)).asScala.map(
      // for all of the jobs, we need to find on which clusters are/were they running
      bsj => (bsj, Option(bsj.getJobClusters.asScala).getOrElse(List.empty[JobCluster]))
      // and then leave only the jobs which have current cluster id in their list
    ).filter(jobAndClusters => {
      jobAndClusters._2.toList.exists(b => b.getJobClusterKey.equals(clusterResponse.getClusterId))
    }).map(j => j._1).toList

    ClusterResponse(
      detailedClusterResponse.getClusterId,
      List(databricksToNexlaNode(detailedClusterResponse.getDriver)),
      List.empty[ClusterNode],
      detailedClusterResponse.getExecutors.asScala.map(databricksToNexlaNode).toList,
      clusterJobs.map(databricksToNexlaJobDef)
    )
  }

  override def stopCluster(clusterId: String): Unit = {
    val start = System.currentTimeMillis()
    try {
      databricksClient.clusters().waitGetClusterTerminated(clusterId)
    } catch {
      case e: Exception =>
        logger.error("Error during cluster termination, cluster id {}", clusterId)
        throw e
    } finally {
      val end = System.currentTimeMillis()
      metricsHelper.incrementClusterTerminates()
      metricsHelper.gaugeClusterTermination(cloudName, end - start)
    }
  }

  override def trackUntilTerminalState(jobId: String, clusterId: String): JobDefinition = {
    val req = new ListRunsRequest
    req.setJobId(jobId.toLong)
    req.setExpandTasks(true)
    req.setActiveOnly(true)

    logger.debug(s"$logTag Starting to track until terminal state: [job-id: {} cluster-id: {}]", jobId, clusterId)
    val lastJobRuns = databricksClient.jobs().listRuns(req).asScala.toList

    if (lastJobRuns.isEmpty) {
      // nothing yet, cannot track
      throw new IllegalArgumentException(s"$logTag job runs is empty for the job id $jobId")
    }

    val lastJobRunId = lastJobRuns.maxBy(_.getStartTime).getRunId
    val start = System.currentTimeMillis()
    try {
      // can take a while
      databricksClient.jobs().waitGetRunJobTerminatedOrSkipped(lastJobRunId)
    } catch {
      case e: IllegalStateException =>
        logger.error("Job got illegal state exception, assuming it's failed: ", e)
    } finally {
      val end = System.currentTimeMillis()
      metricsHelper.gaugeTaskRunTime(end - start)
    }
    val currJob = databricksClient.jobs().getRun(lastJobRunId)
    val itsParams = currJob.getTasks.asScala.head.getSparkJarTask.getParameters.asScala.toList
    val sinkId = itsParams(0).toInt
    val partitions = List(itsParams(1))
    val apiEnv = itsParams(2)
    val runId = itsParams(3).toLong
    logger.debug(s"$logTag Finished tracking until terminal state: [job-id: {} cluster-id: {}]", jobId, clusterId)
    val status = if (currJob.getState.getLifeCycleState.equals(RunLifeCycleState.TERMINATED) &&
      currJob.getState.getResultState.equals(RunResultState.SUCCESS)) "Succeeded" else "Failed"

    JobDefinition(
      jobId,
      sinkId,
      runId,
      partitions,
      status
    )
  }

  override def checkExistingCluster(clusterId: String): ClusterResponse = {
    val detailedClusterResponse = if (clusterId.equals(Utils.DEFAULT_NEXLA_CLUSTER_NAME) || clusterId.equals(Utils.DEFAULT_NEXLA_CLUSTER_NAME_WITH_CATALOG)) {
      logger.info("Default cluster name was chosen for the current pipeline, trying to find default Nexla cluster")
      val existingClusters = databricksClient.clusters().list(new ListClustersRequest())
      val existingClustersWithDefaultName = if (existingClusters == null) {
        logger.warn("Received null from Databricks client, assuming no clusters exist")
        List.empty[ClusterDetails]
      } else {
        existingClusters.asScala.toList.filter(_.getClusterName.equals(clusterId))
      }

      if (existingClustersWithDefaultName.isEmpty) {
        logger.warn(s"No cluster found with name ${Utils.DEFAULT_NEXLA_CLUSTER_NAME} or ${Utils.DEFAULT_NEXLA_CLUSTER_NAME_WITH_CATALOG}")
        return ClusterResponse(null, List.empty, List.empty, List.empty, List.empty)
      } else {
        existingClustersWithDefaultName.maxBy(_.getStartTime)
      }
    } else {
      databricksClient.clusters().get(clusterId)
    }

    val clusterJobs = databricksClient.jobs().listRuns(new ListRunsRequest().setExpandTasks(true)).asScala.map(
      // for all of the jobs, we need to find on which clusters are/were they running
      bsj => (bsj, Option(bsj.getJobClusters.asScala).getOrElse(List.empty[JobCluster]))
      // and then leave only the jobs which have current cluster id in their list
    ).filter(jobAndClusters => {
      jobAndClusters._2.toList.exists(b => b.getJobClusterKey.equals(detailedClusterResponse.getClusterId))
    }).map(j => j._1).toList

    val execs = Option(detailedClusterResponse.getExecutors).map {
      execs => execs.asScala.map(databricksToNexlaNode).toList
    }.getOrElse(List.empty[ClusterNode])

    ClusterResponse(
      detailedClusterResponse.getClusterId,
      List(databricksToNexlaNode(detailedClusterResponse.getDriver)),
      List.empty[ClusterNode],
      execs,
      clusterJobs.map(databricksToNexlaJobDef)
    )
  }

  /**
   * for now works just for s3, make it work for others
   * @param dstCloudJarLocation
   */
  private def copyFileIfNotExists(dstCloudJarLocation: String, baseAuthConfig: BaseAuthConfig, connectionType: ConnectionType): String = {
    try {
      connectionType match {
        case e if e == ConnectionType.S3 =>
          val awsCfg = new AWSAuthConfig(cfg.originals(), -1)
          // first, check destination using client's AWS credentials
          val uri: AmazonS3URI = new AmazonS3URI(dstCloudJarLocation)
          val creds = buildFromNexlaCreds(awsCfg)
          val destinationS3Client = getS3Client(creds, Regions.fromName(awsCfg.region))
          val jarExists = destinationS3Client.doesObjectExist(uri.getBucket, uri.getKey)
          if (jarExists) {
            logger.warn(s"Target jar at specified path [$dstCloudJarLocation] already exists, checking its md5")
            val remoteMd5 = destinationS3Client.getObjectMetadata(uri.getBucket, uri.getKey).getRawMetadata.get("ETag").toString
            val currentFileMd5 = getMd5(new File(LOCAL_JAR_FILE_PATH))
            logger.info(s"Remote file md5: $remoteMd5, local file md5: $currentFileMd5")
            if (currentFileMd5.equalsIgnoreCase(remoteMd5)) {
              logger.info("md5 digest matches, using the existing file")
            } else {
              logger.warn("replacing it with a new file")
              val agentJarPath = LOCAL_JAR_FILE_PATH
              val putObjectRequest = new PutObjectRequest(uri.getBucket, uri.getKey, new File(agentJarPath))
              destinationS3Client.putObject(putObjectRequest)
              logger.info(s"File successfully uploaded to specified folder [$dstCloudJarLocation]")
            }
          } else {
            logger.info(s"Target jar does not exist at specified path [$dstCloudJarLocation], uploading it")
            // our docker image has this file built-in
            val agentJarPath = LOCAL_JAR_FILE_PATH
            val putObjectRequest = new PutObjectRequest(uri.getBucket, uri.getKey, new File(agentJarPath))
            destinationS3Client.putObject(putObjectRequest)
            logger.info(s"File successfully uploaded to specified folder [$dstCloudJarLocation]")
          }
          dstCloudJarLocation

        case e if e == ConnectionType.DATABRICKS =>
          logger.info(s"databricks source, just relying on the provided path $jarLocation")
          try {
            s"dbfs:${databricksClient.dbfs().getStatus(jarLocation).getPath}"
          } catch {
            case e: Exception =>
              logger.error(s"cannot check file or cannot find a file $jarLocation", e)
              logger.info("trying to upload file to dbfs")

              uploadFileRaw(LOCAL_JAR_FILE_PATH, jarLocation) match {
                case Success(_) => s"dbfs:$jarLocation"
                case Failure(exc) => throw exc
              }
          }
        case _ =>
          logger.warn(s"uploading and/or replacing the Nexla Spark Agent JAR is not supported for connection type ${connectionType}.")
          logger.warn(s"returning just the known jar location [$jarLocation]")
          jarLocation
      }
    } catch {
      case e: Exception =>
        logger.error("Error during file copy or upload", e)
        throw e
    }
  }

  private def uploadFileRaw(localPath: String, dbfsPath: String): Try[Unit] = {
    logger.info(s"trying to upload file to dbfs - FROM $localPath TO $dbfsPath")

    var targetPath: String = dbfsPath.replace("dbfs:", "")

    if (!targetPath.startsWith("/")) {
      targetPath = "/" + targetPath
    }

    val bis: BufferedInputStream = new BufferedInputStream(Files.newInputStream(Paths.get(localPath)), 8192*1024) // 8MiB buffer
    Try {
      val handle: Long = databricksClient.dbfs().create(targetPath).getHandle
      val buffer = new Array[Byte](MaxDbfsChunkSize)
      var bytesRead = 0
      val base64Encoder = Base64.getEncoder
      while ( {
        bytesRead = bis.read(buffer); bytesRead
      } != -1) {
        val chunk = if (bytesRead == buffer.length) {
          buffer
        } else {
          buffer.take(bytesRead)
        }

        databricksClient.dbfs().addBlock(new AddBlock().setHandle(handle).setData(base64Encoder.encodeToString(chunk)))
      }
      databricksClient.dbfs().close(handle)
    } recoverWith {
      case e =>
        bis.close()
        Failure(e)
    } map { result =>
      bis.close()
      result
    }
  }

  private def getMd5(file: File): String = {
    val byteSource = com.google.common.io.Files.asByteSource(file)
    val hc = byteSource.hash(Hashing.md5)
    hc.toString.toLowerCase
  }

  override def runSparkSubmitStep(clusterId: String, sinkId: Int, partitionToHandle: String,
                                  nexlaEnv: String, runId: Long): List[JobDefinition] = {
    logger.info(s"$logTag Adding the Nexla Admin API key to the secrets")
    val nexlaApiKey = new PutSecret
    nexlaApiKey.setKey("nexla.admin.api.key")
    val existingScope = databricksClient.secrets().listScopes().asScala.find(_.getName.equals(clusterId))
    if (existingScope.isEmpty) {
      logger.info(s"$logTag secrets scope for the cluster $clusterId not found, creating it")
      databricksClient.secrets().createScope(clusterId)
    }
    nexlaApiKey.setScope(clusterId)
    nexlaApiKey.setStringValue(nexlaAdminApiKey)
    try {
      databricksClient.secrets().putSecret(nexlaApiKey)
    } catch {
      case e: Exception =>
        logger.error("Exception on setting admin api key: ", e)
        throw e
    }
    logger.info(s"$logTag Added the Nexla Admin API key to the secrets")
    storageProvider match {
      case e if e == ConnectionType.S3 =>
        val awsCfg = new AWSAuthConfig(cfg.originals(), -1)
        val accessKey = new PutSecret
        accessKey.setKey("aws_access_key_id")
        accessKey.setStringValue(awsCfg.accessKeyId)
        accessKey.setScope(clusterId)
        databricksClient.secrets().putSecret(accessKey)
        val secretKey = new PutSecret
        secretKey.setKey("aws_secret_access_key")
        secretKey.setStringValue(awsCfg.secretKey)
        secretKey.setScope(clusterId)
        databricksClient.secrets().putSecret(secretKey)
      case _ =>
        logger.warn(s"Accessing the data with credentials not yet supported for $storageProvider, skipping setting storage access keys")
    }
    val destinationLocation = copyFileIfNotExists(jarLocation, cfg, src.getConnectionType)
    this.jarDestination.set(destinationLocation)
    val jobName = s"Nexla Spark Job sink-$sinkId"
    // first, find or create job
    logger.info(s"$logTag looking for job with name [$jobName]")
    val job = databricksClient.jobs().list(new ListJobsRequest().setName(jobName).setExpandTasks(true)).asScala.headOption
    val start = System.currentTimeMillis()
    val finalJobId = if (job.isDefined) {
      val actualJob = job.get
      // then use it, refreshing its parameters with the list we have and running on the cluster we have
      logger.info(s"$logTag job id found, verifying on which cluster it is going to run")
      val jobClusterId = actualJob.getSettings.getTasks.asScala.head.getExistingClusterId
      val jobJar = actualJob.getSettings.getTasks.asScala.head.getLibraries.asScala.head.getJar
      if (jobClusterId.equals(clusterId) && jobJar.equals(this.jarDestination.get())) {
        logger.info(s"$logTag job id is using the same cluster id and same jar as in config: [$clusterId], leaving it as is and sending RunNow.")
        databricksClient.jobs().runNow(new RunNow().setJobId(job.get.getJobId).setJarParams(
          List(sinkId.toString,
            partitionToHandle,
            nexlaEnv,
            runId.toString).asJavaCollection
        ))
        actualJob.getJobId
      } else {
        logger.info(s"$logTag job is found, but it uses other cluster: [$jobClusterId], or other jar [$jobJar] updating the Task definition first")
        val jarTask = new SparkJarTask()
        jarTask.setMainClassName("com.nexla.spark_agent.SparkAgentApp")
        jarTask.setParameters(List(sinkId.toString,
          partitionToHandle,
          nexlaEnv,
          runId.toString).asJavaCollection)
        val tasks = java.util.Arrays.asList(
          new Task()
            .setDescription("Nexla Spark Job")
            .setTaskKey(jobName.replaceAll(" ", "_"))
            .setExistingClusterId(clusterId)
            .setLibraries(List(new Library().setJar(this.jarDestination.get())).asJavaCollection)
            .setSparkJarTask(jarTask)
        )
        val updatedSettings = actualJob.getSettings
        updatedSettings.setTasks(tasks)
        val updateJob = new UpdateJob
        updateJob.setJobId(actualJob.getJobId)
        updateJob.setNewSettings(updatedSettings)
        databricksClient.jobs().update(updateJob)
        logger.info(s"$logTag Job definition is now updated and it will run on the requested cluster: [$clusterId]. Sending RunNow.")
        databricksClient.jobs().runNow(new RunNow().setJobId(job.get.getJobId).setJarParams(
          List(sinkId.toString,
            partitionToHandle,
            nexlaEnv,
            runId.toString).asJavaCollection
        ))
        actualJob.getJobId
      }
    } else {
      logger.info(s"$logTag job id not found, creating a new job")
      val createJob = new CreateJob()
      createJob.setName(jobName)

      val jarTask = new SparkJarTask()
      jarTask.setMainClassName("com.nexla.spark_agent.SparkAgentApp")
      jarTask.setParameters(List(sinkId.toString,
        partitionToHandle,
        nexlaEnv,
        runId.toString).asJavaCollection)

      val tasks = java.util.Arrays.asList(
        new Task()
          .setDescription("Nexla Spark Job")
          .setTaskKey(jobName.replaceAll(" ", "_"))
          .setExistingClusterId(clusterId)
          .setLibraries(List(new Library().setJar(this.jarDestination.get())).asJavaCollection)
          .setSparkJarTask(jarTask)
      )

      createJob.setTasks(tasks)

      val createdJobId = databricksClient.jobs().create(createJob)
      logger.info(s"$logTag Created job with id [${createdJobId.getJobId}]")
      logger.info(s"$logTag now sending a RunNow for it")
      databricksClient.jobs().runNow(new RunNow().setJobId(createdJobId.getJobId).setJarParams(
        List(sinkId.toString,
          partitionToHandle,
          nexlaEnv,
          runId.toString).asJavaCollection
      ))
      createdJobId.getJobId
    }
    val end = System.currentTimeMillis()
    metricsHelper.incrementTaskAssignments()
    metricsHelper.gaugeTaskAssignment(cloudName, end - start)
    // databricks doesn't have a notion of X jobs running on the same cluster
    val allJobRuns = databricksClient.jobs().listRuns(new ListRunsRequest().setJobId(finalJobId).setExpandTasks(true)).asScala.toList
    val j2 = allJobRuns.filter(_.getJobId.equals(finalJobId)).maxBy(_.getStartTime)
    logger.info(s"$logTag after filtering: $j2")
    List(databricksToNexlaJobDef(j2))
  }
}

object DatabricksCloudProvider {
  def apply(metricsHelper: MetricsHelper, logTag: String, cfg: DatabricksAuthConfig,
            src: DataSource, hwProvider: String, storageProvider: ConnectionType, apiKey: String): DatabricksCloudProvider = {

    val cloudCfg: DatabricksCloudSpecificCfg = DatabricksCloudSpecificCfg.apply(cfg.originals().asScala.toMap)
    val bricksCfg: DatabricksConfig = ConfigLoader.getDefault

    // some databricks configurations cannot be modified after construction, best to initialize everything first
    applyDatabricksAuthConfig(bricksCfg, cloudCfg.auth).setHost(cloudCfg.host)

    new DatabricksCloudProvider(metricsHelper, logTag, cfg, src, hwProvider, storageProvider, apiKey,
      new WorkspaceClient(bricksCfg))
  }

  private def applyDatabricksAuthConfig(bricksCfg: DatabricksConfig, auth: DatabricksAuth): DatabricksConfig = {
    auth match {
      case ta: DatabricksTokenAuth => bricksCfg.setToken(ta.token)
      case oidc: DatabricksOidcAuth =>
        bricksCfg.setClientId(oidc.clientId).setClientSecret(oidc.clientSecret)
    }
    bricksCfg
  }
}
