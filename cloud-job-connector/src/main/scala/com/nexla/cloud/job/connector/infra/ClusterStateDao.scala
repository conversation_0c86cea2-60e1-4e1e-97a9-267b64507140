package com.nexla.cloud.job.connector.infra

import com.nexla.cloud.job.connector.Utils
import com.nexla.cloud.job.connector.cloud.CloudProvider
import com.nexla.cloud.job.connector.pipeline.ClusterAndLastOperationTs
import com.nexla.common.datetime.DateTimeUtils.nowUtc
import com.nexla.db.postgres.tables.CloudEnvironment._
import com.nexla.db.postgres.tables.records.CloudEnvironmentRecord
import com.nexla.sc.util.{ConnectionFn, StrictNexlaLogging, WithJooq, WithLogging}
import org.jooq.{DSLContext, Field, SQLDialect}
import org.jooq.impl.DSL

import java.time.temporal.{ChronoUnit, TemporalUnit}
import java.time.{Instant, LocalDateTime}
import java.util
import java.util.concurrent.ConcurrentHashMap
import java.util.{Collections, Optional}
import scala.collection.JavaConversions._


class ClusterStateDao(val getConnectionFn: ConnectionFn)
  extends WithLogging
    with StrictNexlaLogging
    with WithJooq {

  val cloudProviders = new ConcurrentHashMap[String, CloudProvider]()

  def selectBySrcSink(srcId: Int, sinkId: Int): Option[ClusterAndLastOperationTs] = {
    logger.info("running selectBySrcSink for srcId {} sinkId {}", srcId, sinkId)
    withJooqCommit {
      jooq =>
        val e =
          jooq.selectFrom(CLOUD_ENVIRONMENT)
            .where(CLOUD_ENVIRONMENT.SOURCE_ID.equal(srcId))
            .and(CLOUD_ENVIRONMENT.SINK_ID.equal(sinkId))
            .orderBy(CLOUD_ENVIRONMENT.LAST_OPERATION_TS.desc())
            .limit(1)
            .fetchOptional()

        if (e.isEmpty) {
          None
        } else {
          // touch it
          jooq.update(CLOUD_ENVIRONMENT)
            .set(CLOUD_ENVIRONMENT.LAST_OPERATION_TS, DSL.localDateTime(LocalDateTime.now()))
            .where(CLOUD_ENVIRONMENT.SOURCE_ID.equal(srcId))
            .and(CLOUD_ENVIRONMENT.SINK_ID.equal(sinkId))
            .and(CLOUD_ENVIRONMENT.CLUSTER_ID.equal(e.get().getClusterId))
          // and return to sender
          Option(Utils.fromJooqWithCloudProvider(e, cloudProviders))
        }
    }
  }

  def selectClustersIdleForMoreThan(ttlMilli: Int): Set[ClusterAndLastOperationTs] = {
    logger.info("running selectClustersIdleForMoreThan with ttlMilli {}", ttlMilli)
    val idleWindow = nowUtc().minus(ttlMilli, ChronoUnit.MILLIS)

    withJooqReadOnly {
      jooq =>
        val e =
          jooq.selectFrom(CLOUD_ENVIRONMENT)
            .where(CLOUD_ENVIRONMENT.LAST_OPERATION_TS.le(idleWindow))
            .orderBy(CLOUD_ENVIRONMENT.LAST_OPERATION_TS.desc())
            .fetch()

        val hm: util.List[ClusterAndLastOperationTs] = if (e.isEmpty) {
          Collections.emptyList()
        } else {
          e.map {
            obj =>
              Utils.fromJooqWithCloudProvider(Optional.of(obj), cloudProviders)
          }
        }
        hm.toSet
    }
  }

  def countAll(): Int = {
    logger.info("running countAll")
    withJooqReadOnly { jooq => jooq.fetchCount(jooq.selectFrom(CLOUD_ENVIRONMENT)) }
  }

  def update(key: ClusterAndLastOperationTs): Option[CloudProvider] = {
    logger.info(s"running Update for clusterId ${key.clusterId} and cloudProvider ${key.cloudProvider}, srcId {}, sinkId {}", key.srcId, key.sinkId)
    // update where src id + sink id + cluster id match
    // set last action ts = now + status from key
    // and refresh cloud provider here
    withJooqCommit {
      _
        .update(CLOUD_ENVIRONMENT)
        .set(CLOUD_ENVIRONMENT.LAST_OPERATION_TS, DSL.localDateTime(LocalDateTime.now()))
        .set(CLOUD_ENVIRONMENT.LAST_STATUS, key.lastStatus)
        .where(CLOUD_ENVIRONMENT.SOURCE_ID.equal(key.srcId).and(CLOUD_ENVIRONMENT.SINK_ID.equal(key.sinkId).and(CLOUD_ENVIRONMENT.CLUSTER_ID.equal(key.clusterId))))
        .execute()
    }
    if (key.cloudProvider.isDefined) {
      this.cloudProviders.replace(Utils.keyFormat(key.srcId, key.sinkId, key.clusterId), key.cloudProvider.get)
    }

    key.cloudProvider
  }

  def delete(key: ClusterAndLastOperationTs): Option[CloudProvider] = {
    logger.info(s"running delete Update for clusterId ${key.clusterId} and cloudProvider ${key.cloudProvider}, srcId {}, sinkId {}", key.srcId, key.sinkId)
    // delete where src id + sink id + cluster id match
    withJooqCommit {
      _
        .delete(CLOUD_ENVIRONMENT)
        .where(CLOUD_ENVIRONMENT.SOURCE_ID.equal(key.srcId).and(CLOUD_ENVIRONMENT.SINK_ID.equal(key.sinkId).and(CLOUD_ENVIRONMENT.CLUSTER_ID.equal(key.clusterId))))
        .execute()
    }
    key.cloudProvider
  }

  def insert(key: ClusterAndLastOperationTs): Option[CloudProvider] = {
    logger.info(s"running insert Update for clusterId ${key.clusterId} and cloudProvider ${key.cloudProvider}, srcId {}, sinkId {}", key.srcId, key.sinkId)
    withJooqCommit {
      jooq =>
        val a = new CloudEnvironmentRecord()
        a.setSourceId(key.srcId)
        a.setSinkId(key.sinkId)
        a.setClusterId(key.clusterId)
        a.setLastOperationTs(key.lastOperationTs.getOrElse(LocalDateTime.now()))
        a.setLastStatus(key.lastStatus)

        jooq
          .insertInto(CLOUD_ENVIRONMENT)
          .set(a)
          .onDuplicateKeyIgnore()
          .execute()
    }
    if (key.cloudProvider.isDefined) {
      this.cloudProviders.put(Utils.keyFormat(key.srcId, key.sinkId, key.clusterId), key.cloudProvider.get)
    }
    key.cloudProvider
  }
}
