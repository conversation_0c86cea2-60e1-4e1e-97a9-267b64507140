package com.nexla.cloud.job.connector.infra

import com.nexla.cloud.job.connector.{AppProps, MetricsHelper, Utils}
import com.nexla.cloud.job.connector.pipeline.ClusterAndLastOperationTs
import com.typesafe.scalalogging.LazyLogging

import java.io.File
import java.nio.file.Files
import java.nio.file.attribute.BasicFileAttributes
import java.time.{Instant, LocalDateTime, ZoneId}
import java.util.concurrent.ConcurrentMap
import scala.collection.JavaConverters._

trait ClusterStateStorage {
  def getIdleClusters(ttlMilli: Int): Set[ClusterAndLastOperationTs]

  def findAndTouch(srcId: Int, sinkId: Int): Option[ClusterAndLastOperationTs]

  def put(ts: ClusterAndLastOperationTs): Unit
  def remove(key: ClusterAndLastOperationTs): Unit
  def update(ts: ClusterAndLastOperationTs): Unit
  def size(): Int
  def load(): Unit
}


/**
 * Backed by files stored in tmp folder on a local machine
 */
class LocalStateStorage(metricsHelper: MetricsHelper) extends ClusterStateStorage with LazyLogging {

  private final val trackedClusters: ConcurrentMap[ClusterAndLastOperationTs, File] = {
    new java.util.concurrent.ConcurrentHashMap[ClusterAndLastOperationTs, File]()
  }

  val s: String = File.separator
  private val sep: String = "___"
  val defaultTmpFilesPath: String = s"${s}tmp${s}trackedClusters"

  def loadLastFileUpdateTs(file: File): Option[LocalDateTime] = {
    val attrs = Files.readAttributes(file.toPath, classOf[BasicFileAttributes])
    Option(LocalDateTime.ofInstant(attrs.lastModifiedTime().toInstant, ZoneId.systemDefault()))
  }

  private def loadFromLocation(path: String): Unit = {
    logger.info(s"Restoring cluster tracker state from directory [$path]")
    val folder = new File(path)
    folder.mkdirs()
    val filesInFolder = Option(folder.listFiles())
    if (filesInFolder.isEmpty) {
      logger.info(s"folder $path doesn't contain files")
    } else {
      filesInFolder.get.foreach {
        file => {
          // try reconstructing whatever info we may have
          // files are named SRC-ID___SINK-ID___CLUSTER-NAME
          val pipeInfo = file.getName.split(sep)
          if (pipeInfo.length == 3) {
            val srcId = pipeInfo(0).toInt
            val sinkId = pipeInfo(1).toInt
            val clusterId = pipeInfo(2)
            val lastOperationTs = loadLastFileUpdateTs(file)

            val reconstructedInfo = ClusterAndLastOperationTs(
              srcId, sinkId, clusterId, lastOperationTs, Utils.IDLE, cloudProvider = None
            )
            logger.info(s"Reconstructed cluster status from file: ${file.getName} - $reconstructedInfo")
            this.put(reconstructedInfo)
          } else {
            logger.info(s"File name doesn't contain 3 entries for src, sink and cluster id, separated with [$sep]")
          }
        }
      }
    }

    logger.info(s"Finished restoring cluster tracker state from directory [$path]")
  }

  override def put(ts: ClusterAndLastOperationTs): Unit = {
    // put to map, create file
    logger.debug(s"adding $ts to tracked list")
    val fileName = ts.srcId + sep + ts.sinkId + sep + ts.clusterId
    val trackedFile = new File(defaultTmpFilesPath + s + fileName)
    trackedFile.createNewFile()
    logger.debug(s"file created for $ts - ${trackedFile.getName}")
    trackedClusters.put(ts, trackedFile)
    metricsHelper.incrementTrackedClusters()
  }

  override def remove(key: ClusterAndLastOperationTs): Unit = {
    val trackedFile = this.trackedClusters.asScala.find(a => a._1.srcId == key.srcId && a._1.sinkId == key.sinkId && a._1.clusterId == key.clusterId)
    if (trackedFile.isDefined) trackedFile.get._2.delete()
    this.trackedClusters.remove(key)
    metricsHelper.decrementTrackedClusters()
    logger.debug(s"removed $key")
  }

  override def size(): Int = {
    this.trackedClusters.size()
  }

  override def load(): Unit = {
    loadFromLocation(defaultTmpFilesPath)
  }

  override def update(ts: ClusterAndLastOperationTs): Unit = {
    val existingEntry = this.trackedClusters.entrySet().asScala.find(a => {
      a.getKey.srcId == ts.srcId && a.getKey.sinkId == ts.sinkId
    })

    if (existingEntry.isDefined) {
      logger.debug(s"Updating tracked state + touching file entry for $ts")
      val existingCluster = existingEntry.get
      val time = Instant.now()
      existingCluster.getValue.setLastModified(time.toEpochMilli)
      this.trackedClusters.replace(
        existingCluster.getKey.copy(lastOperationTs = Option(time.atZone(ZoneId.systemDefault()).toLocalDateTime), lastStatus = ts.lastStatus),
        existingCluster.getValue
      )
    } else {
      logger.warn(s"attempt to touch untracked cluster: $ts")
    }
  }

  override def findAndTouch(srcId: Int, sinkId: Int): Option[ClusterAndLastOperationTs] = {
    // touch it
    val key = this.trackedClusters.entrySet().asScala.find(a => {
      a.getKey.srcId == srcId && a.getKey.sinkId == sinkId
    })

    if (key.isDefined) {
      val foundCluster = key.get.getKey
      logger.debug(s"Found tracked cluster: $foundCluster, reusing it")
      this.update(foundCluster.copy(lastOperationTs = Option(LocalDateTime.now())))
      Option(foundCluster)
    } else {
      logger.debug(s"No tracked cluster found for src $srcId and sink $sinkId")
      None
    }
  }

  override def getIdleClusters(ttlMilli: Int): Set[ClusterAndLastOperationTs] = {
    this.trackedClusters.asScala
      .filter {
        t => java.time.Duration.between(t._1.lastOperationTs.get, LocalDateTime.now()).toMillis > ttlMilli
      }
      .keySet.toSet
  }
}

/**
 * Backed by table in listing database with the clusters
 * @param connectionFn connection to listing database
 */
class DbStateStorage(props: AppProps, metricsHelper: MetricsHelper) extends ClusterStateStorage with LazyLogging {
  private lazy val pool = Utils.dbPool(4, props)
  val dao = new ClusterStateDao(() => pool.getConnection())

  override def put(key: ClusterAndLastOperationTs): Unit = dao.insert(key)
  override def remove(key: ClusterAndLastOperationTs): Unit = {
    logger.info(s"removing $key")
    dao.delete(key)
  }
  override def update(key: ClusterAndLastOperationTs): Unit = dao.update(key)
  override def size(): Int = dao.countAll()
  override def load(): Unit = { logger.info("No-op in this impl - info is stored in DB") }

  override def getIdleClusters(ttlMilli: Int): Set[ClusterAndLastOperationTs] = dao.selectClustersIdleForMoreThan(ttlMilli)
  override def findAndTouch(srcId: Int, sinkId: Int): Option[ClusterAndLastOperationTs] = {
    val inDb = dao.selectBySrcSink(srcId, sinkId)
    logger.info(s"found a cluster for src $srcId and sink $sinkId - $inDb")
    inDb
  }
}
