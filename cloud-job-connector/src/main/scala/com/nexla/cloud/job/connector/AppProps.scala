package com.nexla.cloud.job.connector

import com.nexla.connector.config.vault.NexlaAppConfig
import com.nexla.sc.config._

class AppProps(val config: NexlaAppConfig)
  extends NexlaCreds
    with Vault
    with SecretNames
    with AwsCredentials
    with NexlaClusterApplication
    with NexlaSslConfig
    with NexlaAdminApi
    with NexlaEndpoints
    with TelemetryConfig
    with DataDog
    with KafkaProperties
    with NexlaTasks
    with NexlaDecryptKey
    with ListingDb
    with Prometheus {

  val taskType: Option[String] = config.getOptString("task.type")
  val nodeTags: Option[Seq[String]] = config.getOptString("node.tags")
    .map(_.split(",").toSeq)

  val clusterTrackerStorage: String = config.getOptString("cluster.tracker.storage").orElse(Option("db")).get
  val idleClusterTtlMilli: Option[Int] = config.getOptInt("idle.cluster.ttl.milli")
}
