package com.nexla.cloud.job.connector.compat

import akka.Done
import akka.actor.Cancellable
import akka.stream.scaladsl.{Sink, Source}
import akka.stream.{Materializer, SharedKillSwitch}
import cats.instances.future._
import cats.syntax.functor._
import com.nexla.common.{NexlaMessage, NexlaMetaData}
import com.nexla.connect.common.SchemaDetectionResult
import com.nexla.connector.config.file.FileSourceConnectorConfig
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat
import com.nexla.control.ListingFileStatus
import com.nexla.control.coordination.SetFileStatusCoordination
import com.nexla.file.service.FileConnectorService
import com.nexla.sc.client.OffsetSaver
import com.nexla.sc.client.job_scheduler.PipelineTaskStateEnum.PipelineTaskState
import com.nexla.sc.client.listing.{CoordinationAppClient, ListingAppClient}
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import org.apache.commons.lang3.StringUtils

import java.io.File
import java.time.LocalDateTime
import java.util.UUID
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal

package object compat extends StrictNexlaLogging with WithLogging {
}

case class PipelineRunContext(killSwitch: SharedKillSwitch, newTasksKillSwitch: SharedKillSwitch, streamFuture: Future[Done], ctx: PipelineContext)

class PipelineState(val runContext: PipelineRunContext, pipelineStats: PipelineStats)
  extends StrictNexlaLogging
    with WithLogging {

  private var dataIngestionTs: Option[LocalDateTime] = None
  private var status: Option[PipelineTaskState] = None

  private var readStatus: Option[PipelineTaskState] = None
  private var readStartTs: Option[LocalDateTime] = None
  private var readDoneTs: Option[LocalDateTime] = None

  private var writeStatus: Option[PipelineTaskState] = None
  private var writeStartTs: Option[LocalDateTime] = None
  private var writeDoneTs: Option[LocalDateTime] = None

  private var stopped = false
  private var restarting = false // this value is needed to prevent taskStatus from being set on restart

  def setDataIngestionTs(value: LocalDateTime) = dataIngestionTs = Some(value)
  def setStatus(value: PipelineTaskState) = {
    status = Some(value)
  }

  def setReadStatus(value: PipelineTaskState) = {
    readStatus = Some(value)
  }
  def setReadStartTs(value: LocalDateTime) = {
    readStartTs = Some(value)
  }
  def setReadDoneTs(value: LocalDateTime) = {
    readDoneTs = Some(value)
  }

  def setWriteStatus(value: PipelineTaskState) = {
    writeStatus = Some(value)
  }
  def setWriteStartTs(value: LocalDateTime) = {
    writeStartTs = Some(value)
  }
  def setWriteDoneTs(value: LocalDateTime) = {
    writeDoneTs = Some(value)
  }

  def setStopped(value: Boolean) = {
    stopped = value
  }

  // this value is needed to prevent taskStatus from being set on restart
  def setRestarting(value: Boolean) = {
    restarting = value
  }

  def getDataIngestionTs() = dataIngestionTs
  def getStatus() = status

  def getReadStatus() = readStatus

  def getReadStartTs() = {
    if (pipelineStats.isSourceEmpty() || pipelineStats.isSinkEmpty()) None
    else readStartTs
  }

  def getReadDoneTs() = readDoneTs

  def getWriteStatus() = writeStatus

  def getWriteStartTs() = {
    if (pipelineStats.isSourceEmpty() || pipelineStats.isSinkEmpty()) None
    else writeStartTs
  }

  def getWriteDoneTs() = writeDoneTs

  def getStopped() = stopped
  def getRestarting() = restarting // this value is needed to prevent taskStatus from being get on restart

  override def toString(): String = {
    s"PipelineState [dataIngestionTs=${getDataIngestionTs()}, status=${getStatus()}, readStatus=${getReadStatus()}," +
    s"readStartTs=${getReadStartTs()}, readDoneTs=${getReadDoneTs()}, writeStatus=${getWriteStatus()}, writeStartTs=${getWriteStartTs()}," +
    s"writeDoneTs=${getWriteDoneTs()}, stopped=${getStopped()}, restarting=${getRestarting()}]"
  }
}

trait FastConnectorOffset extends Ordered[FastConnectorOffset] {

  def save(listingClient: ListingAppClient, coordinationClient: CoordinationAppClient, offsetSaver: OffsetSaver): Future[Unit]

  def groupKey: Any

}

case class RecordWithOffset(headDatasetId: Option[Int],
                            message: Option[NexlaMessage],
                            offset: FastConnectorOffset,
                            metadataCallback: Option[SchemaDetectionResult => NexlaMetaData] = None)

case class ReplicationSourceFile(listedId: Option[Long],
                                 storagePath: String,
                                 nativeStoragePath: String,
                                 fileConfig: Option[WarehouseCopyFileFormat],
                                 file: Option[File],
                                 recordCount: Option[Long])
