package com.nexla.cloud.job.connector

import akka.actor.ActorSystem
import akka.http.scaladsl.{ConnectionContext, Http, HttpsConnectionContext}
import akka.stream.Materializer
import com.databricks.sdk.core.UserAgent
import com.nexla.admin.client.{AdminApiClient, AdminApiClientBuilder}
import com.nexla.cloud.job.connector.api.{<PERSON><PERSON><PERSON><PERSON><PERSON>, CloudJobConnectorApiHandler}
import com.nexla.cloud.job.connector.compat.{ScheduledJobs, TaskReceiver}
import com.nexla.cloud.job.connector.infra.ClusterTracker
import com.nexla.cloud.job.connector.pipeline.CloudJobPipelineRegistry
import com.nexla.common.NexlaConstants.{TOPIC_METRICS, TOPIC_NOTIFY}
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.common.{AppType, RestTemplateBuilder}
import com.nexla.connector.config.PipelineTaskType
import com.nexla.kafka.KafkaMessageTransport
import com.nexla.sc.client.OffsetSaver
import com.nexla.sc.client.job_scheduler.NodeTaskManagerClient
import com.nexla.sc.client.listing.{CoordinationAppClient, ListingAppClient}
import com.nexla.sc.util.{AppUtils, Async, StrictNexlaLogging}
import com.nexla.telemetry.jmx.JmxExporter
import com.nexla.transform.schema.FormatDetector
import fr.davit.akka.http.metrics.core.scaladsl.server.HttpMetricsRoute

import scala.compat.java8.OptionConverters._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

object CloudJobConnectorApp
  extends App
    with AppUtils
    with StrictNexlaLogging {

  implicit private val (props, _, envMap) = loadProps(AppType.CLOUD_JOB_CONNECTOR, new AppProps(_))
  lazy val (telemetry, httpMetricsRegistry) = initTelemetry(props.telemetryConf, AppType.CLOUD_JOB_CONNECTOR.appName, Some(props.dataDog), Some(props.prometheus))
  private lazy val metricsHelper = MetricsHelper(telemetry)
  private lazy val jmxExporter: JmxExporter = new JmxExporter(telemetry)

  implicit lazy val system: ActorSystem = defaultActorSystem(logger)
  implicit lazy val materializer: Materializer = Materializer(system)

  private lazy val appSslContext = nexlaSslContext(props)
  private lazy val restTemplate = new RestTemplateBuilder().withSSL(appSslContext).build()
  private lazy val nodeId = props.nodeId.getOrElse(java.util.UUID.randomUUID().toString)

  val adminApi = new AdminApiClientBuilder()
    .setAppName(s"CloudJobConnectorApp-$nodeId")
    .setDataPlaneUid(props.dataplaneUid)
    .setEnrichmentUrl(props.credEnrichmentUrl)
    .setExpandResources(true)
    .setNoCache(true)
    .create(props.apiCredentialsServer, props.apiAccessKey, restTemplate)

  lazy val listingClient = new ListingAppClient(props.listingAppServer, props.nexlaCreds)
  private lazy val coordinationClient = new CoordinationAppClient(props.coordinationAppServer, props.nexlaCreds)
  private val nexlaMessageProducer = new NexlaMessageProducer(new KafkaMessageTransport(props.bootstrapServers, nexlaSslContext(props), TOPIC_METRICS, TOPIC_NOTIFY))

  implicit lazy val executionContext: ExecutionContext = Async.ioExecutorContext
  FormatDetector.initDefault()

  private lazy val clusterTracker: ClusterTracker = ClusterTracker(
    60*1000*15,   // check clusters every 15 minutes
    60*1000*60,   // maximum idle time is 60 minutes
    props.clusterTrackerStorage,
    props,
    metricsHelper
  )

  private def initApp(): Future[Http.ServerBinding] = {
    val pipelineRegistry = new CloudJobPipelineRegistry(adminApi, listingClient, nexlaMessageProducer, clusterTracker, metricsHelper, props)
    val handler = new CloudJobConnectorApiHandler(pipelineRegistry, metricsHelper)
    val api = new ApiHandler(props.nexlaCreds, handler, envMap, httpMetricsRegistry)

    val nodeTaskManagerClient = new NodeTaskManagerClient(props.ctrlNodeTaskManagerUrl, props.nexlaCreds)
    val taskType = props.taskType
      .map(PipelineTaskType.fromString)
      .getOrElse(PipelineTaskType.SPARK)
    configureHttpClientSslContext(appSslContext)

    val taskReceiver = new TaskReceiver(nodeId, props.podIp, props.podName, dedicatedNode = false,
      Set(taskType), Option(pipelineRegistry), None, adminApi, nodeTaskManagerClient, props.nodeTags, props.appVersion)
    val offsetSaver = new OffsetSaver(coordinationClient, nexlaMessageProducer)
    val scheduledJobs = new ScheduledJobs(taskReceiver, offsetSaver, Option(props.receiveTasksCron), None)

    // init metrics
    httpMetricsRegistry.map {
      actualReg =>
        val metricsRoute = HttpMetricsRoute(api.metricsRoute).recordMetrics(actualReg)
        val metricsBinding = Http().newServerAt("0.0.0.0", props.nexlaMetricsPort).bindFlow(metricsRoute)
        jmxExporter.enable()

        metricsBinding.andThen {
          case Success(Http.ServerBinding(_)) =>
            logger.info(s"Metrics server started on ${props.podIp}:${props.nexlaMetricsPort}")
          case Failure(e) =>
            logger.error(s"Could not start Metrics server on ${props.nexlaMetricsPort}.", e)
        }
    }

    // Databricks partnership compliance: set client ua string
    val version = getClass.getPackage.getImplementationVersion
    UserAgent.withProduct("Nexla (Spark ETL)", version)

    val httpSystem = Http(system)
    val context: ConnectionContext = appSslContext
      .getServerKeystoreStore
      .asScala
      .map(sslContext => httpsContext(sslContext, appSslContext.getServerTruststoreStore.orElse(null)))
      .getOrElse(ConnectionContext.noEncryption())

    val binding: Future[Http.ServerBinding] = context match {
      case connectionContext: HttpsConnectionContext =>
        httpSystem.newServerAt("0.0.0.0", props.nexlaAppPort).enableHttps(connectionContext).bindFlow(api.route)
      case _ =>
        httpSystem.newServerAt("0.0.0.0", props.nexlaAppPort).bindFlow(api.route)
    }

    appSslContext.getServerKeystoreStore.asScala.foreach(_.clean())
    binding.andThen {
      case Success(Http.ServerBinding(_)) =>
        scheduledJobs.scheduleJobs()

      case Failure(e) =>
        logger.error(s"Could not start API server on ${props.nexlaAppPort}. Exiting...", e)
        System.exit(1)
    }
  }

  initApp()

}
