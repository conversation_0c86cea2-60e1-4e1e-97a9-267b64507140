package com.nexla.cloud.job.connector.compat

import com.nexla.admin.client.AdminApiClient
import com.nexla.common.ResourceType
import com.nexla.common.datetime.DateTimeUtils.nowUtc
import com.nexla.connector.config.PipelineTaskType
import com.nexla.sc.client.job_scheduler._
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging, WithTryLock}

import scala.concurrent.{ExecutionContext, Future}

class TaskReceiver(nodeId: String,
                   ip: String,
                   podName: Option[String],
                   dedicatedNode: Boolean,
                   taskTypes: Set[PipelineTaskType],
                   sourceIdentifiedPipelineRegistry: Option[BasicPipelineRegistry],
                   sinkIdentifiedPipelineRegistry: Option[BasicPipelineRegistry],
                   adminApiClient: AdminApiClient,
                   client: NodeTaskManagerClient,
                   nodeTags: Option[Seq[String]],
                   appVersion: String)
                  (implicit val ec: ExecutionContext)
  extends WithTryLock
    with StrictNexlaLogging
    with WithLogging {

  def receiveAndProcessTasks(): Future[Unit] = {
    val statuses = pipelineStatuses
    withTryLock {
      receiveAndProcessTasks(statuses)
    } {
      logger.info(s"receiveAndProcessTasks is still busy, notify current state")
      val request = PipelineNodeDto(nodeId, ip, dedicatedNode, podName, taskTypes, statuses, nodeTags, Some(appVersion), None)
      client.nodeTasksNotify(request)
    }
  }

  private def receiveAndProcessTasks(statuses: Seq[NodeTaskStatus]): Future[Unit] = {
    for {
      nodeTaskResponse <- client.nodeTasksReceive(
        PipelineNodeDto(nodeId, ip, dedicatedNode, podName, taskTypes, statuses, nodeTags,
          Some(appVersion), None)
      )
      _ <- if (nodeTaskResponse.ready) {
        val tasks = nodeTaskResponse.tasks.values.flatten.toSeq
        logger.info(s"NodeTaskManager received ${tasks.size} tasks")

        val thingsToStop = nodeTaskResponse.stopIds.getOrElse(Map())
        val stopSrcIds = thingsToStop.filterKeys(_.equals(ResourceType.SOURCE)).values.flatten.toSet
        val stopSinkIds = thingsToStop.filterKeys(_.equals(ResourceType.SINK)).values.flatten.toSet

        val thingsToRestart = nodeTaskResponse.restartIds.getOrElse(Map())
        val restartSrcIds = thingsToRestart.filterKeys(_.equals(ResourceType.SOURCE)).values.flatten.toSet
        val restartSinkIds = thingsToRestart.filterKeys(_.equals(ResourceType.SINK)).values.flatten.toSet

        sourceIdentifiedPipelineRegistry.map(_.setCurrentActive(tasks, stopSrcIds, restartSrcIds)).getOrElse(Future.successful())
        sinkIdentifiedPipelineRegistry.map(_.setCurrentActive(tasks, stopSinkIds, restartSinkIds)).getOrElse(Future.successful())
      } else {
        logger.info("NodeTaskManager is not ready")
        Future.successful(())
      }
    } yield {
    }
  }

  private def pipelineStatuses: Seq[NodeTaskStatus] = {
    Seq(sourceIdentifiedPipelineRegistry, sinkIdentifiedPipelineRegistry)
      .filter(_.isDefined).map(_.get)
      .flatMap { registry =>
        registry.runningPipelines
          .filter(_._2.pipelineState.flatMap(_.getStatus()).isDefined)
          .map { case (taskId, pipeline) =>
            NodeTaskStatus(
              taskId,
              Some(pipeline.runId),
              pipeline.pipelineState.flatMap(_.getStatus()).get,
              pipeline.pipelineState.flatMap(_.getReadStatus()),
              pipeline.pipelineState.flatMap(_.getReadStartTs()),
              pipeline.pipelineState.flatMap(_.getReadDoneTs()),
              pipeline.pipelineState.flatMap(_.getWriteStatus()),
              pipeline.pipelineState.flatMap(_.getWriteStartTs()),
              pipeline.pipelineState.flatMap(_.getWriteDoneTs()),
              pipeline.pipelineState.flatMap(_.getDataIngestionTs()),
              Some(nowUtc())
            )
          }
          .toSeq
      }
  }
}