package com.nexla.cloud.job.connector.api

import akka.actor.ActorSystem
import akka.http.scaladsl.server.Route
import akka.http.scaladsl.server.Route.seal
import akka.stream.Materializer
import com.nexla.sc.api.{AccessBodyLogging, CommonApiHandler}
import com.nexla.sc.config.NexlaCredsConf
import fr.davit.akka.http.metrics.core.HttpMetricsRegistry
import ch.megard.akka.http.cors.scaladsl.CorsDirectives._
import com.nexla.common.AppType

import scala.concurrent.ExecutionContext

class ApiHandler(nexla: NexlaCredsConf,
                 cloudJobConnectorApiHandler: CloudJobConnectorApiHandler,
                 val envMap: java.util.Map[String, String],
                 val registry: Option[HttpMetricsRegistry])
                (implicit val system: ActorSystem,
                 val m: Materializer,
                 val ex: ExecutionContext)
  extends CommonApiHandler with AccessBodyLogging {

  private val basicAuthRoutes =
    seal {
      basicAuth(nexla, AppType.CLOUD_JOB_CONNECTOR.appName) {
        _ =>
          concat(
            cloudJobConnectorApiHandler.listRunningJobs
          )
      }
    }

  val route: Route = buildRoutes(Some(basicAuthRoutes), Some(cors()), loggingDirective = withAccessBodyLogging)
}