package com.nexla.cloud.job.connector.cloud

import com.nexla.common.NexlaConstants

class CloudSpecificCfg(original: Map[String, AnyRef]) {
  def getString(key: String, default: Option[String] = None): String = {
    val value = getStringOption(key).orElse(default)
    value.getOrElse(throw new IllegalArgumentException(s"No value found for key [$key]"))
  }

  def getStringOption(key: String): Option[String] = {
    original.get(key).map(_.toString)
  }

  def getInt(key: String, default: Option[Int] = None): Int = {
    val value = original.get(key).orElse(default).map(_.toString.toInt)
    value.getOrElse(throw new IllegalArgumentException(s"No value found for key [$key]"))
  }

  def getIntOption(key: String): Option[Int] = {
    original.get(key).map(_.toString.toInt)
  }

  def getBoolean(key: String, default: Option[Boolean] = None): Boolean = {
    val value = original.get(key).orElse(default).map(_.toString.toBoolean)
    value.getOrElse(throw new IllegalArgumentException(s"No value found for key [$key]"))
  }

  val cloudJarLocation: String = getString(NexlaConstants.CLOUD_JAR_LOCATION)
  // not necessary if the client provides us a cluster
  val mainNodeVCPU: Option[Int] = getIntOption("cloud.cluster.main.node.vcpu")
  val workerNodeVCPU: Option[Int] = getIntOption("cloud.cluster.worker.node.vcpu")
  val numberOfWorkers: Option[Int] = getIntOption("cloud.cluster.worker.nodes")
}

case class AwsCloudSpecificCfg(originalCfg: Map[String, AnyRef]) extends CloudSpecificCfg(originalCfg) {
  val sshKeyName: Option[String] = getStringOption(NexlaConstants.CLOUD_SSH_KEY_NAME)
  val roleWithEMRAccess: String = getString(NexlaConstants.CLOUD_ROLE_WITH_CLUSTER_ACCESS)
  val clusterServiceRole: String = getString(NexlaConstants.CLOUD_CLUSTER_SERVICE_ROLE)
  val clusterEntityRole: String = getString(NexlaConstants.CLOUD_CLUSTER_ENTITY_ROLE)
  val region: String = getString(NexlaConstants.CLOUD_REGION)
  val clusterVmSubnet: String = getString(NexlaConstants.CLOUD_CLUSTER_VM_SUBNET)
}

sealed trait DatabricksAuth
case class DatabricksTokenAuth(token: String) extends DatabricksAuth
case class DatabricksOidcAuth(clientId: String, clientSecret: String) extends DatabricksAuth

case class DatabricksCloudSpecificCfg(originalCfg: Map[String, AnyRef], host: String, auth: DatabricksAuth) extends CloudSpecificCfg(originalCfg) {
  def this(originalCfg: Map[String, AnyRef], host: String, token: String) = this(originalCfg, host, DatabricksTokenAuth(token))
  def this(originalCfg: Map[String, AnyRef], host: String, clientId: String, clientSecret: String) =
    this(originalCfg, host, DatabricksOidcAuth(clientId, clientSecret))
}

object DatabricksCloudSpecificCfg {
  def apply(originalCfg: Map[String, AnyRef]): DatabricksCloudSpecificCfg = {
    val cfg = new CloudSpecificCfg(originalCfg)
    val databricksAuthType = cfg.getString("cloud.databricks.auth-type", Option("token"))
    val databricksWorkspaceHost = cfg.getString("cloud.databricks.workspace.host")

    databricksAuthType match {
      case "token" =>
        val token = cfg.getString("cloud.databricks.token")
        new DatabricksCloudSpecificCfg(originalCfg, databricksWorkspaceHost, token)
      case "oidc" =>
        val clientId = cfg.getString("cloud.databricks.client-id")
        val clientSecret = cfg.getString("cloud.databricks.secret")
        new DatabricksCloudSpecificCfg(originalCfg, databricksWorkspaceHost, clientId, clientSecret)
      case _ => throw new IllegalArgumentException(s"Unknown databricks auth type: $databricksAuthType")
    }
  }
}
