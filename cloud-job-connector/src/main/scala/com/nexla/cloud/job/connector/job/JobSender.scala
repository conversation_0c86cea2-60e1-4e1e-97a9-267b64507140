package com.nexla.cloud.job.connector.job

import com.nexla.cloud.job.connector.MetricsHelper
import com.nexla.cloud.job.connector.cloud.{CloudProvider, JobDefinition}
import com.typesafe.scalalogging.LazyLogging

import scala.concurrent.{ExecutionContext, Future}

class JobSender(val cloudProvider: CloudProvider, logTag: String, metricsHelper: MetricsHelper)(implicit context: ExecutionContext) extends LazyLogging {

  def trackUntilTerminalState(jobId: String, clusterId: String): Future[JobDefinition] = {
    Future {
      cloudProvider.trackUntilTerminalState(jobId, clusterId)
    }
  }

  def sendJob(clusterId: String, sinkId: Int, partitionToHandle: String, nexlaEnv: String, runId: Long): String = {
    val clusterDescription = cloudProvider.checkExistingCluster(clusterId)
    logger.info(s"$logTag Sending request to cloud [${cloudProvider.cloudName}] to add Spark job to cluster [$clusterId]")
    val r = cloudProvider.runSparkSubmitStep(clusterDescription.clusterId, sinkId, partitionToHandle, nexlaEnv, runId)
    metricsHelper.incrementTaskAssignments()
    logger.debug(s"$logTag Following jobs are now present on the cluster: [ \n${r.mkString("\n")}\n]")
    r.filter {
      jobDef =>
        jobDef.sinkId.equals(sinkId) &&
          jobDef.runId.equals(runId)
    }.head.jobId
  }
}

object JobSender {
  def apply(cloudProvider: CloudProvider, logTag: String, metricsHelper: MetricsHelper)(implicit context: ExecutionContext): JobSender = {
    new JobSender(cloudProvider, logTag, metricsHelper: MetricsHelper)
  }
}
