package com.nexla.cloud.job.connector.cloud

import com.nexla.connector.config.rest.BaseAuthConfig

case class ClusterConfig(mainNodeVCPU: Int, workerNodeVCPU: Int, mainNodes: Int, workerNodes: Int, name: Option[String] = None, sourceConfig: Option[Map[String, AnyRef]])
case class ClusterNode(id: String, hostName: String, address: String)
case class JobDefinition(jobId: String, sinkId: Int, runId: Long, partitionInfo: List[String], status: String)
case class ClusterResponse(clusterId: String, mainNodes: List[ClusterNode], dataNodes: List[ClusterNode],
                           workerNodes: List[ClusterNode], jobs: List[JobDefinition])

abstract class CloudProvider(cloudAuthConfig: BaseAuthConfig) {
  val cloudName: String

  def createCluster(): ClusterResponse
  def stopCluster(clusterId: String): Unit
  def trackUntilTerminalState(jobId: String, clusterId: String): JobDefinition
  def checkExistingCluster(clusterId: String): ClusterResponse
  def runSparkSubmitStep(clusterId: String, sinkId: Int, partitionToHandle: String, nexlaEnv: String, runId: Long): List[JobDefinition]
}
