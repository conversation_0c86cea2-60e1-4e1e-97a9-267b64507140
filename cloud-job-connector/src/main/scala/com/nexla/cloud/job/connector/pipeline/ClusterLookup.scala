package com.nexla.cloud.job.connector.pipeline

import com.nexla.admin.client.AdminApiClient
import com.nexla.cloud.job.connector.{AppProps, Utils}
import com.nexla.cloud.job.connector.cloud.CloudProvider
import com.nexla.cloud.job.connector.compat.PipelineContext
import com.nexla.cloud.job.connector.infra.ClusterTracker
import com.nexla.common.{NexlaDataCredentials, Resource, ResourceType}
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.sc.util.StrictNexlaLogging

import java.time.LocalDateTime
import java.util.Optional
import java.util.concurrent.atomic.AtomicReference
import scala.compat.java8.OptionConverters.RichOptionalGeneric
import scala.jdk.CollectionConverters.mapAsScalaMapConverter

class ClusterLookup(props: AppProps,
                    adminApiClient: AdminApiClient,
                    appMessageProducer: NexlaMessageProducer,
                    cloudProvider: CloudProvider,
                    assignedCluster: AtomicReference[ClusterAndLastOperationTs]) extends StrictNexlaLogging {

  def getCfgClusterId(ctx: PipelineContext): String = {
    val additionalCloudCfgId = Optional.ofNullable(ctx.dataSource.getSourceConfig.get("cloud.credentials.id")).asScala.map(_.toString)
    additionalCloudCfgId.map { actualConfigClusterId =>
      val creds = adminApiClient.getDataCredentials(actualConfigClusterId.toInt).get()
      val additionalCloudCreds = NexlaDataCredentials.getCreds(props.decryptKey, creds.getCredentialsEnc, creds.getCredentialsEncIv)
      additionalCloudCreds.get("cloud.cluster.id")
    }.orNull
  }

  def getOrCreateCluster(ctx: PipelineContext, clusterTracker: ClusterTracker): String = {
    val cfgClusterId = getCfgClusterId(ctx)
    if (Utils.isNullOrEmptyOrBlank(cfgClusterId)) {
      val logMsg = s"${ctx.readLog} No provided cluster id for execution on cloud, trying to reuse or create a cluster"
      logger.info(logMsg)
      val msg = Utils.prepareFlowInsightsMsg(
        ctx.dataSource.getOrg.getId, logMsg,
        ctx.runId,
        new Resource(ctx.dataSource.getId, ResourceType.SOURCE),
        NexlaMonitoringLogSeverity.INFO)
      appMessageProducer.publishMonitoringLog(msg)

      val sinkId = ctx.dataSinks.head._2.getId
      val maybeStillExistingCluster = clusterTracker.getFromTrackList(ctx.sourceId, sinkId)
      if (maybeStillExistingCluster.isEmpty) {
        logger.info(s"There's no tracked cluster for src ${ctx.sourceId} and sink ${ctx.dataSinks.head._2.getId}, trying to find one in the cloud")
        val expectedDefaultClusterName = if (Utils.unityCatalogEnabled(ctx.dataSource.getSourceConfig.asScala.toMap)) {
          Utils.DEFAULT_NEXLA_CLUSTER_NAME_WITH_CATALOG
        } else {
          Utils.DEFAULT_NEXLA_CLUSTER_NAME
        }

        val defaultCluster = cloudProvider.checkExistingCluster(expectedDefaultClusterName)
        if (defaultCluster.clusterId != null) {
          logger.info(s"Found default Nexla cluster: $expectedDefaultClusterName, using it")
          return defaultCluster.clusterId
        } else {
          logger.info("No default Nexla cluster was found. Creating a new one")
        }
        // todo: calculate the config here. it will be used completely, we won't need any additional config on job submits
        // 1 - ask s3 how much data is there
        // 2 - assess flow complexity
        val createdClusterId: String = cloudProvider.createCluster().clusterId
        // this one is created and killed by Nexla, this is why "managed"
        val currCluster = ClusterAndLastOperationTs(
          ctx.sourceId,
          ctx.dataSinks.head._2.getId,
          createdClusterId,
          Option(LocalDateTime.now()),
          Utils.IN_USE,
          Option(cloudProvider))
        clusterTracker.putToTrackList(currCluster)
        logger.info("setting assigned cluster to the one just created")
        assignedCluster.set(currCluster)
        createdClusterId
      } else {
        logger.info("there is tracked cluster found, reusing it")
        val actualCluster = maybeStillExistingCluster.get
        assignedCluster.set(actualCluster)
        actualCluster.clusterId
      }

    } else {
      // and for the provided one, we are not responsible, so we should not manage or kill it
      val logMsg = s"${ctx.readLog} Trying to use provided cloud cluster id [$cfgClusterId]"
      logger.info(logMsg)
      val msg = Utils.prepareFlowInsightsMsg(
        ctx.dataSource.getOrg.getId, logMsg,
        ctx.runId,
        new Resource(ctx.dataSource.getId, ResourceType.SOURCE),
        NexlaMonitoringLogSeverity.INFO)
      appMessageProducer.publishMonitoringLog(msg)

      cloudProvider.checkExistingCluster(cfgClusterId).clusterId
    }
  }
}
