package com.nexla.cloud.job.connector.compat

import akka.Done
import akka.stream.scaladsl.{Flow, Keep, Sink}
import cats.implicits._
import com.nexla.admin.client._
import com.nexla.common.NexlaConstants.TOPIC_NOTIFY
import com.nexla.common.ResourceType.SINK
import com.nexla.common.datetime.DateTimeUtils._
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.common.{Resource, ResourceType}
import com.nexla.connector.config.BaseConnectorConfig._
import com.nexla.connector.config.FlowType
import com.nexla.sc.client.job_scheduler.PipelineTaskStateEnum
import com.nexla.sc.client.job_scheduler.TaskRequest.TaskId
import com.nexla.sc.client.listing.FileStatuses.FileStatus
import com.nexla.sc.config.ConfigEnricher
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}

import java.time.LocalDateTime
import java.util.Comparator
import java.util.concurrent.ConcurrentSkipListSet
import scala.collection.JavaConverters._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

case class ProcessedFile(fileId: Long, lastTouched: LocalDateTime, lastKnownStatus: FileStatus)

case class PipelineContext(runId: Long,
                           taskId: TaskId,
                           dataSource: DataSource,
                           dataSinks: Map[Int, DataSink],
                           dataSets: List[DataSet],
                           pipelineConf: PipelineConf,
                           pipelineType: FlowType,
                           filesToHeartbeat: ConcurrentSkipListSet[ProcessedFile] = new ConcurrentSkipListSet[ProcessedFile](Comparator.comparingLong[ProcessedFile]((value: ProcessedFile) => value.fileId))) {

  val sourceId: Integer = dataSource.getId

  // first component in log is active one - for readability
  val readLog: String = {
    val sourceConnType = dataSource.getConnectionType.name().toLowerCase()
    val sinkTag = dataSinks.map(a => s"${a._1}-${a._2.getConnectionType}").mkString(",")
    s"[$taskId][read-$sourceId-$sourceConnType][write-$sinkTag]"
  }

  val writeLog: String = {
    val sourceConnType = dataSource.getConnectionType.name().toLowerCase()
    val sinkTag = dataSinks.map(a => s"${a._1}-${a._2.getConnectionType}").mkString(",")
    s"[$taskId][write-$sinkTag][read-$sourceId-$sourceConnType]"
  }
}

abstract class Pipeline(val taskId: TaskId,
                        val runId: Long,
                        val srcId: Int,
                        val sinkId: Int,
                        val messageProducer: NexlaMessageProducer,
                        val adminApiClient: AdminApiClient)
                       (implicit ec: ExecutionContext)

  extends StrictNexlaLogging
    with WithLogging
    with ConfigEnricher {

  // if Some(), pipeline was started
  var pipelineState: Option[PipelineState] = None

  val pipelineType: FlowType

  lazy val ctx: PipelineContext = {
    val pipeline = adminApiClient.getPipeline(sinkId).get()
    val dataSource = adminApiClient.getDataSource(pipeline.dataSource.getId).get()
    val dataSets = pipeline.dataSets.asScala.toList.map(x => adminApiClient.getDataSet(x.id).get())

    val flow = adminApiClient.getFlowByResource(new Resource(sinkId, ResourceType.SINK)).get()

    val dataSinks = flow.dataSinks.asScala.map { flowSink =>
      val dataSink = adminApiClient.getDataSink(flowSink.id).get()
      dataSink.getSinkConfig.put(FAST_MODE, "true")
      flowSink.id -> dataSink
    }.toMap

    dataSource.getSourceConfig.put(FAST_MODE, "true")
    val pipelineConf = PipelineConf.getPipelineConf(dataSource, dataSets, dataSinks.values.toList)
    PipelineContext(runId, taskId, dataSource, dataSinks, dataSets, pipelineConf, pipelineType)
  }

  def startPipeline(pipelineStats: PipelineStats): PipelineRunContext

  def stopForRestart(): Future[Unit] = {
    pipelineState.foreach(_.setRestarting(true))
    stop()
  }

  def stopReceivingNewTasks(): Future[Unit] = Future.successful {
    pipelineState.map(_.runContext) match {
      case Some(PipelineRunContext(_, newTasksKillSwitch, _, ctx)) =>
        // main thread unlocked, future kicks in
        logger.info(s"[$taskId] Stopping receiving new tasks for pipeline runId=${ctx.runId}")
        newTasksKillSwitch.shutdown()
      case _ =>
    }
  }

  def stop(): Future[Unit] = {
    logger.info(s"[$taskId] Stopping pipeline")
    val res = NamedFuture(s"Pipeline.stop(${pipelineState.map(_.runContext.ctx.readLog)})") {
      pipelineState
        .fold {
          logger.info(s"[$taskId] Pipeline has not started yet")
          Future.successful(())
        } { ps =>
          if (!ps.getStopped()) {
            logger.warn(s"Stopping pipeline ${ps.runContext.ctx.readLog}")
            pipelineState.map(_.runContext) match {
              case Some(PipelineRunContext(killSwitch, sharedKillswitch, streamFuture, ctx)) =>
                ps.setStopped(true) // set before killSwitch - we check this flag to know if we need to flush: we dont flush on stop command
                killSwitch.shutdown()
                // turn off other things as well
                sharedKillswitch.shutdown()
                streamFuture
                  .andThen { case _ =>
                    logger.info(s"[$taskId] Stopped pipeline runId=${ctx.runId}")
                  }
                  .void
              case _ =>
                logger.warn(s"pipeline state for $ps")
                Future.successful()
            }
          } else {
            logger.info(s"[$taskId] Pipeline is already stopped runId=${ps.runContext.ctx.runId}")
            Future.successful(())
          }
        }
    }
    res.flatten
  }

  def start(): Pipeline = {
    logger.info(s"[$taskId] runId=$runId Starting pipeline")

    val pipelineStats = new PipelineStats()
    val pipelineRun = startPipeline(pipelineStats)

    val state = new PipelineState(pipelineRun, pipelineStats)
    state.setStatus(PipelineTaskStateEnum.Running)
    this.pipelineState = Some(state)
    logger.info(s"[$taskId] runId=$runId Started")

    notifyReadStart

    pipelineRun
      .streamFuture
      .andThen { case _ =>
        if (pipelineStats.isSourceEmpty() || pipelineStats.isSinkEmpty()) {
          logger.info(s"[$taskId] no read/written records. ReadDone and WriteDone notifications won't be sent")
          notifyReadOnEmptyData
          notifyWriteOnEmptyData
        } else {
          notifyReadDone
          notifyWriteDone
        }
      }
      .andThen {
        case result@Success(_) =>
          logger.info(s"[$taskId] SUCCESS runId=$runId: $result")
          if (!state.getRestarting()) {
            state.setStatus(PipelineTaskStateEnum.Finished)
          }

        case Failure(e) =>
          logger.info(s"[$taskId] ERROR runId=$runId", e)
          state.setStatus(PipelineTaskStateEnum.Failed)
          messageProducer.publishErrorMessage(new Resource(sinkId, SINK), runId,
            "Pipeline stopped due to error", e.getMessage, TOPIC_NOTIFY)
      }

    this
  }

  protected lazy val notifyReadStart: Unit = {
    pipelineState.foreach(x => {
      x.setReadStartTs(nowUtc())
      x.setReadStatus(PipelineTaskStateEnum.Running)
    })
  }

  protected lazy val notifyReadDone: Unit = {
    pipelineState.foreach(x => {
      x.setReadDoneTs(nowUtc())
      x.setReadStatus(PipelineTaskStateEnum.Finished)
    })
  }

  protected lazy val notifyReadOnEmptyData: Unit = {
    pipelineState.foreach(x => {
      x.setReadStatus(PipelineTaskStateEnum.Finished)
    })
  }

  protected lazy val notifyWriteStart: Unit = {
    pipelineState.foreach(x => {
      x.setWriteStartTs(nowUtc())
      x.setWriteStatus(PipelineTaskStateEnum.Running)
    })
  }

  protected lazy val notifyWriteDone: Unit = {
    pipelineState.foreach(x => {
      x.setWriteDoneTs(nowUtc())
      x.setWriteStatus(PipelineTaskStateEnum.Finished)
    })
  }

  protected lazy val notifyWriteOnEmptyData: Unit = {
    pipelineState.foreach(x => {
      x.setWriteStatus(PipelineTaskStateEnum.Finished)
    })
  }

  def updateDataIngestionTs(callerMethod: String): Unit = {
    logger.info(s"${ctx.readLog} updating data ingestion ts, caller method [$callerMethod]")
    this.pipelineState.foreach(_.setDataIngestionTs(nowUtc()))
  }

  def updateTimestampFlow[T]: Sink[T, Future[Done]] = Flow[T].toMat(Sink.foreach(_ => updateDataIngestionTs("Pipeline.updateTimestampFlow()")))(Keep.right)

  def updateTimestampFlowPass(): Flow[List[RecordWithOffset], List[RecordWithOffset], _] = {
    Flow[List[RecordWithOffset]].map { any =>
      updateDataIngestionTs("Pipeline.updateTimestampFlowPass()")
      any
    }
  }
}
