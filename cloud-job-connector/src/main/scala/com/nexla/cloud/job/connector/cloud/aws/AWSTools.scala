package com.nexla.cloud.job.connector.cloud.aws

import com.amazonaws.auth.{AWSCredentialsProvider, DefaultAWSCredentialsProvider<PERSON><PERSON><PERSON>, STSAssumeRoleSessionCredentialsProvider}
import com.amazonaws.regions.Regions
import com.amazonaws.services.elasticmapreduce.model.{Step, StepSummary}
import com.amazonaws.services.elasticmapreduce.{AmazonElasticMapReduce, AmazonElasticMapReduceClientBuilder}
import com.amazonaws.services.s3.{AmazonS3, AmazonS3ClientBuilder}
import com.amazonaws.services.securitytoken.AWSSecurityTokenServiceClient
import com.nexla.cloud.job.connector.AppProps
import com.nexla.cloud.job.connector.cloud.JobDefinition
import com.nexla.connector.config.file.{AWSAuthConfig, NexlaAWSCredentialsProvider}
import com.nexla.sc.util.StrictNexlaLogging

object AWSTools extends StrictNexlaLogging {

  def roleCredentialsProvider(roleArn: String, region: String, roleSessionName: String): AWSCredentialsProvider = {
    val stsClientBuilder = AWSSecurityTokenServiceClient.builder
    stsClientBuilder.setRegion(region)
    val tokenServiceClient = stsClientBuilder.build()

    val credsBuilder = new STSAssumeRoleSessionCredentialsProvider.Builder(roleArn, roleSessionName)
    credsBuilder.withStsClient(tokenServiceClient).build
  }

  def buildFromNexlaCreds(awsAuthConfig: AWSAuthConfig): AWSCredentialsProvider = {
    NexlaAWSCredentialsProvider.getCredentialsProvider(awsAuthConfig)
  }

  def getEMRClient(creds: AWSCredentialsProvider, regions: Regions): AmazonElasticMapReduce = {
    // create an EMR client using the credentials and region specified in order to create the cluster
    val emr: AmazonElasticMapReduce = AmazonElasticMapReduceClientBuilder.standard()
      .withCredentials(creds)
      .withRegion(regions)
      .build()

    emr
  }

  def getS3Client(creds: AWSCredentialsProvider, regions: Regions): AmazonS3 = {
    AmazonS3ClientBuilder.standard()
      .withCredentials(creds)
      .withRegion(regions)
      .build()
  }

  def toJobDef(step: StepSummary): JobDefinition = {
    val args = step.getConfig.getArgs.toArray.map(_.toString).takeRight(4)
    // last 4 args are
    // 0 - String.valueOf(sinkId),
    // 1 - partitionToHandle,
    // 2 - nexlaEnv,
    // 3 - String.valueOf(runId)
    JobDefinition(step.getId, args(0).toInt, args(3).toLong, List(args(1)), step.getStatus.getState)
  }

  def toJobDef(step: Step): JobDefinition = {
    val args = step.getConfig.getArgs.toArray.map(_.toString).takeRight(4)
    // last 4 args are
    // 0 - String.valueOf(sinkId),
    // 1 - partitionToHandle,
    // 2 - nexlaEnv,
    // 3 - String.valueOf(runId)
    JobDefinition(step.getId, args(0).toInt, args(3).toLong, List(args(1)), step.getStatus.getState)
  }

  def toAmazonVmType(workerNodeVCPU: Int): String = {
    workerNodeVCPU match {
      case 2 => "m4.large"
      case 4 => "m4.xlarge"
      case 8 => "m4.2xlarge"
      case 16 => "m4.4xlarge"
      case 40 => "m4.10xlarge"
      case _ =>
        logger.warn(s"Unknown AWS VM type for $workerNodeVCPU requested VCPU, assuming smallest")
        "m4.large"
    }
  }

}
