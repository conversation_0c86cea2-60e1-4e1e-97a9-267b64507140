package com.nexla.cloud.job.connector.compat

import com.nexla.admin.client.{DataSet, DataSink, DataSource}

import scala.collection.JavaConverters._

object PipelineConf {

  val ConfTxParallelism = "fast.tx.parallelism"
  val ConfSinkParallelism = "fast.sink.parallelism"
  val ConfSourceParallelism = "fast.source.parallelism"

  val DefaultTxParallelism = 1
  val DefaultSinkParallelism = 5
  val DefaultSourceParallelism = 1

  def getPipelineConf(dataSource: DataSource, dataSets: List[DataSet], dataSinks: List[DataSink]): PipelineConf = {
    val srcConfig = dataSource.getSourceConfig.asScala
    val txConfigs = dataSets.map { dataSet =>
      val id = dataSet.getId
      val txParallelism = dataSet.getRuntimeConfig.asScala.get(ConfTxParallelism).map(_.toInt).getOrElse(DefaultTxParallelism)
      (id.toInt, txParallelism)
    }.toMap
    val sinkConfigs = dataSinks.map { dataSink =>
      val id = dataSink.getId
      val sinkParallelism = dataSink.getSinkConfig.asScala.get(ConfSinkParallelism).map(_.toString.toInt).getOrElse(DefaultSinkParallelism)
      (id.toInt, sinkParallelism)
    }.toMap

    PipelineConf(
      txParallelisms = txConfigs,
      sinkParallelisms = sinkConfigs,
      sourceParallelism = srcConfig.get(ConfSourceParallelism).map(_.toString.toInt).getOrElse(DefaultSourceParallelism))
  }
}

case class PipelineConf(txParallelisms: Map[Int, Int],
                        sinkParallelisms: Map[Int, Int],
                        sourceParallelism: Int)
