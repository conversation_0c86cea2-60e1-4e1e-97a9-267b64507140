package com.nexla.cloud.job.connector.compat

import akka.actor.{ActorSystem, Props}
import com.nexla.common.datetime.DateTimeUtils.UTC_TIME_ZONE
import com.nexla.sc.client.OffsetSaver
import com.typesafe.akka.extension.quartz.QuartzSchedulerExtension

import scala.concurrent.ExecutionContext

class ScheduledJobs(taskReceiver: TaskReceiver,
                    offsetSaver: OffsetSaver,
                    receiveTasksCron: Option[String],
                    flushOffsetsCron: Option[String])
                   (implicit system: ActorSystem,
                    ec: ExecutionContext) {

  private val schedulerActor = system.actorOf(Props(new SchedulerActor(taskReceiver, offsetSaver)))

  private val scheduler = QuartzSchedulerExtension(system)

  def scheduleJobs() = {
    receiveTasksCron.foreach {
      cronExpression =>
        scheduler.createSchedule("receive-tasks", None, cronExpression, None, UTC_TIME_ZONE)
        scheduler.schedule("receive-tasks", schedulerActor, ReceiveTasksCommand)
    }

    flushOffsetsCron.foreach {
      cronExpression =>
        scheduler.createSchedule("flush-offsets", None, cronExpression, None, UTC_TIME_ZONE)
        scheduler.schedule("flush-offsets", schedulerActor, FlushOffsetsCommand)
    }
  }

}
