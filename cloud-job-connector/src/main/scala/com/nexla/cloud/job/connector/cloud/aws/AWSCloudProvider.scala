package com.nexla.cloud.job.connector.cloud.aws

import com.amazonaws.auth.AWSCredentialsProvider
import com.amazonaws.regions.Regions
import com.amazonaws.services.elasticmapreduce.AmazonElasticMapReduce
import com.amazonaws.services.elasticmapreduce.model.{AddJobFlowStepsRequest, Application, AutoTerminationPolicy, ClusterState, Configuration, DescribeClusterRequest, DescribeClusterResult, DescribeStepRequest, DescribeStepResult, HadoopJarStepConfig, JobFlowInstancesConfig, ListStepsRequest, RunJobFlowRequest, StepConfig, StepState, TerminateJobFlowsRequest}
import com.amazonaws.services.s3.model.PutObjectRequest
import com.amazonaws.services.s3.{AmazonS3, AmazonS3URI}
import com.amazonaws.services.secretsmanager.model.{CreateSecretRequest, GetSecretValueRequest, UpdateSecretRequest}
import com.amazonaws.services.secretsmanager.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AWSSecretsManagerClientBuilder}
import com.github.rholder.retry._
import com.google.common.hash.Hashing
import com.nexla.cloud.job.connector.{MetricsHelper, Utils}
import com.nexla.cloud.job.connector.cloud.aws.AWSTools._
import com.nexla.cloud.job.connector.cloud._
import com.nexla.connector.config.rest.BaseAuthConfig
import com.typesafe.scalalogging.LazyLogging

import java.io.File
import java.util
import java.util.concurrent.TimeUnit
import scala.collection.JavaConverters._
import scala.concurrent.ExecutionContext
import scala.util.Try

class AWSCloudProvider(metricsHelper: MetricsHelper, logTag: String, cfg: BaseAuthConfig, srcCfg: Map[String, AnyRef],
                       cloudCfg: Map[String, AnyRef],
                       emrClientArg: Option[AmazonElasticMapReduce] = None,
                       secretsMgr: Option[AWSSecretsManager] = None,
                       s3ClientArg: Option[AmazonS3] = None,
                       sleepTimeMilli: Int,
                       maxApiRetries: Int,
                       adminApiKey: String) extends CloudProvider(cfg) with LazyLogging {
  override val cloudName: String = "AWS"
  private val EMR_VERSION = "emr-6.7.0"
  private val NEXLA_API_KEY_SECRET = "nexla.admin.api.key"
  implicit val ec: ExecutionContext = ExecutionContext.global
  private val awsCfg: AwsCloudSpecificCfg = AwsCloudSpecificCfg(cloudCfg)

  private def getAwsCreds(useDataCredentials: Boolean): AWSCredentialsProvider = {
    if (useDataCredentials) {
      buildFromNexlaCreds(cfg.asAWS())
    } else {
      roleCredentialsProvider(awsCfg.roleWithEMRAccess, awsCfg.region, "Nexla-Spark-Pipeline-Runner-" + Thread.currentThread().getId)
    }
  }

  private lazy val emrClient: AmazonElasticMapReduce = {
    if (emrClientArg.isDefined) {
      emrClientArg.get
    } else {
      getEMRClient(getAwsCreds(false), Regions.fromName(awsCfg.region))
    }
  }

  private lazy val destinationS3Client: AmazonS3 = {
    if (s3ClientArg.isDefined) {
      s3ClientArg.get
    } else {
      // todo: currently we support only the same region, as for EMR
      getS3Client(getAwsCreds(false), Regions.fromName(awsCfg.region))
    }
  }

  private val emrStatusRetryer: Retryer[DescribeClusterResult] = RetryerBuilder
    .newBuilder[DescribeClusterResult]()
    .withStopStrategy(new StopStrategy {
      override def shouldStop(failedAttempt: Attempt[_]): Boolean = {
        if (failedAttempt.getAttemptNumber >= maxApiRetries) {
          true
        } else {
          val castValue = failedAttempt.asInstanceOf[Attempt[DescribeClusterResult]]
          val terminalClusterStates = util.EnumSet.of(ClusterState.TERMINATED, ClusterState.TERMINATING, ClusterState.TERMINATED_WITH_ERRORS)
          val currentClusterState = ClusterState.fromValue(castValue.get().getCluster.getStatus.getState)

          castValue.hasResult && terminalClusterStates.contains(currentClusterState)
        }
      }
    })
    .retryIfResult(arpt => {
      val expectedStates = util.EnumSet.of(ClusterState.RUNNING, ClusterState.WAITING)
      val currentState = ClusterState.fromValue(arpt.getCluster.getStatus.getState)
      !expectedStates.contains(currentState)
    })
    .withWaitStrategy(WaitStrategies.fixedWait(sleepTimeMilli, TimeUnit.MILLISECONDS))
    .withRetryListener(new RetryListener {
      override def onRetry[V](attempt: Attempt[V]): Unit = {
        if (attempt.hasException) {
          logger.debug(s"got error on attempt $attempt, cause ${attempt.getExceptionCause}")
        } else {
          val clusterState = attempt.get().asInstanceOf[DescribeClusterResult].getCluster.getStatus
          logger.debug(s"retrying because cluster state/status $clusterState is not RUNNING yet")
        }
      }
    })
    .build()

  private val jobStatusRetryer: Retryer[DescribeStepResult] = RetryerBuilder.newBuilder[DescribeStepResult]()
    .withStopStrategy(new StopStrategy {
      override def shouldStop(failedAttempt: Attempt[_]): Boolean = {
        if (failedAttempt.getAttemptNumber >= maxApiRetries) {
          true
        } else {
          val castValue = failedAttempt.asInstanceOf[Attempt[DescribeStepResult]]
          val terminalStepStates = util.EnumSet.of(
            StepState.CANCELLED, StepState.COMPLETED,
            StepState.FAILED, StepState.INTERRUPTED
          )
          val currentClusterState = StepState.fromValue(castValue.get().getStep.getStatus.getState)

          castValue.hasResult && terminalStepStates.contains(currentClusterState)
        }
      }
    })
    .retryIfResult(arpt => {
      val inFlightStates = util.EnumSet.of(StepState.PENDING, StepState.RUNNING, StepState.CANCEL_PENDING)
      val currentState = StepState.fromValue(arpt.getStep.getStatus.getState)
      inFlightStates.contains(currentState)
    })
    .withWaitStrategy(WaitStrategies.fixedWait(sleepTimeMilli, TimeUnit.MILLISECONDS))
    .withRetryListener(new RetryListener {
      override def onRetry[V](attempt: Attempt[V]): Unit = {
        if (attempt.hasException) {
          logger.error(s"got error on attempt $attempt, cause ${attempt.getExceptionCause}")
        } else {
          val stepState = attempt.get().asInstanceOf[DescribeStepResult].getStep.getStatus
          logger.debug(s"retrying because step $stepState has not reached terminal status yet")
        }
      }
    })
    .build()

  override def createCluster(): ClusterResponse = {
    val clusterConfig = Utils.getClusterConfig(logTag, awsCfg, srcCfg)
    val hive = new Application().withName("Hive")
    val spark = new Application().withName("Spark")
    val ganglia = new Application().withName("Ganglia")
    val clusterName = if (clusterConfig.name.isDefined) {
      clusterConfig.name.get
    } else {
      s"Nexla-Spark-Pipeline-${System.currentTimeMillis()}"
    }

    val hdfsProps: Map[String, String] = Map(
      "dfs.client.use.datanode.hostname" -> "true", "dfs.datanode.use.datanode.hostname" -> "true")
    val javaHome11: Map[String, String] = Map(
      "JAVA_HOME" -> "/usr/lib/jvm/java-11-amazon-corretto.x86_64",
      "HADOOP_OPTS" -> "-XX:+IgnoreUnrecognizedVMOptions"
    )
    val sparkDefaultEnvs: Map[String, String] = Map(
      "spark.executorEnv.JAVA_HOME" -> "/usr/lib/jvm/java-11-amazon-corretto.x86_64",
      "spark.driver.defaultJavaOptions" -> "-XX:+IgnoreUnrecognizedVMOptions",
      "spark.executor.defaultJavaOptions" -> "-XX:+IgnoreUnrecognizedVMOptions"
    )

    val sparkResourceAllocation: Map[String, String] = Map("maximizeResourceAllocation" -> "true")

    val hdfsConf: Configuration = new Configuration().withClassification("hdfs-site")
      .withProperties(hdfsProps.asJava)
    val hadoopJdk11CfgsInternal = new Configuration().withClassification("export").withConfigurations()
      .withProperties(javaHome11.asJava)
    val hadoopJdk11CfgExternal = new Configuration().withClassification("hadoop-env")
      .withConfigurations(hadoopJdk11CfgsInternal)

    val sparkJdk11External = new Configuration().withClassification("spark-env")
      .withConfigurations(hadoopJdk11CfgsInternal)

    val sparkDefaultsJdk11External = new Configuration().withClassification("spark-defaults")
      .withProperties(sparkDefaultEnvs.asJava)

    val sparkMaximizeResourceAllocation = new Configuration().withClassification("spark")
      .withProperties(sparkResourceAllocation.asJava)

    val jobFlowInstancesConfig = new JobFlowInstancesConfig().withEc2SubnetId(awsCfg.clusterVmSubnet)

    // set the ec2 key name if it's defined
    awsCfg.sshKeyName.foreach(sshKeyName => jobFlowInstancesConfig.setEc2KeyName(sshKeyName))
    val autoTerminationPolicy = new AutoTerminationPolicy
    // max idle period: 30 minutes in seconds
    autoTerminationPolicy.setIdleTimeout(60 * 30)

    val request: RunJobFlowRequest = new RunJobFlowRequest()
      .withName(clusterName)
      .withReleaseLabel(EMR_VERSION)
      .withApplications(hive, spark, ganglia)
      .withLogUri(s"s3://nexla-emr-test-bucket/logs/$clusterName") // a URI in S3 for log files is required when debugging is enabled
      .withServiceRole(awsCfg.clusterServiceRole)
      .withAutoTerminationPolicy(autoTerminationPolicy)
      .withConfigurations(hdfsConf, hadoopJdk11CfgExternal, sparkJdk11External, sparkDefaultsJdk11External, sparkMaximizeResourceAllocation)
      .withJobFlowRole(awsCfg.clusterEntityRole)
      .withInstances(
        jobFlowInstancesConfig
          .withInstanceCount(clusterConfig.mainNodes + clusterConfig.workerNodes)
          // this needs to be true because we may reuse the cluster between runs
          .withKeepJobFlowAliveWhenNoSteps(true)
          .withMasterInstanceType(toAmazonVmType(clusterConfig.mainNodeVCPU))
          .withSlaveInstanceType(toAmazonVmType(clusterConfig.workerNodeVCPU))
      )

    // secrets are also created at this stage
    val awsSecretMgr = secretsMgr.getOrElse(AWSSecretsManagerClientBuilder.standard()
      .withRegion(awsCfg.region)
      .withCredentials(getAwsCreds(true))
      .build())
    logger.info("Verifying necessary secrets")
    val maybeExistingApiKey: Option[String] = Option(awsSecretMgr
      .getSecretValue(new GetSecretValueRequest().withSecretId(NEXLA_API_KEY_SECRET)).getSecretString)
    if (maybeExistingApiKey.isDefined) {
      logger.info("Existing Nexla Admin API Key found in AWS Secrets Manager, updating it to be sure it's actual")
      val updateKeyRequest = new UpdateSecretRequest()
      updateKeyRequest.setSecretId(NEXLA_API_KEY_SECRET)
      updateKeyRequest.setSecretString(adminApiKey)
      updateKeyRequest.setDescription("Nexla Admin API Key (necessary for the Spark pipeline")
      awsSecretMgr.updateSecret(updateKeyRequest)
    } else {
      logger.info("No Nexla Admin API Keys found in AWS Secrets Manager, creating new entry")
      val createSecret = new CreateSecretRequest()
        .withName(NEXLA_API_KEY_SECRET)
        .withSecretString(adminApiKey)
      awsSecretMgr.createSecret(createSecret)
    }
    logger.info("AWS Secrets Manager entry for Nexla Admin API Key is now created")

    logger.info(s"Cluster creation request prepared: [$request]")
    logger.info("Sending to AWS")
    val start = System.currentTimeMillis()
    try {
      val response = emrClient.runJobFlow(request)
      metricsHelper.incrementClusterSpawns()
      logger.info("CLUSTER CREATION REQUEST SENT: The cluster ID is " + response.getJobFlowId)
      val describeReq: DescribeClusterRequest = new DescribeClusterRequest()
      describeReq.setClusterId(response.getJobFlowId)

      // that may take up to 10 minutes worst case
      val result = emrStatusRetryer.call(() => emrClient.describeCluster(describeReq))
      val end = System.currentTimeMillis()
      metricsHelper.gaugeClusterCreation(cloudName, end - start)

      ClusterResponse(result.getCluster.getId,
        List(ClusterNode("N/A", result.getCluster.getMasterPublicDnsName, result.getCluster.getMasterPublicDnsName)),
        // it's safe to put empty lists here, as we don't have any jobs yet anyway
        List(), List(), List()
      )
    } catch {
      case e: Exception =>
        val end = System.currentTimeMillis()
        metricsHelper.gaugeClusterCreation(cloudName, end - start)
        throw new IllegalArgumentException("cannot create AWS EMR cluster", e)
      case t: Throwable =>
        val end = System.currentTimeMillis()
        metricsHelper.gaugeClusterCreation(cloudName, end - start)
        throw new IllegalArgumentException("unknown issue during cluster creation", t)
    }
  }

  override def stopCluster(clusterId: String): Unit = {
    val terminateRequest = new TerminateJobFlowsRequest().withJobFlowIds(clusterId)
    val start = System.currentTimeMillis()
    val response = emrClient.terminateJobFlows(terminateRequest)
    logger.info(s"Cluster terminate request issued for cluster ID [$clusterId]")
    metricsHelper.incrementClusterTerminates()
    val end = System.currentTimeMillis()
    metricsHelper.gaugeClusterTermination(cloudName, end - start)
    logger.info(s"Response received: [$response]")
  }

  override def checkExistingCluster(clusterId: String): ClusterResponse = {
    val describeReq: DescribeClusterRequest = new DescribeClusterRequest()
    describeReq.setClusterId(clusterId)
    val serviceResponse = emrClient.describeCluster(describeReq)

    val currentClusterState = ClusterState.fromValue(serviceResponse.getCluster.getStatus.getState)
    val deadStates = util.EnumSet.of(ClusterState.TERMINATED, ClusterState.TERMINATING, ClusterState.TERMINATED_WITH_ERRORS)

    if (deadStates.contains(currentClusterState)) {
      throw new IllegalArgumentException("This EMR cluster is unusable, either terminating or terminated already")
    } else {
      val req = new ListStepsRequest()
      req.setClusterId(clusterId)
      val runningJobs = emrClient.listSteps(req).getSteps.asScala.map(step => toJobDef(step)).toList
      ClusterResponse(serviceResponse.getCluster.getId,
        List(ClusterNode("N/A", serviceResponse.getCluster.getMasterPublicDnsName, serviceResponse.getCluster.getMasterPublicDnsName)),
        List(),
        List(),
        runningJobs
      )
    }
  }

  override def trackUntilTerminalState(jobId: String, clusterId: String): JobDefinition = {
    val req = new DescribeStepRequest()
    req.setClusterId(clusterId)
    req.setStepId(jobId)
    logger.debug("Starting to track until terminal state: [job-id: {} cluster-id: {}]", jobId, clusterId)
    val start = System.currentTimeMillis()
    val resp = try {
      jobStatusRetryer.call( () => emrClient.describeStep(req) )
    } catch {
      case e: Exception =>
        logger.error(s"error during tracking jobId [$jobId] until terminal state", e)
        val end = System.currentTimeMillis()
        metricsHelper.gaugeTaskRunTime(end - start)
        throw e
    }
    val end = System.currentTimeMillis()
    metricsHelper.gaugeTaskRunTime(end - start)

    // can take a while
    logger.debug("Finished tracking until terminal state: [job-id: {} cluster-id: {}]", jobId, clusterId)
    AWSTools.toJobDef(resp.getStep)
  }

  private def copyFileIfNotExists(dstCloudJarLocation: String): Unit = {
    // first, check destination using client's AWS credentials
    val uri: AmazonS3URI = new AmazonS3URI(dstCloudJarLocation)
    val jarExists = destinationS3Client.doesObjectExist(uri.getBucket, uri.getKey)
    if (jarExists) {
      logger.warn(s"Target jar at specified path [$dstCloudJarLocation] already exists, checking its md5")
      val objMetadata = destinationS3Client.getObjectMetadata(uri.getBucket, uri.getKey)
      val currentFileMd5 = getMd5(new File(LOCAL_JAR_FILE_PATH))
      if (objMetadata.getContentMD5.toLowerCase.equals(currentFileMd5)) {
        logger.info("md5 digest matches, using the existing file")
      } else {
        logger.warn("replacing it with a new file")
        val agentJarPath = LOCAL_JAR_FILE_PATH
        val putObjectRequest = new PutObjectRequest(uri.getBucket, uri.getKey, new File(agentJarPath))
        destinationS3Client.putObject(putObjectRequest)
        logger.info(s"File successfully uploaded to specified folder [$dstCloudJarLocation]")
      }
    } else {
      logger.info(s"Target jar does not exist at specified path [$dstCloudJarLocation], uploading it")
      // our docker image has this file built-in
      val agentJarPath = LOCAL_JAR_FILE_PATH
      val putObjectRequest = new PutObjectRequest(uri.getBucket, uri.getKey, new File(agentJarPath))
      destinationS3Client.putObject(putObjectRequest)
      logger.info(s"File successfully uploaded to specified folder [$dstCloudJarLocation]")
    }
  }

  private def getMd5(file: File): String = {
    val byteSource = com.google.common.io.Files.asByteSource(file)
    val hc = byteSource.hash(Hashing.md5)
    hc.toString.toLowerCase
  }

  override def runSparkSubmitStep(clusterId: String, sinkId: Int, partitionToHandle: String, nexlaEnv: String, runId: Long): List[JobDefinition] = {
    // check if our jar is already in target place
    // if not, upload it. remember, that it has to be built in advance, with built-in shaded admin API jar file.
    // after making sure file's there, send this request
    copyFileIfNotExists(awsCfg.cloudJarLocation)

    val req: AddJobFlowStepsRequest = new AddJobFlowStepsRequest()
    req.withJobFlowId(clusterId)

    val stepConfigs: util.ArrayList[StepConfig] = new java.util.ArrayList[StepConfig]()
    val mainArgs = Seq(
      "spark-submit",
      "--class", "com.nexla.spark_agent.SparkAgentApp",
      "--jars", awsCfg.cloudJarLocation,
      "--conf", "spark.hadoop.fs.s3a.fast.upload.buffer=bytebuffer",
      "--conf", s"spark.jars.packages=$scalaLoggingJar,$guavaJar,$awsHadoopJar,$awsSdkBundleJar"
    )
    val args: java.util.ArrayList[String] = new util.ArrayList[String]()
    args.addAll(mainArgs.toList.asJava)

    if (Utils.isDebugMode(srcCfg)) {
      args.addAll(Seq(
        "--conf", "spark.executor.extraJavaOptions=-DdebugEnabled",
        "--conf", "spark.driver.extraJavaOptions=-DdebugEnabled"
      ).toList.asJava)
    }

    args.addAll(Seq(
      awsCfg.cloudJarLocation, String.valueOf(sinkId), partitionToHandle, nexlaEnv, String.valueOf(runId)
    ).toList.asJava)

    val sparkStepConf: HadoopJarStepConfig = new HadoopJarStepConfig()
      .withJar("command-runner.jar")
      .withArgs(args.asScala: _*)

    val partitionIsInt = Try(partitionToHandle.toInt).isSuccess

    val sparkStep: StepConfig = new StepConfig()
      .withName(s"Nexla sink [$sinkId], source ${if (partitionIsInt) "max src files per run" else "partition" } [$partitionToHandle]")
      .withActionOnFailure("CONTINUE")
      .withHadoopJarStep(sparkStepConf)

    stepConfigs.add(sparkStep)
    req.withSteps(stepConfigs)
    emrClient.addJobFlowSteps(req)
    // and now list what's running there
    val listJobsRunning = new ListStepsRequest()
    listJobsRunning.setClusterId(clusterId)
    emrClient.listSteps(listJobsRunning).getSteps.asScala.map(step => toJobDef(step)).toList
  }

  private val scalaLoggingJar: String = "com.typesafe.scala-logging:scala-logging_2.12:3.9.3"
  private val guavaJar: String = "com.google.guava:guava:31.1-jre"
  private val awsHadoopJar: String = "org.apache.hadoop:hadoop-aws:3.2.2"
  private val awsSdkBundleJar: String = "com.amazonaws:aws-java-sdk-bundle:1.12.170"
  private val LOCAL_JAR_FILE_PATH: String = if (Utils.devMode()) {
    System.getProperty("user.dir") + "/cloud-job-connector/target/nexla-spark-agent.jar"
  } else {
    "/nexla-spark-agent.jar"
  }
}

object AWSCloudProvider {
  private val defaultSleep: Int = 30 * 1000
  private val defaultMaxApiRetries: Int = 256

  def apply(metricsHelper: MetricsHelper, logTag: String, cfg: BaseAuthConfig,
            srcCfg: Map[String, AnyRef], extraCfg: Map[String, AnyRef], apiKey: String): AWSCloudProvider = {
    this (metricsHelper, logTag, cfg, srcCfg, extraCfg, apiKey,
      None, None, None // empty options for the possible mocks
    )
  }

  def apply(metricsHelper: MetricsHelper,
            logTag: String,
            cfg: BaseAuthConfig,
            srcCfg: Map[String, AnyRef],
            extraCfg: Map[String, AnyRef],
            apiKey: String,
            emrClientArg: Option[AmazonElasticMapReduce] = None,
            secretsMgrArg: Option[AWSSecretsManager] = None,
            s3ClientArg: Option[AmazonS3] = None,
            sleepTimeMilli: Int = defaultSleep,
            maxApiRetries: Int = defaultMaxApiRetries): AWSCloudProvider = {
    new AWSCloudProvider(metricsHelper, logTag, cfg, srcCfg, extraCfg, emrClientArg, secretsMgrArg, s3ClientArg, sleepTimeMilli, maxApiRetries, apiKey)
  }
}
