package com.nexla.cloud.job.connector.pipeline

import akka.actor.ActorSystem
import akka.stream.Materializer
import com.nexla.admin.client.AdminApiClient
import com.nexla.cloud.job.connector.compat.{BasicPipelineRegistry, Pipeline}
import com.nexla.cloud.job.connector.infra.ClusterTracker
import com.nexla.cloud.job.connector.job.JobSender
import com.nexla.cloud.job.connector.{AppProps, MetricsHelper, Utils}
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity
import com.nexla.common.{NexlaDataCredentials, Resource, ResourceType}
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.sc.api.helper.AdminApiHelper
import com.nexla.sc.client.job_scheduler.TaskRequest.TaskId
import com.nexla.sc.client.listing.ListingAppClient

import java.net.URI
import java.util
import scala.concurrent.ExecutionContext

class CloudJobPipelineRegistry(adminApiClient: AdminApiClient,
                               listingClient: ListingAppClient,
                               appMessageProducer: NexlaMessageProducer,
                               clusterTracker: ClusterTracker,
                               metricsHelper: MetricsHelper,
                               props: AppProps)(implicit system: ActorSystem, mat: Materializer, ec: ExecutionContext) extends BasicPipelineRegistry {

  /** the one which needs to be overridden by clients, depending on the pipelines they may or may not have */
  override def createPipeline(sinkId: Int, runId: Long, taskId: TaskId): Pipeline = {
    logger.info("Creating Cloud Job Pipeline for sinkId {}, runId {}, taskId {}", sinkId, runId, taskId)
    val adminApiHelper = new AdminApiHelper(adminApiClient)
    val dataSink = adminApiClient.getDataSink(sinkId).get()
    val dataSource = adminApiHelper.getParentSource(dataSink.getDataSetId)

    val msg = Utils.prepareFlowInsightsMsg(
      dataSource.getOrg.getId,
      s"Spinning up containers for data source with ID [${dataSource.getId}]",
      runId,
      new Resource(dataSource.getId, ResourceType.SOURCE),
      NexlaMonitoringLogSeverity.INFO)
    appMessageProducer.publishMonitoringLog(msg)
    // these are the credentials for the source itself, but for the cloud we may have more
    val credsMap: java.util.Map[String, String] = NexlaDataCredentials.getCreds(props.decryptKey,
      dataSource.getDataCredentials.getCredentialsEnc, dataSource.getDataCredentials.getCredentialsEncIv)
    val logTag = taskId
    // these are creds for the cloud provider itself
    val extraCredsOpt = Option(dataSource.getSourceConfig.get("cloud.credentials.id"))
      .map(crId => adminApiClient.getDataCredentials(crId.toString.toInt).get())

    val extraCreds: java.util.Map[String, String] = if (extraCredsOpt.isEmpty) {
      logger.warn(s"cloud.credentials.id is empty for the source ${dataSource.getId}, trying to infer the info from Databricks JDBC URL.")
      val inferredDatabricksCredentialsUrl = credsMap.get("url")
      if (inferredDatabricksCredentialsUrl == null) {
        throw new IllegalArgumentException(s"cloud.credentials.id is not present in the source [${dataSource.getId}] configuration, cannot proceed.")
      }
      val jmap = new util.HashMap[String, String]()
      jmap.put("cloud.cluster.provider", "databricks-aws")
      jmap.put("cloud.jar.location", "s3://qa.nexla.com/akotliar/emr_jar/2.16.2-databricks.jar") // TODO: fetch from the ui cfg

      val url = new URI(new URI(inferredDatabricksCredentialsUrl).getSchemeSpecificPart)
      jmap.put("cloud.databricks.workspace.host", "https://" + url.getHost)
      val patTokenParam = url.getPath.split(";").find(_.contains("PWD")).get
      val patToken = patTokenParam.split("=").last
      jmap.put("cloud.databricks.token", patToken)

      jmap
    } else {
      val extraCreds = extraCredsOpt.get
      NexlaDataCredentials.getCreds(props.decryptKey, extraCreds.getCredentialsEnc, extraCreds.getCredentialsEncIv)
    }

    val cloudProviderForPipeline = Utils.getCloudProvider(metricsHelper, credsMap, extraCreds, dataSource, logTag, props)
    val clusterAuthConfig = Utils.enrichComputeClusterAuthConfig(cloudProviderForPipeline.cloudName, dataSource, credsMap, extraCreds)
    val jobSender = JobSender(cloudProviderForPipeline, logTag, metricsHelper)
    new CloudJobPipeline(taskId,
      runId,
      dataSource.getId.toInt,
      sinkId,
      clusterAuthConfig,
      adminApiClient,
      listingClient,
      appMessageProducer, cloudProviderForPipeline, jobSender, clusterTracker, metricsHelper, props)
  }
}
