package com.nexla.cloud.job.connector.cloud.gcp

import com.nexla.cloud.job.connector.cloud.{CloudProvider, ClusterConfig, ClusterResponse, JobDefinition}
import com.nexla.connector.config.rest.BaseAuthConfig

class GCPCloudProvider(cfg: BaseAuthConfig) extends CloudProvider(cfg) {
  override val cloudName: String = "GCP"

  override def createCluster(): ClusterResponse = ???
  override def stopCluster(clusterId: String): Unit = ???
  override def checkExistingCluster(clusterId: String): ClusterResponse = ???
  override def trackUntilTerminalState(jobId: String, clusterId: String): JobDefinition = ???
  override def runSparkSubmitStep(clusterId: String, sinkId: Int, partitionToHandle: String, nexlaEnv: String, runId: Long): List[JobDefinition] = ???
}
