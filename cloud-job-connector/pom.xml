<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<artifactId>cloud-job-connector</artifactId>

	<parent>
		<groupId>com.nexla</groupId>
		<artifactId>backend-connectors</artifactId>
		<version>3.2.1-SNAPSHOT</version>
	</parent>

	<properties>
		<aws.emr.sdk.version>1.12.112</aws.emr.sdk.version>
		<databricks.sdk.version>0.19.0</databricks.sdk.version>
	</properties>

	<dependencies>

		<dependency>
			<groupId>com.enragedginger</groupId>
			<artifactId>akka-quartz-scheduler_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-kms</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-s3</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-dynamodb</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-secretsmanager</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-sts</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-rds</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common-sc</artifactId>
            <version>${nexla-backend-common.version}</version>
		</dependency>
		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-stream_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-stream-kafka_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-utils</artifactId>
            <version>${nexla-backend-common.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>transform-service</artifactId>
            <version>${nexla-backend-transform.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>log4j-over-slf4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>


		<!-- Enable JSON logging -->
		<dependency>
			<groupId>net.logstash.logback</groupId>
			<artifactId>logstash-logback-encoder</artifactId>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common</artifactId>
            <version>${nexla-backend-common.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka-clients</artifactId>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>connector-properties</artifactId>
			<version>${project.version}</version>
		</dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>deprecated-db-schema</artifactId>
            <version>${nexla-backend-common.version}</version>
        </dependency>

        <dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common-connector</artifactId>
            <version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>file-source-sink</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>connector-test</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-emr</artifactId>
			<version>${aws.emr.sdk.version}</version>
		</dependency>
		<dependency>
			<groupId>com.databricks</groupId>
			<artifactId>databricks-sdk-java</artifactId>
			<version>${databricks.sdk.version}</version>
		</dependency>

		<!-- is not necessary in code, but required to assemble docker image -->
		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>nexla-spark-agent</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- for connecting to listing db -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>${mysql-connector-java.version}</version>
		</dependency>
		<dependency>
			<groupId>com.mchange</groupId>
			<artifactId>c3p0</artifactId>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy</id>
						<phase>process-test-resources</phase>
						<goals>
							<goal>copy</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<artifactItems>
						<artifactItem>
							<groupId>com.nexla</groupId>
							<artifactId>nexla-spark-agent</artifactId>
							<version>${nexla.version}</version>
							<type>jar</type>
							<classifier>jar-with-dependencies</classifier>
							<overWrite>true</overWrite>
							<outputDirectory>${project.build.directory}/</outputDirectory>
							<destFileName>nexla-spark-agent.jar</destFileName>
						</artifactItem>
					</artifactItems>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
