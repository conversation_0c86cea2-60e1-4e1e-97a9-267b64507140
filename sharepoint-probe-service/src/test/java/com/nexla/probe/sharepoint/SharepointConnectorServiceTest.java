package com.nexla.probe.sharepoint;

import com.microsoft.graph.drives.DrivesRequestBuilder;
import com.microsoft.graph.drives.item.DriveItemRequestBuilder;
import com.microsoft.graph.drives.item.items.ItemsRequestBuilder;
import com.microsoft.graph.drives.item.items.item.DriveItemItemRequestBuilder;
import com.microsoft.graph.drives.item.items.item.children.ChildrenRequestBuilder;
import com.microsoft.graph.drives.item.items.item.content.ContentRequestBuilder;
import com.microsoft.graph.drives.item.root.RootRequestBuilder;
import com.microsoft.graph.models.*;
import com.microsoft.graph.models.odataerrors.ODataError;
import com.microsoft.graph.serviceclient.GraphServiceClient;
import com.microsoft.graph.sites.SitesRequestBuilder;
import com.microsoft.graph.sites.item.SiteItemRequestBuilder;
import com.microsoft.kiota.RequestAdapter;
import com.microsoft.kiota.RequestInformation;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.ListingResourceType;
import com.nexla.common.ResourceType;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.sharepoint.SharepointAuthConfig;
import com.nexla.listing.client.ListingClient;
import com.nexla.test.UnitTests;
import lombok.SneakyThrows;
import org.apache.commons.lang3.NotImplementedException;
import org.junit.experimental.categories.Category;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.FileInputStream;
import java.net.URI;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.nexla.common.probe.ProbeControllerConstants.*;
import static com.nexla.common.probe.ProbeControllerConstants.MIME_TYPE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@Category(UnitTests.class)
@TestInstance(TestInstance.Lifecycle.PER_METHOD)
class SharepointConnectorServiceTest {

    private final Integer CRED_ID = 1;

    @Mock
    private AdminApiClient adminApiClientMock;

    @Mock
    private ListingClient listingClientMock;

    @Mock
    private GraphServiceClient graphServiceClientMock;

    @Mock
    private FileConnectorAuth fileConnectorAuthMock;

    @Mock
    private FileSourceConnectorConfig sourceConfigMock;

    @Mock
    private BaseAuthConfig authConfigMock;

    @Mock
    private SitesRequestBuilder sitesRequestBuilderMock;

    @Mock
    private SiteItemRequestBuilder siteItemRequestBuilderMock;

    @Mock
    private DrivesRequestBuilder drivesRequestBuilderMock;

    @Mock
    private com.microsoft.graph.sites.item.drives.DrivesRequestBuilder siteDrivesRequestBuilderMock;

    @Mock
    private DriveItemRequestBuilder driveItemRequestBuilderMock;

    @Mock
    private ItemsRequestBuilder itemsRequestBuilderMock;

    @Mock
    private DriveItemItemRequestBuilder driveItemItemRequestBuilderMock;

    @Mock
    private ContentRequestBuilder contentRequestBuilderMock;

    @Mock
    private RootRequestBuilder rootRequestBuilderMock;

    @Mock
    private ChildrenRequestBuilder childrenRequestBuilderMock;

    @Mock
    private RequestAdapter requestAdapterMock;

    @Mock
    private RequestInformation requestInformationMock;

    @InjectMocks
    private SharepointConnectorService service;

    @SneakyThrows
    @Test
    void testWriteInternal() {
        var filePath = getClass().getResource("/example.json").getPath();
        var file = new File(filePath);
        assertThrows(NotImplementedException.class, () -> service.writeInternal(fileConnectorAuthMock, "key", file));

        try (var fileInputStream = new FileInputStream(file)) {
            assertThrows(NotImplementedException.class, () -> service.writeInternal(fileConnectorAuthMock, "key", fileInputStream));
        }
    }

    @Test
    void testCheckWriteAccess() {
        assertThrows(NotImplementedException.class, () -> service.checkWriteAccess(null));
    }


    @SneakyThrows
    @Test
    void testReadInputStream() {
        var driveId = "driveId-123";
        var driveItemId = "driveItemId-123";
        var fullPath = "Sites/siteId-123/" + driveId;
        var filePath = getClass().getResource("/example.json").getPath();
        var file = new FileInputStream(filePath);
        var staticMock = mockStatic(GraphClientFactory.class);

        mockFileConnectorAuth(fullPath);
        when(graphServiceClientMock.drives()).thenReturn(drivesRequestBuilderMock);
        when(drivesRequestBuilderMock.byDriveId(driveId)).thenReturn(driveItemRequestBuilderMock);
        when((driveItemRequestBuilderMock).items()).thenReturn(itemsRequestBuilderMock);
        when(itemsRequestBuilderMock.byDriveItemId(driveItemId)).thenReturn(driveItemItemRequestBuilderMock);
        when(driveItemItemRequestBuilderMock.content()).thenReturn(contentRequestBuilderMock);
        when(contentRequestBuilderMock.get()).thenReturn(file);
        mockStaticMethods();

        var inputStream = service.readInputStreamInternal(fileConnectorAuthMock, withSlashPrefix(driveItemId));

        var streamExpected = new FileInputStream(filePath);
        assertEquals(streamExpected.read(), inputStream.read());

        verifyFileConnectorAuth();
        verify(graphServiceClientMock).drives();
        verify(drivesRequestBuilderMock).byDriveId(driveId);
        verify(driveItemRequestBuilderMock).items();
        verify(itemsRequestBuilderMock).byDriveItemId(driveItemId);
        verify(driveItemItemRequestBuilderMock).content();
        verify(contentRequestBuilderMock).get();
        verifyStaticMockAndClose(staticMock);
        streamExpected.close();
        file.close();
    }

    @Test
    void testDoesFileExistsInternalWhenFileExists() {
        var driveId = "driveId-123";
        var driveItemId = "driveItemId-123";
        var fullPath = "Sites/siteId-123/" + driveId;
        var staticMock = mockStatic(GraphClientFactory.class);

        mockFileConnectorAuth(fullPath);
        when(graphServiceClientMock.drives()).thenReturn(drivesRequestBuilderMock);
        when(drivesRequestBuilderMock.byDriveId(driveId)).thenReturn(driveItemRequestBuilderMock);
        when((driveItemRequestBuilderMock).items()).thenReturn(itemsRequestBuilderMock);
        when(itemsRequestBuilderMock.byDriveItemId(driveItemId)).thenReturn(driveItemItemRequestBuilderMock);
        when(driveItemItemRequestBuilderMock.get()).thenReturn(new DriveItem());
        mockStaticMethods();

        assertTrue(service.doesFileExistsInternal(fileConnectorAuthMock, driveItemId));

        verifyFileConnectorAuth();
        verify(graphServiceClientMock).drives();
        verify(drivesRequestBuilderMock).byDriveId(driveId);
        verify(driveItemRequestBuilderMock).items();
        verify(itemsRequestBuilderMock).byDriveItemId(driveItemId);
        verify(driveItemItemRequestBuilderMock).get();
        verifyStaticMockAndClose(staticMock);
    }

    @Test
    void testDoesFileExistsInternalWhenFileDoesNotExists() {
        var driveId = "driveId-123";
        var driveItemId = "driveItemId-123";
        var fullPath = "Sites/siteId-123/" + driveId;
        var staticMock = mockStatic(GraphClientFactory.class);

        mockFileConnectorAuth(fullPath);
        when(graphServiceClientMock.drives()).thenReturn(drivesRequestBuilderMock);
        when(drivesRequestBuilderMock.byDriveId(driveId)).thenReturn(driveItemRequestBuilderMock);
        when((driveItemRequestBuilderMock).items()).thenReturn(itemsRequestBuilderMock);
        when(itemsRequestBuilderMock.byDriveItemId(driveItemId)).thenReturn(driveItemItemRequestBuilderMock);
        when(driveItemItemRequestBuilderMock.get()).thenReturn(null);
        mockStaticMethods();

        assertFalse(service.doesFileExistsInternal(fileConnectorAuthMock, driveItemId));

        verifyFileConnectorAuth();
        verify(graphServiceClientMock).drives();
        verify(drivesRequestBuilderMock).byDriveId(driveId);
        verify(driveItemRequestBuilderMock).items();
        verify(itemsRequestBuilderMock).byDriveItemId(driveItemId);
        verify(driveItemItemRequestBuilderMock).get();
        verifyStaticMockAndClose(staticMock);
    }

    @Test
    void testDoesFileExistsInternalWhenTokenGetsRefreshed() {
        var driveId = "driveId-123";
        var driveItemId = "driveItemId-123";
        var fullPath = "Sites/siteId-123/" + driveId;
        var staticMock = mockStatic(GraphClientFactory.class);
        var oDataErrorUnauthorizedError = new ODataErrorUnauthorizedError();

        mockFileConnectorAuth(fullPath);
        when(graphServiceClientMock.drives()).thenReturn(drivesRequestBuilderMock);
        when(drivesRequestBuilderMock.byDriveId(anyString())).thenThrow(oDataErrorUnauthorizedError).thenReturn(driveItemRequestBuilderMock);
        when(driveItemRequestBuilderMock.items()).thenReturn(itemsRequestBuilderMock);
        when(itemsRequestBuilderMock.byDriveItemId(driveItemId)).thenReturn(driveItemItemRequestBuilderMock);
        when(driveItemItemRequestBuilderMock.get()).thenReturn(new DriveItem());
        when(listingClientMock.refreshRestToken(CRED_ID)).thenReturn(true);
        mockStaticMethods();

        assertTrue(service.doesFileExistsInternal(fileConnectorAuthMock, driveItemId));

        verifyFileConnectorAuth();
        verify(graphServiceClientMock, times(2)).drives();
        verify(drivesRequestBuilderMock, times(2)).byDriveId(driveId);
        verify(driveItemRequestBuilderMock).items();
        verify(itemsRequestBuilderMock).byDriveItemId(driveItemId);
        verify(driveItemItemRequestBuilderMock).get();
        verify(listingClientMock).refreshRestToken(CRED_ID);
        verify(adminApiClientMock).invalidate(CRED_ID, Optional.empty(), ResourceType.CREDENTIALS);
        verifyStaticMockAndClose(staticMock, 2);
    }

    @Test
    void testAuthenticateWhenSuccess() {
        var staticMock = mockStatic(GraphClientFactory.class);

        when(authConfigMock.getCredsId()).thenReturn(CRED_ID);
        when(authConfigMock.originals()).thenReturn(Map.of(SharepointAuthConfig.QUERY_PARAMETER, "search=*"));
        when(graphServiceClientMock.sites()).thenReturn(sitesRequestBuilderMock);
        mockRequestToGetAllSites();

        SiteCollectionResponse value = new SiteCollectionResponse();
        value.setValue(List.of(new Site()));

        when(requestAdapterMock.send(eq(requestInformationMock), any(), any()))
                .thenReturn(value);

        mockStaticMethods();

        var authenticate = service.authenticate(authConfigMock);

        assertTrue(authenticate.success);
        assertTrue(authenticate.message.isEmpty());
        assertTrue(authenticate.responseBody.isEmpty());

        verify(authConfigMock, atLeast(1)).getCredsId();
        verify(graphServiceClientMock).sites();
        verifyGetAllSitesMocks(1);
        verifyStaticMockAndClose(staticMock);
    }

    @Test
    void testAuthenticateWhenNoSitesFound() {
        var staticMock = mockStatic(GraphClientFactory.class);

        when(authConfigMock.getCredsId()).thenReturn(CRED_ID);
        when(authConfigMock.originals()).thenReturn(Map.of(SharepointAuthConfig.QUERY_PARAMETER, "search=*"));
        when(graphServiceClientMock.sites()).thenReturn(sitesRequestBuilderMock);
        mockRequestToGetAllSites();

        when(requestAdapterMock.send(eq(requestInformationMock), any(), any()))
                .thenReturn(new SiteCollectionResponse());

        mockStaticMethods();

        var authenticate = service.authenticate(authConfigMock);

        assertFalse(authenticate.success);
        assertEquals("No sites found", authenticate.message.get());

        verify(authConfigMock, atLeast(1)).getCredsId();
        verify(graphServiceClientMock).sites();
        verifyGetAllSitesMocks(1);
        verifyStaticMockAndClose(staticMock);
    }

    @SneakyThrows
    @Test
    void testAuthenticateWhenFailure() {
        var staticMock = mockStatic(GraphClientFactory.class);
        var exception = new RuntimeException("Unable to authenticate!");

        when(authConfigMock.getCredsId()).thenReturn(CRED_ID);
        when(authConfigMock.originals()).thenReturn(Map.of(SharepointAuthConfig.QUERY_PARAMETER, "search=*"));
        when(graphServiceClientMock.sites()).thenReturn(sitesRequestBuilderMock);
        mockRequestToGetAllSites();
        when(requestAdapterMock.send(eq(requestInformationMock), any(), any())).thenThrow(exception);
        mockStaticMethods();

        var authenticate = service.authenticate(authConfigMock);

        assertFalse(authenticate.success);
        assertTrue(authenticate.message.isPresent());
        assertEquals(exception.getMessage(), authenticate.message.get());
        assertTrue(authenticate.responseBody.isEmpty());

        verify(authConfigMock, times(3)).getCredsId();
        verify(graphServiceClientMock).sites();
        verifyGetAllSitesMocks(1);
        verifyStaticMockAndClose(staticMock);
    }

    @SneakyThrows
    private void verifyGetAllSitesMocks(int getRequestAdapterTimes) {
        verify(sitesRequestBuilderMock).toGetRequestInformation();
        verify(requestInformationMock).getUri();
        verify(graphServiceClientMock, times(getRequestAdapterTimes)).getRequestAdapter();
        verify(requestAdapterMock).send(eq(requestInformationMock), any(), any());
        verify(requestInformationMock).setUri(URI.create("https://graph.microsoft.com/v1.0/sites?search=*"));
    }

    @SneakyThrows
    private void mockRequestToGetAllSites() {
        when(sitesRequestBuilderMock.toGetRequestInformation()).thenReturn(requestInformationMock);
        when(requestInformationMock.getUri()).thenReturn(URI.create("https://graph.microsoft.com/v1.0/sites"));
        when(graphServiceClientMock.getRequestAdapter()).thenReturn(requestAdapterMock);
    }

    @SneakyThrows
    private void mockRequestToGetDriveChildren() {
        when(rootRequestBuilderMock.toGetRequestInformation()).thenReturn(requestInformationMock);
        when(requestInformationMock.getUri()).thenReturn(URI.create("https://graph.microsoft.com/v1.0/sites/drives"));
        when(graphServiceClientMock.getRequestAdapter()).thenReturn(requestAdapterMock);
    }

    @SneakyThrows
    private void verifyRequestToGetDriveChildren() {
        verify(rootRequestBuilderMock).toGetRequestInformation();
        verify(requestInformationMock).getUri();
        verify(graphServiceClientMock, times(2)).getRequestAdapter();
        verify(requestAdapterMock).send(eq(requestInformationMock), any(), any());
        verify(requestInformationMock).setUri(URI.create("https://graph.microsoft.com/v1.0/sites/drives/children"));
    }

    @Test
    void testListTopLevelBucketsSites() {
        var staticMock = mockStatic(GraphClientFactory.class);
        var siteCollectionResponse = mockSiteCollectionResponse();

        mockSourceConfig("/");
        when(graphServiceClientMock.sites()).thenReturn(sitesRequestBuilderMock);
        mockRequestToGetAllSites();
        when(requestAdapterMock.send(eq(requestInformationMock), any(), any())).thenReturn(siteCollectionResponse);
        mockStaticMethods();
        when(authConfigMock.originals()).thenReturn(Map.of(SharepointAuthConfig.QUERY_PARAMETER, "search=*"));

        var nexlaFiles = service.listTopLevelBuckets(sourceConfigMock).collect(Collectors.toList());
        var expectedType = ListingResourceType.FOLDER;

        assertEquals(2, nexlaFiles.size());
        var sitesFolder = nexlaFiles.get(0);
        var sites = "Sites";

        assertEquals(sites, sitesFolder.getFullPath());
        assertNull(sitesFolder.getCreatedAt());
        assertNull(sitesFolder.getLastModified());
        assertEquals(expectedType, sitesFolder.getType());
        assertNull(sitesFolder.getSize());
        assertTrue(sitesFolder.getMetadata().isPresent());
        assertMetadata(sitesFolder.getMetadata().get(), sites, sites, sites, expectedType);

        var nexlaFile = nexlaFiles.get(1);
        var site = siteCollectionResponse.getValue().get(0);

        assertEquals("Sites" + "/" + site.getId(), nexlaFile.getFullPath());
        assertEquals(site.getCreatedDateTime().toInstant().toEpochMilli(), nexlaFile.getCreatedAt());
        assertEquals(site.getLastModifiedDateTime().toInstant().toEpochMilli(), nexlaFile.getLastModified());
        assertEquals(expectedType, nexlaFile.getType());
        assertNull(nexlaFile.getSize());
        assertTrue(nexlaFile.getMetadata().isPresent());
        assertMetadata(nexlaFile.getMetadata().get(), site.getId(), sites + "/" + site.getId(), sites + "/" + site.getDisplayName(), expectedType);

        verifySourceConfig();
        verify(graphServiceClientMock).sites();
        verifyGetAllSitesMocks(2);
        verifyStaticMockAndClose(staticMock);
        invalidateIdToNameCache();
    }

    @Test
    void testListTopLevelBucketsDrives() {
        var fullPath = "Sites/1";
        var staticMock = mockStatic(GraphClientFactory.class);
        var driveCollectionResponse = mockDriveCollectionResponse();
        var siteMock = mockSite();

        mockSourceConfig(fullPath);
        when(graphServiceClientMock.sites()).thenReturn(sitesRequestBuilderMock);
        when(sitesRequestBuilderMock.bySiteId("1")).thenReturn(siteItemRequestBuilderMock);
        when(siteItemRequestBuilderMock.drives()).thenReturn(siteDrivesRequestBuilderMock);
        when(siteDrivesRequestBuilderMock.get()).thenReturn(driveCollectionResponse);
        when(siteItemRequestBuilderMock.get()).thenReturn(siteMock);
        when(graphServiceClientMock.getRequestAdapter()).thenReturn(requestAdapterMock);
        mockStaticMethods();

        var nexlaFiles = service.listTopLevelBuckets(sourceConfigMock).collect(Collectors.toList());
        var expectedType = ListingResourceType.FOLDER;

        assertEquals(1, nexlaFiles.size());
        var driveFolder = nexlaFiles.get(0);
        var drive = driveCollectionResponse.getValue().get(0);

        assertEquals(withSlashPrefix(drive.getId()), driveFolder.getFullPath());
        assertEquals(drive.getCreatedDateTime().toInstant().toEpochMilli(), driveFolder.getCreatedAt());
        assertEquals(drive.getLastModifiedDateTime().toInstant().toEpochMilli(), driveFolder.getLastModified());
        assertEquals(expectedType, driveFolder.getType());
        assertNull(driveFolder.getSize());
        assertTrue(driveFolder.getMetadata().isPresent());
        assertMetadata(driveFolder.getMetadata().get(), drive.getId(), fullPath + "/" + drive.getId(),  "Sites/Site_1_Display_Name/" + drive.getName(), expectedType);

        verifySourceConfig();
        verify(graphServiceClientMock, times(2)).sites();
        verify(sitesRequestBuilderMock, times(2)).bySiteId("1");
        verify(siteItemRequestBuilderMock).drives();
        verify(siteDrivesRequestBuilderMock).get();
        verify(siteItemRequestBuilderMock).get();
        verify(graphServiceClientMock).getRequestAdapter();

        verifyStaticMockAndClose(staticMock);
        invalidateIdToNameCache();
    }

    @Test
    void testListTopLevelBucketsDriveItems() {
        var fullPath = "Sites/1/12345";
        var driveId = "12345";

        var staticMock = mockStatic(GraphClientFactory.class);
        var driveItemCollectionResponseMock = mockDriveItemCollectionResponse();

        mockSourceConfig(fullPath);
        when(graphServiceClientMock.drives()).thenReturn(drivesRequestBuilderMock);
        when(drivesRequestBuilderMock.byDriveId(driveId)).thenReturn(driveItemRequestBuilderMock);
        when(driveItemRequestBuilderMock.root()).thenReturn(rootRequestBuilderMock);
        mockGetSiteName();
        mockGetDriveName(driveId);
        mockStaticMethods();
        mockRequestToGetDriveChildren();
        when(requestAdapterMock.send(eq(requestInformationMock), any(), any())).thenReturn(driveItemCollectionResponseMock);

        when(authConfigMock.getCredsId()).thenReturn(CRED_ID);

        var nexlaFiles = service.listTopLevelBuckets(sourceConfigMock).collect(Collectors.toList());

        assertEquals(2, nexlaFiles.size());
        var fileItem = nexlaFiles.get(0);
        var driveItemFile = driveItemCollectionResponseMock.getValue().get(0);

        assertEquals(withSlashPrefix(driveItemFile.getId()), fileItem.getFullPath());
        assertEquals(driveItemFile.getCreatedDateTime().toInstant().toEpochMilli(), fileItem.getCreatedAt());
        assertEquals(driveItemFile.getLastModifiedDateTime().toInstant().toEpochMilli(), fileItem.getLastModified());
        assertEquals(ListingResourceType.FILE, fileItem.getType());
        assertEquals(driveItemFile.getSize(), fileItem.getSize());
        assertEquals(driveItemFile.getFile().getHashes().getQuickXorHash(), fileItem.getMd5());
        assertTrue(fileItem.getMetadata().isPresent());
        assertMetadata(fileItem.getMetadata().get(), driveItemFile.getId(), fullPath + "/" + driveItemFile.getId(),  "Sites/Site_1_Display_Name/Drive_Nexla/" + driveItemFile.getName(), ListingResourceType.FILE);

        var folderItem = nexlaFiles.get(1);
        var driveItemFolder = driveItemCollectionResponseMock.getValue().get(1);

        assertEquals(withSlashPrefix(driveItemFolder.getId()), folderItem.getFullPath());
        assertEquals(driveItemFolder.getCreatedDateTime().toInstant().toEpochMilli(), folderItem.getCreatedAt());
        assertEquals(driveItemFolder.getLastModifiedDateTime().toInstant().toEpochMilli(), folderItem.getLastModified());
        assertEquals(ListingResourceType.FOLDER, folderItem.getType());
        assertEquals(driveItemFolder.getSize(), folderItem.getSize());
        assertNull(folderItem.getMd5());
        assertTrue(folderItem.getMetadata().isPresent());
        assertMetadata(folderItem.getMetadata().get(), driveItemFolder.getId(), fullPath + "/" + driveItemFolder.getId(),  "Sites/Site_1_Display_Name/Drive_Nexla/" + driveItemFolder.getName(), ListingResourceType.FOLDER);

        verifySourceConfig();
        verify(graphServiceClientMock, times(2)).drives();
        verify(drivesRequestBuilderMock, times(2)).byDriveId(driveId);
        verify(driveItemRequestBuilderMock).root();
        verify(driveItemRequestBuilderMock).get();
        verifyGetSiteName();
        verifyRequestToGetDriveChildren();

        verifyStaticMockAndClose(staticMock);
        invalidateIdToNameCache();
    }

    @Test
    void testListTopLevelBucketsDriveItemChildren() {
        var fullPath = "Sites/1/12345/45678";
        var driveId = "12345";
        var driveItemId = "45678";
        var staticMock = mockStatic(GraphClientFactory.class);
        var driveItemMock = mockDriveItemCollectionResponse();

        mockSourceConfig(fullPath);
        when(graphServiceClientMock.drives()).thenReturn(drivesRequestBuilderMock);
        when(drivesRequestBuilderMock.byDriveId(driveId)).thenReturn(driveItemRequestBuilderMock);
        when(driveItemRequestBuilderMock.items()).thenReturn(itemsRequestBuilderMock);
        when(itemsRequestBuilderMock.byDriveItemId(driveItemId)).thenReturn(driveItemItemRequestBuilderMock);
        when(driveItemItemRequestBuilderMock.children()).thenReturn(childrenRequestBuilderMock);
        when(childrenRequestBuilderMock.get()).thenReturn(driveItemMock);
        when(driveItemItemRequestBuilderMock.get()).thenReturn(mockDriveItemFolder());
        when(graphServiceClientMock.getRequestAdapter()).thenReturn(requestAdapterMock);
        mockGetSiteName();
        mockGetDriveName(driveId);

        mockStaticMethods();

        var nexlaFiles = service.listTopLevelBuckets(sourceConfigMock).collect(Collectors.toList());

        assertEquals(2, nexlaFiles.size());
        var fileItem = nexlaFiles.get(0);
        var driveItemFile = driveItemMock.getValue().get(0);

        assertEquals(withSlashPrefix(driveItemFile.getId()), fileItem.getFullPath());
        assertEquals(driveItemFile.getCreatedDateTime().toInstant().toEpochMilli(), fileItem.getCreatedAt());
        assertEquals(driveItemFile.getLastModifiedDateTime().toInstant().toEpochMilli(), fileItem.getLastModified());
        assertEquals(ListingResourceType.FILE, fileItem.getType());
        assertEquals(driveItemFile.getSize(), fileItem.getSize());
        assertEquals(driveItemFile.getFile().getHashes().getQuickXorHash(), fileItem.getMd5());
        assertTrue(fileItem.getMetadata().isPresent());
        assertMetadata(fileItem.getMetadata().get(), driveItemFile.getId(), fullPath + "/" + driveItemFile.getId(),  "Sites/Site_1_Display_Name/Drive_Nexla/Nexla/" + driveItemFile.getName(), ListingResourceType.FILE);

        verifySourceConfig();
        verify(graphServiceClientMock, times(3)).drives();
        verify(drivesRequestBuilderMock, times(3)).byDriveId(driveId);
        verify(driveItemRequestBuilderMock, times(2)).items();
        verify(itemsRequestBuilderMock, times(2)).byDriveItemId(driveItemId);
        verify(driveItemItemRequestBuilderMock).children();
        verify(childrenRequestBuilderMock).get();
        verify(driveItemItemRequestBuilderMock).get();
        verify(driveItemRequestBuilderMock).get();
        verify(graphServiceClientMock).getRequestAdapter();
        verifyGetSiteName();

        verifyStaticMockAndClose(staticMock);
        invalidateIdToNameCache();
    }

    @Test
    void testListTopLevelBucketsWhenInvalidPath() {
        var fullPath = "01STQH2DBPN5GUZO2JJFBKMSJ4GI75RWET";
        var staticMock = mockStatic(GraphClientFactory.class);

        mockSourceConfig(fullPath);
        mockStaticMethods();

        assertThrows(IllegalArgumentException.class, () -> service.listTopLevelBuckets(sourceConfigMock).collect(Collectors.toList()));

        verifySourceConfig();
        verify(graphServiceClientMock, never()).drives();
        verify(drivesRequestBuilderMock, never()).byDriveId(any());
        verify(driveItemRequestBuilderMock, never()).items();
        verify(itemsRequestBuilderMock, never()).byDriveItemId(any());
        verify(driveItemItemRequestBuilderMock, never()).children();
        verify(childrenRequestBuilderMock, never()).get();
        verify(driveItemItemRequestBuilderMock, never()).get();
        verify(driveItemRequestBuilderMock, never()).get();
        verifyStaticMockAndClose(staticMock);
    }

    private void verifySourceConfig() {
        verify(sourceConfigMock).getPath();
        verify(sourceConfigMock).getAuthConfig();
        verify(authConfigMock, atLeast(1)).getCredsId();
    }

    private void verifyFileConnectorAuth() {
        verify(fileConnectorAuthMock).getPath();
        verify(fileConnectorAuthMock).getAuthConfig();
        verify(authConfigMock).getCredsId();
    }

    private void mockSourceConfig(String fullPath) {
        when(sourceConfigMock.getPath()).thenReturn(fullPath);
        when(authConfigMock.getCredsId()).thenReturn(CRED_ID);
        when(sourceConfigMock.getAuthConfig()).thenReturn(authConfigMock);
    }

    private void mockFileConnectorAuth(String fullPath) {
        when(fileConnectorAuthMock.getPath()).thenReturn(fullPath);
        when(authConfigMock.getCredsId()).thenReturn(CRED_ID);
        when(fileConnectorAuthMock.getAuthConfig()).thenReturn(authConfigMock);
    }

    private void mockGetDriveName(String driveId) {
        when(graphServiceClientMock.drives()).thenReturn(drivesRequestBuilderMock);
        when(drivesRequestBuilderMock.byDriveId(driveId)).thenReturn(driveItemRequestBuilderMock);
        when(driveItemRequestBuilderMock.get()).thenReturn(mockDrive());
    }

    private void mockGetSiteName() {
        when(graphServiceClientMock.sites()).thenReturn(sitesRequestBuilderMock);
        when(sitesRequestBuilderMock.bySiteId("1")).thenReturn(siteItemRequestBuilderMock);
        when(siteItemRequestBuilderMock.get()).thenReturn(mockSite());
    }

    private void verifyGetSiteName() {
        verify(graphServiceClientMock).sites();
        verify(sitesRequestBuilderMock).bySiteId("1");
        verify(siteItemRequestBuilderMock).get();
    }

    private void mockStaticMethods() {
        when(GraphClientFactory.createClient(adminApiClientMock, null, CRED_ID)).thenReturn(graphServiceClientMock);
    }

    private void verifyStaticMockAndClose(MockedStatic<GraphClientFactory> staticMock) {
        staticMock.verify(() -> GraphClientFactory.createClient(adminApiClientMock, null, CRED_ID));
        staticMock.close();
    }

    private void verifyStaticMockAndClose(MockedStatic<GraphClientFactory> staticMock, int times) {
        staticMock.verify(times(2), () -> GraphClientFactory.createClient(adminApiClientMock, null, CRED_ID));
        staticMock.close();
    }

    private String withSlashPrefix(String id) {
        return "/" + id;
    }

    private void invalidateIdToNameCache() {
        SharepointConnectorService.ID_TO_NAMES.invalidateAll();
    }

    private void assertMetadata(Map<String, Object> metadata, String id, String treeifyPath, String displayPath, ListingResourceType type) {
        assertEquals(id, metadata.get(ID));
        assertEquals(treeifyPath, metadata.get(TREEIFY_PATH));
        assertEquals(displayPath, metadata.get(DISPLAY_PATH));
        assertEquals(type, metadata.get(MIME_TYPE));
    }

    private DriveCollectionResponse mockDriveCollectionResponse() {
        var drive = mockDrive();

        var driveCollectionResponse = new DriveCollectionResponse();
        driveCollectionResponse.setValue(List.of(drive));
        return driveCollectionResponse;
    }

    private DriveItemCollectionResponse mockDriveItemCollectionResponse() {
        var driveItemCollectionResponse = new DriveItemCollectionResponse();
        driveItemCollectionResponse.setValue(List.of(mockDriveItemFile(), mockDriveItemFolder()));
        return driveItemCollectionResponse;
    }

    private DriveItem mockDriveItemFile() {
        var file = new DriveItem();
        var f = new com.microsoft.graph.models.File();
        var hashes = new Hashes();
        hashes.setQuickXorHash("012345");
        f.setHashes(hashes);
        file.setFile(f);
        file.setId("654321");
        file.setName("filename.json");
        file.setCreatedDateTime(OffsetDateTime.now());
        file.setLastModifiedDateTime(OffsetDateTime.now());
        file.setSize(22L);
        return file;
    }

    private DriveItem mockDriveItemFolder() {
        var folder = new DriveItem();
        folder.setId("45678");
        folder.setName("Nexla");
        folder.setCreatedDateTime(OffsetDateTime.now());
        folder.setLastModifiedDateTime(OffsetDateTime.now());
        return folder;
    }

    private static Drive mockDrive() {
        var drive = new Drive();
        drive.setName("Drive_Nexla");
        drive.setId("12345");
        drive.setCreatedDateTime(OffsetDateTime.now());
        drive.setLastModifiedDateTime(OffsetDateTime.now());
        return drive;
    }

    private SiteCollectionResponse mockSiteCollectionResponse() {
        var site = mockSite();

        var siteCollectionResponse = new SiteCollectionResponse();
        siteCollectionResponse.setValue(List.of(site));
        return siteCollectionResponse;
    }


    private Site mockSite() {
        var site = new Site();
        site.setName("Site_1");
        site.setDisplayName("Site_1_Display_Name");
        site.setId("1");
        site.setCreatedDateTime(OffsetDateTime.now());
        site.setLastModifiedDateTime(OffsetDateTime.now());
        return site;
    }

    private static class ODataErrorUnauthorizedError extends ODataError {

        ODataErrorUnauthorizedError(){
            super();
            super.setResponseStatusCode(401);
        }

        @Override
        public String getMessage() {
            return "Unauthorized";
        }
    }

}