package com.nexla.probe.azure.datalake;

import com.azure.core.credential.TokenCredential;
import com.azure.core.util.Context;
import com.azure.storage.common.StorageSharedKeyCredential;
import com.azure.storage.file.datalake.*;
import com.azure.storage.file.datalake.models.ListPathsOptions;
import com.azure.storage.file.datalake.models.PathItem;
import com.azure.storage.file.datalake.models.PathProperties;
import com.drew.imaging.FileType;
import com.google.common.annotations.VisibleForTesting;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.ListingResource;
import com.nexla.common.ListingResourceType;
import com.nexla.common.NexlaBucket;
import com.nexla.common.NexlaFile;
import com.nexla.connector.config.file.AWSAuthConfig.BucketPrefix;
import com.nexla.connector.config.file.AzureAuthConfig;
import com.nexla.connector.config.file.DirScanningMode;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.file.service.FileConnectorService;
import com.nexla.file.service.FileDetails;
import com.nexla.file.service.FileWalk;
import com.nexla.listing.client.ListingClient;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.config.AbstractConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ForkJoinPool;
import java.util.function.Supplier;

import static com.azure.storage.common.StorageSharedKeyCredential.fromConnectionString;
import static com.nexla.common.ListingResourceType.FILE;
import static com.nexla.common.ListingResourceType.FOLDER;
import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;
import static com.nexla.connector.ConnectorService.AuthResponse.authError;
import static com.nexla.connector.config.file.AWSAuthConfig.toBucketPrefix;
import static com.nexla.connector.config.file.AzureServicePrincipalAuthHelper.buildClientCertificateCredential;
import static com.nexla.connector.config.file.AzureServicePrincipalAuthHelper.buildClientServiceCredential;
import static com.nexla.connector.config.file.DirScanningMode.BOTH;
import static com.nexla.connector.config.file.DirScanningMode.DIRECTORIES;
import static com.nexla.connector.config.file.DirScanningMode.FILES;
import static com.nexla.connector.config.file.S3Constants.NEXLA_TEMP_FILE_NAME;
import static com.nexla.file.service.FileWalk.walkFileTreeDfs;
import static com.nexla.probe.azure.blob.AzureBlobStoreConnectorService.toEpoch;
import static java.util.Optional.empty;
import static org.apache.commons.codec.binary.Base64.encodeBase64String;
import static org.apache.commons.lang.StringUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.removeStart;

public class AzureDataLakeConnectorService extends FileConnectorService<AzureAuthConfig> {

    private final Logger logger = LoggerFactory.getLogger(AzureDataLakeConnectorService.class);

    private static final ForkJoinPool EXECUTOR = new ForkJoinPool(30);
    private static final int LIST_PATHS_TIMEOUT_MINS = 5;
    private static final int LIST_PATHS_MAX_RECORDS = 5000;
    private static final int GET_PROPERTIES_TIMEOUT_SECONDS = 10;
    private static final OffsetDateTime DEFAULT_CREATED_MODIFIED_DATE = OffsetDateTime.parse("1970-01-01T00:00:00Z", DateTimeFormatter.ISO_OFFSET_DATE_TIME);
    private static final int MAX_GET_PROPERTIES_ATTEMPTS = 3;

    public AzureDataLakeConnectorService() {
        this(null, null, null);
    }

    public AzureDataLakeConnectorService(AdminApiClient adminApiClient, ListingClient listingClient, String credentialsDecryptKey) {
        super(adminApiClient, listingClient, credentialsDecryptKey);
    }

    public DataLakeServiceClient getDataLakeServiceClient(AzureAuthConfig authConfig) {
        DataLakeServiceClientBuilder builder = new DataLakeServiceClientBuilder();
        if (!isEmpty(authConfig.connectionString)) {
            builder.credential(fromConnectionString(authConfig.connectionString));
        }
        if (!isEmpty(authConfig.storageAccountName)) {
            builder.endpoint(getStorageAccountBaseUrl(authConfig.storageAccountName));
            if (!isEmpty(authConfig.storageAccountKey)) {
                builder.credential(new StorageSharedKeyCredential(authConfig.storageAccountName, authConfig.storageAccountKey));
            } else if (!isEmpty(authConfig.sasToken)) {
                builder.sasToken(authConfig.sasToken);
            } else if (!isEmpty(authConfig.clientId) && !isEmpty(authConfig.tenantId)) {
                TokenCredential tokenCredential;
                builder.endpoint(getStorageAccountBaseUrl(authConfig.storageAccountName));
                if (!isEmpty(authConfig.clientSecret)) {
                    tokenCredential = buildClientServiceCredential(authConfig);
                } else {
                    tokenCredential = buildClientCertificateCredential(authConfig);
                }
                builder.credential(tokenCredential);
            }
        }
        return builder.buildClient();
    }

    @Override
    public AuthResponse authenticate(AzureAuthConfig authConfig) {
        try {
            DataLakeServiceClient dataLakeServiceClient = getDataLakeServiceClient(authConfig);
            if (authConfig.testPath.isPresent()) {
                BucketPrefix bucketPrefix = toBucketPrefix(authConfig.testPath.get(), true);
                
                dataLakeServiceClient.getFileSystemClient(bucketPrefix.bucket).listPaths(new ListPathsOptions(), Duration.of(LIST_PATHS_TIMEOUT_MINS, ChronoUnit.MINUTES));
            } else {
                dataLakeServiceClient.listFileSystems();
            }
            return SUCCESS;
        } catch (Exception e) {
            logger.error("Exception while authenticating, credsId={}", authConfig.getCredsId(), e);
            return authError(e);
        }
    }

    @Override
    public StreamEx<NexlaBucket> listBuckets(AbstractConfig config) {
        FileSourceConnectorConfig connectorConfig = (FileSourceConnectorConfig) config;
        DataLakeServiceClient dataLakeServiceClient = getDataLakeServiceClient(asAzure(connectorConfig.getAuthConfig()));
        return StreamEx
                .of(dataLakeServiceClient.listFileSystems().stream())
                .map(bucket -> new NexlaBucket(bucket.getName()));
    }

    @Override
    public StreamEx<NexlaFile> listBucketContents(AbstractConfig config) {
        FileSourceConnectorConfig connectorConfig = (FileSourceConnectorConfig) config;
        BucketPrefix bucketPrefix = toBucketPrefix(connectorConfig.path, false);

        StreamEx<PathItem> paths = listFoldersAndFilesByPrefix(connectorConfig, bucketPrefix.bucket, bucketPrefix.prefix);
        final DataLakeFileSystemClient fileSystemClient = getFileSystemClient(asAzure(connectorConfig.getAuthConfig()), bucketPrefix.bucket);
        if (connectorConfig.dirScanningMode == FILES) {
            return paths
                    .filter(file -> !file.isDirectory())
                    .filter(file -> com.nexla.common.FileUtils.filterIfDeltaFile(connectorConfig.getConnectionType(), file.getName()))
                    .parallel(EXECUTOR)
                    .map(file -> toNexlaFile(fileSystemClient, file, FILES));
        } else {
            return paths
                    .parallel(EXECUTOR)
                    .map(file -> toNexlaFile(fileSystemClient, file, file.isDirectory() ? DIRECTORIES : FILES));
        }
    }

    @Override
    public boolean checkWriteAccess(AbstractConfig config) {
        FileConnectorAuth connectorConfig = (FileConnectorAuth) config;
        String bucket = toBucketPrefix(connectorConfig.getPath(), false).bucket;
        final DataLakeFileClient dataLakeFileClient = getDataLakeFileClient(asAzure(connectorConfig.getAuthConfig()), bucket, NEXLA_TEMP_FILE_NAME);
        String sample = "test";
        try {
            dataLakeFileClient.upload(new ByteArrayInputStream(sample .getBytes()), sample.length(), true);
            dataLakeFileClient.delete();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @SneakyThrows
    @Override
    public InputStream readInputStreamInternal(FileConnectorAuth connectorConfig, String file) {
        String bucket = toBucketPrefix(connectorConfig.getPath(), true).bucket;
        logger.info("fileSystem={} path={}", bucket, file);
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            getDataLakeFileClient(asAzure(connectorConfig.getAuthConfig()), bucket, file).read(outputStream);
            return new ByteArrayInputStream(outputStream.toByteArray());
        }
    }

    @Override
    public boolean doesFileExistsInternal(FileConnectorAuth connectorConfig, String fileNameNoBucket) {
        BucketPrefix bucketPrefix = toBucketPrefix(connectorConfig.getPath(), true);
        final DataLakeFileClient fileClient = getDataLakeServiceClient(asAzure(connectorConfig.getAuthConfig()))
                .getFileSystemClient(bucketPrefix.bucket)
                .getFileClient(fileNameNoBucket);
        return fileClient.exists();
    }

    @Override
    public FileDetails writeInternal(FileConnectorAuth connectorConfig, String key, File file) {
        String fileSystem = toBucketPrefix(connectorConfig.getPath(), true).bucket;
        getDataLakeServiceClient(asAzure(connectorConfig.getAuthConfig()))
                .getFileSystemClient(fileSystem)
                .getFileClient(key)
                .uploadFromFile(file.getPath(), true);
        return new FileDetails(key, empty(), empty());
    }

    @SneakyThrows
    @Override
    public FileDetails writeInternal(FileConnectorAuth connectorConfig, String key, InputStream inputStream) {
        String fileSystem = toBucketPrefix(connectorConfig.getPath(), true).bucket;
        getDataLakeServiceClient(asAzure(connectorConfig.getAuthConfig()))
                .getFileSystemClient(fileSystem)
                .getFileClient(key)
                .upload(inputStream, inputStream.available(), true);
        return new FileDetails(key, empty(), empty());
    }

    @Override
    public StreamEx<NexlaFile> listTopLevelBuckets(AbstractConfig configTemp) {
        FileSourceConnectorConfig config = (FileSourceConnectorConfig) configTemp;

        AzureAuthConfig authConfig = asAzure(config.getAuthConfig());
        final DataLakeServiceClient dataLakeServiceClient = getDataLakeServiceClient(authConfig);

        BucketPrefix bucketPrefix = toBucketPrefix(config.getPath(), true);
        if (authConfig.testPath.isPresent() || StringUtils.isNotEmpty(bucketPrefix.bucket)) {

            final String bucket;
            final String prefix;
            if (StringUtils.isNotEmpty(bucketPrefix.bucket)) {
                bucket = bucketPrefix.bucket;
                prefix = bucketPrefix.prefix;
            } else {
                BucketPrefix testBucketPrefix = toBucketPrefix(authConfig.testPath.get(), true);
                bucket = testBucketPrefix.bucket;
                prefix = testBucketPrefix.prefix;
            }

            return StreamEx
                    .of(listPrefixesAndFiles(dataLakeServiceClient,
                            bucket,
                            prefix,
                            config.depth)
                    )
                    .parallel(EXECUTOR)
                    .map(res -> toNexlaFile(bucket, res));

        } else {

            StreamEx<NexlaBucket> buckets = listBuckets(config);
            if (config.depth == 1) {
                return buckets.map(b -> toNexlaFile(b.getName(), new ListingResource("", FOLDER, empty())));
            } else {
                Map<String, List<ListingResource>> processingMap = buckets.toMap(
                        NexlaBucket::getName,
                        b -> listPrefixesAndFiles(dataLakeServiceClient, b.getName(), bucketPrefix.prefix, config.depth - 1).toList());

                return EntryStream
                        .of(processingMap)
                        .flatMapKeyValue((bucket, listing) -> listing.isEmpty()
                                ? StreamEx.of(toNexlaFile(bucket, new ListingResource("", FOLDER, empty())))
                                : StreamEx.of(listing).map(res -> toNexlaFile(bucket, res)));
            }
        }
    }

    @VisibleForTesting
    StreamEx<ListingResource> listPrefixesAndFiles(DataLakeServiceClient dataLakeServiceClient, String bucket, String prefix, int maxDepth) {

        Supplier<StreamEx<FileWalk.LevelFile<ListingResource>>> firstLayer = () ->
                listLevel(dataLakeServiceClient, bucket, prefix)
                        .map(file -> new FileWalk.LevelFile<>(1, file, null, file.getType() == FOLDER));

        StreamEx<FileWalk.LevelFile<ListingResource>> fileStream =
                walkFileTreeDfs(
                        BOTH,
                        firstLayer,
                        file -> nextStream(dataLakeServiceClient, bucket, file, maxDepth));

        if (maxDepth > 1) {
            fileStream.parallel(EXECUTOR);
        }

        return fileStream.map(f -> f.file);
    }

    private StreamEx<ListingResource<PathItem>> listLevel(DataLakeServiceClient dataLakeServiceClient, String bucket, String prefix) {
        return listFoldersAndFilesByPrefixNonRecursive(dataLakeServiceClient, bucket, removeStart(prefix, "/"))
                .map(b -> new ListingResource<>(b.getName(), b.isDirectory() ? FOLDER : FILE, Optional.ofNullable(b)));
    }

    private StreamEx<FileWalk.LevelFile<ListingResource>> nextStream(DataLakeServiceClient dataLakeServiceClient, String bucket, FileWalk.LevelFile<ListingResource> levelFile, int maxDepth) {
        ListingResource currFile = levelFile.file;
        if (levelFile.level == maxDepth || currFile.getType() == ListingResourceType.FILE) {
            return StreamEx.empty();
        } else {
            return StreamEx
                    .of(listLevel(dataLakeServiceClient, bucket, currFile.getValue()).spliterator())
                    .map(f -> new FileWalk.LevelFile(levelFile.level + 1, f, f.getValue(), f.getType() == FOLDER));
        }
    }

    private DataLakeFileSystemClient getFileSystemClient(AzureAuthConfig authConfig, String fileSystemName) {
        return getDataLakeServiceClient(authConfig).getFileSystemClient(fileSystemName);
    }

    private DataLakeFileClient getDataLakeFileClient(AzureAuthConfig authConfig, String fileSystemName, String fileName) {
        return getFileSystemClient(authConfig, fileSystemName).getFileClient(fileName);
    }

    private StreamEx<PathItem> listFoldersAndFilesByPrefix(FileSourceConnectorConfig config, String bucket, String prefix) {
        return listFoldersAndFilesByPrefix(getDataLakeServiceClient(asAzure(config.getAuthConfig())), bucket, prefix);
    }

    private StreamEx<PathItem> listFoldersAndFilesByPrefix(DataLakeServiceClient dataLakeServiceClient, String bucket, String prefix) {
        return StreamEx
                .of(dataLakeServiceClient.getFileSystemClient(bucket).getDirectoryClient(prefix).listPaths(true, false, LIST_PATHS_MAX_RECORDS, Duration.of(LIST_PATHS_TIMEOUT_MINS, ChronoUnit.MINUTES)).stream());
    }

    private StreamEx<PathItem> listFoldersAndFilesByPrefixNonRecursive(DataLakeServiceClient dataLakeServiceClient, String bucket, String prefix) {
        return StreamEx
                .of(dataLakeServiceClient.getFileSystemClient(bucket).getDirectoryClient(prefix).listPaths(false, false, LIST_PATHS_MAX_RECORDS, Duration.of(LIST_PATHS_TIMEOUT_MINS, ChronoUnit.MINUTES)).stream());
    }

    private NexlaFile toNexlaFile(DataLakeFileSystemClient fileSystemClient, PathItem pathItem, DirScanningMode scanningMode) {
        String pathItemName = pathItem.getName();
        long contentLength = pathItem.getContentLength();
        ListingResourceType fileType = scanningMode == FILES ? FILE : FOLDER;

        Supplier<Optional<PathProperties>> getProperties = () -> {
            DataLakePathClient client = scanningMode == FILES
                 ? fileSystemClient.getFileClient(pathItemName)
                 : fileSystemClient.getDirectoryClient(pathItemName);
            
            return retryOnIllegalStateException(MAX_GET_PROPERTIES_ATTEMPTS, () ->
                      client.getPropertiesWithResponse(null, Duration.ofSeconds(GET_PROPERTIES_TIMEOUT_SECONDS), Context.NONE).getValue());
        };

        Optional<PathProperties> properties = getProperties.get();

        Long created;
        Long lastModified;
        String contentMd5;
        if (properties.isPresent()) {
            PathProperties props = properties.get();
            created = toEpoch(props.getCreationTime());
            lastModified = toEpoch(props.getLastModified());
            contentMd5 = encodeBase64String(props.getContentMd5());
        } else {
            logger.error("Skipped NexlaFile metadata enrichment for " + pathItemName);
            created = toEpoch(DEFAULT_CREATED_MODIFIED_DATE);
            lastModified = toEpoch(DEFAULT_CREATED_MODIFIED_DATE);
            contentMd5 = StringUtils.EMPTY;
        }
        
        return new NexlaFile(pathItemName, contentLength, null, contentMd5, created, lastModified, fileType);
    }

    private NexlaFile toNexlaFile(String bucket, ListingResource<PathItem> commonPrefix) {
        final Optional<PathItem> summary = commonPrefix.getSummary();
        Long lastModified = summary
                .map(p -> toEpoch(p.getLastModified()))
                .orElse(null);
        Long size = summary
                .map(PathItem::getContentLength)
                .orElse(null);
        return new NexlaFile(bucket + "/" + commonPrefix.getValue(), size, null, null, null, lastModified, commonPrefix.getType());
    }

    private static String getStorageAccountBaseUrl(String storageAccount) {
        return String.format("https://%s.dfs.core.windows.net", storageAccount);
    }

    private <T> Optional<T> retryOnIllegalStateException(int maxRetries, Supplier<T> operation) {
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return Optional.ofNullable(operation.get());
            } catch (IllegalStateException ise) {
                if (attempt == maxRetries) {
                    logger.error("Operation failed after " + maxRetries + " attempts due to IllegalStateException.", ise);
                }
            }
        }
        return Optional.empty();
    }

    public static AzureAuthConfig asAzure(BaseAuthConfig authConfig) {
        return (AzureAuthConfig) authConfig;
    }

}
