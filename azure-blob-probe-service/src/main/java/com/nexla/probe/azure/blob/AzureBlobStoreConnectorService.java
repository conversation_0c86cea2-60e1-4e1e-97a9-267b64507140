package com.nexla.probe.azure.blob;

import com.azure.core.credential.TokenCredential;
import com.azure.core.http.rest.PagedIterable;
import com.azure.core.http.rest.Response;
import com.azure.core.util.Context;
import com.azure.core.util.ProgressListener;
import com.azure.storage.blob.BlobClient;
import com.azure.storage.blob.BlobContainerClient;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import com.azure.storage.blob.models.*;
import com.azure.storage.blob.options.BlobDownloadToFileOptions;
import com.azure.storage.common.ParallelTransferOptions;
import com.azure.storage.common.StorageSharedKeyCredential;
import com.google.common.annotations.VisibleForTesting;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.ListingResource;
import com.nexla.common.ListingResourceType;
import com.nexla.common.NexlaBucket;
import com.nexla.common.NexlaFile;
import com.nexla.connector.config.file.AWSAuthConfig.BucketPrefix;
import com.nexla.connector.config.file.AzureAuthConfig;
import com.nexla.connector.config.file.DirScanningMode;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.file.service.FileConnectorService;
import com.nexla.file.service.FileDetails;
import com.nexla.file.service.FileWalk;
import com.nexla.listing.client.ListingClient;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.config.AbstractConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.function.Supplier;
import java.util.stream.Stream;

import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.common.FileUtils.removeSlashes;
import static com.nexla.common.ListingResourceType.FILE;
import static com.nexla.common.ListingResourceType.FOLDER;
import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;
import static com.nexla.connector.ConnectorService.AuthResponse.authError;
import static com.nexla.connector.config.file.AWSAuthConfig.toBucketPrefix;
import static com.nexla.connector.config.file.DirScanningMode.BOTH;
import static com.nexla.connector.config.file.DirScanningMode.DIRECTORIES;
import static com.nexla.connector.config.file.DirScanningMode.FILES;
import static com.nexla.connector.config.file.S3Constants.NEXLA_TEMP_FILE_NAME;
import static com.nexla.file.service.FileWalk.walkFileTreeDfs;
import static com.nexla.connector.config.file.AzureServicePrincipalAuthHelper.buildClientCertificateCredential;
import static com.nexla.connector.config.file.AzureServicePrincipalAuthHelper.buildClientServiceCredential;
import static com.nexla.probe.azure.datalake.AzureDataLakeConnectorService.asAzure;
import static java.util.Optional.empty;
import static org.apache.commons.codec.binary.Base64.encodeBase64String;
import static org.apache.commons.lang.BooleanUtils.isNotTrue;
import static org.apache.commons.lang.BooleanUtils.isTrue;
import static org.apache.commons.lang.StringUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.removeStart;

public class AzureBlobStoreConnectorService extends FileConnectorService<AzureAuthConfig> {

	private final static Logger logger = LoggerFactory.getLogger(AzureBlobStoreConnectorService.class);
	static final ForkJoinPool EXECUTOR = new ForkJoinPool(30);

	public AzureBlobStoreConnectorService() {
		this(null, null, null);
	}
	public AzureBlobStoreConnectorService(AdminApiClient adminApiClient, ListingClient listingClient, String credentialsDecryptKey) {
		super(adminApiClient, listingClient, credentialsDecryptKey);
	}

	public BlobServiceClient getBlobServiceClient(AzureAuthConfig authConfig) {
		BlobServiceClientBuilder builder = new BlobServiceClientBuilder();
		if (!isEmpty(authConfig.connectionString)) {
			builder.connectionString(authConfig.connectionString);
		} else if (!isEmpty(authConfig.storageAccountName)) {
			builder.endpoint(getStorageAccountBaseUrl(authConfig.storageAccountName));
			if (!isEmpty(authConfig.storageAccountKey)) {
				builder.credential(new StorageSharedKeyCredential(authConfig.storageAccountName, authConfig.storageAccountKey));
			} else if (!isEmpty(authConfig.sasToken)) {
				builder.sasToken(authConfig.sasToken);
			} else if (!isEmpty(authConfig.clientId) && !isEmpty(authConfig.tenantId)) {
				TokenCredential tokenCredential;
				builder.endpoint(getStorageAccountBaseUrl(authConfig.storageAccountName));
				if (!isEmpty(authConfig.clientSecret)) {
					tokenCredential = buildClientServiceCredential(authConfig);
				} else {
					tokenCredential = buildClientCertificateCredential(authConfig);
				}
				builder.credential(tokenCredential);
			}
		}
		return builder.buildClient();
	}

	@Override
	public AuthResponse authenticate(AzureAuthConfig authConfig) {
		try {
			BlobServiceClient blobServiceClient = getBlobServiceClient(authConfig);
			if (authConfig.testPath.isPresent()) {
				BucketPrefix bucketPrefix = toBucketPrefix(authConfig.testPath.get(), true);
				blobServiceClient.getBlobContainerClient(bucketPrefix.bucket).listBlobsByHierarchy(bucketPrefix.prefix);
			} else {
				blobServiceClient.listBlobContainers();
			}
			return SUCCESS;
		} catch (Exception e) {
			logger.error("Exception while authenticating, credsId={}", authConfig.getCredsId(), e);
			return authError(e);
		}
	}

	@Override
	public StreamEx<NexlaBucket> listBuckets(AbstractConfig config) {
		FileSourceConnectorConfig connectorConfig = (FileSourceConnectorConfig) config;
		BlobServiceClient blobServiceClient = getBlobServiceClient(asAzure(connectorConfig.getAuthConfig()));
		return StreamEx
			.of(blobServiceClient.listBlobContainers().stream())
			.map(bucket -> new NexlaBucket(bucket.getName()));
	}

	@Override
	public StreamEx<NexlaFile> listBucketContents(AbstractConfig config) {
		FileSourceConnectorConfig connectorConfig = (FileSourceConnectorConfig) config;
		return listBucketContents(config, connectorConfig.path, connectorConfig.dirScanningMode);
	}

	public StreamEx<NexlaFile> listBucketContents(AbstractConfig config, String path, DirScanningMode dirScanningMode) {
		FileSourceConnectorConfig connectorConfig = (FileSourceConnectorConfig) config;
		BucketPrefix bucketPrefix = toBucketPrefix(path, false);
		StreamEx<BlobItem> blobStream = StreamEx.ofTree(
			(BlobItem) null,
			blobItem -> {
				if (blobItem == null) {
					return listFoldersAndBlobsByPrefix(connectorConfig, bucketPrefix.bucket, bucketPrefix.prefix);
				} else {
					if (isTrue(blobItem.isPrefix())) {
						return listFoldersAndBlobsByPrefix(connectorConfig, bucketPrefix.bucket, blobItem.getName());
					} else {
						return Stream.of();
					}
				}
			}
		).skip(1);

		if (dirScanningMode == FILES) {
			return blobStream
				.filter(file -> isNotTrue(file.isPrefix()))
				.filter(file -> com.nexla.common.FileUtils.filterIfDeltaFile(connectorConfig.getConnectionType(), file.getName()))
				.map(file -> toNexlaFile(file, FILES));
		} else {
			return blobStream.map(file -> toNexlaFile(file, isTrue(file.isPrefix()) ? DIRECTORIES : FILES));
		}
	}

	@Override
	public boolean checkWriteAccess(AbstractConfig config) {
		FileConnectorAuth connectorConfig = (FileConnectorAuth) config;
		String bucket = toBucketPrefix(connectorConfig.getPath(), false).bucket;
		final BlobClient blobClient = getBlobClient(asAzure(connectorConfig.getAuthConfig()), bucket, NEXLA_TEMP_FILE_NAME);
		String sample = "test";
		try {
			blobClient.upload(new ByteArrayInputStream(sample.getBytes()), sample.length(), true);
			blobClient.delete();
			return true;
		} catch (Exception e) {
			return false;
		}
	}

	@SneakyThrows
	@Override
	public InputStream readInputStreamInternal(FileConnectorAuth config, String file) {
		String bucket = toBucketPrefix(config.getPath(), true).bucket;
		logger.info("[creds-{}] container={} file={}", config.getAuthConfig().getCredsId(), bucket, file);

		File temp = getTempFile(config, bucket, file);

		return new FileInputStream(temp);
	}

	@SneakyThrows
	@Override
	public void readToOutputStreamInternal(FileConnectorAuth config, String file, OutputStream os) {
		InputStream fis = readInputStreamInternal(config, file);
		IOUtils.copyLarge(fis, os);
		closeSilently(fis);
	}

	@SneakyThrows
	private File getTempFile(FileConnectorAuth config, String bucket, String file) {
		Path destination = Files.createTempDirectory("nexla-azure")
				.resolve(removeSlashes(bucket))
				.resolve(UUID.randomUUID() + removeSlashes(file) + ".tmp");

		File destinationFile = destination.toFile();

		destinationFile
				.getParentFile()
				.mkdirs();

		destinationFile
				.deleteOnExit();

		BlobClient client = getBlobClient(asAzure(config.getAuthConfig()), bucket, file);

		BlobDownloadToFileOptions blobDownloadToFileOptions = new BlobDownloadToFileOptions(destination.toString())
						.setDownloadRetryOptions(new DownloadRetryOptions().setMaxRetryRequests(5))
						.setParallelTransferOptions(
								new ParallelTransferOptions()
										.setMaxConcurrency(48)
										.setBlockSizeLong(4L * 1024 * 1024)
										.setProgressListener(new ProgressListener() {
											private volatile long lastReported = System.currentTimeMillis();

                                            @Override
                                            public void handleProgress(long bytes) {
												if (System.currentTimeMillis() - lastReported > 1_000) {
													long now = System.currentTimeMillis();
                                                    logger.info("Downloaded {}MB", Math.floor(bytes / 1024.0 / 1024.0));
													lastReported = now;
												}
                                            }
                                        })
						);

		Response<BlobProperties> response = client.downloadToFileWithResponse(blobDownloadToFileOptions, null, Context.NONE);
		if (response == null) {
			throw new FileNotFoundException("There is no response from Azure Blob");
		}

		logger.info("Downloaded file {}. Status: {}, Tier: {}, Metadata: {}", file, response.getStatusCode(), response.getValue().getAccessTier(), response.getValue().getMetadata());

		return destinationFile;
	}

	@Override
	public boolean doesFileExistsInternal(FileConnectorAuth config, String fileNameNoBucket) {
		BucketPrefix bucketPrefix = toBucketPrefix(config.getPath(), true);
		return getBlobServiceClient(asAzure(config.getAuthConfig()))
			.getBlobContainerClient(bucketPrefix.bucket)
			.getBlobClient(fileNameNoBucket)
			.exists();
	}

	@Override
	public FileDetails writeInternal(FileConnectorAuth config, String key, File file) {
		String container = toBucketPrefix(config.getPath(), true).bucket;
		getBlobServiceClient(asAzure(config.getAuthConfig()))
			.getBlobContainerClient(container)
			.getBlobClient(key)
			.uploadFromFile(file.getPath(), true);
		return new FileDetails(key, empty(), empty());
	}

	@SneakyThrows
	@Override
	public FileDetails writeInternal(FileConnectorAuth config, String key, InputStream inputStream) {
		String container = toBucketPrefix(config.getPath(), true).bucket;
		getBlobServiceClient(asAzure(config.getAuthConfig()))
			.getBlobContainerClient(container)
			.getBlobClient(key)
			.upload(inputStream, inputStream.available(), true);
		return new FileDetails(key, empty(), empty());
	}

	@Override
	public StreamEx<NexlaFile> listTopLevelBuckets(AbstractConfig configTemp) {
		FileSourceConnectorConfig config = (FileSourceConnectorConfig) configTemp;

		AzureAuthConfig authConfig = asAzure(((FileSourceConnectorConfig) configTemp).getAuthConfig());
		final BlobServiceClient blobServiceClient = getBlobServiceClient(authConfig);

		BucketPrefix bucketPrefix = toBucketPrefix(config.getPath(), true);
		if (authConfig.testPath.isPresent() || StringUtils.isNotEmpty(bucketPrefix.bucket)) {

			final String bucket;
			final String prefix;
			if (StringUtils.isNotEmpty(bucketPrefix.bucket)) {
				bucket = bucketPrefix.bucket;
				prefix = bucketPrefix.prefix;
			} else {
				BucketPrefix testBucketPrefix = toBucketPrefix(authConfig.testPath.get(), true);
				bucket = testBucketPrefix.bucket;
				prefix = testBucketPrefix.prefix;
			}

			return StreamEx
				.of(listPrefixesAndFiles(blobServiceClient,
					bucket,
					prefix,
					config.depth)
				)
				.map(res -> toNexlaFile(bucket, res));

		} else {

			StreamEx<NexlaBucket> buckets = listBuckets(config);
			if (config.depth == 1) {
				return buckets.map(b -> toNexlaFile(b.getName(), new ListingResource("", FOLDER, empty())));
			} else {
				Map<String, List<ListingResource>> processingMap = buckets.toMap(
					NexlaBucket::getName,
					b -> listPrefixesAndFiles(blobServiceClient, b.getName(), bucketPrefix.prefix, config.depth - 1).toList());

				return EntryStream
					.of(processingMap)
					.flatMapKeyValue((bucket, listing) -> listing.isEmpty()
						? StreamEx.of(toNexlaFile(bucket, new ListingResource("", FOLDER, empty())))
						: StreamEx.of(listing).map(res -> toNexlaFile(bucket, res)));
			}
		}
	}

	@VisibleForTesting
	StreamEx<ListingResource> listPrefixesAndFiles(BlobServiceClient blobServiceClient, String bucket, String prefix, int maxDepth) {

		Supplier<StreamEx<FileWalk.LevelFile<ListingResource>>> firstLayer = () ->
			listLevel(blobServiceClient, bucket, prefix)
				.map(file -> new FileWalk.LevelFile<>(1, file, null, file.getType() == FOLDER));

		StreamEx<FileWalk.LevelFile<ListingResource>> fileStream =
			walkFileTreeDfs(
				BOTH,
				firstLayer,
				file -> nextStream(blobServiceClient, bucket, file, maxDepth));

		if (maxDepth > 1) {
			fileStream.parallel(EXECUTOR);
		}

		return fileStream.map(f -> f.file);
	}

	private StreamEx<ListingResource<BlobItem>> listLevel(BlobServiceClient blobServiceClient, String bucket, String prefix) {
		return listFoldersAndBlobsByPrefix(blobServiceClient, bucket, removeStart(prefix, "/"))
			.map(b -> new ListingResource<>(b.getName(), isTrue(b.isPrefix()) ? FOLDER : FILE, Optional.ofNullable(b)));
	}

	private StreamEx<FileWalk.LevelFile<ListingResource>> nextStream(BlobServiceClient blobServiceClient, String bucket, FileWalk.LevelFile<ListingResource> levelFile, int maxDepth) {
		ListingResource currFile = levelFile.file;
		if (levelFile.level == maxDepth || currFile.getType() == ListingResourceType.FILE) {
			return StreamEx.empty();
		} else {
			return StreamEx
				.of(listLevel(blobServiceClient, bucket, currFile.getValue()).spliterator())
				.map(f -> new FileWalk.LevelFile(levelFile.level + 1, f, f.getValue(), f.getType() == FOLDER));
		}
	}

	private BlobContainerClient getBlobContainerClient(AzureAuthConfig authConfig, String containerName) {
		return getBlobServiceClient(authConfig).getBlobContainerClient(containerName);
	}

	private BlobClient getBlobClient(AzureAuthConfig authConfig, String containerName, String blobName) {
		return getBlobContainerClient(authConfig, containerName).getBlobClient(blobName);
	}

	private StreamEx<BlobItem> listFoldersAndBlobsByPrefix(FileSourceConnectorConfig config, String bucket, String prefix) {
		return listFoldersAndBlobsByPrefix(getBlobServiceClient(asAzure(config.getAuthConfig())), bucket, prefix);
	}

	private StreamEx<BlobItem> listFoldersAndBlobsByPrefix(BlobServiceClient blobServiceClient, String bucket, String prefix) {
		// listBlobsByHierarchy might return the same folder as prefix or not, which means some folders might be considered as files
		// to avoid that we sort the item to force folders appear first

		PagedIterable<BlobItem> blobItems = blobServiceClient.getBlobContainerClient(bucket)
			.listBlobsByHierarchy(prefix);

		return StreamEx.of(blobItems.stream())
			.sorted((blob1, blob2) -> {
				if (blob1.isPrefix() && !blob2.isPrefix()) {
					return -1;
				} else if (!blob1.isPrefix() && blob2.isPrefix()) {
					return 1;
				} else {
					return 0;
				}
			});
	}

	private Stream<BlobItem> listBlobsInBucket(FileConnectorAuth config, String bucket) {
		return getBlobServiceClient(asAzure(config.getAuthConfig()))
			.getBlobContainerClient(bucket)
			.listBlobs()
			.stream();
	}

	private NexlaFile toNexlaFile(BlobItem blob, DirScanningMode scanningMode) {
		if (scanningMode == FILES) {
			final BlobItemProperties blobProperties = blob.getProperties();
			Long created = toEpoch(blobProperties.getCreationTime());
			Long lastModified = toEpoch(blobProperties.getLastModified());
			return new NexlaFile(blob.getName(), blobProperties.getContentLength(), null
				, encodeBase64String(blobProperties.getContentMd5()), created, lastModified, FILE);
		} else {
			return new NexlaFile(blob.getName(), 0l, null, null, null, null, FOLDER);
		}
	}

	private NexlaFile toNexlaFile(String bucket, ListingResource<BlobItem> commonPrefix) {
		final Optional<BlobItemProperties> blobItemProperties = commonPrefix.getSummary()
			.filter(item -> !item.isPrefix())
			.map(BlobItem::getProperties);
		Long lastModified = blobItemProperties
			.map(p -> toEpoch(p.getLastModified()))
			.orElse(null);
		Long createdAt = blobItemProperties
			.map(p -> toEpoch(p.getCreationTime()))
			.orElse(null);
		Long size = blobItemProperties
			.map(BlobItemProperties::getContentLength)
			.orElse(null);
		return new NexlaFile(bucket + "/" + commonPrefix.getValue(), size, null, null, createdAt, lastModified, commonPrefix.getType());
	}

	private static String getStorageAccountBaseUrl(String storageAccount) {
		return String.format("https://%s.blob.core.windows.net", storageAccount);
	}

	public static Long toEpoch(OffsetDateTime offsetDateTime) {
		return Optional.ofNullable(offsetDateTime)
			.map(t -> t.toInstant().toEpochMilli())
			.orElse(null);
	}

	public void deleteFile(AzureAuthConfig azureAuthConfig, String fileName, String uploadBucket, String uploadPrefix) {
		getBlobServiceClient(azureAuthConfig)
			.getBlobContainerClient(uploadBucket)
			.getBlobClient(getBlobKey(fileName, uploadPrefix))
			.delete();

	}

	public void uploadFile(AzureAuthConfig azureAuthConfig, File localFile, String uploadBucket, String uploadPrefix) {
		getBlobServiceClient(azureAuthConfig)
			.getBlobContainerClient(uploadBucket)
			.getBlobClient(getBlobKey(localFile.getName(), uploadPrefix))
			.uploadFromFile(localFile.getPath(), true);
	}

	private String getBlobKey(String fileName, String prefix) {
		return Paths.get(prefix, fileName).toString();
	}

}
