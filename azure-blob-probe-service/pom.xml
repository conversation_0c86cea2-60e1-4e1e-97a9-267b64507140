<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.nexla</groupId>
        <artifactId>backend-connectors</artifactId>
        <version>3.2.1-SNAPSHOT</version>
    </parent>

    <groupId>com.nexla.probe</groupId>
    <artifactId>azure-blob-probe</artifactId>

    <dependencies>

        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
            <version>${reactor.version}</version>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-common</artifactId>
            <version>${netty.version}</version>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-handler</artifactId>
            <version>${netty.version}</version>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-transport</artifactId>
            <version>${netty.version}</version>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-tcnative</artifactId>
            <version>${netty-tcnative.version}</version>
        </dependency>

        <dependency>
            <groupId>com.azure</groupId>
            <artifactId>azure-core</artifactId>
            <version>${azure-core.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.azure</groupId>
            <artifactId>azure-core-http-netty</artifactId>
            <version>${azure-core-http-netty.version}</version>
        </dependency>

        <dependency>
            <groupId>com.azure</groupId>
            <artifactId>azure-storage-blob</artifactId>
            <version>${azure-storage-blob.version}</version>
        </dependency>

        <dependency>
            <groupId>com.azure</groupId>
            <artifactId>azure-storage-file-datalake</artifactId>
            <version>${azure-storage-file-datalake.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>file-service</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>connector-properties</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>connector-test</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

</project>
