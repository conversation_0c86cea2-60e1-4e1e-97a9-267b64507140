package com.nexla.kafka

import akka.kafka.ConsumerMessage.CommittableMessage
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.control.health.metrics.KafkaTimeEvent
import com.nexla.control.message.MessageCreatedAt

import scala.compat.java8.OptionConverters._
import scala.concurrent.Future

abstract class AbstractMessageListener[MESSAGE_TYPE <: MessageCreatedAt](messageProducer: NexlaMessageProducer) extends MessageListener[MESSAGE_TYPE] {

  override def startMonitoring(): Future[Unit] = {
    doStartMonitoring()
  }

  def doStartMonitoring(): Future[Unit]



}
