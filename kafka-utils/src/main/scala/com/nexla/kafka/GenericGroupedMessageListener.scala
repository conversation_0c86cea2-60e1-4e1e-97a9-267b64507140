package com.nexla.kafka

import akka.kafka.Subscriptions.topics
import akka.kafka.scaladsl.{Committer, Consumer}
import akka.stream.Materializer
import com.nexla.common.AppType
import com.nexla.common.metrics.HealthMetricAggregator
import com.nexla.control.message.MessageCreatedAt
import com.nexla.telemetry.utils.ExecutionMetricSetImpl
import com.nexla.telemetry.utils.ExecutionTelemetryUtils.metricSet

import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ExecutionContext, Future}

abstract class GenericGroupedMessageListener[MESSAGE_TYPE <: MessageCreatedAt](ctrlCommon: CtrlTopics,
                                                                               appType: AppType,
                                                                               metricAggregator: HealthMetricAggregator,
                                                                               parallelism: Int)
                                                                              (implicit ec: ExecutionContext,
                                                           mat: Materializer)
  extends GenericMessageListener[MESSAGE_TYPE](ctrlCommon, appType, metricAggregator, parallelism) {

  def groupedWithinCount: Int = 100000

  def groupedWithinDuration: Option[FiniteDuration] = None

  private val startMonitoringMetrics = metricSet(this.getClass, "startMonitoring")
  override def startMonitoring(): Future[Unit] = {

    val source = Consumer
      .committableSource(ctrlCommon.consumerSettings(getGroupId, getTopicNames), topics(getTopicNames))

    val sourceGrouped = groupedWithinDuration
      .map { window =>
        source
          .groupedWithin(groupedWithinCount, window)
          .mapConcat { x => startMonitoringMetrics.markHist("group.size", x.size); x }
      }
      .getOrElse {
        source
      }

    startMonitoring(sourceGrouped, Committer.sink(ctrlCommon.commitDefault))
  }

}