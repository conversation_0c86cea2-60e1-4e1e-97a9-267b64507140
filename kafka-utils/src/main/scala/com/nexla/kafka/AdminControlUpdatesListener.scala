package com.nexla.kafka

import akka.kafka.ConsumerMessage.CommittableMessage
import akka.stream.Materializer
import com.nexla.admin.client.{AdminApiClientBuilder, AdminApiCondensedBuilder}
import com.nexla.common.metrics.HealthMetricAggregator
import com.nexla.common.{AppType, NexlaConstants, Resource}
import com.nexla.control.message.{ControlMessage, ControlResourceType}
import com.nexla.sc.api.GroupId
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}

import java.util
import scala.concurrent.{ExecutionContext, Future}

class AdminControlUpdatesListener(appType: AppType,
                                  ctrlCommon: CtrlTopics,
                                  metricAggregator: HealthMetricAggregator)
                                 (implicit ec: ExecutionContext,
                                  mat: Materializer)
  extends GenericMessageListener[ControlMessage](ctrlCommon, appType, metricAggregator, 10)
    with StrictNexlaLogging
    with WithLogging {

  def getTopicNames: Set[String] = Set(NexlaConstants.TOPIC_CONTROL_UPDATES)

  override def doParse(message: CommittableMessage[String, String]): (Option[ControlMessage], AppType) = {
    ctrlCommon.parseCtrlMessage(message)
  }

  override def getGroupId: GroupId =
    GroupId(s"${appType.appName}-admin" +
      s"-${NexlaConstants.TOPIC_CONTROL_UPDATES}" +
      s"-${ctrlCommon.getPodNameUUID()}")

  override def doProcess(m: ControlMessage, origin: AppType): Future[Unit] = Future {

    if (m.getResourceType.isFlowResource) {
      // Order is important here. First - invalidate resource in AdminApiClient, then update AdminApiClientCondensed cache
      Option(AdminApiClientBuilder.INSTANCE)
        .foreach(_.invalidate(m.getResourceId, m.getFlow.flatMap(x => x.getId), m.getResourceType.toResourceType))

      Option(AdminApiCondensedBuilder.INSTANCE)
        .foreach(_.updateCache(m.getResourceId, m.getResourceType.toResourceType, m.getEventType, m.getResourceJson))
    }

    if (m.getResourceType.eq(ControlResourceType.NOTIFICATION_SETTING) && m.getResourceJson.isPresent) {
      Option(AdminApiClientBuilder.INSTANCE)
        .foreach(_.invalidateNotificationSettingCache(m.getResourceJson.get()))
    }
  }

}