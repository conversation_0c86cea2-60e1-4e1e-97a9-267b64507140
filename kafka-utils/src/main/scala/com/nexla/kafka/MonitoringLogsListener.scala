package com.nexla.kafka

import akka.kafka.ConsumerMessage.CommittableMessage
import akka.stream.Materializer
import com.nexla.common.NexlaConstants.TOPIC_MONITORING_LOGS
import com.nexla.common.metrics.HealthMetricAggregator
import com.nexla.common.{AppType, StreamUtils}
import com.nexla.common.notify.monitoring.NexlaMonitoringLogEvent
import com.nexla.sc.api.GroupId
import scala.concurrent.{ExecutionContext, Future}

abstract class MonitoringLogsListener(ctrlCommon: CtrlTopics,
                                      appType: AppType,
                                      parallelism: Int,
                                      singleGroupId: Boolean,
                                      metricAggregator: HealthMetricAggregator)
                                     (implicit ec: ExecutionContext,
                                      mat: Materializer)
  extends GenericMessageListener[NexlaMonitoringLogEvent](ctrlCommon, appType, metricAggregator, parallelism) {

  // Do not provide the third parameter in order not to change group ID of existing consumer in production
  override def getGroupId: GroupId = ctrlCommon.groupIdName(singleGroupId, appType)

  override def getTopicNames: Set[String] = Set(TOPIC_MONITORING_LOGS)

  override def doParse(m: CommittableMessage[String, String]): (Option[NexlaMonitoringLogEvent], AppType) = {
    val message = Some(StreamUtils.jsonUtil().stringToType(m.record.value(), classOf[NexlaMonitoringLogEvent]))
    (message, AppType.UNKNOWN)
  }

  override def doProcess(message: NexlaMonitoringLogEvent, origin: AppType): Future[Unit] = {
    doProcess(message)
  }

  def doProcess(message: NexlaMonitoringLogEvent): Future[Unit]

}