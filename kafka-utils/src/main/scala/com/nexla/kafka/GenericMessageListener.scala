package com.nexla.kafka

import akka.Done
import akka.kafka.ConsumerMessage
import akka.kafka.ConsumerMessage.{CommittableMessage, GroupTopicPartition}
import akka.kafka.Subscriptions.topics
import akka.kafka.scaladsl.{Committer, Consumer}
import akka.stream.Materializer
import akka.stream.scaladsl.{Sink, Source}
import cats.implicits.toFunctorOps
import com.nexla.common.AppType
import com.nexla.common.metrics.HealthMetricAggregator
import com.nexla.control.message.MessageCreatedAt
import com.nexla.sc.util.AppUtils.logConsumerFailed
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import com.nexla.telemetry.utils.ExecutionMetricSetImpl
import com.nexla.telemetry.utils.ExecutionTelemetryUtils.metricSet

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Try, Using}
import scala.compat.java8.OptionConverters._

/**
 * Class designed to provide general Listener interface for listening messages from Kafka.
 * Specify Kafka group ID explicitly for every kafka listener
 */
// TODO add and expose metrics
abstract class GenericMessageListener[MESSAGE_TYPE <: MessageCreatedAt](ctrlCommon: CtrlTopics,
                                                                        appType: AppType,
                                                                        healthMetric: HealthMetricAggregator,
                                                                        parallelism: Int)
                                                                       (implicit ec: ExecutionContext,
                                                                        mat: Materializer)
  extends MessageListener[MESSAGE_TYPE]
    with StrictNexlaLogging
    with WithLogging {

  /**
   * Should implement message processing logic
   */
  def doProcess(message: MESSAGE_TYPE, origin: AppType): Future[Unit]

  def startMonitoring(): Future[Unit] = {
    startMonitoring(
      Consumer.committableSource(ctrlCommon.consumerSettings(getGroupId, getTopicNames), topics(getTopicNames)),
      Committer.sink(ctrlCommon.commitDefault))
  }

  /**
   * Method to start Listener run with provided stream source and sink.
   * Usually SHOULD not be called from client code (except only unit tests)
   */
  private val startMonitoringMetrics = metricSet(this.getClass, "startMonitoring")
  final def startMonitoring(src: Source[CommittableMessage[String, String], Consumer.Control],
                            sink: Sink[ConsumerMessage.Committable, Future[Done]]): Future[Unit] = {
    logger.info(s"Starting ${this.getClass.getSimpleName} to listen for topics $getTopicNames with group ID $getGroupId")
    src
      .mapAsync(parallelism)(processMessage)
      .runWith(sink)
      .recoverWith { case e =>
        logConsumerFailed(e, logger)
        startMonitoringMetrics.incError()
        startMonitoring()
      }
      .void
  }

  /**
   * Note. Method MUST always return offset for every consumed message in order to have it committed
   */
  private val processMessageMetrics = metricSet(this.getClass, "processMessage")
  final def processMessage(committableMessage: CommittableMessage[String, String]): Future[ConsumerMessage.CommittableOffset] = {
    trackedFuture(processMessageMetrics)(() =>
      Some(committableMessage)
        .filter(filterCommittableMessage)
        .map(parseMessage)
        .filter {
          case (None, _) => false // Parsed to empty Option
          case (Some(parsedMessage), origin) => filterParsedMessage(parsedMessage, origin)
        }
        .map { x => x._1.asJava.ifPresent(message => collectHealthMetrics(committableMessage.committableOffset.partitionOffset.key, message)); x }
        .map { case (Some(message), origin) => processParsedMessage(committableMessage, message, origin) }
        .getOrElse(Future.successful(committableMessage.committableOffset))
    )
  }

  private def filterCommittableMessage(committableMessage: CommittableMessage[String, String]): Boolean = {
    Try(doFilterMessage(committableMessage))
      .recover { case e: Exception => logger.error(s"Error processing: failed to filter committableMessage $committableMessage", e); false }
      .get
  }

  private val parseMessageMetrics = metricSet(this.getClass, "parseMessage")
  private def parseMessage(message: CommittableMessage[String, String]): (Option[MESSAGE_TYPE], AppType) = {
    Using(parseMessageMetrics.time()) { _ => doParse(message) }
      .recover { case e: Exception => parseMessageMetrics.incError(); logger.error(s"Error processing: failed to parse ${message.record.value()}", e); (None, AppType.UNKNOWN) }
      .get
  }

  private val filterParsedMessageMetrics = metricSet(this.getClass, "filterParsedMessage")
  private def filterParsedMessage(parsedMessage: MESSAGE_TYPE, origin: AppType): Boolean = {
    Using(filterParsedMessageMetrics.time())(_ => doFilterMessage(parsedMessage, origin))
      .recover { case e: Exception => filterParsedMessageMetrics.incError(); logger.error(s"Error processing: failed to filter parsed message $parsedMessage", e); false }
      .get
  }

  private def collectHealthMetrics(groupTopicPartition: GroupTopicPartition, message: MESSAGE_TYPE): Unit = {
    Try(healthMetric.collectTime(groupTopicPartition.topic, groupTopicPartition.groupId, message))
      .recover { case e: Exception => logger.error(s"Failed to collect health metrics for message $message", e); () }
      .get
  }

  private val processParsedMetrics = metricSet(this.getClass, "processParsedMessage")
  private def processParsedMessage(committableMessage: CommittableMessage[String, String], message: MESSAGE_TYPE, origin: AppType): Future[ConsumerMessage.CommittableOffset] = {
    trackedFuture(processParsedMetrics)(() =>
      Try(
        doProcess(message, origin)
          .recoverWith { case e => logger.error(s"Error processing: failed execute parsed message processing future: ${committableMessage.record.value()}", e); processParsedMetrics.incError(); Future.successful(()) }
          .map(_ => committableMessage.committableOffset))
        .recover { case e: Exception => logger.error(s"Error processing: failed to get parsed message processing future: ${committableMessage.record.value()}", e); processParsedMetrics.incError(); Future(committableMessage.committableOffset) }
        .get
    )
  }
}