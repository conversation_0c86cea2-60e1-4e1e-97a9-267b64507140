package com.nexla.kafka

import akka.kafka.ConsumerMessage.CommittableMessage
import akka.kafka.Subscriptions._
import akka.kafka._
import akka.kafka.scaladsl.{Committer, Consumer}
import akka.stream.scaladsl.{Keep, RestartSource}
import akka.stream.{Attributes, Materializer, RestartSettings}
import com.nexla.common.NexlaConstants.TASKS_TOPIC
import com.nexla.common.license.NexlaLicenseManager
import com.nexla.common.metrics.HealthMetricAggregator
import com.nexla.common.{AppType, StreamUtils}
import com.nexla.control.crontask.NexlaTaskStatus.FAILED
import com.nexla.control.crontask._
import com.nexla.sc.api.GroupId
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}

import scala.compat.java8.OptionConverters._
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

abstract class NexlaTaskListener(podName: String,
                                 ctrlCommon: CtrlTopics,
                                 appType: AppType,
                                 parallelism: Int,
                                 licenseManager: NexlaLicenseManager,
                                 metricAggregator: HealthMetricAggregator)
                                (implicit ec: ExecutionContext,
                                 mat: Materializer)
  extends StrictNexlaLogging
    with WithLogging {

  def doProcess(m: TaskRequestMessage, startedTime: Long): Future[Option[TaskCompleteMessage]]

  protected val topic: String = TASKS_TOPIC

  private val restartSettings = RestartSettings(minBackoff = 1.second, maxBackoff = 5.seconds, randomFactor = 0.2)

  def startMonitoring(): Unit = {
    val groupId = GroupId(appType.appName)
    val consumerSettings = ctrlCommon.consumerSettings(groupId, topic)
    RestartSource.withBackoff(restartSettings) { () =>
        logger.info(s"Starting task listener for consumer group id: $groupId")
        Consumer
          .committableSource(consumerSettings, topics(topic))
          .mapAsync(parallelism)(processMessage(groupId, _.getTaskType.important))
          .alsoTo(ctrlCommon.publishTaskComplete)
          .map(_._2)
          .viaMat(Committer.flow(ctrlCommon.commitDefault))(Keep.right)
          .log(s"NexlaTaskListener [$groupId]").addAttributes(
            Attributes.logLevels(onElement = Attributes.LogLevels.Off, onFinish = Attributes.LogLevels.Error, onFailure = Attributes.LogLevels.Error)
          )
      }
      .run()

    val fastGroupId = GroupId(s"${appType.appName}-fast")
    val fastConsumerSettings = ctrlCommon.consumerSettings(fastGroupId, topic)
    RestartSource.withBackoff(restartSettings) { () =>
        logger.info(s"Starting task listener for consumer group id: $fastGroupId")
        Consumer
          .committableSource(fastConsumerSettings, topics(topic))
          .mapAsyncUnordered(parallelism)(processMessage(fastGroupId, !_.getTaskType.important))
          .alsoTo(ctrlCommon.publishTaskComplete)
          .map(_._2)
          .viaMat(Committer.flow(ctrlCommon.commitDefault))(Keep.right)
          .log(s"NexlaTaskListener [$groupId]").addAttributes(
            Attributes.logLevels(onElement = Attributes.LogLevels.Off, onFinish = Attributes.LogLevels.Error, onFailure = Attributes.LogLevels.Error)
          )
      }
      .run()
  }

  private def processMessage(groupId: GroupId, filter: TaskRequestMessage => Boolean)(kafkaMessage: CommittableMessage[String, String]): Future[(Option[TaskCompleteMessage], ConsumerMessage.CommittableOffset)] = {
    licenseManager.getValidationResult().asScala match {
      case Some(msg) => Future.failed(new IllegalStateException(msg))
      case _ =>
        val partitionOffset = s"${kafkaMessage.committableOffset.partitionOffset._1.partition}:${kafkaMessage.committableOffset.partitionOffset.offset}"
        val startedTime = System.currentTimeMillis()
        parseMessage(kafkaMessage)
          .filter(filter)
          .map { message =>
            val fut = try {
              metricAggregator.collectTime(topic, groupId.name, message)
              doProcess(message, startedTime)
            } catch {
              case e: Exception => Future.failed(e)
            }
            fut.transformWith {
              case Success(processed) =>
                processed.foreach(result => {
                  logger.info(s"Processed message [$partitionOffset]: ${StreamUtils.jsonUtil().toJsonString(result)}")
                })
                Future.successful(processed)
              case Failure(e) =>
                logger.error(s"Unexpected error in doProcess [$partitionOffset]: ${kafkaMessage.record.value()}", e)
                Future.successful(Some(new TaskCompleteMessage(startedTime, e.getMessage, message, FAILED, podName)))
            } map { x =>
              (x, kafkaMessage.committableOffset)
            }
          }
          .getOrElse(Future.successful(None, kafkaMessage.committableOffset))
    }
  }

  private def parseMessage(m: CommittableMessage[String, String]): Option[TaskRequestMessage] = {
    Try(StreamUtils.jsonUtil().stringToType(m.record.value(), classOf[TaskRequestMessage]))
      .fold({ e =>
        logger.error(s"Skipping message ${m.record.value()}", e)
        None
      }, { res =>
        Some(res)
      })
  }

}