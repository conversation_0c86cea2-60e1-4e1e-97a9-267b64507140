package com.nexla.kafka

import akka.NotUsed
import akka.actor.ActorSystem
import akka.kafka.ConsumerMessage.CommittableMessage
import akka.kafka.scaladsl.Producer
import akka.kafka.{CommitterSettings, ConsumerMessage, ConsumerSettings, ProducerMessage, ProducerSettings}
import akka.stream.scaladsl.{Flow, Keep, Sink}
import com.bazaarvoice.jolt.JsonUtils
import com.google.common.collect.Maps
import com.nexla.common.NexlaConstants.TASKS_RESULT_TOPIC
import com.nexla.common.{AppType, NexlaSslContext, StreamUtils}
import com.nexla.control.crontask.TaskCompleteMessage
import com.nexla.control.message.ControlMessage
import com.nexla.kafka.control.listener.conf.SslContextApplier
import com.nexla.sc.api.CommonApiHandler.{registerConsumerGroup, registeredConsumerGroups}
import com.nexla.sc.api.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GroupId}
import com.nexla.sc.config.{KafkaProperties, NexlaClusterApplication}
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.{StringDeserializer, StringSerializer}

import java.util
import java.util.{Collections, UUID}
import scala.collection.JavaConverters._
import scala.compat.java8.OptionConverters.RichOptionalGeneric
import scala.util.Try

class CtrlTopics(val nodeId: Option[UUID],
                 props: KafkaProperties with NexlaClusterApplication,
                 sslContext: NexlaSslContext,
                 system: ActorSystem)
  extends StrictNexlaLogging
    with WithLogging {

  def parseCtrlMessage(m: CommittableMessage[String, String]): (Option[ControlMessage], AppType) = {
    val ctrlMessage = m.record.value()
    val map = StreamUtils.jsonUtil().jsonToMap(ctrlMessage)
    val ctrlOrigin = map.asScala.get("origin")
      .flatMap(Option.apply)
      .flatMap(x => AppType.getByName(x.toString).asScala)
      .getOrElse(AppType.CTRL_HTTP)

    val message = Try(StreamUtils.jsonUtil().stringToType(ctrlMessage, classOf[ControlMessage])).toOption
    if (message.isEmpty) {
      logger.error(s"Cant parse message $ctrlMessage")
    }
    (message, ctrlOrigin)
  }

  // Avoid using this method. Specify groupId explicitly in your listener
  @Deprecated
  def consumerSettings(singleGroupId: Boolean, appType: AppType, topic: String): ConsumerSettings[String, String] = {
    consumerSettings(groupIdName(singleGroupId, appType), topic)
  }

  def consumerSettings(groupId: GroupId, topic: String): ConsumerSettings[String, String] = {
    consumerSettings(groupId, Set(topic))
  }

  def consumerSettings(groupId: GroupId, topics: Set[String]): ConsumerSettings[String, String] = {
    ConsumerSettings(props.kafkaConsumerConfig, new StringDeserializer, new StringDeserializer)
      .withBootstrapServers(props.bootstrapServers)
      .withGroupId(registerConsumerGroup(groupId, topics))
      .withProperties(props.consumerProps)
      .withProperties(
        new SslContextApplier(sslContext)
          .apply(Collections.emptyMap())
          .asScala
          .map { case (k, v) => (k, v.toString) }
          .toMap
      )
  }

  def getPodNameUUID(): String = {
    props
      .podName
      .map(name => s"--pod-$name--")
      .get
  }

  /**
   * Avoid using this method. Specify groupId explicitly in your listener
    */
  @Deprecated
  def groupIdName(singleGroupId: Boolean, appType: AppType): GroupId = groupIdName(singleGroupId, appType, None)

  /**
   * Avoid using this method. Specify groupId explicitly in your listener
    */
  @Deprecated
  def groupIdName(singleGroupId: Boolean, appType: AppType, topicName: Option[GroupId]) = {
    val groupId = if (singleGroupId) {
      if (nodeId.isEmpty) {
        throw new IllegalStateException("Validation error: no NodeId for sharded instance")
      }
      appType.appName +
        topicName.map(x => s"-$x").getOrElse("")
    } else {
      if (nodeId.isEmpty) {
        logger.warn(s"Node ID is not specified when generating group ID. Could be dangerous when application gets associated with nodeID value")
      }
      appType.appName +
        nodeId.map(x => s"-${x.toString}").getOrElse("") +
        topicName.map(x => s"-$x").getOrElse("")
    }
    GroupId(groupId)
  }

  val defaultProducerSettings: akka.kafka.ProducerSettings[String, String] = {
    val producerConfig = props.config.getConfig("akka.kafka.producer")
    ProducerSettings(producerConfig, new StringSerializer(), new StringSerializer())
      .withBootstrapServers(props.bootstrapServers)
      .withProperties {
        import scala.collection.JavaConverters._
        new SslContextApplier(sslContext)
          .apply(Collections.emptyMap())
          .asScala
          .map { case (k, v) => (k, v.toString) }
          .toMap
      }
  }


  val commitDefault: CommitterSettings = CommitterSettings.apply(system)
  val commitNoBatch: CommitterSettings = CommitterSettings.apply(system.settings.config.getConfig("akka.kafka.immediate-committer"))

  val publishTaskComplete: Sink[(Option[TaskCompleteMessage], ConsumerMessage.CommittableOffset), NotUsed] = {
    Flow[(Option[TaskCompleteMessage], ConsumerMessage.CommittableOffset)]
      .mapConcat(_._1)
      .map(x => ProducerMessage.single(new ProducerRecord[String, String](TASKS_RESULT_TOPIC, null, JsonUtils.toJsonString(x))))
      .via(Producer.flexiFlow(defaultProducerSettings))
      .toMat(Sink.ignore)(Keep.left)
  }


}