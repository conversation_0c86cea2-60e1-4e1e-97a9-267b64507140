package com.nexla.kafka

import akka.kafka.ConsumerMessage.CommittableMessage
import com.nexla.common.AppType
import com.nexla.sc.api.GroupId
import scala.concurrent.Future

trait MessageListener[MESSAGE_TYPE] {

  /**
   * Should return topic names to read from
   */
  def getTopicNames: Set[String]

  /**
   * Should return group ID for Kafka consumer
   * - Every app MUST have it’s unique Kafka group ID based on AppType
   * - Every listener in a single app MUST have it’s unique Kafka  group ID.
   * Do not use the same group ID for different listeners within the same application, even when Kafka topics are different.
   * - Use pod name instead of UUID when consumer group should be used only during single application (pod) run (after restart consumer group will be different)
   * - Listeners in scaled applications MAY share the same Kafka group ID when messages are needed to be distributed among the applications
   * - Method SHOULD be explicitly overridden in all child classes
   * - Make sure you do not accidentally change group ID for already deployed Kafka topic listener
   */
  def getGroupId: GroupId

  /**
   * Should return message filter condition. Applied on raw message before any processing logic
   */
  def doFilterMessage(message: CommittableMessage[String, String]) = true

  /**
   * Should return message filter condition. Applied on parsed message before any processing logic
   */
  def doFilterMessage(message: MESSAGE_TYPE, origin: AppType) = true

  /**
   * Should parse original Kafka message
   * Returns parsed message and message origin AppType
   * SHOULD throw an exception if parsing is failed
   */
  def doParse(message: CommittableMessage[String, String]): (Option[MESSAGE_TYPE], AppType)

  /**
   * Common method to start Listener run.
   */
  def startMonitoring(): Future[Unit]
}