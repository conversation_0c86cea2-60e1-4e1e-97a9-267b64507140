package com.nexla.kafka

import akka.Done
import akka.kafka.ConsumerMessage
import akka.kafka.ConsumerMessage.{CommittableMessage, CommittableOffset, GroupTopicPartition}
import akka.kafka.Subscriptions.topics
import akka.kafka.scaladsl.{Committer, Consumer}
import akka.stream.Materializer
import akka.stream.scaladsl.{Sink, Source}
import cats.implicits.toFunctorOps
import com.nexla.common.AppType
import com.nexla.common.metrics.HealthMetricAggregator
import com.nexla.control.message.MessageCreatedAt
import com.nexla.sc.util.AppUtils.logConsumerFailed
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import com.nexla.telemetry.utils.{ExecutionMetricSet, ExecutionMetricSetImpl}
import com.nexla.telemetry.utils.ExecutionTelemetryUtils.metricSet

import scala.compat.java8.OptionConverters._
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Try, Using}

/**
 * Class designed to provide batched Listener interface for listening messages from Kafka.
 * Messages are processed in groups within groupedWithinCount and groupedWithinDuration windows
 * Additionally, there is timerTickInterval which triggers processMessageGroup even if there are no messages in Kafka
 *
 * Specify Kafka group ID explicitly for every kafka listener
 */
abstract class GenericBatchedMessageListener[MESSAGE_TYPE <: MessageCreatedAt](ctrlCommon: CtrlTopics,
                                                                               appType: AppType,
                                                                               healthMetric: HealthMetricAggregator,
                                                                               parallelism: Int)
                                                                              (implicit ec: ExecutionContext,
                                                                               mat: Materializer)
  extends MessageListener[MESSAGE_TYPE]
    with StrictNexlaLogging
    with WithLogging {

  /**
   * Specify in how many threads to pre-process records from Kafka. Affects parse and filter operations
   */
  def messagePreparationParallelism: Int = 1

  /**
   * Specify grouping window count
   */
  def groupedWithinCount: Int = 100000

  /**
   * Specify grouping window time interval
   */
  def groupedWithinDuration: FiniteDuration = 1.minute

  /**
   * Specify timer tick interval to trigger message group processing even without actual messages from Kafka
   */
  def timerTickInterval: FiniteDuration = 1.minute

  /**
   * Implement the processing of consumed message group. Messages list may be empty if there were no messages consumed
   */
  def doProcessMessageGroup(messages: Seq[MESSAGE_TYPE]): Future[Unit]

  override def startMonitoring(): Future[Unit] = {
    startMonitoring(
      Consumer.committableSource(ctrlCommon.consumerSettings(getGroupId, getTopicNames), topics(getTopicNames)),
      Committer.sink(ctrlCommon.commitDefault))
  }

  /**
   * Method to start Listener run with provided stream source and sink.
   * Usually SHOULD not be called from client code (except only unit tests)
   */
  private val startMonitoringMetrics = metricSet(this.getClass, "startMonitoring")
  final def startMonitoring(src: Source[CommittableMessage[String, String], Consumer.Control],
                            sink: Sink[ConsumerMessage.Committable, Future[Done]]): Future[Unit] = {
    logger.info(s"Starting ${this.getClass.getSimpleName} to listen for topics $getTopicNames with group ID $getGroupId")

    val timer = Source.tick[(Option[MESSAGE_TYPE], Option[ConsumerMessage.CommittableOffset])](timerTickInterval, timerTickInterval, (None, None))

    src
      .mapAsync(messagePreparationParallelism)(prepareMessage)
      .merge(timer)
      .groupedWithin(groupedWithinCount, groupedWithinDuration)
      .mapAsync(parallelism)(processMessageGroup)
      .mapConcat(x => x)
      .runWith(sink)
      .recoverWith { case e =>
        logConsumerFailed(e, logger)
        startMonitoringMetrics.incError()
        startMonitoring()
      }
      .void
  }

  private val processMessageGroupMetrics: ExecutionMetricSet = metricSet(this.getClass, "processMessageGroup").withTimeLogger(LOGGER)
  private final def processMessageGroup(messages: Seq[(Option[MESSAGE_TYPE], Option[CommittableOffset])]): Future[Seq[CommittableOffset]] = {
    trackedFuture(processMessageGroupMetrics)(() => {
      val messagesToProcess = messages.filter(_._1.isDefined).map(_._1.get)
      processMessageGroupMetrics.markHist("batch.size", messagesToProcess.size)
      Try(doProcessMessageGroup(messagesToProcess))
        .recover { case e: Exception => logger.error(s"Error processing of ${messagesToProcess.size} messages: failed to get future", e); processMessageGroupMetrics.incError(); Future.successful() }
        .get
        .recoverWith { case e => logger.error(s"Error processing of ${messagesToProcess.size} messages", e); processMessageGroupMetrics.incError(); Future.successful() }
        .map(_ => messages.flatMap(m => m._2))
    })
  }

  private val prepareMessageMetrics = metricSet(this.getClass, "prepareMessage")
  private def prepareMessage(committableMessage: CommittableMessage[String, String]): Future[(Option[MESSAGE_TYPE], Option[CommittableOffset])] = Future {
    Using(prepareMessageMetrics.time()) { _ =>
      Some(committableMessage)
        .filter(filterCommittableMessage)
        .map(parseMessage)
        .filter {
          case (None, _) => false // Parsed to empty Option
          case (Some(parsedMessage), origin) => filterParsedMessage(parsedMessage, origin)
        }
        .map { x => x._1.asJava.ifPresent(message => collectHealthMetrics(committableMessage.committableOffset.partitionOffset.key, message)); x }
        .map { case (maybeMessage, _) => maybeMessage -> Some(committableMessage.committableOffset) }
        .getOrElse(None -> Some(committableMessage.committableOffset))
    }.get
  }

  private def filterCommittableMessage(committableMessage: CommittableMessage[String, String]): Boolean = {
    Try(doFilterMessage(committableMessage))
      .recover { case e: Exception => logger.error(s"Error processing: failed to filter committableMessage $committableMessage", e); false }
      .get
  }

  private val parseMessageMetrics = metricSet(this.getClass, "parseMessage")
  private def parseMessage(message: CommittableMessage[String, String]): (Option[MESSAGE_TYPE], AppType) = {
    Using(parseMessageMetrics.time()) { _ => doParse(message) }
      .recover { case e: Exception => parseMessageMetrics.incError(); logger.error(s"Error processing: failed to parse ${message.record.value()}", e); (None, AppType.UNKNOWN) }
      .get
  }

  private val filterParsedMessageMetrics = metricSet(this.getClass, "filterParsedMessage")
  private def filterParsedMessage(parsedMessage: MESSAGE_TYPE, origin: AppType): Boolean = {
    Using(filterParsedMessageMetrics.time())(_ => doFilterMessage(parsedMessage, origin))
      .recover { case e: Exception => filterParsedMessageMetrics.incError(); logger.error(s"Error processing: failed to filter parsed message $parsedMessage", e); false }
      .get
  }

  private def collectHealthMetrics(groupTopicPartition: GroupTopicPartition, message: MESSAGE_TYPE): Unit = {
    Try(healthMetric.collectTime(groupTopicPartition.topic, groupTopicPartition.groupId, message))
      .recover { case e: Exception => logger.error(s"Failed to collect health metrics for message $message", e); () }
      .get
  }

}