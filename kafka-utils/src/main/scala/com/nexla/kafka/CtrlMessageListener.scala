package com.nexla.kafka

import akka.kafka.ConsumerMessage.CommittableMessage
import akka.stream.Materializer
import com.nexla.common.AppType
import com.nexla.common.NexlaConstants.TOPIC_CONTROL
import com.nexla.common.metrics.HealthMetricAggregator
import com.nexla.control.message.ControlMessage

import scala.concurrent.ExecutionContext

abstract class CtrlMessageListener(ctrlCommon: CtrlTopics,
                                   appType: AppType,
                                   parallelism: Int,
                                   metricAggregator: HealthMetricAggregator)
                                  (implicit ec: ExecutionContext,
                                   mat: Materializer)
  extends GenericMessageListener[ControlMessage](ctrlCommon, appType, metricAggregator, parallelism) {

  override def getTopicNames: Set[String] = Set(TOPIC_CONTROL)

  override def doParse(message: CommittableMessage[String, String]): (Option[ControlMessage], AppType) = {
    ctrlCommon.parseCtrlMessage(message)
  }
}