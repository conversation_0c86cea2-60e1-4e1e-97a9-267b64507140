<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.nexla</groupId>
		<artifactId>backend-connectors</artifactId>
		<version>3.2.1-SNAPSHOT</version>
	</parent>

	<groupId>com.nexla.probe</groupId>
	<artifactId>dropbox-probe</artifactId>

	<dependencies>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>file-service</artifactId>
			<version>${project.version}</version>
			<exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>connector-properties</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common</artifactId>
			<version>${nexla-backend-common.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>connector-properties</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-library</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>

		<dependency>
			<groupId>com.dropbox.core</groupId>
			<artifactId>dropbox-core-sdk</artifactId>
			<version>5.4.4</version>
		</dependency>

	</dependencies>

</project>
