package com.nexla.probe.dropbox;

import com.dropbox.core.DbxException;
import com.dropbox.core.DbxRequestConfig;
import com.dropbox.core.v2.DbxClientV2;
import com.dropbox.core.v2.DbxTeamClientV2;
import com.dropbox.core.v2.common.PathRoot;
import com.dropbox.core.v2.files.FileMetadata;
import com.dropbox.core.v2.files.FolderMetadata;
import com.dropbox.core.v2.files.ListFolderResult;
import com.dropbox.core.v2.files.Metadata;
import com.dropbox.core.v2.files.WriteMode;
import com.dropbox.core.v2.team.NamespaceMetadata;
import com.dropbox.core.v2.team.NamespaceType;
import com.dropbox.core.v2.team.TeamMemberInfo;
import com.dropbox.core.v2.team.TeamMemberProfile;
import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.ListingResourceType;
import com.nexla.common.NexlaBucket;
import com.nexla.common.NexlaFile;
import com.nexla.common.ResourceType;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.probe.ExceptionResolution;
import com.nexla.common.probe.ProbeException;
import com.nexla.connector.config.file.DirScanningMode;
import com.nexla.connector.config.file.DropBoxAuthConfig;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.file.service.FileConnectorService;
import com.nexla.file.service.FileDetails;
import com.nexla.listing.client.ListingClient;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.kafka.common.config.AbstractConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.nexla.common.ListingResourceType.FOLDER;
import static com.nexla.common.probe.ExceptionResolution.RETRY;
import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;
import static com.nexla.connector.ConnectorService.AuthResponse.authError;
import static com.nexla.connector.config.file.DirScanningMode.BOTH;
import static com.nexla.file.service.FileWalk.LevelFile;
import static com.nexla.file.service.FileWalk.walkFileTreeDfs;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static one.util.streamex.StreamEx.of;
import static org.apache.commons.lang3.StringUtils.removeEnd;

/**
 * S3 Probe Implementation. The credentials are passed encrypted
 * in the ProbeInputObject Additional Params (Including region) are
 * passed in ProbeInputObject.params
 */
public class DropboxConnectorService extends FileConnectorService<DropBoxAuthConfig> {

	private Logger logger = LoggerFactory.getLogger(DropboxConnectorService.class);

	private static final String NEXLA_TEMP_FILE_NAME = "nexla-temp";

	private static final Map<String, String> TEAM_FOLDER_TO_NAMESPACE_ID = Maps.newConcurrentMap();

	public DropboxConnectorService() {
		this(null, null, null);
	}

	public DropboxConnectorService(AdminApiClient adminApiClient, ListingClient listingClient, String credentialsDecryptKey) {
		super(adminApiClient, listingClient, credentialsDecryptKey);
	}

	@SneakyThrows
	DbxClientV2 getDropBoxClient(DropBoxAuthConfig authConfig) {
		String accessToken = authConfig.accessToken;

		DbxRequestConfig requestConfig = DbxRequestConfig
			.newBuilder("NexlaDemo")
			.withUserLocale("en_US")
			.build();

		return new DbxClientV2(requestConfig, accessToken);
	}

	DbxTeamClientV2 getDropBoxTeamClient(DropBoxAuthConfig authConfig) {
		String accessToken = authConfig.accessToken;

		DbxRequestConfig requestConfig = DbxRequestConfig
			.newBuilder("NexlaDemo")
			.withUserLocale("en_US")
			.build();

		return new DbxTeamClientV2(requestConfig, accessToken);
	}

	@Override
	public void initLogger(ResourceType resourceType, Integer resourceId, Optional<Integer> taskId) {
		this.logger = new NexlaLogger(logger, new NexlaLogKey(resourceType, resourceId, taskId));
	}

	@SneakyThrows
	@Override
	public AuthResponse authenticate(DropBoxAuthConfig authConfig) {
		try {

			if (authConfig.isBusinessAccount) {
				getDropBoxTeamClient(authConfig).team().teamFolderList();
			}else {
				getDropBoxClient(authConfig).files().listFolder("");
			}

			return SUCCESS;
		} catch (Exception e) {
			logger.error("DropBox Authentication failed, credsId={}", authConfig.getCredsId(), e);
			return authError(e);
		}
	}

	@Override
	public StreamEx<NexlaFile> listBucketContents(AbstractConfig c) {
		FileSourceConnectorConfig config = (FileSourceConnectorConfig) c;
		DropBoxAuthConfig authConfig = (DropBoxAuthConfig) config.getAuthConfig();

		if (authConfig.isBusinessAccount) {
			return listTeamFolders(authConfig, config.path, config.dirScanningMode, config.depth);
		}

		DbxClientV2 client = getDropBoxClient(authConfig);
		return of(getFiles(client, config.path, config.dirScanningMode, config.depth, ""));
	}

	@Override
	public boolean doesFileExistsInternal(FileConnectorAuth config, String key) {
		try {
			DropBoxAuthConfig authConfig = (DropBoxAuthConfig) config.getAuthConfig();

			Pair<DbxClientV2, String> dbxClientAndFilePath = getDbxClientAndFilePath(authConfig, key);

			Metadata result = dbxClientAndFilePath.getLeft()
				.files()
				.getMetadata(dbxClientAndFilePath.getRight());

			return result != null;
		} catch (Exception e) {
			return false;
		}
	}

	@Override
	public StreamEx<NexlaBucket> listBuckets(AbstractConfig config) {
		return StreamEx.empty();
	}

	@SneakyThrows
	@Override
	public InputStream readInputStreamInternal(FileConnectorAuth config, String file) {
		DropBoxAuthConfig authConfig = (DropBoxAuthConfig) config.getAuthConfig();

		Pair<DbxClientV2, String> dbxClientAndFilePath = getDbxClientAndFilePath(authConfig, file);

		return dbxClientAndFilePath.getLeft()
			.files()
			.downloadBuilder(dbxClientAndFilePath.getRight())
			.start()
			.getInputStream();
	}

	@Override
	public boolean checkWriteAccess(AbstractConfig c) {
		FileConnectorAuth config = (FileConnectorAuth) c;
		DropBoxAuthConfig authConfig = (DropBoxAuthConfig) config.getAuthConfig();
		String tempFile = Paths.get(config.getPath(), NEXLA_TEMP_FILE_NAME).toString();
		Pair<DbxClientV2, String> dbxClientAndFilePath = getDbxClientAndFilePath(authConfig, tempFile);
		DbxClientV2 client = dbxClientAndFilePath.getLeft();
		try {

			InputStream in = new ByteArrayInputStream("test".getBytes());

			client.files().uploadBuilder(dbxClientAndFilePath.getRight()).uploadAndFinish(in);
		} catch (Exception e) {
			logger.error("Failed to write a temp file", e);
			return false;
		}
		try {
			client.files().delete(tempFile);
		} catch (Exception e) {
			logger.error("Failed to delete the temp file", e);
		}

		return true;
	}

	@SneakyThrows
	@Override
	public FileDetails writeInternal(FileConnectorAuth config, String key, File file) {
		DropBoxAuthConfig authConfig = (DropBoxAuthConfig) config.getAuthConfig();
		Pair<DbxClientV2, String> dbxClientAndFilePath = getDbxClientAndFilePath(authConfig, key);
		DbxClientV2 client = dbxClientAndFilePath.getLeft();
		InputStream in = new FileInputStream(file);
		client.files().uploadBuilder(dbxClientAndFilePath.getRight()).withMode(WriteMode.OVERWRITE).uploadAndFinish(in);
		return new FileDetails(key, empty(), empty());
	}

	@SneakyThrows
	@Override
	public FileDetails writeInternal(FileConnectorAuth config, String key, InputStream inputStream) {
		DropBoxAuthConfig authConfig = (DropBoxAuthConfig) config.getAuthConfig();
		Pair<DbxClientV2, String> dbxClientAndFilePath = getDbxClientAndFilePath(authConfig, key);
		DbxClientV2 client = dbxClientAndFilePath.getLeft();
		client.files().uploadBuilder(dbxClientAndFilePath.getRight()).withMode(WriteMode.OVERWRITE).uploadAndFinish(inputStream);
		return new FileDetails(key, empty(), empty());
	}

	@Override
	public StreamEx<NexlaFile> listTopLevelBuckets(AbstractConfig configTemp) {

		FileSourceConnectorConfig config = (FileSourceConnectorConfig) configTemp;
		DropBoxAuthConfig authConfig = (DropBoxAuthConfig) config.getAuthConfig();
		DirScanningMode dirScanningMode = BOTH;

		if (authConfig.isBusinessAccount) {
			return listTeamFolders(authConfig, config.path, dirScanningMode, config.depth);
		}

		Integer maxDepth = config.depth;

		DbxClientV2 dropboxClient = getDropBoxClient(authConfig);
		return of(getFiles(dropboxClient, config.path, dirScanningMode, maxDepth, ""));
	}

	private StreamEx<NexlaFile> getFiles(
		DbxClientV2 dropboxClient, String path, DirScanningMode scanningMode, Integer maxDepth, String teamFolderName
	) {
		StreamEx<LevelFile<Metadata>> levelFiles = walkFileTreeDfs(
			scanningMode,
			() -> {
				try {
					String pathToScan = "/".equals(path) ? "" : path;
					return readDropboxFolder(dropboxClient, pathToScan)
						.map(f -> new LevelFile<>(1, f, path, isFolder(f)));
				} catch (DbxException e) {
					throw new RuntimeException(e);
				}
			},
			currFile -> {
				try {
					if (maxDepth == currFile.level || !isFolder(currFile.file)) {
						return StreamEx.empty();
					} else {
						String pathToScan = currFile.file.getPathLower();
						return readDropboxFolder(dropboxClient, pathToScan)
							.map(f -> new LevelFile(currFile.level + 1, f, pathToScan, isFolder(f)));
					}
				} catch (DbxException e) {
					throw new RuntimeException("Error scanning path: " + path, e);
				}
			});

		return levelFiles
			.map(it -> DropboxConnectorService.toNexlaFile(it, teamFolderName));
	}

	private StreamEx<Metadata> readDropboxFolder(DbxClientV2 dropboxClient, String pathToScan) throws DbxException {
		return StreamEx.iterate(dropboxClient.files().listFolder(pathToScan), listFolderResult -> listFolderContinue(dropboxClient, listFolderResult))
			.takeWhileInclusive(ListFolderResult::getHasMore)
			.flatMap(x -> x.getEntries().stream());
	}

	@SneakyThrows
	private ListFolderResult listFolderContinue(DbxClientV2 dropboxClient, ListFolderResult listFolderResult) {
		return dropboxClient.files().listFolderContinue(listFolderResult.getCursor());
	}

	private static boolean isFolder(Metadata file) {
		return file instanceof FolderMetadata;
	}

	private static NexlaFile toNexlaFile(LevelFile<Metadata> levelFile, String teamFolderName) {
		Metadata metadata = levelFile.file;
		String pathLower = teamFolderName + ofNullable(metadata.getPathLower()).orElse("");

		if (metadata instanceof FileMetadata) {

			FileMetadata fileMetadata = (FileMetadata) metadata;
			long modified = fileMetadata.getServerModified().getTime();
			long size = fileMetadata.getSize();
			String hash = fileMetadata.getContentHash();
			return new NexlaFile(pathLower, size, levelFile.dirPath, hash, modified, modified, ListingResourceType.FILE);

		} else if (metadata instanceof FolderMetadata) {
			return new NexlaFile(pathLower, 0L, levelFile.dirPath, null, null, null, FOLDER);

		} else {
			throw new IllegalArgumentException();
		}
	}

	@Override
	@SneakyThrows
	public void createDestination(AbstractConfig c, Optional<Integer> sourceId) {
		FileConnectorAuth config = (FileConnectorAuth) c;
		DropBoxAuthConfig authConfig = (DropBoxAuthConfig) config.getAuthConfig();
		String targetFilePath = removeEnd(config.getPath(), "/");
		Pair<DbxClientV2, String> dbxClientAndFilePath = getDbxClientAndFilePath(authConfig, targetFilePath);
		DbxClientV2 client = dbxClientAndFilePath.getLeft();
		try {
			client.files().createFolder(dbxClientAndFilePath.getRight());
		} catch (Exception e) {
			logger.error("Failed to write" + targetFilePath, e);
			throw new ProbeException("Could not create folder=" + targetFilePath);
		}
	}

	@Override
	protected Optional<ExceptionResolution> findResolution(Throwable e) {
		if (e instanceof DbxException) {
			return Optional.of(RETRY);
		} else {
			return Optional.empty();
		}
	}

	@SneakyThrows
	private Pair<DbxClientV2, String> getDbxClientAndFilePath(DropBoxAuthConfig authConfig, String file) {
		if (!authConfig.isBusinessAccount) {
			return Pair.of(getDropBoxClient(authConfig), file);
		}

		Pair<String, String> splitTeamFolderPath = splitTeamFolderPath(file);
		String teamFolder = splitTeamFolderPath.getLeft();
		String filePath = splitTeamFolderPath.getRight();
		DbxTeamClientV2 teamClient = getDropBoxTeamClient(authConfig);
		DbxClientV2 dbxClient = getDbxClientAsMember(teamClient, teamFolder, authConfig);
		return Pair.of(dbxClient, filePath);
	}

	@SneakyThrows
	private String getTeamMemberId(DbxTeamClientV2 temClient, DropBoxAuthConfig authConfig){
		if (StringUtils.isNotBlank(authConfig.teamMemberId)){
			return authConfig.teamMemberId;
		}

		var members = temClient.team()
			.membersList()
			.getMembers();

		if (StringUtils.isNotBlank(authConfig.teamMemberEmail)) {
			return members.stream()
				.map(TeamMemberInfo::getProfile)
				.filter(it -> it.getEmail().equals(authConfig.teamMemberEmail))
				.map(TeamMemberProfile::getTeamMemberId)
				.findFirst()
				.orElseThrow(() -> new RuntimeException("No team member found with provided email!"));
		}

		return members.get(0).getProfile().getTeamMemberId();
	}

	private static Pair<String, String> splitTeamFolderPath(String path) {
		String[] split = path.split("/");
		String teamFolder = split[0];

		if (split.length == 1) {
			return Pair.of(teamFolder, "");
		}

		String pathWithoutTeamFolder = StringUtils.join(split, "/", 1, split.length);

		return Pair.of(teamFolder, "/" + pathWithoutTeamFolder);
	}

	public static String getNamespaceId(DbxTeamClientV2 dropBoxTeamClient, String teamFolderName) {
		return TEAM_FOLDER_TO_NAMESPACE_ID.computeIfAbsent(teamFolderName, tfn -> getTeamFolders(dropBoxTeamClient)
			.stream()
			.filter(it -> it.getName().equals(tfn))
			.map(NamespaceMetadata::getNamespaceId)
			.findFirst()
			.orElseThrow(() -> new RuntimeException("Namespace ID not found for team " + tfn)));
	}

	@SneakyThrows
	private static List<NamespaceMetadata> getTeamFolders(DbxTeamClientV2 dropBoxTeamClient) {
		return dropBoxTeamClient
			.team()
			.namespacesList()
			.getNamespaces()
			.stream()
			.filter(it -> it.getNamespaceType().equals(NamespaceType.TEAM_FOLDER))
			.collect(Collectors.toList());
	}

	private StreamEx<NexlaFile> listTeamFolders(DropBoxAuthConfig authConfig, String path, DirScanningMode dirScanningMode, Integer depth) {
		DbxTeamClientV2 dropBoxTeamClient = getDropBoxTeamClient(authConfig);

		if (path.equals("") || path.equals("/")) {
			List<NamespaceMetadata> namespaceMetadata = getTeamFolders(dropBoxTeamClient);

			return StreamEx.of(namespaceMetadata)
				.map(teamFolderMetadata -> new NexlaFile(teamFolderMetadata.getName(), 0L, null, null, null, null, FOLDER));
		}

		Pair<String, String> splitTeamFolderPath = splitTeamFolderPath(path);
		String teamFolderName = splitTeamFolderPath.getLeft();

		DbxClientV2 listFilesClient = getDbxClientAsMember(dropBoxTeamClient, teamFolderName, authConfig);

		return of(getFiles(listFilesClient, splitTeamFolderPath.getRight(), dirScanningMode, depth, teamFolderName + "/"));
	}

	private DbxClientV2 getDbxClientAsMember(DbxTeamClientV2 dropBoxTeamClient, String teamFolderName, DropBoxAuthConfig authConfig) {
		String namespaceId = getNamespaceId(dropBoxTeamClient, teamFolderName);
		String teamMemberId = getTeamMemberId(dropBoxTeamClient, authConfig);

		return dropBoxTeamClient
			.asMember(teamMemberId)
			.withPathRoot(PathRoot.namespaceId(namespaceId));
	}
}
