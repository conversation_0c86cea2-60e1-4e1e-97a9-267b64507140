package com.nexla.sourceagent


import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import com.nexla.admin.client.AdminApiClient
import com.nexla.admin.client.config.SourceConfigUtils
import com.nexla.common.NexlaNamingUtils._
import com.nexla.common.{Resource, ResourceType}
import com.nexla.control.message.{ConnectorConfig, ServiceState}
import com.nexla.sc.api.RequestDtoFormat
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod.GET
import org.springframework.http.{HttpEntity, HttpHeaders, MediaType}
import org.springframework.web.client.{HttpClientErrorException, RestTemplate}
import spray.json.{DefaultJsonProtocol, RootJsonFormat}

import java.util
import java.util.Base64
import java.util.Collections.singletonList
import scala.collection.JavaConverters._
import scala.collection.mutable
import scala.util.{Failure, Success, Try}

class SourceConnectorClient(props: AppProps, restTemplate: RestTemplate, adminApiClient: AdminApiClient) extends StrictNexlaLogging
  with WithLogging
  with SprayJsonSupport
  with DefaultJsonProtocol
  with RequestDtoFormat {

  implicit val setConnectorStatusRequestFormat: RootJsonFormat[SetConnectorStatusRequest] = jsonFormat2(SetConnectorStatusRequest)

  private val CONNECTORS_URL = "http://localhost:8083/connectors"
  private val headers = {
    val h = new HttpHeaders()
    h.setContentType(MediaType.APPLICATION_JSON)
    h.setAccept(singletonList(MediaType.APPLICATION_JSON))
    h
  }

  def getConnectors: mutable.Buffer[String] = {
    val responseType = new ParameterizedTypeReference[util.List[String]]() {}
    val request = new HttpEntity("", headers)
    restTemplate.exchange(CONNECTORS_URL, GET, request, responseType)
      .getBody
      .asScala
  }

  def startConnector(): Unit = Try {
    logger.info("Starting connector...")
    val sourceId = props.sourceId
    val ds = adminApiClient.getDataSource(sourceId).get()
    val configMap = SourceConfigUtils.baseSourceConfig(ds)

    val name = connectorName(new Resource(sourceId, ResourceType.SOURCE))
    val connectorConfig = new ConnectorConfig(name, configMap)

    Try(restTemplate.postForObject(CONNECTORS_URL, new HttpEntity[ConnectorConfig](connectorConfig, headers), classOf[util.Map[_, _]]))
      .recover {
        case e: HttpClientErrorException.Conflict =>
          logger.warn(s"Source agent connector already exists (${e.getMessage}). Update existing connector configuration")
          val connectorUpdateUrl = CONNECTORS_URL + "/" + connectorConfig.name + "/config"
          restTemplate.put(connectorUpdateUrl, new HttpEntity[util.Map[String, String]](connectorConfig.config, headers))
        case e => throw e
      }
  } match {
    case Success(_) =>
      logger.info("Source agent connector started")
      updateState(props.sourceId, ServiceState.RUNNING, "Source agent connector started")
    case Failure(e) =>
      updateState(props.sourceId, ServiceState.ERROR, e.getMessage)
      throw e
  }

  private def updateState(sourceId: Int, state: ServiceState, message: String): Unit = {
    val url = s"${props.ctrlListenersUrl}/source/$sourceId/status"
    Try {
      restTemplate.postForObject(url, new HttpEntity[String](SetConnectorStatusRequest(state, message).toJson.compactPrint, buildAuthorizationHeader()), classOf[util.Map[_, _]])
    } match {
      case Success(_) => logger.info(s"Connector state for source $sourceId was successfully updated to ${state.toString}")
      case Failure(e) => logger.error(s"Connector state update for source $sourceId was failed: ${e.getMessage}")
    }
  }

  private def buildAuthorizationHeader(): HttpHeaders = {
    val authString = props.nexlaCreds.username + ":" + props.nexlaCreds.password
    val authorizationHeader = "Basic " + new String(Base64.getEncoder.encode(authString.getBytes))
    val headers = new HttpHeaders
    headers.add("Authorization", authorizationHeader)
    headers.setContentType(MediaType.APPLICATION_JSON)
    headers.setAccept(singletonList(MediaType.APPLICATION_JSON))
    headers
  }

  case class SetConnectorStatusRequest(state: ServiceState, message: String)
}
