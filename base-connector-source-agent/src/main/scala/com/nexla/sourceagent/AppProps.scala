package com.nexla.sourceagent

import com.nexla.connector.config.vault.NexlaAppConfig
import com.nexla.sc.config.{AwsCredentials, NexlaAdminApi, NexlaCreds, NexlaEndpoints, NexlaSslConfig}

class AppProps(val config: NexlaAppConfig) extends NexlaSslConfig
  with NexlaAdminApi
  with NexlaEndpoints
  with AwsCredentials
  with NexlaCreds {

  val sourceId: Int = config.getInt("source.id")
  val ctrlListenersUrl: String = config.getOptString("ctrl.listeners.url").getOrElse("https://ctrl-listeners.nexla.svc.cluster.local:8080")
}
