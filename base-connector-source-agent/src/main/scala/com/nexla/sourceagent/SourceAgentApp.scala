package com.nexla.sourceagent

import akka.actor.ActorSystem
import akka.stream.Materializer
import com.nexla.admin.client.AdminApiClientBuilder
import com.nexla.common.{AppType, NexlaSslContext, RestTemplateBuilder}
import com.nexla.sc.util.{AppUtils, Async}
import com.typesafe.scalalogging.Logger
import org.slf4j.LoggerFactory

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}

object SourceAgentApp extends App
  with AppUtils {

  implicit val system: ActorSystem = defaultActorSystem(Logger(LoggerFactory.getLogger(getClass.getName)))
  implicit val materializer: Materializer = Materializer(system)
  implicit val executionContext: ExecutionContext = Async.ioExecutorContext

  implicit private val (props, _, _) = loadProps(AppType.SOURCE_AGENT, new AppProps(_))
  implicit val appSslContext: NexlaSslContext = nexlaSslContext(props)

  val restTemplate = new RestTemplateBuilder().withSSL(appSslContext).build()

  val adminApi = new AdminApiClientBuilder()
    .setAppName(AppType.SOURCE_AGENT.appName)
    .setDataPlaneUid(props.dataplaneUid)
    .setEnrichmentUrl(props.credEnrichmentUrl)
    .setNoCache(true)
    .create(props.apiCredentialsServer, props.apiAccessKey, restTemplate)

  private val sourceConnectorClient = new SourceConnectorClient(props, restTemplate, adminApi)
  while (Try(sourceConnectorClient.getConnectors).isFailure) {
    logger.info(s"Kafka Connect application is not ready...")
    Thread.sleep(10000)
  }

  logger.info(s"Kafka Connect application is ready")

  startSourceAgent()

  private def startSourceAgent(): Unit = {
    Try {
      logger.info(s"Starting SourceAgent")
      sourceConnectorClient.startConnector()
    } match {
      case Success(_) => logger.info("SourceAgent started")
      case Failure(e) =>
        logger.error("Failed to start SourceAgent: {}", e.getMessage, e)
        System.exit(1)
    }
  }
}
