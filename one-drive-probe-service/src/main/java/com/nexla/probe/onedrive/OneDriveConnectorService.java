package com.nexla.probe.onedrive;

import com.azure.core.credential.AccessToken;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.google.common.annotations.VisibleForTesting;
import com.microsoft.graph.core.models.IProgressCallback;
import com.microsoft.graph.core.tasks.LargeFileUploadTask;
import com.microsoft.graph.core.tasks.PageIterator;
import com.microsoft.graph.drives.item.items.item.createuploadsession.CreateUploadSessionPostRequestBody;
import com.microsoft.graph.models.DriveItem;
import com.microsoft.graph.models.DriveItemCollectionResponse;
import com.microsoft.graph.models.DriveItemUploadableProperties;
import com.microsoft.graph.serviceclient.GraphServiceClient;
import com.microsoft.kiota.ApiException;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.*;
import com.nexla.common.exception.AuthFailException;
import com.nexla.common.exception.ResourceNotFoundException;
import com.nexla.common.io.CloseableInputStream;
import com.nexla.connector.config.file.DirScanningMode;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.rest.RestAuthConfig;
import com.nexla.file.service.FileConnectorService;
import com.nexla.file.service.FileDetails;
import com.nexla.file.service.FileWalk;
import com.nexla.listing.client.ListingClient;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.kafka.common.config.AbstractConfig;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.*;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;

import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;
import static com.nexla.connector.ConnectorService.AuthResponse.authError;
import static com.nexla.connector.config.file.DirScanningMode.BOTH;
import static com.nexla.connector.config.rest.RestAuthConfig.OAUTH2_TOKEN_THREE_LEGGED;
import static com.nexla.file.service.FileWalk.walkFileTreeDfs;
import static java.util.Objects.nonNull;
import static java.util.Optional.*;

public class OneDriveConnectorService extends FileConnectorService<BaseAuthConfig> {
    private static Logger logger = LoggerFactory.getLogger(OneDriveConnectorService.class);
    public static final String[] SCOPES = new String[]{"files.readwrite.all", "user.read", "offline_access"};

    private static final String NEXLA_TEMP_FILE_NAME = "nexla-temp.txt";
    private static final int PAGE_SIZE = 500;

    /*
        Upload limit is 4mb according to https://learn.microsoft.com/en-us/onedrive/developer/rest-api/api/driveitem_put_content?view=odsp-graph-online
        But we take value that is slightly small that 4mb to allow some error margin on API implementation. 4000000L is good number for that
     */
    private static final Long SMALL_UPLOAD_LIMIT = 4000000L;

    /*  lifetime is assigned a random value ranging between 60-90 minutes (75 minutes on average). We take the minimum.
        https://learn.microsoft.com/en-us/entra/identity-platform/configurable-token-lifetimes
    */
    private static long TOKEN_LIFETIME_SECONDS = 60 * 60;

    public OneDriveConnectorService(AdminApiClient adminApiClient, ListingClient listingClient, String credentialsDecryptKey) {
        super(adminApiClient, listingClient, credentialsDecryptKey);
    }

    private static OffsetDateTime convertTime(LocalDateTime localDateTime) {
        Long millis = localDateTime.toDateTime(DateTimeZone.UTC).toInstant().getMillis();
        return OffsetDateTime.ofInstant(Instant.ofEpochMilli(millis), ZoneId.of("UTC"));
    }

    @SneakyThrows
    @VisibleForTesting
    GraphServiceClient createGraphService(Integer credsId) {
        var creds = adminApiClient.getDataCredentials(credsId)
                .orElseThrow(() -> new ResourceNotFoundException(String.format("Credential with ID %s not found!", credsId)));
        var credsMap = NexlaDataCredentials.getCreds(credentialsDecryptKey, creds.getCredentialsEnc(), creds.getCredentialsEncIv());

        var restConfig = new RestAuthConfig(credsMap, credsId);

        if (is3LeggedOauth(restConfig)) {
            var expiresAt = restConfig.getVendorLastRefreshedAt()
                    .map(OneDriveConnectorService::convertTime)
                    .orElse(OffsetDateTime.now())
                    .plusSeconds(restConfig.getVendorTokenExpiresInSec().orElse(TOKEN_LIFETIME_SECONDS));

            var accessTokenMono = Mono.just(new AccessToken(restConfig.vendorAccessToken, expiresAt));
            logger.info("Microsoft-graph [creds-{}]: token vendor last refreshed at={}", credsId, restConfig.vendorLastRefreshedAt);

            return new GraphServiceClient(tokenRequestContext -> accessTokenMono, SCOPES);
        }

        var clientSecretCredentialAuth = new ClientSecretCredentialBuilder()
                .clientId(restConfig.getOauth2ClientId())
                .clientSecret(restConfig.getOauth2ClientSecret())
                .tenantId(extractTenantIdFromTokenURL(restConfig.getOauth2AccessTokenUrl()))
                .maxRetry(3)
                .build();

        return new GraphServiceClient(clientSecretCredentialAuth);
    }

    private String extractTenantIdFromTokenURL(String tokenUrl) {
        // URl looks like https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/token
        return tokenUrl.replace("https://", "").split("/")[1];
    }

    private boolean is3LeggedOauth(RestAuthConfig restConfig) {
        return restConfig.getTokenExchangeType().filter(it -> it.equals(OAUTH2_TOKEN_THREE_LEGGED)).isPresent()
                || restConfig.getVendorRefreshToken().isPresent();
    }

    private <T, U> U withClientRetriable(Integer credsId, Function<GraphServiceClient, U> consumer) {
        try {
            GraphServiceClient client = createGraphService(credsId);
            return consumer.apply(client);
        } catch (ApiException e) {
            if (isErrorCausedByToken(e)) {
                listingClient.refreshRestToken(credsId);
                // invalidate cache to get fresh config after refresh
                adminApiClient.invalidate(credsId, Optional.empty(), ResourceType.CREDENTIALS);
                GraphServiceClient client = createGraphService(credsId);
                return consumer.apply(client);
            }
            throw e;
        }
    }

    private boolean isErrorCausedByToken(ApiException e) {
        return e.getResponseStatusCode() == HttpStatus.SC_UNAUTHORIZED;
    }

    @Override
    @SneakyThrows
    public AuthResponse authenticate(BaseAuthConfig authConfig) {
        try {
            return withClientRetriable(authConfig.getCredsId(), this::doAuth);
        } catch (Throwable e) {
            logger.error("[creds-{}]", authConfig.getCredsId(), e);
            return authError(e);
        }
    }

    @SneakyThrows
    private AuthResponse doAuth(GraphServiceClient graphService) {
        // If the user hasn't set up OneDrive yet, there is no sense in
        // Auth him (even if he has access to other resources in the graph).
        // Getting an ID is the easiest way to check the use of OneDrive
        var oneDriveId = getOneDriveId(graphService);
        if (nonNull(oneDriveId)) {
            return SUCCESS;
        } else {
            throw new AuthFailException("Can't get one drive id");
        }
    }

    @Override
    public StreamEx<NexlaBucket> listBuckets(AbstractConfig abstractConfig) {
        return StreamEx.empty();
    }

    @Override
    @SneakyThrows
    public StreamEx<NexlaFile> listTopLevelBuckets(AbstractConfig c) {
        FileSourceConnectorConfig config = (FileSourceConnectorConfig) c;
        return withClientRetriable(config.getAuthConfig().getCredsId(), driveService -> listTopLevelBucketsInternal(config, driveService));
    }

    public StreamEx<NexlaFile> listTopLevelBucketsInternal(FileSourceConnectorConfig config, GraphServiceClient graphService) {
        var oneDriveId = getOneDriveId(graphService);
        var dirScanningMode = BOTH;
        var depth = Optional.ofNullable(config.depth).orElse(0);
        return getFiles(graphService, oneDriveId, config.path, dirScanningMode, depth);
    }

    @Override
    public StreamEx<NexlaFile> listBucketContents(AbstractConfig abstractConfig) {
        FileSourceConnectorConfig config = (FileSourceConnectorConfig) abstractConfig;
        return withClientRetriable(config.getAuthConfig().getCredsId(), driveService -> listBucketContentsInternal(config, driveService));
    }

    public StreamEx<NexlaFile> listBucketContentsInternal(FileSourceConnectorConfig config, GraphServiceClient graphService) {
        var oneDriveId = getOneDriveId(graphService);
        var depth = Optional.ofNullable(config.depth).orElse(0);
        return getFiles(graphService, oneDriveId, config.path, config.dirScanningMode, depth);
    }

    private StreamEx<NexlaFile> getFiles(GraphServiceClient graphService, String oneDriveId, String path, DirScanningMode scanningMode, Integer maxDepth) {
        StreamEx<FileWalk.LevelFile<NexlaFile>> levelFiles = walkFileTreeDfs(
                scanningMode,
                () -> {
                    try {
                        String pathToScan = isRootPath(path) ? "" : path;
                        return readOneDriveFolder(graphService, oneDriveId, pathToScan)
                                .map(f -> new FileWalk.LevelFile<>(1, f, path, isFolder(f)));
                    } catch (ApiException e) {
                        throw new RuntimeException(e);
                    }
                },
                currFile -> {
                    try {
                        if (maxDepth == currFile.level || !isFolder(currFile.file)) {
                            return StreamEx.empty();
                        } else {
                            String pathToScan = currFile.file.getFullPath();
                            return readOneDriveFolder(graphService, oneDriveId, pathToScan)
                                    .map(f -> new FileWalk.LevelFile(currFile.level + 1, f, pathToScan, isFolder(f)));
                        }
                    } catch (ApiException e) {
                        throw new RuntimeException("Error scanning path: " + path, e);
                    }
                });

        return levelFiles.map(it -> it.file);
    }

    private String getOneDriveId(GraphServiceClient graphService) {
        return graphService.me().drive().get().getId();
    }

    private boolean isFolder(NexlaFile file) {
        return file.getType() == ListingResourceType.FOLDER;
    }

    private Boolean isRootPath(String path) {
        return path == null || path.trim().isEmpty() || path.trim().equals("/");
    }

    @SneakyThrows
    private String encodePathSegment(String segment) {
        return URLEncoder.encode(segment, "UTF-8");
    }

    private String toOneDriveEncodedPath(String path) {
        if (!isRootPath(path)) {
            var pathWithoutPrefix = StringUtils.removeStart(path, "/");
            return "root:/" + StreamEx.of(pathWithoutPrefix.split("/")).map(x -> encodePathSegment(x)).joining("/") + ":";
        } else {
            return "root";
        }
    }

    @SneakyThrows
    private StreamEx<NexlaFile> readOneDriveFolder(GraphServiceClient graphService, String oneDriveId, String pathToScan) {
        String oneDrivePath = toOneDriveEncodedPath(pathToScan);
        DriveItemCollectionResponse driveItemFirstPage = graphService
                .drives()
                .byDriveId(oneDriveId)
                .items()
                .byDriveItemId(oneDrivePath).children().get(requestConfiguration -> requestConfiguration.queryParameters.top = PAGE_SIZE);

        List<DriveItem> allDriveItems = new ArrayList();
        PageIterator<DriveItem, DriveItemCollectionResponse> pageIterator = new PageIterator.Builder<DriveItem, DriveItemCollectionResponse>()
                .client(graphService)
                // The first page of the collection is passed to the collectionPage method
                .collectionPage(driveItemFirstPage)
                // CollectionPageFactory is called to create a new collection page from the nextLink
                .collectionPageFactory(DriveItemCollectionResponse::createFromDiscriminatorValue)
                // ProcessPageItemCallback is called for each item in the collection
                .processPageItemCallback(driveItem -> {
                    allDriveItems.add(driveItem);
                    return true;
                }).build();

        // Handles the process of iterating through every page and every item
        pageIterator.iterate();

        Stream<NexlaFile> nexlaFileStream = allDriveItems
                .stream()
                .map(item -> {
                    String fullPath = isRootPath(pathToScan) ? item.getName() : Paths.get(pathToScan, item.getName()).toString();
                    var file = item.getFile();
                    var hash = ofNullable(file).map(x -> x.getHashes()).map(x -> x.getQuickXorHash()).orElse(null);

                    return new NexlaFile(fullPath, item.getSize(), null, hash,
                            extractTimeMillis(item.getCreatedDateTime()),
                            extractTimeMillis(item.getLastModifiedDateTime()),
                            nonNull(file) ? ListingResourceType.FILE : ListingResourceType.FOLDER
                    );
                });

        return StreamEx.of(nexlaFileStream);
    }

    private Long extractTimeMillis(OffsetDateTime dateTime) {
        return ofNullable(dateTime)
                .map(it -> it.toInstant().toEpochMilli())
                .orElse(null);
    }

    @Override
    public boolean doesFileExistsInternal(FileConnectorAuth config, String key) {
        return withClientRetriable(config.getAuthConfig().getCredsId(), driveService -> doesFileExistsInternal(driveService, key));
    }

    @SneakyThrows
    public boolean doesFileExistsInternal(GraphServiceClient driveService, String key) {
        try {
            String oneDriveId = getOneDriveId(driveService);
            String oneDrivePath = toOneDriveEncodedPath(key);

            DriveItem driveItem = driveService
                    .drives()
                    .byDriveId(oneDriveId)
                    .items()
                    .byDriveItemId(oneDrivePath)
                    .get();

            return nonNull(driveItem);
        } catch (ApiException e) {
            if (HttpStatus.SC_NOT_FOUND == e.getResponseStatusCode()) {
                return false;
            }
            throw e;
        }
    }

    @Override
    public InputStream readInputStreamInternal(FileConnectorAuth config, String file) {
        return withClientRetriable(config.getAuthConfig().getCredsId(), driveService -> readInputStreamInternal(driveService, config, file));
    }

    @SneakyThrows
    public InputStream readInputStreamInternal(GraphServiceClient driveService, FileConnectorAuth config, String file) {
        java.io.File tempFile = Files.createTempFile("", "").toFile();
        OutputStream outputStream = new FileOutputStream(tempFile);

        try {
            String oneDriveId = getOneDriveId(driveService);
            String oneDrivePath = toOneDriveEncodedPath(file);

            long bytesTransferred = driveService
                    .drives()
                    .byDriveId(oneDriveId)
                    .items()
                    .byDriveItemId(oneDrivePath)
                    .content()
                    .get()
                    .transferTo(outputStream);

            logger.info("M=readInputStreamInternal, path={}, file={}, bytesTransferred={}", config.getPath(), file, bytesTransferred);
        } catch (ApiException e) {
            logger.error("M=readInputStreamInternal, path={}, file={}. Error: {}", config.getPath(), file, e.getMessage());
            throw e;
        }

        return new CloseableInputStream(new FileInputStream(tempFile))
                .onClose(tempFile::delete);
    }

    @Override
    public boolean checkWriteAccess(AbstractConfig c) {
        FileSourceConnectorConfig config = (FileSourceConnectorConfig) c;
        return withClientRetriable(config.getAuthConfig().getCredsId(), driveService -> checkWriteAccessInternal(driveService, config));
    }

    @SneakyThrows
    public boolean checkWriteAccessInternal(GraphServiceClient driveService, FileConnectorAuth config) {
        java.io.File tempFile = Files.createTempFile("", "").toFile();

        OutputStream outputStream = new FileOutputStream(tempFile);
        outputStream.write("test".getBytes());
        outputStream.close();

        String tempFileKey = Paths.get(config.getPath(), NEXLA_TEMP_FILE_NAME).toString();
        String oneDrivePath = toOneDriveEncodedPath(tempFileKey);
        String oneDriveId;

        try {
            oneDriveId = getOneDriveId(driveService);
            uploadFileInternal(driveService, oneDriveId, tempFileKey, tempFile);
        } catch (Exception e) {
            logger.error("Failed to write a temp file", e);
            tempFile.delete();
            return false;
        }

        try {
            driveService
                    .drives()
                    .byDriveId(oneDriveId)
                    .items()
                    .byDriveItemId(oneDrivePath)
                    .delete();
        } catch (Exception e) {
            logger.error("Failed to delete the temp file", e);
        }

        tempFile.delete();
        return true;
    }


    @Override
    public FileDetails writeInternal(FileConnectorAuth c, String key, File file) {
        FileSourceConnectorConfig config = (FileSourceConnectorConfig) c;
        return withClientRetriable(config.getAuthConfig().getCredsId(), driveService -> uploadFileInternal(driveService, key, file));
    }

    @Override
    public FileDetails writeInternal(FileConnectorAuth config, String key, InputStream inputStream) {
        java.io.File tempFile = copyToTempFile(inputStream);
        try {
            return writeInternal(config, key, tempFile);
        } finally {
            tempFile.delete();
        }
    }

    @SneakyThrows
    private java.io.File copyToTempFile(InputStream inputStream) {
        java.io.File tempFile = Files.createTempFile("", "").toFile();
        FileUtils.copyInputStreamToFile(inputStream, tempFile);
        return tempFile;
    }

    public FileDetails uploadFileInternal(GraphServiceClient driveService, String filePathKey, File file) {
        String oneDriveId = getOneDriveId(driveService);
        uploadFileInternal(driveService, oneDriveId, filePathKey, file);
        return new FileDetails(filePathKey, empty(), empty());
    }

    @SneakyThrows
    public void uploadFileInternal(GraphServiceClient driveService, String oneDriveId, String filePathKey, File file) {
        long fileSize = file.length();
        // Uploading API for small files requires fewer API calls and is more efficient,
        // but it has limits. So, we prefer it when we can
        if (fileSize <= SMALL_UPLOAD_LIMIT) {
            uploadSmallFileInternal(driveService, oneDriveId, file, filePathKey);
        } else {
            uploadBigFileInternal(driveService, oneDriveId, file, filePathKey);
        }
    }

    @SneakyThrows
    public void uploadSmallFileInternal(GraphServiceClient driveService, String oneDriveId, File file, String filePathKey) {
        try {
            String oneDrivePath = toOneDriveEncodedPath(filePathKey);
            long fileSize = file.length();
            FileInputStream fileInput = new FileInputStream(file);

            driveService
                    .drives()
                    .byDriveId(oneDriveId)
                    .items()
                    .byDriveItemId(oneDrivePath)
                    .content()
                    .put(fileInput, configuration -> configuration.headers.add("Content-Length", String.valueOf(fileSize)));

            logger.info("M=uploadSmallFileInternal, key={}", filePathKey);
        } catch (ApiException e) {
            logger.error("M=uploadSmallFileInternal, key={}. Error: {}", filePathKey, e.getMessage());
            throw e;
        }
    }

    @SneakyThrows
    public void uploadBigFileInternal(GraphServiceClient driveService, String oneDriveId, File file, String filePathKey) {
        String oneDrivePath = toOneDriveEncodedPath(filePathKey);
        String fileName = Paths.get(filePathKey).getFileName().toString();

        long fileSize = file.length();
        FileInputStream fileInput = new FileInputStream(file);

        DriveItemUploadableProperties driveItemUploadableProperties = new DriveItemUploadableProperties();
        driveItemUploadableProperties.setName(fileName);

        // Finish setting up the request body
        CreateUploadSessionPostRequestBody uploadSessionPostRequestBody = new CreateUploadSessionPostRequestBody();
        uploadSessionPostRequestBody.setItem(driveItemUploadableProperties);

        var uploadSession = driveService
                .drives()
                .byDriveId(oneDriveId)
                .items()
                .byDriveItemId(oneDrivePath)
                .createUploadSession()
                .post(uploadSessionPostRequestBody);

        // Create the large file upload task
        LargeFileUploadTask<DriveItemUploadableProperties> uploadTask =
                new LargeFileUploadTask(driveService.getRequestAdapter(),
                        uploadSession,
                        fileInput,
                        fileSize,
                        DriveItemUploadableProperties::createFromDiscriminatorValue);

        IProgressCallback progressCallback = (current, max) -> {
            // Upload seems slow, so better write progress
            logger.info("M=uploadBigFileInternal, path={}, uploaded {} from {}", filePathKey, current, max);
        };

        // We don't retry with the uploader and leave retry logic for the upper level to control. TODO: confirm
        try {
            uploadTask.upload(1, progressCallback);
            logger.info("M=uploadBigFileInternal, key={}", filePathKey);
        } catch (ApiException e) {
            logger.error("M=uploadBigFileInternal, key={}. Error: {}", filePathKey, e.getMessage());
            throw e;
        }
    }
}
