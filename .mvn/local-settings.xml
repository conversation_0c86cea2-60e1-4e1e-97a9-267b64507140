<settings>
    <servers>
        <server>
            <id>codeartifact</id>
            <username>aws</username>
            <password>${env.CODEARTIFACT_AUTH_TOKEN}</password>
        </server>
        <server>
            <id>dev-nexla-maven-central-store</id>
            <username>aws</username>
            <password>${env.CODEARTIFACT_AUTH_TOKEN}</password>
        </server>
    </servers>
    <profiles>
        <profile>
            <id>default</id>
            <repositories>
                <repository>
                    <id>codeartifact</id>
                    <url>https://nexla-433433586750.d.codeartifact.us-east-2.amazonaws.com/maven/nexla/</url>
                </repository>
                <repository>
                    <id>dev-nexla-maven-central-store</id>
                    <url>https://dev-nexla-433433586750.d.codeartifact.us-east-2.amazonaws.com/maven/maven-central-store/</url>
                </repository>
            </repositories>
        </profile>
    </profiles>
    <mirrors>
        <mirror>
            <id>dev-nexla-maven-central-store</id>
            <name>dev-nexla-maven-central-store</name>
            <url>https://dev-nexla-433433586750.d.codeartifact.us-east-2.amazonaws.com/maven/maven-central-store/</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
    </mirrors>
    <activeProfiles>
        <activeProfile>default</activeProfile>
    </activeProfiles>
</settings>
