package com.nexla.sc.client.listing


import akka.actor.ActorSystem
import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import akka.http.scaladsl.marshalling.Marshal
import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.model.{RequestEntity, Uri}
import com.nexla.sc.config.NexlaCredsConf
import org.mockito.Mockito
import org.mockito.Mockito.verify
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import spray.json.DefaultJsonProtocol

import java.time.{Instant, LocalDateTime, ZoneId}
import java.util.Base64
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext, ExecutionContextExecutor}

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class ListingAppClientTest extends AnyFlatSpecLike
  with Matchers with DefaultJsonProtocol
  with SprayJsonSupport {

    implicit val system: ActorSystem = ActorSystem.apply()
    implicit val ec: ExecutionContextExecutor = ExecutionContext.global

    val listingAppUrl = "http://not.a.valid:8181"
    val username = "spy"
    val password = "secret"

    val instance: ListingAppClient =
      Mockito.spy(new ListingAppClient(listingAppUrl, NexlaCredsConf(username, password)))

    it should "marshal arguments into a service call properly" in {
      val sourceId = 42
      instance.listFiles(sourceId,
        Option(List(FileStatuses.New)),
        createdAtFrom = Option(LocalDateTime.ofInstant(Instant.ofEpochSecond(1704067200L), ZoneId.of("UTC"))))

      verify(instance)
        .get(Uri(s"$listingAppUrl/files/$sourceId?status=NEW&createdAtFrom=2024-01-01T00:00"),
          Seq(
            RawHeader("Authorization", s"Basic ${new String(Base64.getEncoder.encode(s"$username:$password".getBytes))}"),
            RawHeader("Accept", "application/json")
          ))
    }

  it should "marshal arguments into a service call properly /batchHeartbeat" in {
    val sourceId = 42
    val filesOfSource = Set(23L, 24L)
    instance.batchHeartBeat(sourceId, filesOfSource)

    val requestEntity: RequestEntity = Await.result(Marshal(filesOfSource).to[RequestEntity], Duration.Inf)
    verify(instance)
      .post(Uri(s"$listingAppUrl/source/$sourceId/batchHeartbeat"),
        Seq(
          RawHeader("Authorization", s"Basic ${new String(Base64.getEncoder.encode(s"$username:$password".getBytes))}"),
          RawHeader("Accept", "application/json")
        ), requestEntity)
  }

  it should "expand statuses to multiple query params" in {
    val sourceId = 42
    instance.listFiles(sourceId, Option(List(FileStatuses.New, FileStatuses.Stopped)),
      createdAtFrom = Option(LocalDateTime.ofInstant(Instant.ofEpochSecond(1704067200L), ZoneId.of("UTC"))))

    verify(instance)
      .get(Uri(s"$listingAppUrl/files/$sourceId?status=NEW&status=STOPPED&createdAtFrom=2024-01-01T00:00"),
        Seq(
          RawHeader("Authorization", s"Basic ${new String(Base64.getEncoder.encode(s"$username:$password".getBytes))}"),
          RawHeader("Accept", "application/json")
        ))
  }
}
