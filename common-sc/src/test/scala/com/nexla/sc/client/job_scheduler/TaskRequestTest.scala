package com.nexla.sc.client.job_scheduler;

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import com.nexla.connector.config.PipelineTaskType
import org.scalatest.{OneInstancePerTest, TagAnnotation}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import spray.json.DefaultJsonProtocol;

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class TaskRequestTest
  extends AnyFlatSpecLike with Matchers
    with SprayJsonSupport
    with DefaultJsonProtocol
    with OneInstancePerTest {

  it should "parse Task ID" in {
    PipelineTaskType.values().foreach(t => {
      val taskId = TaskRequest.taskId(100, t)

      val parsed = TaskRequest.parseTaskId(taskId)
      parsed._1 shouldEqual t
      parsed._2 shouldEqual 100
    })
  }

  it should "fail to parse task ID" in {
    a[IllegalArgumentException] shouldBe thrownBy (TaskRequest.parseTaskId("10-some-100"))   // Pattern not matched
    a[IllegalArgumentException] shouldBe thrownBy (TaskRequest.parseTaskId("sometype-100"))  // Not existing type
  }
}