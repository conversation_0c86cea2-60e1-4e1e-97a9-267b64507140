package com.nexla.sc.util

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import org.scalatest.{OneInstancePerTest, TagAnnotation}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import spray.json.DefaultJsonProtocol

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class TimerLoggerTest
  extends AnyFlatSpecLike
    with Matchers
    with SprayJsonSupport
    with DefaultJsonProtocol
    with OneInstancePerTest {

  it should "TimerLogger" in {
    val timerLogger = new TimerLogger
    timerLogger.addTime("s1", 1000)
    timerLogger.addTime("s2", 1800)
    timerLogger.addTime("s3", 2500)
    timerLogger.logTimes() shouldEqual "s2 = 800,s3 = 700"
    timerLogger.duration() shouldEqual 1500
  }

}
