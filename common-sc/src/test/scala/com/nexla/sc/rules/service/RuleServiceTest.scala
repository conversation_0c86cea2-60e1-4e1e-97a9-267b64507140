package com.nexla.sc.rules.service

import akka.actor.ActorSystem
import com.nexla.admin.client.flownode.{AdminApiFlow, NexlaFlow}
import com.nexla.admin.client.{AdminApiClient, DataSet, DataSink, DataSource, Org}
import com.nexla.common.{AppType, Resource, ResourceType}
import org.mockito.Mockito.{mock, when}
import org.scalatest.flatspec.AnyFlatSpecLike

import java.time.Duration
import java.util
import java.util.Optional
import java.util.Optional.ofNullable
import scala.concurrent.ExecutionContext


class RuleServiceTest extends AnyFlatSpecLike {

  implicit val system: ActorSystem = ActorSystem()
  implicit val ec: ExecutionContext = system.dispatcher

  it should "have empty rule" in {
    val dataSetId = 100
    val orgId = 101
    val flowId = 102

    val adminApiClient = mock(classOf[AdminApiClient])

    val ruleServiceForTest = new RuleServiceForTest(adminApiClient)

    val dataSet = mock(classOf[DataSet])
    val org = mock(classOf[Org])
    when(dataSet.getOrg).thenReturn(org)
    when(org.getId).thenReturn(orgId)
    when(adminApiClient.getDataSet(dataSetId)).thenReturn(Optional.of(dataSet))

    val flow = mock(classOf[NexlaFlow])
    when(adminApiClient.getFlowByResource(new Resource(dataSetId, ResourceType.DATASET))).thenReturn(ofNullable(flow))

    val adminApiFlow = mock(classOf[AdminApiFlow])
    when(adminApiFlow.getId).thenReturn(flowId)
    val list = new util.ArrayList[AdminApiFlow]()
    list.add(adminApiFlow)
    when(flow.getFlows).thenReturn(list)

    val rule = ruleServiceForTest.getRule(dataSetId)

    assert(rule.isEmpty)
  }

  it should "have non empty rule" in {
    val dataSetId = 100
    val orgId = 101
    val flowId = 102

    val adminApiClient = mock(classOf[AdminApiClient])

    val ruleForTest = CanaryRule(1, "sync-api", null, 101, null, "http", null, null, null, false)
    val rulesForTest = Set.apply(ruleForTest)
    val ruleServiceForTest = new RuleServiceForTest(adminApiClient, () => rulesForTest)

    val dataSet = mock(classOf[DataSet])
    val org = mock(classOf[Org])
    when(dataSet.getOrg).thenReturn(org)
    when(org.getId).thenReturn(orgId)
    when(adminApiClient.getDataSet(dataSetId)).thenReturn(Optional.of(dataSet))

    val flow = mock(classOf[NexlaFlow])
    when(adminApiClient.getFlowByResource(new Resource(dataSetId, ResourceType.DATASET))).thenReturn(ofNullable(flow))

    val adminApiFlow = mock(classOf[AdminApiFlow])
    when(adminApiFlow.getId).thenReturn(flowId)
    val list = new util.ArrayList[AdminApiFlow]()
    list.add(adminApiFlow)
    when(flow.getFlows).thenReturn(list)

    val rule = ruleServiceForTest.getRule(dataSetId)

    assert(rule.nonEmpty)
    assert(rule.get.equals(ruleForTest))
  }

  it should "update cache asynchronously" in {
    val dataSetId = 100
    val orgId = 101
    val flowId = 102

    val adminApiClient = mock(classOf[AdminApiClient])

    val ruleForTestExp = CanaryRule(1, "sync-api", null, 101, null, "http", null, null, null, false)
    val ruleForTestExp2 = CanaryRule(1, "sync-api", null, 101, null, "http2", null, null, null, false)

    var ruleForTest = ruleForTestExp
    var flag = false

    val fn = () => {
      if (flag) {
        Thread.sleep(500)
      }
      Set.apply(ruleForTest)
    }

    val ruleServiceForTest = new RuleServiceForTest(adminApiClient, fn)

    val dataSet = mock(classOf[DataSet])
    val org = mock(classOf[Org])
    when(dataSet.getOrg).thenReturn(org)
    when(org.getId).thenReturn(orgId)
    when(adminApiClient.getDataSet(dataSetId)).thenReturn(Optional.of(dataSet))

    val flow = mock(classOf[NexlaFlow])
    when(adminApiClient.getFlowByResource(new Resource(dataSetId, ResourceType.DATASET))).thenReturn(ofNullable(flow))

    val adminApiFlow = mock(classOf[AdminApiFlow])
    when(adminApiFlow.getId).thenReturn(flowId)
    val list = new util.ArrayList[AdminApiFlow]()
    list.add(adminApiFlow)
    when(flow.getFlows).thenReturn(list)

    val rule = ruleServiceForTest.getRule(dataSetId)

    //ruleForTestExp in cache, expecting it to get
    assert(rule.nonEmpty)
    assert(rule.get.equals(ruleForTestExp))

    //waiting 1 sec to force cache to reload
    //cache should contain ruleForTestExp2 but after 500 ms because flag is true (see val fn on line 97)
    ruleForTest = ruleForTestExp2
    flag = true
    Thread.sleep(1000)


    val rule2 = ruleServiceForTest.getRule(dataSetId)

    //expecting to get ruleForTestExp from cache because it is still reloading (asynchronously)
    assert(rule2.nonEmpty)
    assert(rule2.get.equals(ruleForTestExp))

    Thread.sleep(1000)

    val rule3 = ruleServiceForTest.getRule(dataSetId)

    //cache has already reloaded, expecting to get ruleForTestExp2
    assert(rule3.nonEmpty)
    assert(rule3.get.equals(ruleForTestExp2))
  }

  it should "return empty rule if data source is null" in {
    val adminApiClient = mock(classOf[AdminApiClient])

    val ruleForTest = CanaryRule(1, "sync-api", null, 101, null, "http", null, null, null, false)
    val rulesForTest = Set.apply(ruleForTest)
    val ruleServiceForTest = new RuleServiceForTest(adminApiClient, () => rulesForTest)

    val dataSource: DataSource = null

    val rule = ruleServiceForTest.getRule(dataSource)

    assert(rule.isEmpty)
  }

  it should "return empty rule if data sink is null" in {
    val adminApiClient = mock(classOf[AdminApiClient])

    val ruleForTest = CanaryRule(1, "sync-api", null, 101, null, "http", null, null, null, false)
    val rulesForTest = Set.apply(ruleForTest)
    val ruleServiceForTest = new RuleServiceForTest(adminApiClient, () => rulesForTest)

    val dataSink: DataSink = null

    val rule = ruleServiceForTest.getRule(dataSink)

    assert(rule.isEmpty)
  }

  class RuleServiceForTest(adminApiClient: AdminApiClient, testRulesFn: () => Set[CanaryRule] = () => Set.empty)
    extends RuleService(adminApiClient, null, AppType.SYNC_APP.appName) {
    override protected def getAllRules: Set[CanaryRule] = testRulesFn.apply()

    override protected def getDuration: Duration = Duration.ofSeconds(1)
  }

}
