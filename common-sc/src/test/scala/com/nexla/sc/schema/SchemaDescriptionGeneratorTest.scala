package com.nexla.sc.schema

import com.bazaarvoice.jolt.JsonUtils.streamToType
import com.nexla.admin.client.{DataSet, NexlaSchema, Org, Owner}
import com.nexla.sc.schema.SchemaDescriptionGenerator
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.json4s.jackson.Serialization.writePretty
import org.scalatest.TagAnnotation

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class SchemaDescriptionGeneratorTest extends AnyFlatSpecLike with Matchers {

  it should "flatten schema correctly" in {

    val schema = streamToType(getClass().getClassLoader().getResourceAsStream("schemaToFlatten.json"), classOf[NexlaSchema])

    val ds = new DataSet
    ds.setId(1)
    ds.setOrg(Org.getOrgById(2))
    ds.setOwner(Owner.getOwnerById(3))
    ds.setOutputSchema(schema)

    val creator = new SchemaDescriptionGenerator()
    val result = creator
      .generateDescription(ds, None)

    implicit val formats = org.json4s.DefaultFormats
    writePretty(result) shouldBe
      """{
        |  "dataSetId" : 1,
        |  "orgId" : 2,
        |  "ownerId" : 3,
        |  "fields" : [ {
        |    "orgId" : 2,
        |    "ownerId" : 3,
        |    "path" : "a",
        |    "name" : "a",
        |    "typeKeywords" : [ "number", "string", "integer" ]
        |  }, {
        |    "orgId" : 2,
        |    "ownerId" : 3,
        |    "path" : "d",
        |    "name" : "d",
        |    "typeKeywords" : [ "string" ]
        |  }, {
        |    "orgId" : 2,
        |    "ownerId" : 3,
        |    "path" : "c.b3",
        |    "name" : "b3",
        |    "typeKeywords" : [ "number" ]
        |  }, {
        |    "orgId" : 2,
        |    "ownerId" : 3,
        |    "path" : "c",
        |    "name" : "c",
        |    "typeKeywords" : [ "integer", "string", "object" ]
        |  }, {
        |    "orgId" : 2,
        |    "ownerId" : 3,
        |    "path" : "b.b1",
        |    "name" : "b1",
        |    "typeKeywords" : [ "number" ]
        |  }, {
        |    "orgId" : 2,
        |    "ownerId" : 3,
        |    "path" : "b.b2.b22",
        |    "name" : "b22",
        |    "typeKeywords" : [ "number" ]
        |  }, {
        |    "orgId" : 2,
        |    "ownerId" : 3,
        |    "path" : "b.b2.b23.b234",
        |    "name" : "b234",
        |    "typeKeywords" : [ "number" ]
        |  }, {
        |    "orgId" : 2,
        |    "ownerId" : 3,
        |    "path" : "b.b2.b23",
        |    "name" : "b23",
        |    "typeKeywords" : [ "integer", "string", "object" ]
        |  }, {
        |    "orgId" : 2,
        |    "ownerId" : 3,
        |    "path" : "b.b2",
        |    "name" : "b2",
        |    "typeKeywords" : [ "object" ]
        |  }, {
        |    "orgId" : 2,
        |    "ownerId" : 3,
        |    "path" : "b",
        |    "name" : "b",
        |    "typeKeywords" : [ "object", "string" ]
        |  } ]
        |}""".stripMargin
  }

}