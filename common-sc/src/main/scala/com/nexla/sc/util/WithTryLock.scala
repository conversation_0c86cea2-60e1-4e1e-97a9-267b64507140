package com.nexla.sc.util

import com.nexla.sc.util.StrictNexlaLogging

import java.util.concurrent.Semaphore
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

trait WithTryLock
  extends StrictNexlaLogging
    with WithLogging {

  implicit val ec: ExecutionContext

  val lock = new Semaphore(1)

  def withTryLock(fn: => Future[Unit])(onLocked: => Future[Unit] = Future.successful(())) = {
    val locked = lock.tryAcquire(1)
    if (locked) {
      Try(fn)
        .fold( e => {
          logger.error("Error during task processing 1", e)
          Future.successful()
        }, t => t)
        .andThen { case _ => lock.release() }
        .recoverWith { case e =>
          logger.error("Error during task processing 2", e)
          Future.successful(())
        }
    } else {
      onLocked
    }
  }

}
