package com.nexla.sc.util

import java.util.concurrent._
import scala.concurrent.{ExecutionContext, Future}

object Async {
  lazy val ioExecutorContext: ExecutionContext = ExecutionContext.fromExecutorService(new ThreadPoolExecutor(0, Integer.MAX_VALUE, 60L, TimeUnit.SECONDS, new SynchronousQueue[Runnable]))
  lazy val computeExecutorContext: ExecutionContext = ExecutionContext.fromExecutorService(ForkJoinPool.commonPool)

  def futureCompute[T](block: => T): Future[T] = Future(block)(computeExecutorContext)
}
