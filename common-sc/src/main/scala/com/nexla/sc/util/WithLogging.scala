package com.nexla.sc.util

import akka.http.scaladsl.server.Directive0
import akka.http.scaladsl.server.Directives.{extractRequestContext, mapRouteResult}
import com.google.common.util.concurrent.RateLimiter
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.control.health.{ErrorEvent, ExpectedHealthEvent}
import com.nexla.telemetry.utils.{ExecutionMetricSet, ExecutionTelemetryUtils}
import com.typesafe.scalalogging.Logger
import org.apache.commons.lang.StringUtils
import org.apache.commons.lang3.time.StopWatch

import java.util.concurrent.TimeUnit
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

trait WithLogging {

  _: StrictNexlaLogging =>

  def logRequestProcessingTime: Directive0 = extractRequestContext.flatMap { ctx =>
    val stopWatch = StopWatch.createStarted()
    logger.info(s"${ctx.request.method.name} ${ctx.request.uri} started")

    mapRouteResult { result =>
      stopWatch.stop()
      logger.info(s"${ctx.request.method.name} ${ctx.request.uri} finished. TIME=${stopWatch.toString}")
      result
    }
  }

  /** Use tracked instead */
  @Deprecated
  def timed[T](logVal: String)(future: => T): T = {
    val stopWatch = new StopWatch
    stopWatch.start()
    logger.info(s"[$logVal] Starting")
    val result = future

    logger.info(s"[$logVal] Finished. TIME=${stopWatch.toString}")
    result
  }

  def tracked[T](metricSet: ExecutionMetricSet)(call: () => T): T = {
    metricSet.track(() => call())
  }

  def trackedTry[T](metricSet: ExecutionMetricSet)
                   (tryFunction: () => Try[T]): Try[T] = {
    val result: Try[T] = metricSet.track(() => tryFunction())
    result.failed.foreach { _ => metricSet.incError() }
    result
  }

  def trackedFuture[T](executionMetricSet: ExecutionMetricSet,
                       future: Future[T])
                      (implicit ec: ExecutionContext): Future[T] = {
    val time = executionMetricSet.time()
    future
      .andThen { case Success(_) => time.close() }
      .andThen { case Failure(_) => time.close(); executionMetricSet.incError() }
  }

  def trackedFuture[T](executionMetricSet: ExecutionMetricSet)
                      (future: () => Future[T])
                      (implicit ec: ExecutionContext): Future[T] = trackedFuture(executionMetricSet, None)(future)

  def trackedFuture[T](executionMetricSet: ExecutionMetricSet, name: Option[String])
                      (future: () => Future[T])
                      (implicit ec: ExecutionContext): Future[T] = {
    val time = if (name.isEmpty) executionMetricSet.time() else executionMetricSet.time(name.get)
    try {
      future()
        .andThen { case Success(_) => time.close() }
        .andThen { case Failure(_) => time.close(); executionMetricSet.incError() }
    } catch {
      case e: Exception =>  // Exception from () => Future[T] function itself
        time.close()
        executionMetricSet.incError()
        throw e
    }
  }

  def opse[T](operation: => String,
              timed: Boolean = false,
              logRateLimiter: Option[RateLimiter] = None,
              result: Boolean = true,
              metricSet: ExecutionMetricSet = ExecutionTelemetryUtils.noopMetricSet())
             (future: () => Future[T])
             (implicit ec: ExecutionContext): Future[T] = {
    op(operation, startEnd = true, timed = timed, logRateLimiter = logRateLimiter, logResult = result, metricSet = metricSet)(future)
  }

  def hopse[T](operation: => String,
               timed: Boolean = false,
               logRateLimiter: Option[RateLimiter] = None,
               result: Boolean = true,
               expectedHealthEvent: ExpectedHealthEvent,
               podName: String,
               nexlaMessageProducer: NexlaMessageProducer,
               metricSet: ExecutionMetricSet = ExecutionTelemetryUtils.noopMetricSet())
              (future: () => Future[T])
              (implicit ec: ExecutionContext): Future[T] = {
    opse(operation, timed, logRateLimiter, result, metricSet = metricSet)(() => healthyFuture(expectedHealthEvent, podName, nexlaMessageProducer, future()))
  }

  /**
   * Wraps the provided future with health messages generation callbacks.
   * If future is successful - expected health event is generated
   * If future is failed - error health event is generated
   */
  def healthyFuture[T](expectedHealthEvent: ExpectedHealthEvent,
                       podName: String = "",
                       nexlaMessageProducer: NexlaMessageProducer,
                       future: Future[T],
                       sendErrorHealthOnly: Boolean = false)
                      (implicit ec: ExecutionContext): Future[T] = {
    future.andThen {
      case Success(_) if !sendErrorHealthOnly  => nexlaMessageProducer.sendExpectedHealthEvent(expectedHealthEvent, podName)
      case Failure(e) =>
        nexlaMessageProducer.sendHealthMessage(
          new ErrorEvent(System.currentTimeMillis, expectedHealthEvent.getServiceName, podName, expectedHealthEvent.getMetricId + ".error",
            e.getMessage, TimeUnit.SECONDS.toMillis(expectedHealthEvent.getMaxMessageIntervalSec * 5)))
      case _ => // do nothing
    }
  }

  /**
   * Wraps the provided Try with health messages generation callbacks.
   * If Try is successful - expected health event is generated
   * If Try is failed - error health event is generated
   */
  def healthyTry[T](expectedHealthEvent: ExpectedHealthEvent,
                    podName: String = "",
                    nexlaMessageProducer: NexlaMessageProducer,
                    t: Try[T],
                    sendErrorHealthOnly: Boolean = false) : Try[T] = {
    t match {
      case Success(_) if !sendErrorHealthOnly => nexlaMessageProducer.sendExpectedHealthEvent(expectedHealthEvent, podName)
      case Failure(e) => nexlaMessageProducer.sendHealthMessage(
        new ErrorEvent(System.currentTimeMillis, expectedHealthEvent.getServiceName, podName, expectedHealthEvent.getMetricId + ".error",
          e.getMessage, TimeUnit.SECONDS.toMillis(expectedHealthEvent.getMaxMessageIntervalSec * 5)))
      case _ => // do nothing
    }
    t
  }

  def op[T](op: => String,
            timed: Boolean = false,
            startEnd: Boolean = false,
            logRateLimiter: Option[RateLimiter] = None,
            logResult: Boolean = true,
            metricSet: ExecutionMetricSet = ExecutionTelemetryUtils.noopMetricSet())
           (future: () => Future[T])
           (implicit ec: ExecutionContext): Future[T] = {

    val logAllowed = logRateLimiter.forall(_.tryAcquire())

    val result = if (logAllowed && timed) {
      val stopWatch = new StopWatch
      stopWatch.start()
      trackedFuture(metricSet)(future).andThen { case _ => logger.info(s"$op TIME=$stopWatch") }
    } else {
      trackedFuture(metricSet)(future)
    }

    result.andThen { case Failure(e) => logger.error(s"$op failed: ${StringUtils.substring(e.getMessage, 0, 100)}", e) }

    if (logAllowed && startEnd && logResult) result.andThen { case _ => logger.info(s"$op finished, result: $result") }

    result
  }

  def tryOpse[T](op: => String,
                 timed: Boolean = false,
                 rateLimiter: Option[RateLimiter] = None)
                (t: () => Try[T]): Try[T] = tryOp(op, startEnd = true, timed = timed, rateLimiter = rateLimiter)(t)

  def tryOp[T](op: => String,
               timed: Boolean = false,
               startEnd: Boolean = false,
               rateLimiter: Option[RateLimiter] = None)
              (t: () => Try[T]): Try[T] = {

    val logAllowed = rateLimiter.forall(_.tryAcquire())

    if (logAllowed && startEnd) logger.info(s"$op started")
    val result = if (logAllowed && timed) {
      val stopWatch = new StopWatch
      stopWatch.start()
      val res = t()
      logger.info(s"$op TOOK TIME=$stopWatch, result=$res")
      res
    } else {
      t()
    }

    result.failed.foreach { e => logger.error(s"$op failed: ${StringUtils.substring(e.getMessage, 0, 100)}", e) }
    if (logAllowed && startEnd) logger.info(s"$op finished, result: $result")
    result
  }

  implicit class Loggable[T](result: T) {
    def log(message: T => String)(implicit logger: Logger): T = {
      logger.info(message(result))
      result
    }
  }

}
