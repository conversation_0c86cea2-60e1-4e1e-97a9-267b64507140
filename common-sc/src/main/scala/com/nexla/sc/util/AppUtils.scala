package com.nexla.sc.util

import akka.actor.ActorSystem
import akka.http.scaladsl.{ConnectionContext, Http}
import akka.kafka.ConsumerFailed
import com.google.common.collect.Maps
import com.nexla.common.AppUtils.{sslContext, sslContextWithPem}
import com.nexla.common.NexlaConstants.ENVIRONMENT_TYPE_ENV_VAR
import com.nexla.common.{AppType, NexlaSslContext, SSLCertificateStore}
import com.nexla.connector.config.vault.ConfigEnv._
import com.nexla.connector.config.vault.VaultUtils.{createNexlaCredentialsStore, envOrProp}
import com.nexla.connector.config.vault._
import com.nexla.sc.config.{DataDogConf, NexlaSslConfig, PrometheusConf, TelemetryConf}
import com.nexla.telemetry.NoopTelemetry.NOOP_TELEMETRY
import com.nexla.telemetry.akka.AkkaThreadStateMonitor
import com.nexla.telemetry._
import com.typesafe.config.ConfigParseOptions._
import com.typesafe.config.{Config, ConfigFactory, ConfigResolveOptions}
import com.typesafe.scalalogging.{Logger, StrictLogging}
import fr.davit.akka.http.metrics.core.{HttpMetricsRegistry, HttpMetricsSettings}
import fr.davit.akka.http.metrics.datadog.DatadogRegistry
import fr.davit.akka.http.metrics.prometheus.{Buckets, PrometheusRegistry, PrometheusSettings, Quantiles}
import org.apache.http.conn.ssl.TrustSelfSignedStrategy
import org.apache.http.ssl.SSLContextBuilder

import java.io.File
import java.util
import java.util.Collections.{emptyList, emptyMap}
import javax.net.ssl._
import scala.collection.JavaConverters._
import scala.compat.java8.OptionConverters._
import scala.concurrent.Await
import scala.concurrent.duration._
import scala.io.Source
import scala.util.Try

trait AppUtils extends StrictNexlaLogging {

  List(
    "org.apache.kafka.common.serialization.StringDeserializer",
    "org.apache.kafka.common.serialization.StringSerializer",
    "com.snowflake.client.jdbc.SnowflakeDriver",
    "com.amazon.redshift.jdbc.Driver",
    "com.amazon.redshift.jdbc42.Driver",
    "com.mysql.cj.jdbc.Driver",
    "org.postgresql.Driver",
    "oracle.jdbc.OracleDriver",
    "com.microsoft.sqlserver.jdbc.SQLServerDriver",
    "org.apache.hive.jdbc.HiveDriver",
    "com.facebook.presto.jdbc.PrestoDriver",
    "com.ibm.as400.access.AS400JDBCDriver",
    "org.apache.kafka.common.serialization.ByteArrayDeserializer",
    "com.teradata.jdbc.TeraDriver",
    "com.simba.spark.jdbc.Driver",
    "com.netsuite.jdbc.openaccess.OpenAccessDriver",
    "com.google.cloud.spanner.jdbc.JdbcDriver",
    "com.ibm.db2.jcc.DB2Driver",
    "com.sap.db.jdbc.Driver",
    "com.google.cloud.spanner.jdbc.JdbcDriver",
    "com.simba.athena.jdbc.Driver",
    "com.sybase.jdbc4.jdbc.SybDriver"
  ).foreach(x => Try(Class.forName(x)).toOption.map(_.getName).foreach(x => println(s"loaded class $x")))

  val appConfigEnv = resolveEnvType(None)
  val defaultActorSystemName = "default"

  def loadProps[T](appType: AppType, propsFn: NexlaAppConfig => T): (T, NexlaAppConfig, util.Map[String, String]) = {
    val updatedEnv: util.Map[String, String] = Maps.newConcurrentMap()
    lazy val nexlaAppConfig = loadConfig(appType, updatedEnv)
    val tryProps = Try(nexlaAppConfig).map(config => propsFn(config))

    if (tryProps.isFailure) {
      println("Error in config, exiting")
      tryProps.failed.get.printStackTrace()
      System.exit(1)
    }

    (tryProps.get, nexlaAppConfig, updatedEnv)
  }

  def loadAkkaConfig(configEnv: Option[ConfigEnv] = None): Config = {
    val env = configEnv.getOrElse(appConfigEnv)

    println(s"[loadAkkaConfig] Using config: $env")

    val config = env.getName match {
      case LocalFileEnv.NAME =>

        ConfigFactory.systemEnvironment()
          .withFallback(
            ConfigFactory.parseFile(new File(env.asInstanceOf[LocalFileEnv].getFileName))
              .withFallback(
                ConfigFactory.load(s"application.local.conf", defaults(), ConfigResolveOptions.defaults().setAllowUnresolved(true))
                  .withFallback(ConfigFactory.load())))
      case _ =>

        ConfigFactory.systemEnvironment()
          .withFallback(
            ConfigFactory.load(env.getFileName, defaults(), ConfigResolveOptions.defaults().setAllowUnresolved(true))
              .withFallback(ConfigFactory.load()))
    }

    config
      .getConfig("akka")
      .atKey("akka")
  }

  def loadConfig(appType: AppType, updatedEnv: util.Map[String, String], configEnv: Option[ConfigEnv] = None): NexlaAppConfig = {

    val env = configEnv.getOrElse(appConfigEnv)

    println(s"[loadConfig] Using config: $env")

    val cfg = env.getName match {
      case LocalFileEnv.NAME =>

        ConfigFactory.systemEnvironment()
          .withFallback(
            ConfigFactory.parseFile(new File(env.asInstanceOf[LocalFileEnv].getFileName))
              .withFallback(
                ConfigFactory.load("application.local.conf", defaults(), ConfigResolveOptions.defaults().setAllowUnresolved(true))))
      case _ =>

        ConfigFactory.systemEnvironment()
          .withFallback(
            ConfigFactory.load(env.getFileName, defaults(), ConfigResolveOptions.defaults().setAllowUnresolved(true)))
    }

    val store = createNexlaCredentialsStore(Maps.newHashMap())
    val secretNames = Option(envOrProp("SECRET_NAMES", emptyMap(), null))
      .map(_.split(",").toList.asJava)
      .getOrElse(emptyList())

    new NexlaAppConfig(appType.appName, env, cfg, store, secretNames, updatedEnv)
  }


  private def resolveEnvType(configEnv: Option[ConfigEnv]): ConfigEnv = configEnv match {
    case None =>
      val source = Try(Source.fromFile("/etc/nexla/environment.type"))
      val env = source.map(_.getLines().toIterable.head.trim)
        .toOption
        .orElse(Option(System.getenv(ENVIRONMENT_TYPE_ENV_VAR)))
        .getOrElse("dev")

      source.map(_.close())
      env match {
        case "mesos" | "dev" => new DevEnv()
        case "stage" => new StageEnv()
        case "beta" => new BetaEnv()
        case "prod" => new ProdEnv()
        case "local" => new LocalEnv()
        case custom => new CustomMesosEnv(custom)
      }

    case Some(environment) => environment
  }

  def defaultActorSystem(logger: Logger, configEnv: Option[ConfigEnv] = None, name: String = defaultActorSystemName): ActorSystem =
    closeOnShutdown(ActorSystem(name, loadAkkaConfig(configEnv))) { system =>
      logger.info("Shutdown ActorSystem...")
      try {
        system.terminate()
        Await.result(system.whenTerminated, 1.minute)
        logger.info("ActorSystem shut down")
      } catch {
        case e: Exception =>
          logger.error("Error while ActorSystem shutdown", e)
      }
    }

  def closeOnShutdown[A](obj: A)(shutdownFn: A => Unit): A = {
    sys.addShutdownHook(shutdownFn(obj))
    obj
  }

  def configureHttpClientSslContext(ssl: NexlaSslContext)(implicit ioBoundSystem: ActorSystem): Unit = {

    val c: SSLContext = if (ssl.getClientPemCert.isPresent) {
      sslContextWithPem(ssl.getClientPemCert.get())
    } else if (ssl.getClientKeystoreStore.isPresent) {
      sslContext(ssl.getClientKeystoreStore.get(), ssl.getClientTruststoreStore.orElse(null))
    } else null

    if (c != null) {
      logger.info("[HTTPS] Adding client certificate to akka http TrustStore")
      Http(ioBoundSystem).setDefaultClientHttpsContext(ConnectionContext.httpsClient(c))
    }
  }


  def httpsContext(k: SSLCertificateStore, t: SSLCertificateStore): ConnectionContext = {
    val sslContextBuilder = new SSLContextBuilder()
      .setProtocol("TLS")
      .loadKeyMaterial(k.pkcs12, k.getPassword.toCharArray)

    // this branch is for backward compatibility
    if (t == null)
      sslContextBuilder.loadTrustMaterial(new TrustSelfSignedStrategy)
    else
      sslContextBuilder.loadTrustMaterial(t.pkcs12, new TrustSelfSignedStrategy)

    logger.info("[HTTPS] Creating akka http server HTTPS ssl context")
    ConnectionContext.httpsServer(sslContextBuilder.build)
  }

  def nexlaBackendSslContext(sslConfig: NexlaSslConfig): NexlaSslContext = {
    nexlaSslContext(sslConfig, true)
  }

  def nexlaSslContext(sslConfig: NexlaSslConfig): NexlaSslContext = {
    nexlaSslContext(sslConfig, sslConfig.httpsClientEnabled)
  }

  def nexlaSslContext(sslConfig: NexlaSslConfig, httpsClientEnabled: Boolean): NexlaSslContext = {
    NexlaSslContext.newBuilder()
      .withClientCert(sslConfig.clientCertificate.asJava)
      .withClientSsl(httpsClientEnabled, sslConfig.clientKeystoreP12.asJava, sslConfig.clientKeystoreP12Path.asJava, sslConfig.clientTruststoreP12.asJava, sslConfig.clientTruststoreP12Path.asJava, sslConfig.clientPassword.asJava)
      .withServerTLS(sslConfig.httpsEnabled, sslConfig.serverKeystoreP12.asJava, sslConfig.serverKeystoreP12Path.asJava, sslConfig.serverTruststoreP12.asJava, sslConfig.serverTruststoreP12Path.asJava, sslConfig.serverKeyPassword.asJava)
      .withKafkaSsl(sslConfig.kafkaSslEnabled, sslConfig.kafkaTruststoreP12.asJava, sslConfig.kafkaTruststoreP12Path.asJava, sslConfig.kafkaTruststorePassword.asJava, sslConfig.kafkaKeystoreP12.asJava, sslConfig.kafkaKeystoreP12Path.asJava, sslConfig.kafkaKeystorePassword.asJava)
      .build();
  }

  implicit class RichConfig(config: Config) {

    def getOptString(path: String): Option[String] = Try(config.getString(path)).toOption.filterNot(_.isEmpty)

  }

  def initTelemetry(telemetryConf: TelemetryConf,
                    appName: String,
                    datadogConfOpt: Option[DataDogConf] = None,
                    prometheusConfOpt: Option[PrometheusConf] = None,
                    actorSystemName: String = defaultActorSystemName): (Telemetry, Option[HttpMetricsRegistry]) = {

    // Catch non-fatal exception and allow to proceed without telemetry
    // Fatal exceptions will still prevent server startup
    val result = Try {
      telemetryConf.datastore.map(x => x.toLowerCase()) match {
        case Some("datadog") =>
          datadogConfOpt match {
            case Some(datadogConf) =>
              val telemetry = new DatadogTelemetry("localhost", datadogConf.port, appName, appConfigEnv.getName)
              val settings = HttpMetricsSettings.default.withIncludeStatusDimension(true)
                .withIncludeMethodDimension(true)
                .withIncludePathDimension(true)
              (telemetry, Some(new DatadogRegistry(settings)(telemetry.getStatsd)))
            case None =>
              logger.warn("Datadog specified but did not find datadog configuration")
              (NOOP_TELEMETRY, None)
          }
        case Some("prometheus") =>
          val telemetry = prometheusConfOpt match {
            case Some(promConf) =>
              new PrometheusTelemetry(appConfigEnv.getName, appName, promConf.pushGatewayUrl)
            case _ =>
              logger.warn("Prometheus specific, but no pushgateway configuration found, disabling reporting push-style durations")
              new PrometheusTelemetry(appConfigEnv.getName, appName)
          }

          val default: PrometheusSettings = PrometheusSettings.default
            .withIncludeStatusDimension(true)
            .withIncludeMethodDimension(true)
            .withIncludePathDimension(true)
            // duration buckets in seconds. min - 10ms, max - 20s (timeout)
            .withDurationConfig(Buckets(0.010, 0.025, 0.050, 0.075, 0.100, .250, .500, .750, 1, 2.5, 5, 7.5, 10, 20))
            .withReceivedBytesConfig(Quantiles(0.5, 0.75, 0.9, 0.95, 0.99))
            .withSentBytesConfig(PrometheusSettings.DefaultQuantiles)
            .withDefineError(_.status.isFailure);
          (telemetry, Some(new PrometheusRegistry(default, PrometheusTelemetry.COLLECTOR_REGISTRY)))
        case _ =>
          logger.info("No matching telemetry datastore")
          (NOOP_TELEMETRY, None)
      }
    }

    val (telemetry, httpMetricsRegistry) = result.getOrElse(NOOP_TELEMETRY, None)
    telemetryConf.threadStateEnabled.foreach(_ => {
      logger.info("Starting akka thread state monitoring")
      AkkaThreadStateMonitor.start(
        systemName = actorSystemName,
        interval = telemetryConf.threadStateIntervalInSec.getOrElse(AkkaThreadStateMonitor.DEFAULT_INTERVAL_IN_SECONDS),
        delay = telemetryConf.threadStateDelayInSec.getOrElse(AkkaThreadStateMonitor.DEFAULT_INTERVAL_IN_SECONDS))
    })

    TelemetryContext.set(telemetry)
    (telemetry, httpMetricsRegistry)
  }

}

object AppUtils {

  def logConsumerFailed(e: Throwable, logger: Logger): Unit = e match {
    case _: ConsumerFailed => logger.error("Error in listener, restarting", e)
    case _ => logger.error("Error in listener, restarting", e)
  }
}
