package com.nexla.sc.util

import com.nexla.admin.client.OwnerAndOrg

case class OrgIdsFilter(included: Option[Set[Int]],
                        excluded: Option[Set[Int]],
                        currentCluster: Integer) {

  def filterOrgIds[T <: OwnerAndOrg](elements: List[T], ignoreMigratingOrgs: Boolean = false): List[T] = {
    val nonNullOrgElements = elements.filter(_.getOrg != null)
    val nonMigratingOrgElements = if (!ignoreMigratingOrgs) nonNullOrgElements.filter(!_.getOrg.inMigrationState(currentCluster)) else nonNullOrgElements
    val withIncluded = included.fold(nonMigratingOrgElements)(includedIds => nonMigratingOrgElements.filter(ds => includedIds.contains(ds.getOrg.getId)))
    val withExcluded = excluded.fold(withIncluded)(excludedIds => withIncluded.filter(ds => !excludedIds.contains(ds.getOrg.getId)))
    withExcluded
  }
}