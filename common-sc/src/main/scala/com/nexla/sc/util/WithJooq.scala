package com.nexla.sc.util

import com.nexla.db.util.DbUtils.calculateDialect
import com.nexla.sc.util.StrictNexlaLogging
import com.nexla.telemetry.utils.{ExecutionMetricSet, ExecutionTelemetryUtils}
import org.jooq.conf.{RenderNameStyle, Settings}
import org.jooq.exception.DataAccessException
import org.jooq.impl.{DefaultCloseableDSLContext, DefaultConnectionProvider}
import org.jooq.{CloseableDSLContext, DSLContext}

import java.sql.Connection
import scala.util.{Failure, Success, Try}

trait WithJooq extends StrictNexlaLogging {

  val getConnectionFn: ConnectionFn

  val withJooqCommitMetrics: ExecutionMetricSet = ExecutionTelemetryUtils.metricSet(this.getClass, "withJooqCommit")
  def withJooqCommit[T](fn: DSLContext => T): T = {
    withJooqCommitMetrics.track(() => {
      Try(getConnectionFn())
        .map { conn =>
          val jooq = getJooq(conn)
          val result = Try(fn(jooq))
          jooq.close()

          result match {
            case Success(_) =>
              val commitResult = Try(conn.commit())
              conn.close()
              //noinspection ScalaUnusedExpression
              commitResult.get

            case Failure(e) =>
              logger.error("", e)
              conn.close()
          }
          result.get
        }
        .get
    })
  }

  val withJooqCommitRetryMetrics: ExecutionMetricSet = ExecutionTelemetryUtils.metricSet(this.getClass, "withJooqCommitRetry")
  def withJooqCommitRetry[T](fn: DSLContext => T, maxRetries: Int = 3, retryDelayMs: Int = 100): T = {
    withJooqCommitRetryMetrics.track(() => {
      var retries = 0
      var lastException: Exception = null
      
      while (retries < maxRetries) {
        try {
          return withJooqCommit(fn)
        } catch {
          case e: DataAccessException if e.getMessage.contains("Deadlock") =>
            lastException = e
            retries += 1
            if (retries < maxRetries) {
              logger.info(s"Deadlock detected, retrying (${retries}/${maxRetries})...")
              Thread.sleep(retryDelayMs)
            }
          case e: Exception =>
            // For non-deadlock exceptions, don't retry
            throw e
        }
      }
      
      // If we've exhausted all retries, throw the last exception
      logger.error(s"Max retries ($maxRetries) exceeded for deadlock", lastException)
      throw lastException
    })
  }

  def getJooq[T](conn: Connection): CloseableDSLContext = {
    val dialect = calculateDialect(conn.getMetaData.getDatabaseProductName.toLowerCase())
    val oracle = conn.getMetaData.getDatabaseProductName.toLowerCase().contains("oracle")
    val connection = new DefaultConnectionProvider(conn)
    if (oracle) {
      val settings = new Settings
      settings.setRenderNameStyle(RenderNameStyle.AS_IS)
      new DefaultCloseableDSLContext(connection, dialect, settings)
    } else {
      new DefaultCloseableDSLContext(connection, dialect)
    }
  }

  val withJooqReadOnlyMetrics: ExecutionMetricSet = ExecutionTelemetryUtils.metricSet(this.getClass, "withJooqReadOnly")
  def withJooqReadOnly[T](fn: DSLContext => T): T = {
    withJooqReadOnlyMetrics.track(() => {
      Try(getConnectionFn())
        .map { conn =>
          val jooq = getJooq(conn)
          val result = Try(fn(jooq))
          Try(conn.rollback())
          conn.close()
          result.get
        }
        .get
    })
  }
}
