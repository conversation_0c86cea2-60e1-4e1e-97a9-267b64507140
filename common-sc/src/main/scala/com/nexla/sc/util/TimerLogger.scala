package com.nexla.sc.util

import scala.collection.JavaConverters._

class TimerLogger {

  var times = new java.util.ArrayList[(String, Long)]()

  def addTime(label: String): Unit = addTime(label, System.currentTimeMillis())

  def addTime(label: String, l: Long): Unit = times.add((label, l))

  def duration(): Long = {
    val res = for {
      t1 <- times.asScala.headOption
      t2 <- times.asScala.reverse.headOption
    } yield {
      t2._2 - t1._2
    }
    res.getOrElse(0)
  }

  def logTimes() = {
    val res = times.asScala.reverse.sliding(2).toList.map { case x =>
      s"${x.head._1} = ${(x.head._2 - x.reverse.head._2).toString}"
    }
    res.reverse.mkString(",")
  }

}