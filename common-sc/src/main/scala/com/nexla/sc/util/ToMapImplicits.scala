package com.nexla.sc.util

object ToMapImplicits {

  def flattenCaseClassMap(m: Map[String, Any]): List[(String, String)] = {
    m.map {
      case (k, v) =>
        v match {
          case values: Iterable[_] => values.map(value => (k, value)).toList
          case Some(value) => List(k -> value)
          case None => Nil
          case other => List(k -> other)
        }
    }.flatten.map { case (k, v) => k -> v.toString }.toList
  }

}


