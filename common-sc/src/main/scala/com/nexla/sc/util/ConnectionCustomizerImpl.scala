package com.nexla.sc.util

import java.sql.{Connection, SQLException}

import com.mchange.v2.c3p0.AbstractConnectionCustomizer

class ConnectionCustomizerImpl
  extends AbstractConnectionCustomizer {

  override def onAcquire(c: Connection, pdsIdt: String) {
    try {
      c.setAutoCommit(false)
    } catch {
      case e: SQLException => throw new RuntimeException(e)
    }
  }

  override def onCheckIn(c: Connection, parentDataSourceIdentityToken: String) {
    c.rollback()
  }
}