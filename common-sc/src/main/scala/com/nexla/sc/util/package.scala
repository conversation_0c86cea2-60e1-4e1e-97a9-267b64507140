package com.nexla.sc

import java.sql.Connection
import java.util.Collections.emptyMap
import java.util.function.Supplier
import scala.compat.java8.OptionConverters._

import java.sql.Connection
import java.util.function.Supplier
import akka.stream.scaladsl.Sink
import com.nexla.admin.client.DataSource

import scala.concurrent.{ExecutionContext, Future}
import scala.language.implicitConversions
import scala.util.control.NonFatal
import scala.util.{Random, Success, Try}

package object util {

  import akka.http.scaladsl.server.Directives._
  import spray.json._

  type ConnectionFn = () => Connection

  def end = pathEndOrSingleSlash

  def isEmailSource(s: DataSource) = {
    s.getScriptConfig.asScala.flatMap(x => Option(x.parameters)).getOrElse(emptyMap()).containsKey("email")
  }


  implicit class RichOption[T](v: T) {
    def opt: Option[T] = Option(v)
  }

  implicit class RichTry[T](v: Try[T]) {
    def fut: Future[T] = Future.fromTry(v)
  }
  implicit class RichAny[T](v: T) {
    def fut: Future[T] = Future.successful(v)
  }

  implicit class RichFuture[T](v: Future[T]) {
    def catchAll(default: T)
                (implicit ec: ExecutionContext): Future[T] = v.recoverWith { case NonFatal(_) => Future.successful(default) }
  }

  implicit def funToRunnable(fun: () => Unit) = new Runnable() {
    def run() = fun()
  }

  implicit def funToSupplier[T](fun: () => T) = new Supplier[T] {
    override def get(): T = fun()
  }

  implicit def funToFunction[T, R](fun: T => R) = new java.util.function.Function[T, R] {
    override def apply(t: T): R = fun(t)
  }

  implicit val AnyJsonFormat = new JsonFormat[Any] {

    def write(x: Any) = x.opt.map(a => JsString(a.toString)).getOrElse(JsNull)

    def read(value: JsValue) = value.opt.map(_.toString).orNull
  }

  def takeFirstError: Sink[Try[Unit], Future[Try[Unit]]] =
    Sink.fold[Try[Unit], Try[Unit]](Success({}))((a, b) => a.flatMap(_ => b))

  def takeFirstError[T](tryList: Iterable[Try[T]]): Try[Unit] =
    tryList.find(_.isFailure).map(_.map(_ => {})).getOrElse(Success(()))

  def randMillis = new Random().nextInt(10000)

}