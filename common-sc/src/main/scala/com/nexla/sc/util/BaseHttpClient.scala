package com.nexla.sc.util

import java.util.Base64
import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.http.scaladsl.model.HttpMethods.{DELETE, GET, POST, PUT}
import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.model.{HttpHeader, HttpRequest, HttpResponse, Uri, _}
import akka.http.scaladsl.settings.ConnectionPoolSettings
import akka.http.scaladsl.unmarshalling.{Unmarshal, Unmarshaller}
import akka.stream.{Materializer}
import com.bazaarvoice.jolt.JsonUtils
import com.nexla.sc.config.NexlaCredsConf
import com.nexla.sc.util.StrictNexlaLogging

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}
import scala.reflect.ClassTag

trait BaseHttpClient
  extends StrictNexlaLogging {

  val acceptJson = RawHeader("Accept", "application/json")

  def get(uri: Uri,
          headers: Seq[HttpHeader] = Seq.empty)
         (implicit system: ActorSystem,
          ec: ExecutionContext,
          mat: Materializer): Future[HttpResponse] = {
    Http().singleRequest(HttpRequest(GET, uri, headers.to))
  }

  def delete(uri: Uri,
             headers: Seq[HttpHeader] = Seq.empty)
            (implicit system: ActorSystem,
             ec: ExecutionContext,
             mat: Materializer): Future[HttpResponse] = {
    Http().singleRequest(HttpRequest(DELETE, uri, headers.to))
  }

  def put(uri: Uri,
          headers: Seq[HttpHeader] = Seq.empty,
          entity: RequestEntity = HttpEntity.Empty)
         (implicit system: ActorSystem,
          ec: ExecutionContext,
          mat: Materializer): Future[HttpResponse] = {
    Http().singleRequest(HttpRequest(PUT, uri, headers.to, entity))
  }

  def post(uri: Uri,
           headers: Seq[HttpHeader] = Seq.empty,
           entity: RequestEntity = HttpEntity.Empty)
          (implicit system: ActorSystem,
           ec: ExecutionContext,
           mat: Materializer): Future[HttpResponse] = {
    Http().singleRequest(HttpRequest(POST, uri, headers.to, entity))
  }

  def postWithTimeout(uri: Uri,
                      timeout: Duration,
                      headers: Seq[HttpHeader] = Seq.empty,
                      entity: RequestEntity = HttpEntity.Empty)
                     (implicit system: ActorSystem,
                      ec: ExecutionContext,
                      mat: Materializer): Future[HttpResponse] = {
    Http().singleRequest(HttpRequest(POST, uri, headers.to, entity),
      settings = ConnectionPoolSettings(system)
        .withIdleTimeout(timeout)
        .withResponseEntitySubscriptionTimeout(timeout))
  }

  def handle[B](url: String,
                request: Option[String],
                response: HttpResponse,
                methodName: String)
               (implicit unmarshaller: Unmarshaller[ResponseEntity, B],
                mat: Materializer,
                ec: ExecutionContext): Future[B] = {

    val unmarshalled = Unmarshal(response.entity)
    if (response.status.isSuccess()) {
      unmarshalled.to[B]
    } else {
      unmarshalled.to[String].flatMap(responseAsString => Future.failed(
        ApiException(s"Error accessing $url $methodName: ${request.map(r => s"request=$r").getOrElse("")} status=${response.status} response=$responseAsString",
          status = Some(response.status))))
    }
  }

  def handleJackson[B](request: String,
                       response: HttpResponse,
                       methodName: String)
                      (implicit ct: ClassTag[B],
                       mat: Materializer,
                       ec: ExecutionContext): Future[B] = {

    val unmarshalled = Unmarshal(response.entity)
    if (response.status.isSuccess()) {
      unmarshalled.to[String]
        .map { data => JsonUtils.stringToType(data, ct.runtimeClass.asInstanceOf[Class[B]]) }
    } else {
      unmarshalled.to[String].flatMap(responseAsString => Future.failed(
        ApiException(
          s"Error accessing $methodName, status=${response.status.intValue()}, request=$request response=$responseAsString",
          status = Some(response.status))))
    }
  }
}

trait NexlaBasicAuth {

  protected val nexlaCreds: NexlaCredsConf

  val basicAuthHeader = {
    import nexlaCreds._
    val authString = s"$username:$password"
    RawHeader("Authorization", s"Basic ${new String(Base64.getEncoder.encode(authString.getBytes))}")
  }
}

case class ApiException(message: String,
                        cause: Option[Throwable] = None,
                        status: Option[StatusCode] = None)
  extends RuntimeException(message, cause.orNull)