package com.nexla.sc.api

import akka.http.scaladsl.model.{HttpRequest, HttpResponse}
import akka.http.scaladsl.server.RouteResult
import akka.http.scaladsl.server.directives.{DebuggingDirectives, LoggingMagnet}
import org.apache.commons.lang3.time.StopWatch
import org.slf4j.LoggerFactory

trait AccessLogging {

  import AccessLogging._

  def withAccessLogging = DebuggingDirectives.logRequestResult(loggingMagnet)

  @inline private def loggingMagnet: LoggingMagnet[HttpRequest => RouteResult => Unit] = LoggingMagnet(_ => showReqAndResp(_))

  private def showReqAndResp(req: HttpRequest) = {
    val stopWatch = new StopWatch
    stopWatch.start()

    val requestLine = s">>> ${req.method.value} ${req.uri}"
    showResp(requestLine, stopWatch) _
  }

  private def showResp(requestLine: String, startMs: StopWatch)(x: RouteResult) = x match {
    case RouteResult.Complete(r: HttpResponse) =>
      accessLog.info(s"$requestLine <<< ${r.status.value}. Time: $startMs")
    case RouteResult.Rejected(rej) =>
      accessLog.info(s"$requestLine <<< rejected: ${rej.mkString("\n")}. Time: $startMs")
  }
}

object AccessLogging extends AccessLogging {
  val accessLog = LoggerFactory.getLogger("http.access.logger")
}
