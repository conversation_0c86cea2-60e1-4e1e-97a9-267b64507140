package com.nexla.sc.api

import akka.NotUsed
import akka.actor.ActorSystem
import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import akka.http.scaladsl.model.ContentTypes.`application/json`
import akka.http.scaladsl.model.StatusCodes.{NoContent, OK}
import akka.http.scaladsl.model.{HttpEntity, HttpRequest, HttpResponse}
import akka.http.scaladsl.server.directives.{AuthenticationDirective, Credentials}
import akka.http.scaladsl.server.{Directive0, Directives, Route}
import akka.stream.scaladsl.Flow
import com.google.common.collect.Maps
import com.nexla.common.StreamUtils
import com.nexla.common.StreamUtils.jsonUtil
import com.nexla.control.health.ExpectedHealthEvent
import com.nexla.sc.api.CommonApiHandler.registeredConsumerGroups
import com.nexla.listing.client.HealthResponse
import com.nexla.sc.api.LoggingRoute.withLoggingRoute
import com.nexla.sc.config.NexlaCredsConf
import com.nexla.sc.util.{StrictNexlaLogging, end}
import fr.davit.akka.http.metrics.core.HttpMetricsRegistry
import fr.davit.akka.http.metrics.core.scaladsl.server.HttpMetricsDirectives.{metrics, pathPrefixLabeled}
import fr.davit.akka.http.metrics.core.scaladsl.server.HttpMetricsRoute
import fr.davit.akka.http.metrics.prometheus.PrometheusRegistry
import fr.davit.akka.http.metrics.prometheus.marshalling.PrometheusMarshallers._
import spray.json.DefaultJsonProtocol

import java.util.Optional
import scala.collection.JavaConverters._
import scala.jdk.CollectionConverters.mapAsScalaMapConverter

case class GroupId(name: String)

object CommonApiHandler {

  private val registeredConsumerGroups: java.util.Map[String, java.util.Set[String]] = Maps.newConcurrentMap()

  def registerConsumerGroup(groupId: GroupId, topic: String): String = {
    registerConsumerGroup(groupId, Set(topic))
  }

  def registerConsumerGroup(groupId: GroupId, topics: Set[String]): String = {
    topics.foreach { topic =>
      CommonApiHandler.registeredConsumerGroups
        .computeIfAbsent(topic, _ => new java.util.HashSet[String]())
        .add(groupId.name)
    }
    groupId.name
  }

}


trait CommonApiHandler
  extends Directives
    with StrictNexlaLogging
    with AccessLogging
    with SprayJsonSupport
    with DefaultJsonProtocol {

  val envMap: java.util.Map[String, String]
  val version = "0.0.1"
  val registry: Option[HttpMetricsRegistry]
  implicit val system: ActorSystem

  var prometheusRegistry: Option[PrometheusRegistry] = Option.empty

  val putEnv: Route =
    (post & pathPrefixLabeled("putEnv") & end & entity(as[String])) { json =>
      envMap.putAll(jsonUtil.stringToType(json, classOf[java.util.Map[String, String]]))
      complete(OK -> envMap.asScala.toMap)
    }

  private val health =
    (get & pathPrefixLabeled("health")) {
      complete(OK -> HttpEntity(`application/json`, StreamUtils.jsonUtil().toJsonString(
        new HealthResponse(Optional.of(version), Optional.of(expectedHealthEvents().asJava),
          Optional.of(registeredConsumerGroups)))))
    }

  /**
   * Override method to provide expected health events emitted by application.
   * Health events will be monitored by health service
   */
  def expectedHealthEvents(): List[ExpectedHealthEvent] = List()

  val metricsRoute: Route = (get & path("metrics")) {

    registry match {
      case Some(r: PrometheusRegistry) => metrics(r)
      case _ => complete(NoContent)
    }
  }

  private def myUserPassAuthenticator(nexla: NexlaCredsConf)(credentials: Credentials): Option[String] =
    credentials match {
      case <EMAIL>(user) if user == nexla.username && p.verify(nexla.password) => Some(user)
      case _ => None
    }

  def basicAuth(nexla: NexlaCredsConf, realm: String): AuthenticationDirective[String] = {
    authenticateBasic(realm, myUserPassAuthenticator(nexla))
  }

  def buildRoutes(routes: Option[Route] = None,
                  optCors: Option[Directive0] = None,
                  loggingDirective: Directive0 = withAccessLogging): Route = {
    val commonRoute: Route = {
      loggingDirective {
        withLoggingRoute {
          routes match {
            case Some(r) => concat(health, r)
            case _ => health
          }
        }
      }
    }
    val route = optCors match {
      case Some(corsFunc) => corsFunc(commonRoute)
      case _ => commonRoute
    }

    route
  }

  def routesWithPrometheusMetrics(routes: Route): Flow[HttpRequest, HttpResponse, NotUsed] = {
    registry match {
      case Some(r: PrometheusRegistry) => HttpMetricsRoute(routes).recordMetrics(r)
      case _ => routes
    }
  }
}
