package com.nexla.sc.api

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import com.nexla.control.message.ServiceState
import com.nexla.common.{Resource, ResourceType}
import spray.json.{DefaultJsonProtocol, DeserializationException, JsString, JsValue, JsonFormat}

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

case class NexlaResourceDto(resourceId: Int, resourceType: String) {

  // def here to avoid serializing with spray
  def resource = new Resource(resourceId, ResourceType.fromString(resourceType))

}

case class ConnectorStateInfoResponse(resourceId: Int,
                                      resourceType: ResourceType,
                                      state: String,
                                      message: String,
                                      lastModified: LocalDateTime)

case class ReprocessStreamRequest(dateFrom: Long)

case class ConnectorStateInfo(resourceId: Int,
                              resourceType: ResourceType,
                              state: ServiceState,
                              message: String,
                              lastModified: LocalDateTime) {

  val resource = new Resource(resourceId, resourceType)

}

trait RequestDtoFormat {

  _: DefaultJsonProtocol with SprayJsonSupport =>

  implicit val localDateTimeFormat = new JsonFormat[LocalDateTime] {
    private val isoDateTime = DateTimeFormatter.ISO_DATE_TIME

    def write(x: LocalDateTime) = JsString(isoDateTime.format(x))

    def read(value: JsValue) = value match {
      case JsString(x) => LocalDateTime.parse(x, isoDateTime)
      case x => throw new DeserializationException(s"Unexpected type ${x.getClass.getName} when trying to parse LocalDateTime")
    }
  }

  implicit val jodaLocalDateFormat = new JsonFormat[org.joda.time.LocalDate] {
    val format = org.joda.time.format.DateTimeFormat.forPattern("yyyy-MM-dd")

    def write(x: org.joda.time.LocalDate) = JsString(format.print(x))

    def read(value: JsValue) = value match {
      case JsString(x) => format.parseLocalDate(x)
      case x => throw new DeserializationException(s"Unexpected type ${x.getClass.getName} when trying to parse LocalDateTime")
    }
  }

  implicit val resourceTypeFormat = new JsonFormat[ResourceType] {
    def write(x: ResourceType) = JsString(x.name())

    def read(value: JsValue) = value match {
      case JsString(x) => ResourceType.fromString(x)
      case x => throw new DeserializationException(s"Unexpected type ${x.getClass.getName} when trying to parse ResourceType")
    }
  }

  implicit val serviceStateFormat = new JsonFormat[ServiceState] {
    def write(x: ServiceState) = JsString(x.name())

    def read(value: JsValue) = value match {
      case JsString(x) => ServiceState.fromString(x)
      case x => throw new DeserializationException(s"Unexpected type ${x.getClass.getName} when trying to parse ServiceState")
    }
  }

  implicit val nexlaResourceFormat = jsonFormat2(NexlaResourceDto)
  implicit val connectorStateFormat = jsonFormat5(ConnectorStateInfoResponse)

}