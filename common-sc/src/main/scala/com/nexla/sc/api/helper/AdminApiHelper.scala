package com.nexla.sc.api.helper

import com.nexla.admin.client.{AdminApiClient, DataSet, DataSink, DataSource}
import org.slf4j.LoggerFactory

import java.util.Base64
import scala.annotation.tailrec
import scala.collection.JavaConverters.mapAsScalaMapConverter
import scala.sys.exit

sealed trait SparkableTransform extends Serializable {
  def shortStringRepresentation(): String
  def id(): Int
  def orgId(): Int
}

case class SparkTransform(code: List[String], datasetId: Int, orgIdentifier: Int, ownerId: Int) extends SparkableTransform {
  val actualSqlCode: Array[String] = code.toArray
  override def toString: String = {
    s"""
       |
       |${actualSqlCode.mkString(" ")}
       |
       |""".stripMargin
  }

  override def shortStringRepresentation(): String = {
    s"[spark] transform = nexset [$datasetId]"
  }

  def dependencies: Seq[String] = {
    val parsedSparkCode = code.head.split("\n").map(_.trim)
    val regex = "^.*\\{(.*)\\}.*$".r
    // spark SQL may contain additional dependencies. we need to run and execute these as well!
    val requiredDatasetsNames = parsedSparkCode
      .filter(_.contains("{nexset"))
      .toSeq
      .collect { case regex(maybeMatch) => maybeMatch.trim }
      .filter(!_.equalsIgnoreCase("nexset_this"))
    requiredDatasetsNames
  }

  override def id(): Int = datasetId
  override def orgId(): Int = orgIdentifier
}

case class NexlaTransform(code: Object, datasetId: Int, orgIdentifier: Int, ownerId: Int) extends SparkableTransform {
  override def toString: String = {
    s"""
       |
       |$code
       |
       |""".stripMargin
  }

  override def shortStringRepresentation(): String = {
    s"[nexla] transform = nexset [$datasetId]"
  }

  override def id(): Int = datasetId
  override def orgId(): Int = orgIdentifier
}

case class NexlaSource(dataSource: DataSource) extends SparkableTransform {
  override def shortStringRepresentation(): String = {
    s"[source] = [${dataSource.getId}]"
  }

  override def id(): Int = dataSource.getId
  override def orgId(): Int = dataSource.getOrg.getId
}

case class NexlaSink(dataSink: DataSink) extends SparkableTransform {
  override def shortStringRepresentation(): String = {
    s"[sink] = [${dataSink.getId}]"
  }

  override def id(): Int = dataSink.getId
  override def orgId(): Int = dataSink.getOrg.getId
}

class AdminApiHelper(adminApi: AdminApiClient) {

  private val logger = LoggerFactory.getLogger(classOf[AdminApiHelper])
  val JOIN_FEATURE_ENABLED: Boolean = sys.env
    .get("NEXSET_JOIN_ENABLED")
    .exists(_.equalsIgnoreCase("true"))

  private def debase64(s: String): String = {
    new String(Base64.getDecoder.decode(s.getBytes("UTF-8")), "UTF-8")
  }

  def client(): AdminApiClient = {
    adminApi
  }

  def handlePythonJsOrSpark(transformCode: java.util.ArrayList[Object], dataSet: DataSet): List[SparkableTransform] = {
    // todo: assumption this is always like this and Python/Spark transform just has 1 elem
    val mapFromTsCode = transformCode.get(0).asInstanceOf[java.util.LinkedHashMap[String, Object]].asScala
    if (mapFromTsCode.isEmpty) {
      throw new IllegalArgumentException("Map from TSCode is empty, could not proceed")
    }

    var code: List[String] = List[String]()

    for (transformEntry <- mapFromTsCode) {
      val (k, v) = transformEntry
      v match {
        case str: String if str.equals("nexla.custom") =>
          // possibly a spark transform, look into it
          logger.info(s"possibly a spark transform: [$k, $v]")
        case str: String if str.equals("nexla.modify") =>
          // nexla transform, look into it
          logger.info(s"possibly a nexla transform: [$k, $v]")
        case _ =>
          // the actual transform
          if (k.equals("spec")) {
            val castV = v.asInstanceOf[java.util.LinkedHashMap[String, String]]
            val maybeScript = castV.get("script")
            if (maybeScript != null) {
              val possiblySparkTransform: String = debase64(maybeScript)
              code ++= List(possiblySparkTransform)
            }
          }
      }
    }
    // check additional dependencies
    val codeIsSparkSQL: Boolean = if (transformCode.isEmpty) {
      false
    } else {
      val transformCodeMap = transformCode.get(0)
      if (transformCodeMap.isInstanceOf[java.util.Map[String, Object]]) {
        val specMap = transformCodeMap.asInstanceOf[java.util.Map[String, Object]]
        if (specMap.get("spec") != null) {
          val internalSpecObj = specMap.get("spec")
          if (internalSpecObj.isInstanceOf[java.util.Map[String, Object]]) {
            val internalSpecMap = internalSpecObj.asInstanceOf[java.util.Map[String, Object]]
            val codeType = internalSpecMap.get("code_type")
            if (codeType != null) {
              val codeTypeString = codeType.toString
              codeTypeString.equalsIgnoreCase("spark_sql")
            } else {
              false
            }
          } else {
            false
          }
        } else {
          false
        }
      } else {
        false
      }
    }

    if (JOIN_FEATURE_ENABLED && codeIsSparkSQL) {
      // sql stub
      logger.warn("JOINs enabled and this is Spark SQL transform, looking for additional referenced datasets")
      val sparkTx = SparkTransform(code, dataSet.getId, dataSet.getOrg.getId, dataSet.getOwner.getId)
      val itsDependencies = sparkTx.dependencies
      val nexsetDependenciesFromOtherPipelines = itsDependencies.filter(_.startsWith("nexset_")).filter {
        dep =>
          // TODO: check access controls, user can not use datasets which don't belong to them
          val dependencyDatasetId = dep.substring("nexset_".length).toInt
          // assumption: dataset has just 1 parent
          val directDependencyId = dataSet.getParentDatasets.get(0).getId
          dependencyDatasetId != directDependencyId
      }.map(_.substring("nexset_".length).toInt).flatMap {
        a =>
          logger.info(s"Additional dataset discovered from another pipeline: dataset [${dataSet.getId}] depends on Nexset [${a}]")
          val externalDataSetsPipeline = recursiveGetParentDatasets(a).reverse
          val theirSource = NexlaSource(adminApi.getDataSource(externalDataSetsPipeline.head.getDataSourceId.get()).get())
          val rawObjects: List[Object] = theirSource :: externalDataSetsPipeline
          nexlaAdminApiObjectsToSparkableTransforms(rawObjects)
      }

      // if there are dependencies from other datasets than current pipeline, mentioned as $nx_<ID>$,
      // we assume they are nexsets, so we prepend them here and register accordingly
      // todo: complex cases
      nexsetDependenciesFromOtherPipelines.toList :+ sparkTx
    } else {
      // python/js stubbing
      logger.info(s"JOINs disabled or transform [${dataSet.getId}] is not a spark SQL one")
      val operations = transformCode
      List(NexlaTransform(operations, dataSet.getId, dataSet.getOrg.getId, dataSet.getOwner.getId))
    }
  }

  private def handleNexla(transformCode: java.util.ArrayList[Object], dataset: DataSet): List[NexlaTransform] = {
    val operations = transformCode
    List(NexlaTransform(operations, dataset.getId, dataset.getOrg.getId, dataset.getOwner.getId))
  }

  def getFromSingleDataset(dataSet: DataSet): List[SparkableTransform] = {
    logger.info(s"getting code from single dataset, id [${dataSet.getId}]")
    val transformCode = dataSet.getTransformHistory.getTransforms.asInstanceOf[java.util.ArrayList[Object]]
    if (transformCode.size() == 0) {
      logger.info("TransformCode size is 0, could not proceed and returning Empty")
      List()
    } else if (transformCode.size() == 1) {
      // python / js / spark case
      handlePythonJsOrSpark(transformCode, dataSet)
    } else {
      // more than 1 - Nexla Transform
      handleNexla(transformCode, dataSet)
    }
  }

  def recursiveGetParentDatasets(headId: Int): List[DataSet] = {
    @tailrec
    def iter(currDs: DataSet, result: List[DataSet]): List[DataSet] = {
      val freshDs = adminApi.getDataSet(currDs.getId).get()
      logger.info(s"currently getting parent datasets of DS id [${freshDs.getId}], result size [${result.size}]")
      if (freshDs.getParentDatasets == null || freshDs.getParentDatasets.isEmpty) {
        logger.info(s"DS id [${freshDs.getId}] does not have parents, returning result with curr elem")
        result ++ List(freshDs)
      } else {
        logger.info(s"DS id [${freshDs.getId}] has [${freshDs.getParentDatasets.size()}] parent datasets, adding 0th elem [${freshDs.getParentDatasets.get(0).getId}] and appending curr ds id [${freshDs.getId}] to result")
        iter(freshDs.getParentDatasets.get(0), result ++ List(freshDs))
      }
    }
    val newHead = adminApi.getDataSet(headId).get()
    iter(newHead, List())
  }

  def getParentSource(datasetId: Int): DataSource = {
    val parentDataset: DataSet = recursiveGetParentDatasets(datasetId).last
    adminApi.getDataSource(parentDataset.getDataSourceId.get()).get()
  }

  def cleanup(entityName: String): String = {
    entityName.replaceAll("[^A-Za-z0-9_]", "_")
  }

  def nexlaAdminApiObjectsToSparkableTransforms(rawOperationsList: List[Object]): List[SparkableTransform] = {
    // assumption 1: we only need to seek for the NEXSET_<ID> entries - these belong to another pipeline.
    // assumption 2: if a pipeline does not contain any NEXSET_<ID> in the Spark SQL code, it's self-sufficient
    // (meaning we can just return the main branch above and don't seek for anything else
    // assumption 3: our steps for executing Nexla and Spark tx are dumb and would not search for additional dependencies.
    // they will just execute the given code on our Spark context.
    val results: List[SparkableTransform] = rawOperationsList.flatMap(
      obj =>
        obj match {
          case a: DataSource =>
            List(NexlaSource(a))
          case b: DataSink =>
            List(NexlaSink(b))
          case c: DataSet =>
            getFromSingleDataset(c)
          // these two are already mapped
          case d: NexlaSource =>
            List(d)
          case e: NexlaSink =>
            List(e)
          case other =>
            throw new IllegalArgumentException(s"Could not map to spark-executable operation: [${other}]")
        }
    )
    results
  }

  def buildDepTreeForSink(sinkId: Int): List[SparkableTransform] = {
    val sink = adminApi.getDataSink(sinkId).get()
    val dataSetPrecedingSink = sink.getDataSet
    val itsDatasets = recursiveGetParentDatasets(dataSetPrecedingSink.getId).reverse

    // must have, as it's the main source after all
    val sourceId = itsDatasets.head.getDataSourceId.get()
    val source = adminApi.getDataSource(sourceId).get()
    val listWithSource: List[Object] = source :: itsDatasets
    val rawOperationsList = listWithSource :+ sink
    val results = nexlaAdminApiObjectsToSparkableTransforms(rawOperationsList)

    results
  }

  def unwrap(adminApiObj: AnyRef): AnyRef = {
    adminApiObj match {
      case ds: DataSet =>
        val maybeDs = adminApi.getDataSet(ds.getId)
        if (maybeDs.isEmpty) {
          logger.error(s"${ds.getClass.getSimpleName} is empty on Nexla Admin API side, could not proceed")
          // todo retry
          exit(1)
        } else {
          maybeDs.get()
        }
      case sink: DataSink =>
        val maybeSink = adminApi.getDataSink(sink.getId)
        if (maybeSink.isEmpty) {
          logger.error(s"${maybeSink.getClass.getSimpleName} is empty on Nexla Admin API side, could not proceed")
          exit(1)
        } else {
          maybeSink.get()
        }
      case source: DataSource =>
        val maybeSource = adminApi.getDataSource(source.getId)
        if (maybeSource.isEmpty) {
          logger.error(s"${maybeSource.getClass.getSimpleName} is empty on Nexla Admin API side, could not proceed")
          exit(1)
        } else {
          maybeSource.get()
        }
      case other: Any =>
        throw new IllegalArgumentException(s"Don't know how to unwrap Nexla Admin API response object: [${other.getClass.getSimpleName}]")
    }
  }

  def getDatasetOrFail(dataSetId: Int): DataSet = {
    val ds = new DataSet()
    ds.setId(dataSetId)
    unwrap(ds).asInstanceOf[DataSet]
  }

  def getSinkOrFail(sinkId: Int): DataSink = {
    val ds = new DataSink()
    ds.setId(sinkId)
    unwrap(ds).asInstanceOf[DataSink]
  }

  def getSourceOrFail(srcId: Int): DataSource = {
    val ds = new DataSource()
    ds.setId(srcId)
    unwrap(ds).asInstanceOf[DataSource]
  }

}
