package com.nexla.sc.api

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import akka.http.scaladsl.model.StatusCodes._
import akka.http.scaladsl.server.{Directives, Route}
import ch.qos.logback.classic.{Level, Logger => LogbackLogger}
import com.nexla.sc.api.common.ApiResponse
import com.nexla.sc.util._
import com.typesafe.scalalogging.LazyLogging
import org.slf4j.{Logger, LoggerFactory}
import spray.json.DefaultJsonProtocol

import scala.util.control.NonFatal

object LoggingRoute
  extends Directives
    with LazyLogging
    with SprayJsonSupport
    with DefaultJsonProtocol {

  val loggingRoute =
    (get & pathPrefix("logging") & parameters("prefix".?, "level") & end) {
      case (prefix, logLevel) =>

        val apiLogger = LoggerFactory.getLogger(prefix.getOrElse(Logger.ROOT_LOGGER_NAME))

        apiLogger match {
          case logbackLogger: LogbackLogger =>
            val currLevel = logbackLogger.getLevel
            val newLevel = Level.toLevel(logLevel)
            try {
              val message = s"${prefix.orNull} switched from $currLevel to $newLevel"
              logbackLogger.setLevel(newLevel)
              logger.info(message)
              complete(OK -> ApiResponse(message))
            } catch {
              case NonFatal(e) =>
                val message = s"Cannot update log level from $currLevel to $newLevel for prefix ${prefix.orNull}"
                logger.error(message, e)
                complete(BadRequest -> ApiResponse(message))
            }

          case _ =>
            logger.info(s"Logger class is not supported: ${apiLogger.getClass.getName}")
            complete(Conflict)
        }

    }

  def withLoggingRoute(route: Route) = concat(loggingRoute, route)
}
