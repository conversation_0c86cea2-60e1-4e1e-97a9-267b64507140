package com.nexla.sc.api

import akka.actor.ActorSystem
import akka.http.scaladsl.model.{HttpRequest, HttpResponse}
import akka.http.scaladsl.server.RouteResult
import akka.http.scaladsl.server.directives.{DebuggingDirectives, LoggingMagnet}
import akka.stream.Materializer
import com.nexla.sc.util.Async
import org.apache.commons.lang3.time.StopWatch

import scala.concurrent.ExecutionContext
import scala.concurrent.duration.DurationInt
import scala.util.{Failure, Success}

trait AccessBodyLogging {

  import AccessLogging._
  implicit val m: Materializer

  private implicit val ec: ExecutionContext = Async.ioExecutorContext

  def withAccessBodyLogging = DebuggingDirectives.logRequestResult(loggingMagnet)

  @inline private def loggingMagnet: LoggingMagnet[HttpRequest => RouteResult => Unit] = LoggingMagnet(_ => showReqAndResp)

  private def showReqAndResp(req: HttpRequest)(x: RouteResult) = {
    val stopWatch = new StopWatch
    stopWatch.start()

    if (!req.entity.contentType.mediaType.isMultipart) {
      val timeout = 1.second
      val bodyFuture = req
        .entity
        .toStrict(timeout)
        .map(_.data)
        .map(_.utf8String)

      val requestLine = s">>>request: ${req.method.value} ${req.uri}"
      showResp(requestLine, stopWatch)(x)
      bodyFuture.onComplete {
        case Success(body) =>
          accessLog.debug(s">>>body: $body")
        case Failure(t) =>
          accessLog.debug(s">>>failed body: ${t.getStackTrace.mkString(" ")}")
      }
    } else {
      accessLog.debug(s">>>body: (multipart)")
    }
  }

  private def showResp(requestLine: String, startMs: StopWatch)(x: RouteResult) = x match {
    case RouteResult.Complete(r: HttpResponse) =>
      accessLog.info(s"$requestLine <<< ${r.status.value}. Time: $startMs")
    case RouteResult.Rejected(rej) =>
      accessLog.info(s"$requestLine <<< rejected: ${rej.mkString("\n")}. Time: $startMs")
  }
}


