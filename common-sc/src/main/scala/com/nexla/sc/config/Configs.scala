package com.nexla.sc.config

import com.nexla.common.AppUtils.authorizationHeader
import com.nexla.common.NexlaConstants._
import com.nexla.common.datetime.DateTimeUtils.nowUTC
import com.nexla.common.{NexlaConstants, SSLCertificateStore}
import com.nexla.connector.config.file.{AWSAuthConfig, S3Constants}
import com.nexla.connector.config.redis.RedisTlsContext
import com.nexla.connector.config.vault.NexlaAppConfig
import com.nexla.sc.util.OrgIdsFilter
import org.apache.commons.lang3.StringUtils
import org.apache.kafka.common.config.SslConfigs
import org.quartz.CronExpression

import java.io.File
import java.net.InetAddress
import java.time.ZoneId
import java.util.TimeZone
import scala.util.Try

trait WithConfig {
  val config: NexlaAppConfig

  implicit class RichConfig(config: NexlaAppConfig) {

    def getOptString(path: String) = Try(config.getString(path)).toOption.filterNot(_.isEmpty)
    def getOptBoolean(path: String) = Try(config.getBoolean(path)).toOption
    def getOptInt(path: String) = Try(config.getInt(path)).toOption
  }

}

trait ListingDb extends AwsCredentials {
  val listingDb = DbConf(url = config.getString("listing-db.url"),
    user = config.getString("listing-db.user"),
    password = Try(config.getString("listing-db.password")).toOption.getOrElse(""),
    maxConnections = config.getInt("listing-db.max-connections"),
    iamEnabled = config.getBoolean("listing-db.iam-enabled"),
    iamRegion = Try(config.getString("listing-db.iam-region")).toOption.orElse(credentialsAwsRegion).orElse(Some("us-east-1")))

}

trait MetricsDb extends AwsCredentials {
  val metricsDb = DbConf(url = config.getString("metrics-db.url"),
    user = config.getString("metrics-db.user"),
    password = Try(config.getString("metrics-db.password")).toOption.getOrElse(""),
    maxConnections = config.getInt("metrics-db.max-connections"),
    iamEnabled = Try(config.getBoolean("metrics-db.iam-enabled")).toOption.getOrElse(false),
    iamRegion = Try(config.getString("metrics-db.iam-region")).toOption.orElse(credentialsAwsRegion).orElse(Some("us-east-1")))

}

trait Vault extends WithConfig {
  val vault = Try {
    VaultConf(config.getString("vault.host"), config.getString("vault.token"))
  }.toOption
}

trait DataDog extends WithConfig {
  val dataDog = DataDogConf(host = config.getString("datadog.host"),
    port = Try(config.getInt("datadog.port")).toOption.getOrElse(0),
    apiKey = Try(config.getString("datadog.apiKey")).toOption.getOrElse(""),
    env = Try(config.getString("datadog.env")).toOption.getOrElse(""),
    enabled = config.getBoolean("datadog.enabled"))
}

trait Prometheus extends WithConfig {
  val prometheus = PrometheusConf(
    pushGatewayUrl = Try(config.getString("prometheus.pushGatewayUrl")).toOption.getOrElse("")
  )
}

trait NexlaTasks extends WithConfig {
  val taskParallelism = config.getInt("task.parallelism")
  val ctrlTaskParallelism = config.getInt("ctrl.task.parallelism")
  val receiveTasksCron = config.getString("receive.tasks.cron")
  val intervalDuration = {
    val cron = new CronExpression(receiveTasksCron)
    cron.setTimeZone(TimeZone.getTimeZone(ZoneId.of("UTC")))

    val nextCron = cron.getNextValidTimeAfter(nowUTC().toDate)
    val nextAfterNext = cron.getNextValidTimeAfter(nextCron)
    nextAfterNext.getTime - nextCron.getTime
  }

}

trait TelemetryConfig extends WithConfig {
  val telemetryConf = TelemetryConf(datastore=config.getOptString("telemetry.datastore"),
    threadStateEnabled = config.getOptBoolean("telemetry.akka.threadStates.enabled"),
    threadStateIntervalInSec = config.getOptInt("telemetry.akka.threadStates.intervalInSecs"),
    threadStateDelayInSec = config.getOptInt("telemetry.akka.threadStates.delayInSecs")
  )
}

trait NexlaCreds extends WithConfig {
  val nexlaCreds = NexlaCredsConf(config.getString("nexla.username"), config.getString("nexla.password"))
  val authHeader = authorizationHeader(nexlaCreds.username, nexlaCreds.password)
}

trait NexlaDecryptKey extends WithConfig {
  val decryptKey = config.getString(CREDENTIALS_DECRYPT_KEY)
}

trait NexlaSslConfig extends WithConfig {
  val httpsEnabled: Boolean = Try(config.getBoolean(HTTPS_ENABLED)).toOption.getOrElse(false)
  val httpsClientEnabled: Boolean = Try(config.getBoolean(HTTPS_CLIENT_ENABLED)).getOrElse(httpsEnabled)
  val kafkaSslEnabled: Boolean = Try(config.getBoolean(SSL_KAFKA_ENABLED)).toOption.getOrElse(true)
  val clientCertificate: Option[String] = config.getOptString(SSL_CLIENT_CERTIFICATE)
    .map(_
      .replaceAll("-----BEGIN CERTIFICATE-----", "-----BEGINCERTIFICATE-----")
      .replaceAll("-----END CERTIFICATE-----", "-----ENDCERTIFICATE-----")
      .replaceAll(" ", "\n")
      .replaceAll("-----BEGINCERTIFICATE-----", "-----BEGIN CERTIFICATE-----")
      .replaceAll("-----ENDCERTIFICATE-----", "-----END CERTIFICATE-----")
    )

  val serverKeystoreP12: Option[String] =
    config.getOptString(SSL_SERVER_CERTIFICATE_P12).map(_.replaceAll(" ", ""))
  val serverKeystoreP12Path: Option[String] =
    config.getOptString(SSL_SERVER_CERTIFICATE_P12_PATH)
  val serverTruststoreP12: Option[String] =
    config.getOptString(SSL_SERVER_TRUSTSTORE_P12).map(_.replaceAll(" ", ""))
  val serverTruststoreP12Path: Option[String] =
    config.getOptString(SSL_SERVER_TRUSTSTORE_P12_PATH)
  val serverKeyPassword: Option[String] =
    config.getOptString(SSL_SERVER_CERTIFICATE_PASSWORD)

  val clientKeystoreP12: Option[String] =
    config.getOptString(SSL_CLIENT_CERTIFICATE_P12).map(_.replaceAll(" ", ""))
  val clientKeystoreP12Path: Option[String] =
    config.getOptString(SSL_CLIENT_CERTIFICATE_P12_PATH)
  val clientTruststoreP12: Option[String] =
    config.getOptString(SSL_CLIENT_TRUSTSTORE_P12).map(_.replaceAll(" ", ""))
  val clientTruststoreP12Path: Option[String] =
    config.getOptString(SSL_CLIENT_TRUSTSTORE_P12_PATH)
  val clientPassword: Option[String] =
    config.getOptString(SSL_CLIENT_CERTIFICATE_PASSWORD)

  val kafkaTruststoreP12: Option[String] =
    config.getOptString(SSL_KAFKA_TRUSTSTORE_P12).map(_.replaceAll(" ", ""))
  val kafkaTruststoreP12Path: Option[String] =
    config
      .getOptString(SSL_KAFKA_TRUSTSTORE_P12_PATH)
      .orElse(config.getOptString(NexlaConstants.CONNECT_SSL_TRUSTSTORE_LOCATION))
      .orElse(config.getOptString(SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG))
  val kafkaTruststorePassword: Option[String] =
    config
      .getOptString(SSL_KAFKA_TRUSTSTORE_PASSWORD)
      .orElse(config.getOptString(NexlaConstants.CONNECT_SSL_TRUSTSTORE_PASSWORD))
      .orElse(config.getOptString(SslConfigs.SSL_TRUSTSTORE_PASSWORD_CONFIG))

  val kafkaKeystoreP12: Option[String] =
    config.getOptString(SSL_KAFKA_KEYSTORE_P12).map(_.replaceAll(" ", ""))
  val kafkaKeystoreP12Path: Option[String] =
    config
      .getOptString(SSL_KAFKA_KEYSTORE_P12_PATH)
      .orElse(config.getOptString(NexlaConstants.CONNECT_SSL_KEYSTORE_LOCATION))
      .orElse(config.getOptString(SslConfigs.SSL_KEYSTORE_LOCATION_CONFIG))
  val kafkaKeystorePassword: Option[String] =
    config
      .getOptString(SSL_KAFKA_KEYSTORE_PASSWORD)
      .orElse(config.getOptString(NexlaConstants.CONNECT_SSL_KEYSTORE_PASSWORD))
      .orElse(config.getOptString(SslConfigs.SSL_KEYSTORE_PASSWORD_CONFIG))
}

trait FeatureFlagsConfig extends WithConfig {
  val useOldCondensedMethods = config.getOptBoolean("use.old.condensed.methods").getOrElse(false)
}

trait NexlaEndpoints extends WithConfig with NexlaSslConfig {

  val scriptRunnerUrl = config.getString("script.runner.url")
  val listingAppServer = config.getString("listing.app.server.url")
  val coordinationAppServer = config.getString("coordination.server.url")
  val ctrlHttpUrl = config.getString("ctrl.server.url")
  val ctrlNodeTaskManagerUrl = config.getString("ctrl.nodetaskmanager.url")
  val transformStreamUrl = config.getString("transform.stream.url")
  val notifyStreamUrl = config.getString("notify.stream.url")
  val apiCredentialsServer = config.getString("api.credentials.server")
  val kafkaConnectorUrls = config.getString("kafka.connector.server").split(",")
  val fileVaultUrl = config.getString("file.vault.url")
  val syncApiServerUrl = config.getString("sync.api.url")
  val probeApp = config.getString("probe.app.url")
  val sinkStatusPort = config.getInt("sink.status.port")
  val metricsHttpApp = config.getString("metrics.http.app.url")
}

trait StatsD extends WithConfig {
  val statsdHost = config.getString("statsd.host")
  val statsdPort = config.getInt("statsd.port")
  val statsdEnabled = config.getBoolean("statsd.enabled")
  val statsdPrefix = config.getString("statsd.prefix")
}

trait NexlaCredsEncoded extends WithConfig {
  val nexlaCredsEncoded = NexlaCredsEncodedConf(config.getString("nexla.creds.enc"), config.getString("nexla.creds.enc.iv"))
}

trait RedisCreds extends WithConfig {

  val redisLruCacheCapacity = config.getInt("redis.lru.capacity")
  val redisLruExpirationMin = config.getInt("redis.lru.expiration.min")

  val redisCreds = RedisCredsConf(
    hosts = config.getString(REDIS_HOSTS),
    clusterEnabled = config.getBoolean(REDIS_CLUSTER_ENABLED),
    password = config.getOptString(REDIS_PASSWORD),
    tlsContext = Try(config.getBoolean(REDIS_TLS_ENABLED)).toOption
      .orElse(Some(false))
      .filter(identity)
      .map(_ => {
        val kstorePwd = config.getString(REDIS_TLS_KEYSTORE_PASSWORD)
        val kstore = config.getOptString(REDIS_TLS_KEYSTORE_P12)
          .map(_.replaceAll(" ", ""))
          .map(new SSLCertificateStore(_, kstorePwd))
        val kstoreAsFile = config.getOptString(REDIS_TLS_KEYSTORE_P12_PATH)
          .flatMap((path: String) => Try(new File(path)).toOption)
          .map(new SSLCertificateStore(_, kstorePwd))

        val tstorePwd = config.getString(REDIS_TLS_TRUSTSTORE_PASSWORD)
        val tstoreKey = config.getOptString(REDIS_TLS_TRUSTSTORE_P12)
          .map(_.replaceAll(" ", ""))
          .map(new SSLCertificateStore(_, tstorePwd))
        val tstoreAsFile = config.getOptString(REDIS_TLS_TRUSTSTORE_P12_PATH)
          .flatMap((path: String) => Try(new File(path)).toOption)
          .map(new SSLCertificateStore(_, tstorePwd))

        new RedisTlsContext(
          kstore.orElse(kstoreAsFile).orNull,
          tstoreKey.orElse(tstoreAsFile).orNull
        )
      })
  )
}

trait RedisCredsEncoded extends WithConfig {
  val redisCredsEncoded = RedisCredsEncodedConf(config.getString("redis.creds.enc"), config.getString("redis.creds.enciv"))
}

trait NexlaClusterApplication extends WithConfig {

  val nodeId = config.getOptString("node.id")
  val nexlaAppPort = config.getInt("nexla.app.port")
  val nexlaMetricsPort = config.getInt("nexla.metrics.port")

  val podName = config.getOptString("pod.name")
    .orElse(config.getOptString("hostname"))
    .orElse(config.getOptString("service.name"))

  val podIp = config
    .getOptString("pod.ip")
    .getOrElse(InetAddress.getLocalHost.getHostAddress)

 val appVersion = config.getString("app.version")

  val serviceName = config
    .getOptString("marathon.app.id")
    .map(x => StringUtils.removeStart(x, "/"))
    .getOrElse {
      podName.map(_.split("-").dropRight(2).mkString("-")).orNull
    }

}

trait Zookeeper extends WithConfig {
  val zookeeperConnect = config.getString("zookeeper.connect")
}

trait SecretNames extends WithConfig {
  val secretNames = config.getOptString("secret.names")
}

trait MetricsTopic extends WithConfig {
  val metricsWindowMs = config.getLong("metrics.window.ms")
  val metricsTopic = config.getString("topic.metrics")
}

trait AwsCredentials extends WithConfig {
  val credentialsAwsRegion = config.getOptString(NexlaConstants.AWS_SECRET_MANAGER_REGION)
  val credentialsAwsAccessKey = config.getOptString(NexlaConstants.AWS_SECRET_MANAGER_ACCESS_KEY)
  val credentialsAwsSecretKey = config.getOptString(NexlaConstants.AWS_SECRET_MANAGER_SECRET_KEY)
  val credentialsAwsRoleArn = config.getOptString(NexlaConstants.AWS_SECRET_MANAGER_ROLE_ARN)
  val credentialsAwsIdentityTokenFile = config.getOptString(NexlaConstants.AWS_SECRET_MANAGER_IDENTITY_TOKEN_FILE)

  def iAmAuthConfig(iAmRegion: Option[String]) = {
    val cfg = new java.util.HashMap[String, AnyRef]()
    credentialsAwsRegion.orElse(iAmRegion).foreach(x => cfg.put(S3Constants.REGION, x))
    credentialsAwsAccessKey.foreach(x => cfg.put(S3Constants.ACCESS_KEY_ID, x))
    credentialsAwsSecretKey.foreach(x => cfg.put(S3Constants.SECRET_KEY, x))
    credentialsAwsRoleArn.foreach(x => cfg.put(S3Constants.ARN, x))
    credentialsAwsIdentityTokenFile.foreach(x => cfg.put(S3Constants.IDENTITY_TOKEN_FILE, x))
    new AWSAuthConfig(cfg, null)
  }

  val iAmAuthCfg = iAmAuthConfig(None)

  val credEnrichmentUrl = config.getOptString("credential.enrichment.url").orNull
}

trait KafkaProperties extends NexlaSslConfig {
  val connectBootstrapServers = config.getString("connect.bootstrap.servers")
  val bootstrapServers = if (kafkaSslEnabled) connectBootstrapServers else config.getString("bootstrap.servers")

  val producerProps = {
    import scala.collection.JavaConverters._
    config.getConfig("kafka-extra-properties.producer").entrySet().asScala
      .map(e => e.getKey -> StringUtils.removeEnd(StringUtils.removeStart(e.getValue.render(), "\""), "\""))
      .toMap
  }
  val consumerProps = {
    import scala.collection.JavaConverters._
    config.getConfig("kafka-extra-properties.consumer").entrySet().asScala
      .map(e => e.getKey -> StringUtils.removeEnd(StringUtils.removeStart(e.getValue.render(), "\""), "\""))
      .toMap
  }

  val kafkaConsumerConfig = config.getConfig("akka.kafka.consumer")

  val confluentPartnerClientId = config.getOptString("confluent.partner.client.id")

}

trait Dataset extends WithConfig {
  val dataset = DatasetConf(
    config.getInt("dataset.partitions"),
    config.getInt("dataset.replication"))
}

trait NexlaAdminApi extends WithConfig {
  val apiAccessKey = config.getString("api.access.key")
  val dataplaneUid = config.getOptString("dataplane.uid").orNull
}

trait LicenseProperties extends WithConfig {
  val nexlaInstallLicenseServiceUrl = config.getString("nexla.install.license.service.url")
}

trait SparkApplicationProperties extends WithConfig {
  val sparkApplicationDriverCores = config.getInt("nexla.sparkapplication.driver.cores")
  val sparkApplicationDriverCoreLimit = config.getString("nexla.sparkapplication.driver.core.limit")
  val sparkApplicationDriverMemory = config.getString("nexla.sparkapplication.driver.memory")

  val sparkApplicationExecutorCores = config.getInt("nexla.sparkapplication.executor.cores")
  val sparkApplicationExecutorMemory = config.getString("nexla.sparkapplication.executor.memory")
  val sparkApplicationExecutorInstances = config.getInt("nexla.sparkapplication.executor.instances")

  val sparkApplicationNamespace = config.getString("nexla.sparkapplication.namespace")
  val sparkApplicationGroup = config.getString("nexla.sparkapplication.group")
  val sparkApplicationServiceAccount = config.getString("nexla.sparkapplication.service.account")
}

trait StatisticsAppProperties extends WithConfig {
  val statisticsAppImageTag = config.getString("spark.statistics.app.image.tag")
  val statisticsAppClassPath = config.getString("spark.statistics.app.class.path")
  val statisticsAppJarPath = config.getString("spark.statistics.app.jar.path")

  val statisticsAppParallelism = config.getInt("spark.statistics.app.parallelism")
  val statisticsAppUpdaterEnabled = config.getBoolean("spark.statistics.app.updater.enabled")
  val statisticsAppUpdaterCron = config.getString("spark.statistics.app.updater.cron")
  val statisticsAppStartDataSet = config.getInt("spark.statistics.app.start.dataset")

  val statisticsAppDataToProcessLimit = config.getString("spark.statistics.app.data.limit")

  val statisticsAppCtrlUrl = config.getString("spark.statistics.app.ctrl.url")

  val statisticsAppOrgIdsFilter = OrgIdsFilter(
    included = config.getOptString("org.ids.included").map(_.split(",").filterNot(_ == "").map(_.toInt).toSet).filter(_.nonEmpty),
    excluded = config.getOptString("org.ids.excluded").map(_.split(",").filterNot(_ == "").map(_.toInt).toSet).filter(_.nonEmpty),
    currentCluster = config.getInt("cluster.id")
  )
}

trait FeatureFlags extends WithConfig {
  val lookupDataModelVersioningEnabled: Boolean = config.getOptBoolean("feature.lookups.data.model.versioning.enabled").getOrElse(true)
}

case class DockerConf(tag: String,
                      cpu: Double,
                      memoryMb: Double,
                      instances: Int,
                      validateConnectConfig: Boolean,
                      credentialsEnabled: Boolean,
                      credentialsLocation: String,
                      kafkaHeapOpts: Option[String],
                      forcePull: Boolean)

case class VaultConf(host: String,
                     token: String)

case class DbConf(url: String,
                  user: String,
                  password: String,
                  maxConnections: Int,
                  iamEnabled: Boolean,
                  iamRegion: Option[String])

case class DataDogConf(host: String,
                       port: Int,
                       apiKey: String,
                       env: String,
                       enabled: Boolean)

case class PrometheusConf(pushGatewayUrl: String)

case class TelemetryConf(datastore : Option[String],
                         threadStateEnabled: Option[Boolean],
                         threadStateIntervalInSec: Option[Int],
                         threadStateDelayInSec: Option[Int])

case class NexlaCredsConf(username: String,
                          password: String)

case class NexlaCredsEncodedConf(enc: String,
                                 encIv: String)

case class RedisCredsConf(hosts: String,
                          clusterEnabled: Boolean,
                          password: Option[String],
                          tlsContext: Option[RedisTlsContext])

case class RedisCredsEncodedConf(enc: String,
                                 encIv: String)

case class DatasetConf(partitions: Int,
                       replication: Int)