package com.nexla.sc.metric

import com.nexla.common.ResourceType
import com.nexla.common.metrics.NexlaRawMetric

import scala.collection.mutable
import scala.collection.JavaConverters._

/**
 * As for 2022-01-11, these are in-memory only used by the producer of metrics.
 * The wire format of metrics are still in represented by NexlaRawMetric.
 *
 * The intent is to migrate to these definitions even for when it's on the wire so we can ser/de metrics
 * knowing what type of metrics it is.
 */
trait BaseMetric {
  def resourceId: Int

  def resourceType: ResourceType

  def runId: Long

  def timestamp: Long = System.currentTimeMillis()

  def recordCount: Long

  def fileSize: Long

  def errorCount: Long
}

case class BaseSinkMetric(resourceId: Int, resourceType: ResourceType, runId: Long, datasetId: Int,
                          recordCount: Long, fileSize: Long, errorCount: Long, metadata: MetadataBuilder,
                          orgId: Int, ownerId: Int) extends BaseMetric

class MetadataBuilder {

  private val metadata = mutable.Map[String, String]()

  def withDisplayPath(value: String) = {
    metadata.put(NexlaRawMetric.DISPLAY_PATH, value)
    this
  }

  def withName(value: String) = {
    metadata.put(NexlaRawMetric.NAME, value)
    this
  }

  def build() = {
    metadata.asJava
  }
}
