package com.nexla.sc.schema

import java.util

import com.bazaarvoice.jolt.JsonUtils.{jsonToMap, toJsonString}
import com.nexla.admin.client.DataSet
import com.nexla.sc.schema.SchemaDescriptionGenerator._

import scala.collection.JavaConverters._

class SchemaDescriptionGenerator {

  def generateDescription(dataSet: DataSet, teamId: Option[Int]): SchemaDescription = {
    val schemaAsMap = jsonToMap(toJsonString(dataSet.getOutputSchema))
    val orgId = dataSet.getOrg.getId
    val ownerId = dataSet.getOwner.getId
    val elements = flattenObject(orgId, ownerId, teamId, "", schemaAsMap.asMap)
    SchemaDescription(dataSet.getId, orgId, ownerId, teamId, elements)
  }

  def flattenObject(orgId: Int,
                    ownerId: Int,
                    teamId: Option[Int],
                    objectPath: String,
                    schemaMap: Map[String, AnyRef]): Seq[SchemaElement] = {
    schemaMap.get(PROPERTIES)
      .filter(_ => schemaMap(TYPE) == OBJECT)
      .map(_.asMap)
      .map(x =>
        x.flatMap {
          case (name, propContent) =>
            flattenObjectContent(orgId, ownerId, teamId, objectPath, name, propContent.asMap)
        }
      )
      .map(_.toList)
      .getOrElse(List())
  }

  def flattenObjectContent(orgId: Int,
                           ownerId: Int,
                           teamId: Option[Int],
                           objectPath: String,
                           name: String,
                           objContent: Map[String, AnyRef]): Seq[SchemaElement] = {

    val anyOfTypes = getAnyOfTypes(objContent)

    val typeKeywords = objContent.get(TYPE)
      .map(typ => {
        val arrayOrThisType = if (typ == ARRAY) {
          val map = objContent(ITEMS).asMap
          val words = Set(map.get(TYPE), map.get(FORMAT))
          words.flatten.map(_.toString) ++ getAnyOfTypes(map)
        } else {
          Set(Some(typ), objContent.get(FORMAT)).flatten.map(_.toString)
        }
        arrayOrThisType ++ anyOfTypes
      })
      .getOrElse(anyOfTypes)

    val foldedAnyOfObjects = objContent.get(ANY_OF)
      .map(javaAnyOf => {
        javaAnyOf.asInstanceOf[util.List[AnyRef]].asScala
          .map(_.asMap)
          .flatMap(x => {
            if (x.get(TYPE).contains(OBJECT)) {
              flattenObject(orgId, ownerId, teamId, s"$objectPath$name.", x)
            } else {
              Seq()
            }
          })
      })
      .getOrElse(Seq())

    val foldedArrayObjects = objContent.get(ITEMS)
      .filter(_ => objContent.get(TYPE).contains(ARRAY))
      .map(_.asMap)
      .map {
        flattenObjectContent(orgId, ownerId, teamId, s"$objectPath$name", "", _)
          .filter(_.name.nonEmpty)
      }
      .map(_.toList)
      .getOrElse(List())

    val foldedObjects = flattenObject(orgId, ownerId, teamId, s"$objectPath$name.", objContent)

    val context = SchemaElement(orgId, ownerId, teamId, s"$objectPath$name", name, typeKeywords)
    foldedArrayObjects ++ foldedAnyOfObjects ++ foldedObjects :+ context
  }

  private def getAnyOfTypes(objContent: Map[String, AnyRef]) = {
    objContent.get(ANY_OF)
      .map(javaAnyOf => {
        javaAnyOf.asInstanceOf[util.List[AnyRef]].asScala
          .map(_.asMap)
          .flatMap(x => Set(x.get(TYPE), x.get(FORMAT))
            .flatten
            .map(_.toString))
      })
      .map(_.toSet)
      .getOrElse(Set())
  }

  implicit class ScalaSchemaMap(map: AnyRef) {
    def asMap: Map[String, AnyRef] = map.asInstanceOf[util.Map[String, AnyRef]].asScala.toMap
  }

}

case class SchemaDescription(dataSetId: Int,
                             orgId: Int,
                             ownerId: Int,
                             teamId: Option[Int],
                             fields: Seq[SchemaElement])

case class SchemaElement(orgId: Int,
                         ownerId: Int,
                         teamId: Option[Int],
                         path: String,
                         name: String,
                         typeKeywords: Set[String])

object SchemaDescriptionGenerator {
  val ANY_OF = "anyOf"
  val PROPERTIES = "properties"
  val TYPE = "type"
  val FORMAT = "format"
  val OBJECT = "object"
  val ARRAY = "array"
  val ITEMS = "items"
}