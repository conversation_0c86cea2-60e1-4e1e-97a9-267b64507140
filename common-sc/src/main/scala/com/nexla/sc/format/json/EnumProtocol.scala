package com.nexla.sc.format.json

import spray.json.{JsString, JsValue, RootJsonFormat, deserializationError}

object EnumProtocol {

  implicit def enumFormat[A <: Enumeration](implicit enum: A) = new RootJsonFormat[A#Value] {

    def read(value: JsValue): A#Value = value match {
      case JsString(s) => enum.withName(s)
      case x => deserializationError(s"Expected JsString, but got $x")
    }

    def write(obj: A#Value) = JsString(obj.toString)
  }
}