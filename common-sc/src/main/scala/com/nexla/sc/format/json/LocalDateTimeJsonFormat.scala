package com.nexla.sc.format.json

import java.time.LocalDateTime

import spray.json.{JsString, JsValue, JsonFormat, deserializationError}

trait LocalDateTimeJsonFormat {

  implicit object LocalDateTimeJsonFormat extends JsonFormat[LocalDateTime] {

    def read(value: JsValue): LocalDateTime = value match {
      case JsString(s) => LocalDateTime.parse(s)
      case x => deserializationError(s"Expected JsString datetime, but got $x")
    }

    def write(dt: LocalDateTime) = JsString(dt.toString)
  }

}
