package com.nexla.sc.format.json

import java.util.UUID

import spray.json.{JsString, JsValue, JsonFormat}

object UuidJsonFormat extends JsonFormat[UUID] {
  override def write(obj: UUID): JsValue = JsString(obj.toString)

  override def read(json: JsValue): UUID = json match {
    case JsString(string) => UUID.fromString(string)
    case other => throw new IllegalArgumentException(s"Unsupported UUID object: $other")
  }
}
