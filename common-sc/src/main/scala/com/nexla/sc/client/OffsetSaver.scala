package com.nexla.sc.client

import com.nexla.common.listing.ListingUpdateSetFileStatusTask
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.control.ListingFileStatus
import com.nexla.control.coordination.SetFileStatusCoordination
import com.nexla.sc.client.listing.FileStatuses.FileStatus
import com.nexla.sc.client.listing.{CoordinationAppClient, FileStatuses}
import one.util.streamex.StreamEx

import java.util.concurrent.ConcurrentLinkedQueue
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters.asScalaBufferConverter

case class SetFileStatusCoordinationDto(messageId: String,
                                        sourceId: Long,
                                        fileId: Long,
                                        fileStatus: FileStatus,
                                        lastMessageOffset: Option[Long],
                                        message: Option[String]) {
  def toSetFileStatusCoordination(): SetFileStatusCoordination = {
    new SetFileStatusCoordination(messageId,
      sourceId,
      fileId,
      ListingFileStatus.valueOf(fileStatus.toString),
      lastMessageOffset.map(Long.box).orNull,
      message.orNull,
      System.currentTimeMillis() + 1.hours.toMillis
    )
  }

  def toUpdateListingSetFileStatusTask(): ListingUpdateSetFileStatusTask = toSetFileStatusCoordination().toUpdateListingSetFileStatusTask
}

class OffsetSaver(coordination: CoordinationAppClient,
                  nexlaMessageProducer: NexlaMessageProducer)
                 (implicit ec: ExecutionContext){

  val buffer = new ConcurrentLinkedQueue[SetFileStatusCoordinationDto]()

  def saveOffset(offset: SetFileStatusCoordination): Unit = {

      nexlaMessageProducer.sendCoordinationEvent(offset)

      val status = FileStatuses.withName(offset.status.name())
      val coordinationDto = SetFileStatusCoordinationDto(
        offset.messageId, offset.sourceId, offset.fileId, status, Option(offset.lastMessageOffset), Option(offset.getMessage))

      buffer.add(coordinationDto)
  }

  def flush() = {

    val statuses = StreamEx.generate(() => buffer.poll())
      .takeWhile(x => x != null)
      .toList
      .asScala

    if (statuses.nonEmpty) {
      coordination.fileStatusesBatch(statuses)
    } else {
      Future.successful()
    }
  }

}