package com.nexla.sc.client.listing

import com.nexla.sc.format.json.{EnumProtocol, LocalDateTimeJsonFormat}
import spray.json.DefaultJsonProtocol._
import spray.json.RootJsonFormat

object ListingMarshalling extends LocalDateTimeJsonFormat {

  implicit val fileSourceTypeFormat = EnumProtocol.enumFormat(FileSourceTypes)
  implicit val fileStatusTypeFormat: RootJsonFormat[FileStatuses.Value] = EnumProtocol.enumFormat(FileStatuses)
  implicit val ListedFileFormat = jsonFormat11(ListedFile)
  implicit val minimalFile = jsonFormat2(MinimalListedFile)


  implicit val sinkOffsetFormat = jsonFormat3(SinkOffset)

}
