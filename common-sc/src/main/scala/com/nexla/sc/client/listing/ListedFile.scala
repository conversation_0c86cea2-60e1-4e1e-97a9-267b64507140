package com.nexla.sc.client.listing

import java.time.LocalDateTime

import com.nexla.sc.client.listing.FileSourceTypes.FileSourceType
import com.nexla.sc.client.listing.FileStatuses.FileStatus

case class ListedFile(id: Long,
                      fullPath: String,
                      size: Option[Long],
                      hash: Option[String],
                      lastModified: Option[Long],
                      linkToOriginal: Option[Long],
                      source: FileSourceType,
                      status: FileStatus,
                      lastMessageOffset: Option[Long],
                      metadata: Option[Map[String, Option[String]]],
                      createdAt: LocalDateTime)

case class MinimalListedFile(id: Long, fullPath: String)
