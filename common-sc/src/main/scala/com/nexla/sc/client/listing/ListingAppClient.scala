package com.nexla.sc.client.listing

import akka.actor.ActorSystem
import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import akka.http.scaladsl.model.StatusCodes._
import akka.http.scaladsl.model.{HttpResponse, RequestEntity, Uri}
import akka.http.scaladsl.model.Uri.Query
import akka.stream.Materializer
import com.nexla.common.NexlaConstants
import com.nexla.sc.client.SetFileStatusCoordinationDto
import com.nexla.sc.client.listing.FileSourceTypes.FileSourceType
import com.nexla.sc.client.listing.FileStatuses.FileStatus
import com.nexla.sc.config.NexlaCredsConf
import com.nexla.sc.util.{BaseHttpClient, NexlaBasicAuth, _}
import com.nexla.sc.util.StrictNexlaLogging
import spray.json.{DefaultJsonProtocol, enrichAny}
import akka.http.scaladsl.marshalling.Marshal

import java.io.File
import java.time.LocalDateTime
import scala.concurrent.{ExecutionContext, Future}

case class ListingResult(file: Option[ListedFile], inProgress: Boolean)

class ListingAppClient(listingAppUrl: String,
                       protected val nexlaCreds: NexlaCredsConf)
                      (implicit ec: ExecutionContext,
                       system: ActorSystem,
                       mat: Materializer)
  extends BaseHttpClient
    with NexlaBasicAuth
    with StrictNexlaLogging
    with WithLogging
    with DefaultJsonProtocol
    with SprayJsonSupport {

  import com.nexla.sc.client.listing.ListingMarshalling._

  def setFileStatus(sourceId: Int,
                    fileId: Long,
                    status: FileStatus,
                    lastMessageOffset: Option[Long],
                    message: Option[String]): Future[Unit] = {
    val params =
      lastMessageOffset.map("lastMessageOffset" -> _.toString).toSeq ++
        message.map("message" -> _)

    val uri = Uri(s"$listingAppUrl/files/$sourceId/file/$fileId/status/$status").withQuery(Query(params: _*))

    opse(s"setFileStatus(sourceId=$sourceId, fileId=$fileId, lastMessageOffset=$lastMessageOffset, $message)") { () =>
      for {
        response <- post(uri, Seq(basicAuthHeader, acceptJson))
        _ <- handle[String](uri.toString(), None, response, s"setFileStatus($sourceId, $fileId, $lastMessageOffset, $message)")
      } yield ()
    }
  }

  def heartBeat(sourceId: Int, fileId: Long): Future[Unit] = {
    val url = s"$listingAppUrl/files/$sourceId/file/$fileId/heartbeat"
    for {
      response <- post(
        url,
        Seq(basicAuthHeader, acceptJson))
      _ <- handle[String](url, None, response, s"heartBeat($sourceId, $fileId)")
    } yield {}
  }

  /**
   * By default, we don't pay attention to this header, but this method does - and extracts it to a separate flag.
   *
   * @param sourceId - where we're taking the file from
   * @return ListedFile and Boolean // or an Empty option, if there are no files to process.
   *         Boolean flag shows whether the listing lists something related to this source at the moment when the call was made or not.
   */
  def takeFile(sourceId: Int): Future[ListingResult] = {
    if (new File("/tmp/pauseListing").exists()) {
      logger.info("Listing is on pause, waiting")
      Future.successful(ListingResult(None, inProgress = true))
    } else {
      opse(s"takeFile(source-$sourceId)")(() => {
        val url = s"$listingAppUrl/files/$sourceId/take"
        for {
          response <- post(Uri(url), Seq(basicAuthHeader, acceptJson))
          result: Option[ListedFile] <- response.status match {
            case NoContent => Future.successful(None)
            case _ => handle[ListedFile](url, None, response, s"takeFile($sourceId)").map(Option(_))
          }

        } yield {
          ListingResult(result, listingIsInProgressFromHeader(response))
        }
      }).recoverWith { case e =>
        logger.info("Error during listing communication, considering Listing is in progress", e)
        Future.successful(ListingResult(None, inProgress = true))
      }
    }
  }

  def takeFiles(sourceId: Int, howMany: Int): Future[Option[List[ListedFile]]] = opse(s"takeFiles(source-$sourceId, howMany-$howMany)")(() => {
    val url = s"$listingAppUrl/files/$sourceId/takeMany/$howMany"
    for {
      response <- post(Uri(url), Seq(basicAuthHeader, acceptJson))
      result <- response.status match {
        case NoContent => Future.successful(None)
        case _ => handle[List[ListedFile]](url, None, response, s"takeFiles($sourceId, $howMany)").map(Some(_))
      }
    } yield result
  })

  def listFiles(sourceId: Int,
                status: Option[List[FileStatus]] = Option.empty,
                source: Option[List[FileSourceType]] = Option.empty,
                createdAtFrom: Option[LocalDateTime] = Option.empty,
                createdAtTo: Option[LocalDateTime] = Option.empty): Future[Option[List[ListedFile]]] =
    opse(s"listFiles(source-$sourceId)")(() => {
      val url = s"$listingAppUrl/files/$sourceId"
      val params: Seq[(String, String)] = {
        status.fold(Seq.empty[(String, String)]) { _.map("status" -> _.toString) } ++
        source.fold(Seq.empty[(String, String)]) { _.map("source" -> _.toString) } ++
        createdAtFrom.map("createdAtFrom" -> _.toString).toSeq ++
        createdAtTo.map("createdAtTo" -> _.toString).toSeq
      }

      for {
        response <- get(Uri(url).withQuery(Query(params: _*)), Seq(basicAuthHeader, acceptJson))
        result <- response.status match {
          case NoContent => Future.successful(None)
          case _ => handle[List[ListedFile]](url, None, response,
            s"listFiles($sourceId, $status, $source, $createdAtFrom, $createdAtTo)").map(Some(_))
        }
      } yield result
  })

  def listingIsInProgressFromHeader(response: HttpResponse): Boolean = {
    val header = response.getHeader(NexlaConstants.HEADER_LISTING_IN_PROGRESS)
    if (header.isEmpty) {
      false
    } else {
      val headerValue = header.get().value()
      if (headerValue.equalsIgnoreCase("true")) {
        true
      } else {
        false
      }
    }
  }

  def fileStatusesBatch(offsets: Iterable[SetFileStatusCoordinationDto]): Future[Unit] = for {
    entity <- Marshal(offsets).to[RequestEntity]
    url = s"$listingAppUrl/files/status/batch"
    response <- post(
      Uri(url),
      entity = entity,
      headers = Seq(basicAuthHeader, acceptJson))
    _ <- handle[String](url, Some(offsets.toJson.compactPrint), response, s"updateFastOffsets()")
  } yield {}

  def batchHeartBeat(sourceId: Int, fileIds: Set[Long]): Future[Unit] = for {
    entity <- Marshal(fileIds).to[RequestEntity]
    url = s"$listingAppUrl/source/$sourceId/batchHeartbeat"

    response <- post(
      uri = Uri(url),
      headers = Seq(basicAuthHeader, acceptJson),
      entity = entity
    )
    _ <- handle[String](url, None, response, s"batchHeartbeat($sourceId, $fileIds)")
  } yield {}

}
