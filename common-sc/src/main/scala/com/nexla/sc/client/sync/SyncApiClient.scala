package com.nexla.sc.client.sync

import akka.actor.ActorSystem
import akka.http.scaladsl.model.MediaTypes.`application/json`
import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.model.{ContentType, HttpEntity, Uri}
import akka.stream.Materializer
import com.nexla.sc.config.NexlaCredsConf
import com.nexla.sc.util.{BaseHttpClient, NexlaBasicAuth, WithLogging}

import scala.concurrent.{ExecutionContext, Future}

class SyncApiClient(protected val nexlaCreds: NexlaCredsConf)
                   (implicit val ex: ExecutionContext,
                    val system: ActorSystem,
                    val m: Materializer)
  extends BaseHttpClient
    with NexlaBasicAuth
    with WithLogging {
  def sync(url: String, request: String, optAuthHead: Option[String]): Future[String] = opse(s"sync($url, $request")(() => {
    val authHeader = optAuthHead.fold(basicAuthHeader)(auth => RawHeader("Authorization", s"$auth"))

    for {
      response <- post(Uri(url), Seq(authHeader, acceptJson), HttpEntity(ContentType(`application/json`), request))
      result <- handle[String](url, Some(request), response, s"nodeGet($request)")
    } yield result
  })
}