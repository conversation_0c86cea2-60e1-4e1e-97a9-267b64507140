package com.nexla.sc.client.metrics_http;

import akka.actor.ActorSystem
import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import akka.http.scaladsl.marshalling.Marshal
import akka.http.scaladsl.model.Uri.Query
import akka.http.scaladsl.model.{RequestEntity, Uri}
import akka.stream.Materializer
import com.nexla.admin.client.AdminApiClient
import com.nexla.common.NexlaConstants._
import com.nexla.common.{Resource, ResourceType}
import com.nexla.sc.config.NexlaCredsConf
import com.nexla.sc.util.{BaseHttpClient, NexlaBasicAuth, StrictNexlaLogging, WithLogging}
import com.nexla.sc.util.StrictNexlaLogging
import spray.json.DefaultJsonProtocol

import scala.collection.JavaConverters._
import scala.concurrent.{ExecutionContext, Future}

case class MetricsRequestEntity (resourceTypeIdsMap: Map[String, Seq[Int]])

class MetricsHttpAppClient(metricsAppUrl: String,
                           protected val nexlaCreds: NexlaCredsConf,
                           adminApiClient: AdminApiClient)
                          (implicit ec: ExecutionContext,
                          system: ActorSystem,
                          mat: Materializer)
  extends BaseHttpClient
    with NexlaBasicAuth
    with StrictNexlaLogging
    with DefaultJsonProtocol
    with SprayJsonSupport
    with WithLogging
    with MetricsHttpMarshalling {

  /**
   * Retrieves stats for Sink upstream resources in the pipeline (Source and Datasets).
   * Stats include following metrics: records, size, errors.
   *
   * @param resourceType - only Sink resource type is accepted. If any other resource type than Sink is
   *                     passed, empty Option value is returned.
   * @return {@link MetricsHttpResponse}
   */
  def getSinkToDataflowMetrics(resourceType: String,
                               runId: Long,
                               resourceId: Integer): Future[Option[MetricsHttpResponse]] = {
    if (resourceType == SINK) {
      val queryParams = Map("runId" -> runId.toString).toSeq
      val uri = Uri(s"$metricsAppUrl/data_flows/metrics").withQuery(Query(queryParams: _*))

      val requestEntityContent: MetricsRequestEntity = MetricsRequestEntity(getPipelineResourcesFromSink(resourceId))
      for {
        entity <- Marshal[MetricsRequestEntity](requestEntityContent).to[RequestEntity]
        response <- {
          put(
            uri = uri,
            headers = Seq(basicAuthHeader, acceptJson),
            entity = entity)
        }
        result <- handle[MetricsHttpResponse](uri.toString(), None, response, s"getDataflowMetrics($resourceType, $runId, $resourceId)")
      } yield Some(result)
    } else Future {
      Option.empty[MetricsHttpResponse]
    }
  }.recoverWith { case e =>
    logger.error("", e)
    Future.successful(None)
  }

  def getPipelineResourcesFromSink(sinkId: Int): Map[String, Seq[Int]] = {
    val pipeline = adminApiClient.getFlowByResource(new Resource(sinkId, ResourceType.SINK)).get()
    Map(
      DATA_SOURCES -> Seq(pipeline.dataSources.get(0).getId),
      DATA_SETS -> pipeline.dataSets.asScala.map(_.id),
      DATA_SINKS -> Seq(sinkId)
    )
  }

}
