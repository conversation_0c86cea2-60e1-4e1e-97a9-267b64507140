package com.nexla.sc.client

import com.nexla.common.ListingResourceType._
import com.nexla.common.NexlaFile
import spray.json.DefaultJsonProtocol._

import java.lang
import scala.collection.JavaConverters._
import scala.compat.java8.OptionConverters._
import scala.language.implicitConversions

package object listing {

  import ListingMarshalling.fileStatusTypeFormat
  implicit val FastConnectorOffsetsRequestFormat = jsonFormat6(SetFileStatusCoordinationDto)

  implicit def optToLongJava(a: Option[Long]): Option[lang.Long] = a.map(Long.box)

  implicit class RichListedFile(r: ListedFile) {

    def toNexlaFile: NexlaFile = {
      val file = new NexlaFile(
        r.fullPath, optToLongJava(r.size).orNull, null, r.hash.orNull,
        null, optToLongJava(r.lastModified).orNull, FILE)

      val meta = r.metadata
        .map(_.mapValues(_.get.asInstanceOf[AnyRef]))
        .map(_.asJava)
        .asJava

      file.setMetadata(meta)
      file.setId(r.id)

      file
    }
  }

}
