package com.nexla.sc.client.job_scheduler

import com.nexla.sc.client.metrics_http.ResourceMetric

case class TaskResponse(idsToStart: List[PipelineResponse],
                        idsToStop: List[Int])

case class PipelineResponse(sinkId: Int)

case class PipelineRunStateResponse(resourceType: String,
                                    resourceId: Int,
                                    runId: Long,
                                    status: String,
                                    hasData: Boolean,
                                    resourceMetrics: Option[Map[String, List[ResourceMetric]]])
