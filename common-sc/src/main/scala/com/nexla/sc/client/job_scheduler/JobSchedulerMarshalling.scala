package com.nexla.sc.client.job_scheduler

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import com.bazaarvoice.jolt.JsonUtils
import com.nexla.common.{ResourceType, StreamUtils}
import com.nexla.connector.config.PipelineTaskType
import com.nexla.control.message.ControlMessage
import com.nexla.sc.api.ReprocessStreamRequest
import com.nexla.sc.client.job_scheduler.TaskRequest.TaskId
import com.nexla.sc.client.metrics_http.MetricsHttpMarshalling
import com.nexla.sc.format.json.{EnumProtocol, LocalDateTimeJsonFormat, UuidJsonFormat}
import spray.json.{DefaultJsonProtocol, JsNumber, JsString, JsValue, RootJsonFormat, deserializationError, enrichAny, serializationError}

trait JobSchedulerMarshalling
  extends LocalDateTimeJsonFormat
    with MetricsHttpMarshalling {

  _: DefaultJsonProtocol with SprayJsonSupport =>

  implicit val NodeTaskStateFormat = EnumProtocol.enumFormat(PipelineTaskStateEnum)
  implicit val uuidJsonFormat = UuidJsonFormat

  implicit object ActionCommandFormat extends RootJsonFormat[ActionCommand] {
    def write(obj: ActionCommand) = obj match {
      case x: Activate => x.toJson
      case y: Pause => y.toJson
      case unknown @ _ => serializationError(s"Marshalling issue with $unknown")
    }

    def read(value: JsValue) = {
      value.asJsObject.getFields("action", "resourceId", "resourceType") match {
        case Seq(JsString(a), JsNumber(b), JsString(c))
          if a == ActionCommand.activateAction => Activate(b.intValue, ResourceType.fromString(c))
        case Seq(JsString(a), JsNumber(b), JsString(c))
          if a == ActionCommand.pauseAction => Pause(b.intValue, ResourceType.fromString(c))
        case unknown @ _ => deserializationError(s"Unmarshalling issue with ${unknown} ")
      }
    }
  }

  implicit object ResourceTypeJsonFormat extends RootJsonFormat[ResourceType] {
    def write(obj: ResourceType) = obj.toString.toJson

    def read(value: JsValue) = {
      value match {
        case JsString(c) => ResourceType.fromString(c)
        case unknown @ _ => deserializationError(s"Unmarshalling issue with $unknown ")
      }
    }
  }

  implicit object NodeTaskTypeFormat extends RootJsonFormat[PipelineTaskType] {
    def write(obj: PipelineTaskType) = obj.toString.toJson

    def read(value: JsValue) = {
      value match {
        case JsString(c) => PipelineTaskType.fromString(c)
        case unknown @ _ => deserializationError(s"Unmarshalling issue with $unknown ")
      }
    }
  }

  implicit object ControlMessageFormat extends RootJsonFormat[ControlMessage] {
    def write(obj: ControlMessage) = StreamUtils.jsonUtil().toJsonString(obj).toJson

    def read(value: JsValue) = {
      value match {
        case JsString(json) => StreamUtils.jsonUtil.stringToType(json, classOf[ControlMessage])
        case unknown @ _ => deserializationError(s"Unmarshalling issue with $unknown ")
      }
    }
  }

  implicit val ActivateFormat: RootJsonFormat[Activate] = jsonFormat2(Activate)
  implicit val PauseFormat: RootJsonFormat[Pause] = jsonFormat2(Pause)

  implicit val NodeTaskResponseMapFormat = mapFormat[TaskId, Seq[ActionCommand]]

  implicit val PipelineTaskMetaFormat =  jsonFormat10(PipelineTaskMeta)
  implicit val NodeTaskResponseTaskFormat = jsonFormat5(NodeTaskResponseElem)
  implicit val ReprocessStreamRequestFormat = jsonFormat1(ReprocessStreamRequest)
  implicit val NodeTaskResponseFormat = jsonFormat8(NodeTaskResponse)

  implicit val PipelineResponseFormat = jsonFormat1(PipelineResponse)
  implicit val TaskResponseFormat = jsonFormat2(TaskResponse)

  implicit val pipelineRunStateDtoFormat = jsonFormat6(PipelineRunStateResponse)

  implicit val NodeTaskStatusFormat =  jsonFormat11(NodeTaskStatus)
  implicit val RunIdEventTypeFormat = EnumProtocol.enumFormat(RunIdEventTypes)
  implicit val NodeStatusRequestFormat =  jsonFormat9(PipelineNodeDto)
  implicit val NodeHeartbeatRequestFormat = jsonFormat1(NodeIdWrapped)
  implicit val PipelineTaskInfoFormat = jsonFormat1(PipelineTaskInfo)
}
