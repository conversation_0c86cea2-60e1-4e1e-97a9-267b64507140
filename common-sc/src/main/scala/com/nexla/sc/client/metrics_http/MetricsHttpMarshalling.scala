package com.nexla.sc.client.metrics_http

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import com.bazaarvoice.jolt.JsonUtils
import com.bazaarvoice.jolt.JsonUtils.stringToType
import com.nexla.common.metrics.MetricWithErrors
import spray.json.{DefaultJsonProtocol, JsNumber, JsObject, JsString, JsValue, RootJsonFormat, deserializationError, enrichAny}

trait MetricsHttpMarshalling extends SprayJsonSupport with DefaultJsonProtocol {

  implicit object MetricWithErrorsFormat extends RootJsonFormat[MetricWithErrors] {
    def write(obj: MetricWithErrors) = {
      JsObject(
        "errors" -> JsNumber(obj.getErrors),
        "records" -> JsNumber(obj.getRecords),
        "size" -> JsNumber(obj.getSize)
      )
    }

    def read(value: JsValue) = {
      value.asJsObject.getFields("errors", "records", "size") match {
        case Seq(JsN<PERSON>ber(errors), Js<PERSON><PERSON><PERSON>(records), Js<PERSON><PERSON>ber(size)) =>
          new MetricWithErrors(records.toLong, size.toLong, errors.toLong)
      }
    }
  }

  implicit val resourceMetricDtoFormat = jsonFormat2(ResourceMetric)
  implicit val metaDtoFormat = jsonFormat3(Meta)
  implicit val paginatedResultDtoFormat = jsonFormat2(ExternalResult)

  implicit object MetricsHttpResponseDtoFormat extends RootJsonFormat[MetricsHttpResponse] {
    def write(obj: MetricsHttpResponse) = JsonUtils.toJsonString(obj).toJson

    def read(value: JsValue) = {
      MetricsHttpResponse(value.convertTo[ExternalResult])
    }
  }

  implicit object MetricsRequestEntityFormat extends RootJsonFormat[MetricsRequestEntity] {
    def write(obj: MetricsRequestEntity) = {
      obj.resourceTypeIdsMap.toJson
    }

    def read(value: JsValue) = {
      value match {
        case JsString(json) => stringToType(json, classOf[MetricsRequestEntity])
        case unknown @ _ => deserializationError(s"Unmarshalling issue with $unknown ")
      }
    }
  }
}
