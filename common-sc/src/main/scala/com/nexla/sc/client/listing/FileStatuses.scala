package com.nexla.sc.client.listing

object FileStatuses extends Enumeration {
  type FileStatus = Value
  val New = Value("NEW")

  val Started = Value("STARTED")

  val Downloading = Value("DOWNLOADING")
  val Parsing = Value("PARSING")
  val PostProcessing = Value("POST_PROCESSING")

  val Stopped = Value("STOPPED")
  val Done = Value("DONE")

  val inProgressStatuses: Set[FileStatus] = Set(Started, Downloading, Parsing, PostProcessing)

  implicit class FileStatusOps(status: FileStatus) {
    def isInProgress: Boolean = inProgressStatuses.contains(status)
  }
}