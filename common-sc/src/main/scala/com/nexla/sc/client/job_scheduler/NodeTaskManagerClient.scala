package com.nexla.sc.client.job_scheduler

import akka.actor.ActorSystem
import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import akka.http.scaladsl.marshalling.Marshal
import akka.http.scaladsl.model.{RequestEntity, Uri}
import akka.stream.Materializer
import com.nexla.sc.api.RequestDtoFormat
import com.nexla.sc.config.NexlaCredsConf
import com.nexla.sc.util.{BaseHttpClient, NexlaBasicAuth, StrictNexlaLogging, WithLogging}
import com.nexla.sc.util.StrictNexlaLogging
import spray.json.{DefaultJsonProtocol, _}

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}

class NodeTaskManagerClient(nodeTaskManagerUrl: String,
                            protected val nexlaCreds: NexlaCredsConf)
                           (implicit ec: ExecutionContext,
                            system: ActorSystem,
                            mat: Materializer)
  extends BaseHttpClient
    with NexlaBasicAuth
    with StrictNexlaLogging
    with DefaultJsonProtocol
    with SprayJsonSupport
    with RequestDtoFormat
    with JobSchedulerMarshalling
    with WithLogging {

  def nodeTasksReceive(request: PipelineNodeDto): Future[NodeTaskResponse] = op(s"pipelineTasksReceive($request)") { () =>
    for {
      entity <- Marshal(request).to[RequestEntity]
      url = s"$nodeTaskManagerUrl/node/receive"
      response <- postWithTimeout(
        uri = Uri(url),
        timeout = 5.minutes,
        headers = Seq(basicAuthHeader, acceptJson),
        entity = entity
      )
      response <- handle[NodeTaskResponse](url, Some(request.toJson.compactPrint), response, s"pipelineTasksReceive($request)")
    } yield response
  }

  def nodeTasksNotify(request: PipelineNodeDto): Future[Unit] = op(s"pipelineTasksNotify($request)") { () =>
    for {
      entity <- Marshal(request).to[RequestEntity]
      url = s"$nodeTaskManagerUrl/node/notify"
      response <- postWithTimeout(
        uri = Uri(url),
        timeout = 1.minutes,
        headers = Seq(basicAuthHeader, acceptJson),
        entity = entity
      )
      _ <- handle[Any](url, Some(request.toJson.compactPrint), response, s"pipelineTasksNotify($request)")
    } yield ()
  } recoverWith { case e =>
    logger.error("Error during nodeTasksNotify", e)
    Future.successful(())
  }

  def nodeGet(request: NodeIdWrapped): Future[PipelineNodeDto] = op(s"nodeGet($request)") { () =>
    for {
      entity <- Marshal(request).to[RequestEntity]
      url = s"$nodeTaskManagerUrl/node/get"
      response <- postWithTimeout(
        uri = Uri(url),
        timeout = 5.minutes,
        headers = Seq(basicAuthHeader, acceptJson),
        entity = entity
      )
      response <- handle[PipelineNodeDto](url, Some(request.toJson.compactPrint), response, s"nodeGet($request)")
    } yield response
  }

  def taskInfo(taskId: String): Future[PipelineTaskInfo] = op(s"pipeline/task/info/$taskId") { () =>
    val url = s"$nodeTaskManagerUrl/pipeline/task/info/$taskId"
    for {
      response <- get(uri = Uri(url), headers = Seq(basicAuthHeader, acceptJson))
      response <- handle[PipelineTaskInfo](url, None, response, s"pipeline/task/info/$taskId")
    } yield response
  }

  def nodeTasksHeartbeat(request: NodeIdWrapped): Future[Unit] = {
    for {
      entity <- Marshal(request).to[RequestEntity]
      url = s"$nodeTaskManagerUrl/node/heartbeat"
      response <- postWithTimeout(
        uri = Uri(url),
        timeout = 5.minutes,
        headers = Seq(basicAuthHeader, acceptJson),
        entity = entity
      )
      _ <- handle[Any](url, Some(request.toJson.compactPrint), response, s"nodeTasksHeartbeat($request)")
    } yield ()
  } recoverWith { case e =>
    logger.error("Error during task processing", e)
    Future.successful(())
  }

}
