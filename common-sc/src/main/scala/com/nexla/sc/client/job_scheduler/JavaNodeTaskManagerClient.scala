package com.nexla.sc.client.job_scheduler

import com.bazaarvoice.jolt.JsonUtils
import com.nexla.sc.api.RequestDtoFormat
import com.nexla.sc.config.NexlaCredsConf
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import org.springframework.http.{HttpEntity, HttpHeaders, HttpMethod, MediaType}
import org.springframework.web.client.RestTemplate
import spray.json.{DefaultJsonProtocol, _}

import java.util.{Base64, Optional}
import java.util.Collections.singletonList
import scala.collection.JavaConverters._
import scala.compat.java8.OptionConverters._

class JavaNodeTaskManagerClient(restTemplate: RestTemplate,
                                nodeTaskManagerUrl: String,
                                nexlaCreds: NexlaCredsConf)
  extends StrictNexlaLogging
    with <PERSON>faultJsonProtocol
    with RequestDtoFormat
    with JobSchedulerMarshalling
    with WithLogging {

  val authorizationHeader: HttpHeaders = new HttpHeaders()

  val authString = nexlaCreds.username + ":" + nexlaCreds.password
  val basicAuth = "Basic " + new String(Base64.getEncoder.encode(authString.getBytes))
  authorizationHeader.add("Authorization", basicAuth)
  authorizationHeader.setContentType(MediaType.APPLICATION_JSON)
  authorizationHeader.setAccept(singletonList(MediaType.APPLICATION_JSON))

  def nodeTasksExists(nodeId: String): Optional[PipelineNodeDto] = {
    try {
      val url = s"$nodeTaskManagerUrl/node/get"
      val existsCheckJson = JsonUtils.toJsonString(Map("nodeId" -> nodeId).asJava)
      val request = new HttpEntity[String](existsCheckJson, authorizationHeader)
      val result = restTemplate.exchange(url, HttpMethod.POST, request, classOf[String])
      Some(result.getBody.parseJson.convertTo[PipelineNodeDto])
    } catch {
      case _: Exception =>
        None
    }
  }.asJava

  def nodeTasksNotify(dto: PipelineNodeDto): Unit = {
    val url = s"$nodeTaskManagerUrl/node/notify"
    val request = new HttpEntity[String](dto.toJson.compactPrint, authorizationHeader)
    restTemplate.exchange(url, HttpMethod.POST, request, classOf[String])
  }

  def nodeTasksReceive(dto: PipelineNodeDto): NodeTaskResponse = {
    val url = s"$nodeTaskManagerUrl/node/receive"

    val request = new HttpEntity[String](dto.toJson.compactPrint, authorizationHeader)
    val result = restTemplate.exchange(url, HttpMethod.POST, request, classOf[String])
    result.getBody.parseJson.convertTo[NodeTaskResponse]
  }

  def nodeTasksHeartbeat(request: NodeIdWrapped) = {
    val url = s"$nodeTaskManagerUrl/node/heartbeat"
    val req = new HttpEntity[String](request.toJson.compactPrint, authorizationHeader)
    restTemplate.exchange(url, HttpMethod.POST, req, classOf[String])
  }

  def nodeTasksDecommission(request: NodeDecommissionRequest) = {
    import NodeDecommissionResponse._
    val url = s"$nodeTaskManagerUrl/node/decommission"
    val req = new HttpEntity[String](request.nodes.toJson.compactPrint, authorizationHeader)
    val result = restTemplate.exchange(url, HttpMethod.POST, req, classOf[String])
    result.getBody.parseJson.convertTo[NodeDecommissionResponse]
  }

  def getNodeTasksDecommission() = {
    import NodeDecommissionResponse._
    val url = s"$nodeTaskManagerUrl/node/decommission"
    val req = new HttpEntity[String]("", authorizationHeader)
    val result = restTemplate.exchange(url, HttpMethod.GET, req, classOf[String])
    result.getBody.parseJson.convertTo[NodeDecommissionResponse]
  }

}
