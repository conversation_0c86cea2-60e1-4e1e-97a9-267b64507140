package com.nexla.sc.client.listing

import java.time.LocalDateTime

import com.nexla.sc.client.listing.FileSourceTypes.FileSourceType
import com.nexla.sc.client.listing.FileStatuses.FileStatus

// DO NOT RENAME FIELDS, THEY ARE USED IN API
case class ListingParams(status: Iterable[FileStatus] = List.empty,
                         source: Iterable[FileSourceType] = List.empty,
                         createdAtFrom: Option[LocalDateTime] = None,
                         createdAtTo: Option[LocalDateTime] = None,
                         original: Option[Boolean] = None)