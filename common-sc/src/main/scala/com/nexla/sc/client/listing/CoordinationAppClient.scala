package com.nexla.sc.client.listing

import akka.actor.ActorSystem
import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import akka.http.scaladsl.marshalling.Marshal
import akka.http.scaladsl.model.StatusCodes._
import akka.http.scaladsl.model.{RequestEntity, Uri}
import akka.stream.Materializer
import com.nexla.sc.client.SetFileStatusCoordinationDto
import com.nexla.sc.config.NexlaCredsConf
import com.nexla.sc.util.{BaseHttpClient, NexlaBasicAuth, _}
import com.nexla.sc.util.StrictNexlaLogging
import spray.json.{DefaultJsonProtocol, _}

import scala.concurrent.{ExecutionContext, Future}

class CoordinationAppClient(coordinationAppUrl: String,
                            protected val nexlaCreds: NexlaCredsConf)
                           (implicit ec: ExecutionContext,
                            system: ActorSystem,
                            mat: Materializer)
  extends BaseHttpClient
    with NexlaBasicAuth
    with StrictNexlaLogging
    with WithLogging
    with DefaultJsonProtocol
    with SprayJsonSupport {

  def updateFastOffsets(sourceId: Int, sinkId: Int, offsets: Map[String, String]): Future[Unit] = for {
    entity <- Marshal(offsets).to[RequestEntity]
    url = s"$coordinationAppUrl/fast/source/$sourceId/sink/$sinkId/offset"
    response <- post(
      Uri(url),
      entity = entity,
      headers = Seq(basicAuthHeader, acceptJson))
    _ <- handle[String](url, Some(offsets.toJson.compactPrint), response, s"updateFastOffsets($sourceId, $sinkId, $offsets)")
  } yield {}

  def getFastOffsets(sourceId: Int, sinkId: Int): Future[Option[Map[String, String]]] = {
    val url = s"$coordinationAppUrl/fast/source/$sourceId/sink/$sinkId/offset"
    for {
      response <- get(url, Seq(basicAuthHeader, acceptJson))
      result <- response.status match {
        case NotFound => Future.successful(None)
        case _ => handle[Map[String, String]](url, None, response, s"getFastOffsets($sinkId)").map(Some(_))
      }
    } yield result
  }

  @Deprecated
  def fileStatusesBatch(offsets: Iterable[SetFileStatusCoordinationDto]): Future[Unit] = for {
    entity <- Marshal(offsets).to[RequestEntity]
    url = s"$coordinationAppUrl/files/status/batch"
    response <- post(
      Uri(url),
      entity = entity,
      headers = Seq(basicAuthHeader, acceptJson))
    _ <- handle[String](url, Some(offsets.toJson.compactPrint), response, s"updateFastOffsets()")
  } yield {}

  @Deprecated
  def batchHeartBeat(sourceId: Int, fileIds: Set[Long]): Future[Unit] = for {
    entity <- Marshal(fileIds).to[RequestEntity]
    url = s"$coordinationAppUrl/source/$sourceId/batchHeartbeat"

    response <- post(
      uri = Uri(url),
      headers = Seq(basicAuthHeader, acceptJson),
      entity = entity
    )
    _ <- handle[String](url, None, response, s"batchHeartbeat($sourceId, $fileIds)")
  } yield {}

}