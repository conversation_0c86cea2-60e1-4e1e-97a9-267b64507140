package com.nexla.sc.rules.service

import akka.actor.ActorSystem
import com.google.common.cache.{CacheBuilder, CacheLoader}
import com.google.common.util.concurrent.{ListenableFuture, ListenableFutureTask}
import com.mchange.v2.c3p0.ComboPooledDataSource
import com.nexla.admin.client.{AdminApiClient, DataSource}
import com.nexla.admin.client.{AdminApiClient, DataSink, DataSource}
import com.nexla.db.postgres.tables.records.CanaryRulesRecord
import com.nexla.sc.rules.dao.RuleDao
import org.slf4j.LoggerFactory

import java.time
import java.time.LocalDateTime
import scala.compat.java8.OptionConverters.RichOptionalGeneric
import scala.concurrent.ExecutionContext
import scala.util.Random

class RuleService(adminApiClient: AdminApiClient, db: ComboPooledDataSource, appName: String)
                 (implicit val ex: ExecutionContext,
                  val system: ActorSystem) {
  private val logger = LoggerFactory.getLogger(classOf[RuleService])

  private val ruleDao = new RuleDao(db.getConnection)

  private val rules = CacheBuilder.newBuilder.refreshAfterWrite(getDuration).build[String, Set[CanaryRule]](new CacheLoader[String, Set[CanaryRule]] {
    override def load(key: String): Set[CanaryRule] = getAllRules

    override def reload(key: String, oldValue: Set[CanaryRule]): ListenableFuture[Set[CanaryRule]] = {
      val task = ListenableFutureTask.create[Set[CanaryRule]](() => getAllRules)
      ex.execute(task)

      task
    }
  })

  protected def getDuration: time.Duration = time.Duration.ofMinutes(1)

  protected def getAllRules: Set[CanaryRule] = ruleDao.getAllRules(appName).map(r => new CanaryRule(r)).filter(r => !r.isDisabled)

  def getRule(dataSetId: Int): Option[CanaryRule] =
    try {
      adminApiClient.getDataSet(dataSetId).asScala.flatMap { ds => getRule(ds.getOrg.getId, ds.getOriginNodeId) }
    } catch {
      case e: Exception => logger.error(s"Error fetching canary rule for data set $dataSetId: $e")
        None
    }

  def getRule(dataSource: DataSource): Option[CanaryRule] = {
    try {
      getRule(dataSource.getOrg.getId, dataSource.getOriginNodeId)
    } catch {
      case e: Exception => logger.error(s"Error fetching canary rule for data source $dataSource: $e")
        None
    }
  }

  def getRule(dataSink: DataSink): Option[CanaryRule] = {
    try {
      getRule(dataSink.getOrg.getId, dataSink.getOriginNodeId)
    } catch {
      case e: Exception => logger.error(s"Error fetching canary rule for data sink $dataSink: $e")
        None
    }
  }

  def getRule(orgId: Int, flowId: Int): Option[CanaryRule] = {
    val curTime = LocalDateTime.now()

    rules.get(appName)
      .filter(r => r.orgId == null || r.orgId == orgId)
      .filter(r => r.flowId == null || r.flowId == flowId)
      .filter(r => r.startTime == null || r.startTime.isBefore(curTime))
      .find(r => r.endTime == null || r.endTime.isAfter(curTime))
  }

  def excludeRule(canaryRule: CanaryRule): Int = ruleDao.excludeRule(canaryRule.id)
}

case class CanaryRule(id: Long,
                      application: String,
                      percentage: Integer,
                      orgId: Integer,
                      flowId: Integer,
                      canaryValue: String,
                      ruleJson: String,
                      startTime: LocalDateTime,
                      endTime: LocalDateTime,
                      isDisabled: Boolean) {

  private val random = new Random()

  def this(r: CanaryRulesRecord) = this(r.getId, r.getApplication, r.getPercentage, r.getOrgId, r.getFlowId, r.getCanaryValue, r.getRuleJson, r.getStartTime, r.getEndTime, r.getDisabled)

  def shouldBeForward: Boolean = percentage == null || percentage / 100.0 >= random.nextDouble()
}