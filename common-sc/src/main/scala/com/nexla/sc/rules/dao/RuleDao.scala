package com.nexla.sc.rules.dao

import com.nexla.db.postgres.tables.CanaryRules._
import com.nexla.db.postgres.tables.records.CanaryRulesRecord
import com.nexla.sc.util.{ConnectionFn, WithJooq}

import scala.collection.JavaConverters._

class RuleDao(val getConnectionFn: ConnectionFn)
  extends WithJooq {
  def getAllRules(appName: String): Set[CanaryRulesRecord] = {
    withJooqReadOnly(jooq => jooq.selectFrom(CANARY_RULES)
      .where(CANARY_RULES.APPLICATION.eq(appName))
      .fetch()).asScala.toSet
  }

  def excludeRule(ruleId: Long) = {
    withJooqCommit(jooq => jooq.update(CANARY_RULES)
      .set(CANARY_RULES.DISABLED, Boolean.box(true))
      .where(CANARY_RULES.ID.equal(ruleId))
      .execute()
    )
  }
}
