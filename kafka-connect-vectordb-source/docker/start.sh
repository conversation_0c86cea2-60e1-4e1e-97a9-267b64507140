#!/bin/bash

__delete_topic() {
  # $1 - topic name
  local TOPIC_NAME=$1
  echo "WARN Detected connect topic $TOPIC_NAME with not compact strategy: $TOPIC_DESCRIPTION"
  echo "WARN Update compact policy for $TOPIC_NAME"
  kafka-configs --bootstrap-server "$BOOTSTRAP_SERVERS" --topic "$TOPIC_NAME" --alter --add-config cleanup.policy=compact || return
}

__check_topic_compact() {
  # $1 - topic name
  local TOPIC_NAME=$1

  [[ -n $TOPIC_NAME ]] || echo "ERROR: Empty connect topic name"
  [[ -n $TOPIC_NAME ]] || return

  local COMPACT_POLICY_WORD="cleanup.policy=compact"
  local DOES_NOT_EXIST="does not exist as expected"
  local TOPIC_DESCRIPTION="$(kafka-topics --bootstrap-server "$BOOTSTRAP_SERVERS" --topic "$TOPIC_NAME" --describe | head -1)"

  [[ -n $TOPIC_DESCRIPTION ]] || echo "ERROR: Topic description was not retrieved from Kafka"
  [[ -n $TOPIC_DESCRIPTION ]] || return

  [[ $TOPIC_DESCRIPTION == *"$COMPACT_POLICY_WORD"* || $TOPIC_DESCRIPTION == *"$DOES_NOT_EXIST"* ]] || __delete_topic "$TOPIC_NAME" || return
}

__check_connect_topics() {
  __check_topic_compact "$CONNECT_CONFIG_STORAGE_TOPIC"
  __check_topic_compact "$CONNECT_OFFSET_STORAGE_TOPIC"
  __check_topic_compact "$CONNECT_STATUS_STORAGE_TOPIC"
  echo "Check connect topics SUCCESS"
}

__kill_main_docker_process () {
  while true; do
    echo "Killing the main docker container process PID 1"
    kill 1
    sleep 1
  done
}

__startAgent() {
  java -jar /app/agent.jar || __kill_main_docker_process
}

[[ $SOURCE_CONNECT_TOPICS_HEALING_ENABLED == true ]] && __check_connect_topics || return

[[ $SOURCE_AGENT_ENABLED == true ]] && __startAgent &
exec /etc/confluent/docker/run