package com.nexla.connector.vectordb.source;

import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.datetime.DateTimeUtils;
import com.nexla.common.exception.ProbeRetriableException;
import com.nexla.common.tracker.SourceItem;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.BaseSourceTask;
import com.nexla.connect.common.CollectRecordsResult;
import com.nexla.connect.common.SourceRecordCreator;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.ssh.tunnel.SshTunnelSupport;
import com.nexla.connector.config.vectordb.VectorSourceConnectorConfig;
import com.nexla.connector.properties.RestConfigAccessor;
import com.nexla.probe.pinecone.PineconeService;
import com.nexla.probe.vectordb.VectorDbConnectorService;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.errors.ConnectException;
import org.apache.kafka.connect.source.SourceRecord;
import org.joda.time.DateTime;
import scala.Function2;
import scala.Option;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.ConnectionType.PINECONE;
import static com.nexla.common.MetricUtils.calcBytes;
import static com.nexla.common.NexlaConstants.VERSION;
import static com.nexla.common.ResourceType.SOURCE;
import static java.util.Collections.emptyMap;
import static java.util.Collections.singletonMap;
import static org.apache.kafka.connect.data.Schema.STRING_SCHEMA;
import static org.joda.time.DateTimeZone.UTC;

public class VectorDbSourceTask extends BaseSourceTask<VectorSourceConnectorConfig> implements SshTunnelSupport {

    private VectorDbConnectorService<BaseAuthConfig> vectorDB;
    private FetchStrategy fetchStrategy;
    private String taskPartition;
    private String collectionForKeys;

    @Override
    public String version() {
        return VERSION;
    }

    @Override
    public ConfigDef configDef() {
        return VectorSourceConnectorConfig.configDef();
    }

    @Override
    public void doStart(Map<String, String> props) throws ConnectException {
        this.collectionForKeys = config.collection.orElse("default");
        this.taskPartition = collectionForKeys;
        
        this.vectorDB = newConnectorService();
    }

    private VectorDbConnectorService<BaseAuthConfig> newConnectorService() {
        if (config.connectionType.equals(PINECONE)) {
            return new PineconeService();
        }
        throw new IllegalArgumentException("Not a vector based database: " + config.connectionType);
    }

    @Override
    protected VectorSourceConnectorConfig parseConfig(Map<String, String> props) {
        Map<String, Object> vectorConfig = new HashMap<>();
        vectorConfig.putAll(props);
        return new VectorSourceConnectorConfig(vectorConfig);
    }
    
    @Override
    public CollectRecordsResult collectRecords() {
        try {
            List<SourceRecordCreator> records;
            try (Stream<NexlaMessage> fetchStrategyStream = fetchStrategy().stream()) {
                records = fetchStrategyStream
                        .map(this::toSourceRecordCreator)
                        .collect(Collectors.toList());
            }

            List<SourceRecord> result = detectSchemaIfNecessary(config.schemaDetectionOnce, records, Optional.empty());
            sendNexlaMetrics(result);
            return new CollectRecordsResult(result);
        } catch (ProbeRetriableException e) {
            throw e;
        } catch (Exception e) {
            throw new ProbeRetriableException("Failed to read records", e);
        }
    }

    private void sendNexlaMetrics(List<SourceRecord> messages) {
        long sizeBytes = StreamEx.of(messages)
                .mapToInt(r -> calcBytes(r))
                .sum();
        sendMetrics(taskPartition, messages.size(), sizeBytes, 0L, DateTimeUtils.nowUTC().getMillis());
    }

    private SourceRecordCreator toSourceRecordCreator(NexlaMessage message) {
        long now = DateTime.now(UTC).getMillis();

        return new SourceRecordCreator(
                message.getRawMessage(),
                (datasetId, datasetIdTopic) -> {
                    NexlaMessage m = ((Function2<Integer, String, NexlaMessage>) (dataSetId1, dataSetTopic2) -> {
                        String sourceKey = config.database + "." + collectionForKeys;
                        SourceItem trackerId = SourceItem.fullTracker(
                                config.sourceId,
                                dataSetId1,
                                sourceKey,
                                0L, // todo offset
                                config.version,
                                now
                        );

                        NexlaMetaData meta = new NexlaMetaData(
                                config.connectionType,
                                now,
                                0L, // todo offset,
                                sourceKey,
                                dataSetTopic2,
                                SOURCE,
                                config.sourceId,
                                false,
                                new Tracker(Tracker.TrackerMode.FULL, trackerId),
                                runId
                        );

                        Map<String, Object> tags = message.getNexlaMetaData() == null || message.getNexlaMetaData().getTags() == null
                                ? null
                                : message.getNexlaMetaData().getTags();

                        meta.setTags(tags);

                        return new NexlaMessage(message.getRawMessage(), meta);
                    }).apply(datasetId, datasetIdTopic);

                    return new SourceRecord(singletonMap(RestConfigAccessor.PARTITION_KEY, collectionForKeys), emptyMap(), datasetIdTopic, null, null, null, STRING_SCHEMA, toJsonString(m));
                },
                (datasetId, datasetTopic) -> {
                    String sourceKey = config.database + "." + collectionForKeys;
                    SourceItem trackerId = SourceItem.fullTracker(
                            config.sourceId,
                            datasetId,
                            sourceKey,
                            0L, // todo offset
                            config.version,
                            now
                    );

                    NexlaMetaData meta = new NexlaMetaData(
                            config.connectionType,
                            now,
                            0L, // todo offset,
                            sourceKey,
                            datasetTopic,
                            SOURCE,
                            config.sourceId,
                            false,
                            new Tracker(Tracker.TrackerMode.FULL, trackerId),
                            runId
                    );

                    Map<String, Object> tags = message.getNexlaMetaData() == null
                            ? emptyMap()
                            : message.getNexlaMetaData().getTags();

                    if (meta.getTags() != null) {
                        meta.getTags().putAll(tags);
                    } else {
                        meta.setTags(tags);
                    }

                    return new NexlaMessage(message.getRawMessage(), meta);
                }
        );
    }

    private FetchStrategy fetchStrategy() {
        if (this.fetchStrategy != null) {
            return this.fetchStrategy;
        }

        return this.fetchStrategy = new ForceBatchesFetchStrategy(vectorDB, config);
    }

    private static class ForceBatchesFetchStrategy implements FetchStrategy {
        private final VectorDbConnectorService<BaseAuthConfig> vectorDB;
        private final VectorSourceConnectorConfig config;

        private Iterator<NexlaMessage> iterator;

        public ForceBatchesFetchStrategy(VectorDbConnectorService<BaseAuthConfig> vectorDB, VectorSourceConnectorConfig config) {
            this.vectorDB = vectorDB;
            this.config = config;
        }

        @Override
        public Stream<NexlaMessage> stream() {
            StreamEx<NexlaMessage> resultStream;
            if (this.iterator == null) {
                StreamEx<NexlaMessage> docDbStream = vectorDB.stream(config, Option.empty()).map(x -> x.toNexlaMessage());
                this.iterator = docDbStream.iterator();
                resultStream = streamOfIteratorLimited()
                        .onClose(docDbStream::close);
            } else resultStream = streamOfIteratorLimited();
  
            return resultStream;
        }
   
        private StreamEx<NexlaMessage> streamOfIteratorLimited() {
            return StreamEx.of(this.iterator)
                    .limit(config.batchSizeApprox);
        }
   
    }

    public interface FetchStrategy {
        Stream<NexlaMessage> stream();
    }
}