package com.nexla.file.service

import com.nexla.connector.config.file.FileConnectorAuth
import org.mockito.{ArgumentMatchers, Mockito}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfterEach, TagAnnotation}
import org.scalatestplus.mockito.MockitoSugar.mock

import java.io.File
import java.util
import java.util.Optional

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class FileConnectorServiceTest extends AnyFlatSpecLike with Matchers with BeforeAndAfterEach {
  private val fileConnectorService = Mockito.spy(new LocalConnectorService)
  private val config = mock[FileConnectorAuth]
  private val fileDetails = mock[FileDetails]
  private val file = mock[File]
  private val sinkId = 1
  private val sinkIdOpt: Optional[Integer] = Optional.of(sinkId)

  Mockito.doAnswer(_ => fileDetails).when(fileConnectorService).write(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any(classOf[File]))

  it should "create new file if it is absent" in {
    val fileName = "new_file"

    Mockito.doAnswer(_ => false).when(fileConnectorService).doesFileExists(ArgumentMatchers.any(), ArgumentMatchers.any())

    fileConnectorService.writeWithSuffixIfExists(sinkIdOpt, config, fileName, file)

    Mockito.verify(fileConnectorService).write(config, fileName, file)
  }

  it should "add suffix 1 to file name if file was created before" in {
    val fileName = "file_with_suffix"

    Mockito.doAnswer(_ => true).doAnswer(_ => false).when(fileConnectorService).doesFileExists(ArgumentMatchers.any(), ArgumentMatchers.any())

    fileConnectorService.writeWithSuffixIfExists(sinkIdOpt, config, fileName, file)

    Mockito.verify(fileConnectorService).write(config, fileName + "-1", file)
  }

  it should "overwrite file if it is exist" in {
    val fileName = "overwrite_file"

    val map = new util.HashMap[String, String]()
    map.put(fileName, fileName)
    FileConnectorService.FILESINK_FLUSH_FILE_RENAME_TRACKER.put(sinkId, map)

    fileConnectorService.writeWithSuffixIfExists(sinkIdOpt, config, fileName, file)

    Mockito.verify(fileConnectorService, Mockito.times(0)).doesFileExists(ArgumentMatchers.any(), ArgumentMatchers.matches(fileName))

    Mockito.verify(fileConnectorService).write(config, fileName, file)
  }

  override protected def afterEach(): Unit = FileConnectorService.FILESINK_FLUSH_FILE_RENAME_TRACKER.clear()
}
