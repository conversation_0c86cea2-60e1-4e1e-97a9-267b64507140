package com.nexla.file.service;

import com.nexla.common.NexlaBucket;
import com.nexla.common.NexlaFile;
import com.nexla.common.ResourceType;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.file.service.FileConnectorService;
import com.nexla.file.service.FileDetails;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.AbstractConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;

public class LocalConnectorService extends FileConnectorService<AWSAuthConfig> {

	private Logger logger = LoggerFactory.getLogger(LocalConnectorService.class);

	public LocalConnectorService() {
		super(null, null, null);
	}

	@Override
	public void initLogger(ResourceType resourceType, Integer resourceId, Optional<Integer> taskId) {
		this.logger = new NexlaLogger(logger, new NexlaLogKey(resourceType, resourceId, taskId));
	}

	@Override
	public AuthResponse authenticate(AWSAuthConfig authConfig) {
		return SUCCESS;
	}

	@Override
	public StreamEx<NexlaFile> listBucketContents(AbstractConfig c) {
		throw new UnsupportedOperationException();
	}

	@Override
	public boolean doesFileExistsInternal(FileConnectorAuth config, String fileNameNoBucket) {
		return new File(config.getPath(), fileNameNoBucket).exists();
	}

	@Override
	public StreamEx<NexlaBucket> listBuckets(AbstractConfig config) {
		throw new UnsupportedOperationException();
	}

	@Override
	@SneakyThrows
	public InputStream readInputStreamInternal(FileConnectorAuth config, String file) {
		return new FileInputStream(new File(config.getPath(), file));
	}

	@Override
	public boolean checkWriteAccess(AbstractConfig c) {
		return true;
	}

	@Override
	public FileDetails writeInternal(FileConnectorAuth c, String key, File file) {
		throw new UnsupportedOperationException();
	}

	@Override
	public FileDetails writeInternal(FileConnectorAuth c, String key, InputStream inputStream) {
		throw new UnsupportedOperationException();
	}

	@Override
	public StreamEx<NexlaFile> listTopLevelBuckets(AbstractConfig configTemp) {
		throw new UnsupportedOperationException();
	}

	@Override
	public void deleteByName(AbstractConfig c, final List<String> keys) {
		throw new UnsupportedOperationException();	}

	@Override
	public void clearBucket(AbstractConfig c) {
		throw new UnsupportedOperationException();
	}

	@Override
	public void createDestination(AbstractConfig c, Optional<Integer> sourceId) {
		throw new UnsupportedOperationException();
	}

}
