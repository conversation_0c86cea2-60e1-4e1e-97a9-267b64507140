package com.nexla.file.service;

import com.google.common.io.ByteStreams;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.ResourceType;
import com.nexla.common.exception.NexlaException;
import com.nexla.common.io.CloseableInputStream;
import com.nexla.common.io.SequenceInputStream;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.probe.ProbeSampleResult;
import com.nexla.common.probe.ProbeSampleResultEntry;
import com.nexla.common.probe.StringSampleResult;
import com.nexla.connector.ConnectorService;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.file.FileSinkConnectorConfig;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.listing.client.ListingClient;
import com.nexla.parser.ParserUtilsExt.Result;
import com.nexla.telemetry.Telemetry;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.apache.kafka.common.config.AbstractConfig;
import org.apache.tika.config.TikaConfig;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.mime.MediaType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.common.probe.ProbeSampleResult.BINARY_SAMPLE;
import static com.nexla.connector.config.file.FileSourceConnectorConfig.DOWNLOAD_LIMIT;
import static com.nexla.parser.ParserUtilsExt.detectParser;
import static org.apache.commons.io.FilenameUtils.getExtension;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.removeEnd;
import static org.apache.tika.mime.MediaType.TEXT_HTML;
import static org.apache.tika.mime.MediaType.TEXT_PLAIN;

@AllArgsConstructor
public abstract class FileConnectorService<AuthConfig extends BaseAuthConfig>
	extends ConnectorService<AuthConfig> {

	private static Logger logger = LoggerFactory.getLogger(FileConnectorService.class);
	private static final Set<String> TEXT_MEDIA_TYPES = Set.of(
			"text",
			"message"
	);

	private static final Set<String> TEXT_MEDIA_SUB_TYPES = Set.of(
			"html",
			"xhtml",
			"xml",
			"csv",
			"json",
			"log"
	);

	private static final int SAMPLE_READ_KB = 100;

	public final AdminApiClient adminApiClient;
	public final ListingClient listingClient;
	public final String credentialsDecryptKey;

	@SneakyThrows
	public void readToOutputStreamInternal(FileConnectorAuth c, String file, OutputStream os) {
		try (InputStream is = readInputStreamInternal(c, file)) {
			IOUtils.copyLarge(is, os);
		}
	}

	public void setTelemetry(Telemetry telemetry) {

	}

	public abstract InputStream readInputStreamInternal(FileConnectorAuth c, String file);

	public abstract FileDetails writeInternal(FileConnectorAuth config, String key, File file);

	public abstract FileDetails writeInternal(FileConnectorAuth config, String key, InputStream inputStream);

	public abstract boolean doesFileExistsInternal(FileConnectorAuth config, String key);

	public int calculateSinkParallelism(FileConnectorAuth config) {
		if (config instanceof FileSinkConnectorConfig) {
			return ((FileSinkConnectorConfig) config).sinkParallelism;
		}

		return 1;
	}

	@Override
	public void initLogger(ResourceType resourceType, Integer resourceId, Optional<Integer> taskId) {
		this.logger = new NexlaLogger(logger, new NexlaLogKey(resourceType, resourceId, taskId));
	}

	public InputStream readInputStream(FileConnectorAuth c, String file) {
		c.recourceAccessCallback().ifPresent(x -> x.resourceTaken(file));
		return new CloseableInputStream(readInputStreamInternal(c, file))
			.onClose(() -> c.recourceAccessCallback().ifPresent(x -> x.resourceReleased(file)));
	}

	public void readToOutputStream(FileConnectorAuth c, String file, OutputStream os) {
		c.recourceAccessCallback().ifPresent(x -> x.resourceTaken(file));
		readToOutputStreamInternal(c, file, os);
		c.recourceAccessCallback().ifPresent(x -> x.resourceReleased(file));
	}

	public FileDetails fileDetails(AbstractConfig c, Optional<String> mimeType, String file) {
		return new FileDetails(file, mimeType, Optional.empty());
	}

	public FileDetails overwriteFile(FileConnectorAuth config, String key, File file) {
		config.recourceAccessCallback().ifPresent(x -> x.resourceTaken(key));
		FileDetails result = overwriteFileInternal(config, key, file);
		config.recourceAccessCallback().ifPresent(x -> x.resourceReleased(key));
		return result;
	}

	public FileDetails overwriteFileInternal(FileConnectorAuth config, String key, File file) {
		return writeInternal(config, key, file);
	}

	public boolean doesFileExists(FileConnectorAuth config, String key) {
		config.recourceAccessCallback().ifPresent(x -> x.resourceTaken(key));
		boolean result = doesFileExistsInternal(config, key);
		config.recourceAccessCallback().ifPresent(x -> x.resourceReleased(key));
		return result;
	}

	public static Map<Integer, Map<String, String>> FILESINK_FLUSH_FILE_RENAME_TRACKER = new ConcurrentHashMap<>();

	public FileDetails writeWithSuffixIfExists(FileConnectorAuth config, String key, File file) {
		return writeWithSuffixIfExists(Optional.empty(), config, key, file);
	}

	public FileDetails writeWithSuffixIfExists(Optional<Integer> flushSinkIdControl, FileConnectorAuth config, String key, File file) {
		String resultKey = flushSinkIdControl
			.map(sinkId -> {
				Map<String, String> sinkMap = FILESINK_FLUSH_FILE_RENAME_TRACKER.computeIfAbsent(sinkId, s -> new ConcurrentHashMap<>());
				return sinkMap.computeIfAbsent(key, s -> buildSuffixFilePath(config, key));
			})
			.orElseGet(() -> buildSuffixFilePath(config, key));

		return write(config, resultKey, file);
	}

	private String buildSuffixFilePath(FileConnectorAuth config, String key) {
		String extension = getExtension(key);
		String pathWithoutExt = isNotEmpty(extension) ? removeEnd(key, "." + extension) : key;
		String dotExt = isNotEmpty(extension) ? "." + extension : "";

		String currentKey = key;
		int counter = 0;

		while (doesFileExists(config, currentKey)) {
			logger.info("File already exists, trying to add suffix: {}", currentKey);
			counter++;
			currentKey = pathWithoutExt + "-" + counter + dotExt;
		}
		return currentKey;
	}

	@SneakyThrows
	public FileDetails write(FileConnectorAuth config, String key, File file) {
		try {
			config.recourceAccessCallback().ifPresent(x -> x.resourceTaken(key));
			FileDetails result = writeInternal(config, key, file);
			config.recourceAccessCallback().ifPresent(x -> x.resourceReleased(key));
			return result;
		} catch (Exception e) {
			throw new NexlaException(e.getMessage(), e).setData("retryDataFileKey", key);
		}
	}

	public FileDetails write(FileConnectorAuth config, String key, InputStream inputStream) {
		config.recourceAccessCallback().ifPresent(x -> x.resourceTaken(key));
		FileDetails result = writeInternal(config, key, inputStream);
		config.recourceAccessCallback().ifPresent(x -> x.resourceReleased(key));
		return result;
	}

	@Override
	@SneakyThrows
	public ProbeSampleResult readSample(AbstractConfig config, boolean raw) {
		Map<String, Object> originals = config.originals();
		originals.put(DOWNLOAD_LIMIT, SAMPLE_READ_KB);
		FileSourceConnectorConfig fileConfig = new FileSourceConnectorConfig(originals);
		Optional<String> extension = opt(getExtension(fileConfig.path));
		String filePath = AWSAuthConfig.getFilePath(fileConfig.sourceType, fileConfig.path);

		if (filePath.contains(";") || filePath.endsWith(".delta")) {
			return BINARY_SAMPLE;
		}

		try (InputStream inputStream = ByteStreams.limit(readInputStream(fileConfig, filePath), SAMPLE_READ_KB * 1024)) {
			// TODO replace following by this line when implement format autodetection
			// return byParserDetection(config, fileConfig, extension, filePath, inputStream, inputStream);
			byte[] dataBuffer = new byte[SAMPLE_READ_KB * 1024];
			int actualLength = IOUtils.read(inputStream, dataBuffer);
			byte[] partialData = Arrays.copyOf(dataBuffer, actualLength);
			MediaType result = new TikaConfig().getDetector().detect(new ByteArrayInputStream(partialData), new Metadata());

			if (!isTextType(result)) {
				return BINARY_SAMPLE;
			} else {
				FileDetails details = fileDetails(config, Optional.of(result.toString()), filePath);
				InputStream restoredInputStream = new SequenceInputStream(new ByteArrayInputStream(partialData), inputStream);
				return byParserDetection(config, fileConfig, extension, details.getDisplayPath().orElse(details.getPath()), restoredInputStream, details.getMimeType());
			}
		} catch (Exception e) {
			logger.error("failed to read {}", filePath, e);
			return ProbeSampleResult.EMPTY_SAMPLE;
		}
	}

	private ProbeSampleResult byParserDetection(
		AbstractConfig config,
		FileSourceConnectorConfig fileConfig,
		Optional<String> extension,
		String filePath,
		InputStream inputStream,
		Optional<String> mimeType
	) {
		Result detection = detectParser(
			mimeType, inputStream, fileConfig.overridenExtensions, config.originalsStrings(), filePath, null, logger);

		return detection
			.parser()
			.map(parser -> {
				List<ProbeSampleResultEntry<String>> strings = parser
					.readLines(detection.restoredStream())
					.map(ProbeSampleResultEntry::new)
					.toList();

				closeSilently(inputStream);
				return new StringSampleResult(strings, extension, false);
			})
			.getOrElse(() -> {
				closeSilently(inputStream);
				return BINARY_SAMPLE;
			});
	}


	private static boolean isTextType(MediaType mediaType) {
		if (TEXT_MEDIA_TYPES.contains(mediaType.getType().toLowerCase())) {
			return true;
		}

		return TEXT_MEDIA_SUB_TYPES.stream().anyMatch(x -> {
			String sub = mediaType.getSubtype().toLowerCase();

			return sub.equals(x) || sub.endsWith("-" + x) || sub.endsWith("+" + x);
		});
	}

}
