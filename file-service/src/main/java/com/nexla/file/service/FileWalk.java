package com.nexla.file.service;

import com.nexla.connector.config.file.DirScanningMode;
import lombok.AllArgsConstructor;
import one.util.streamex.StreamEx;

import java.nio.file.Path;
import java.util.function.Function;
import java.util.function.Supplier;

public class FileWalk {

	@AllArgsConstructor
	public static class LevelFile<T> {
		public final int level;
		public final T file;
		public final String dirPath;
		public final boolean folder;
	}

	public static <T> StreamEx<LevelFile<T>> walkFileTreeDfs(
		DirScanningMode scanningMode,
		Supplier<StreamEx<LevelFile<T>>> firstLevelFiles,
		Function<LevelFile<T>, StreamEx<LevelFile<T>>> nextIterationFn
	) {
		return filterByType(
			scanningMode,
			StreamEx
				.ofTree(
					(LevelFile<T>) null,
					rootFile -> (rootFile == null) ? firstLevelFiles.get() : nextIterationFn.apply(rootFile)
				)
				.skip(1));
	}

	private static <T> StreamEx<LevelFile<T>> filterByType(DirScanningMode scanningMode, StreamEx<LevelFile<T>> fileStream) {
		switch (scanningMode) {
			case BOTH:
				return fileStream;
			case DIRECTORIES:
				return fileStream.filter(f -> f.folder);
			case FILES:
				return fileStream.filter(f -> !f.folder);
			default:
				throw new IllegalArgumentException();
		}
	}
}
