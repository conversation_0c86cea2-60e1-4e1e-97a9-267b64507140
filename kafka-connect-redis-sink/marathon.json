{"id": "/sink-redis-connector", "instances": 1, "cpus": 0.5, "mem": 2048, "disk": 0, "gpus": 0, "fetch": [{"uri": "https://s3.amazonaws.com/mesos-config3/docker.tar.gz", "extract": true, "executable": false, "cache": false}], "backoffSeconds": 1, "backoffFactor": 1.15, "maxLaunchDelaySeconds": 3600, "container": {"type": "DOCKER", "docker": {"image": "nexla/kafka-connect-redis-sink", "network": "BRIDGE", "portMappings": [{"containerPort": 8083, "hostPort": 0, "servicePort": 11009, "protocol": "tcp", "name": "default", "labels": {"VIP_0": "/sink-redis-connector:8083"}}, {"containerPort": 2000, "hostPort": 0, "protocol": "tcp"}], "privileged": false, "forcePullImage": true}}, "healthChecks": [{"gracePeriodSeconds": 300, "intervalSeconds": 60, "timeoutSeconds": 20, "maxConsecutiveFailures": 3, "portIndex": 0, "path": "/", "protocol": "MESOS_HTTP", "delaySeconds": 15}], "upgradeStrategy": {"minimumHealthCapacity": 1, "maximumOverCapacity": 1}, "unreachableStrategy": {"inactiveAfterSeconds": 300, "expungeAfterSeconds": 600}, "killSelection": "YOUNGEST_FIRST", "requirePorts": true, "taskKillGracePeriodSeconds": 20, "labels": {"DCOS_SERVICE_SCHEME": "http", "DCOS_SERVICE_PORT_INDEX": "0", "DCOS_SERVICE_NAME": "sink-redis-connector", "DCOS_PACKAGE_IS_FRAMEWORK": "false"}, "env": {"CONNECT_KEY_CONVERTER_SCHEMA_REGISTRY_URL": "http://schema-registry.marathon.l4lb.thisdcos.directory:8081", "CONNECT_KEY_CONVERTER": "org.apache.kafka.connect.storage.StringConverter", "CONNECT_VALUE_CONVERTER_SCHEMA_REGISTRY_URL": "http://schema-registry.marathon.l4lb.thisdcos.directory:8081", "CONNECT_REST_PORT": "8083", "CONNECT_BOOTSTRAP_SERVERS": "broker.kafka.l4lb.thisdcos.directory:9092", "CONNECT_GROUP_ID": "dcos-connect-group-redis-sink-poll-7", "CONNECT_INTERNAL_KEY_CONVERTER": "org.apache.kafka.connect.json.JsonConverter", "CONNECT_STATUS_STORAGE_TOPIC": "dcos-connect-status-redis-sink-poll-7", "CONNECT_VALUE_CONVERTER": "org.apache.kafka.connect.storage.StringConverter", "CONNECT_ZOOKEEPER_CONNECT": "zookeeper-0-server.kafka-zookeeper.autoip.dcos.thisdcos.directory:1140,zookeeper-1-server.kafka-zookeeper.autoip.dcos.thisdcos.directory:1140,zookeeper-2-server.kafka-zookeeper.autoip.dcos.thisdcos.directory:1140", "CONNECT_INTERNAL_VALUE_CONVERTER": "org.apache.kafka.connect.json.JsonConverter", "CONNECT_OFFSET_STORAGE_TOPIC": "dcos-connect-offsets-redis-sink-poll-7", "CONNECT_CONFIG_STORAGE_TOPIC": "dcos-connect-configs-redis-sink-poll-7"}}