package com.nexla.connector.redis.sink;

import com.nexla.connect.common.connector.BaseSinkConnector;
import com.nexla.connector.config.redis.RedisConnectorConfig;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.connector.Task;
import org.apache.kafka.connect.errors.ConnectException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.Collections.nCopies;

public class RedisSinkConnector extends BaseSinkConnector {

	public static final String REDIS_SINK_TELEMETRY_NAME = "redis-sink";

	@Override
	protected String telemetryAppName() {
		return REDIS_SINK_TELEMETRY_NAME;
	}

	@Override
	public Class<? extends Task> taskClass() {
		return RedisSinkTask.class;
	}

	@Override
	public ConfigDef config() {
		return RedisConnectorConfig.configDef();
	}
}
