# parsers
Implementation for Parsing data. This is used by the probe service and the connectors.

##Implementation

Each parser implements NexlaParser Interface.

```
public NexlaSchema getSchema(InputStream stream);
public List<NexlaMessage> getMessages(InputStream stream);
public void setConfig(Map<String,String> config);
	
```

Additional configs for the parser can be set using the setConfig interface.
Parser also defines any custom configs as needed.

The library includer should initialize NexlaParserFactory in AppConfig as follows -

```
@Bean
public FactoryBean parserServiceLocatorFactoryBean() {
  ServiceLocatorFactoryBean factoryBean = new ServiceLocatorFactoryBean();
  factoryBean.setServiceLocatorInterface(NexlaParserFactory.class);
  return factoryBean;
}

@Bean(name = "json")
@Scope(scopeName = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public JSONParser nexlaParser() {
  return new JSONParser();
}

@Bean(name = "delimited")
@Scope(scopeName = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public DelimitedTextParser delimitedParser() {
  return new DelimitedTextParser();
}

@Bean(name = "xml")
@Scope(scopeName = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public XMLParser xmlParser() {
  return new XMLParser();
}

@Bean(name = "avro")
@Scope(scopeName = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public AvroParser avroParser() {
  return new AvroParser();
}

```


JunitParams is used to parametrize unit tests.


