package com.nexla.parser.excel;

import lombok.Data;

@Data
public class RangeNoSheet {

	private final RangeFromTo from;
	private final RangeFromTo to;

	public RangeNoSheet(final RangeFromTo from, final RangeFromTo to) {
		this.from = from;
		this.to = to;
	}

	public RangeNoSheet(String r) {
		String[] split = r.split(":");
		this.from = new RangeFromTo(split[0]);
		this.to = new RangeFromTo(split[1]);
	}

	public RangeNoSheet transposed() {
		return new RangeNoSheet(from.transposed(), to.transposed());
	}

	@Override
	public String toString() {
		return from + ":" + to;
	}
}