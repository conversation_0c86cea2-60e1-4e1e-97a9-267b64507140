package com.nexla.parser.excel.functions;

import org.apache.poi.ss.formula.eval.*;
import org.apache.poi.ss.formula.functions.Fixed3ArgFunction;

import java.time.LocalDate;
import java.time.Period;
import java.time.temporal.ChronoUnit;

/**
 * Implementation for the DATEDIF() Excel function.<p>
 *
 * <b>Syntax:</b><br>
 * <b>DATEDIF</b>(<b>start_date, end_date, unit</b>)
 * <p>
 * The <b>DATEDIF</b> function calculates the number of days, months, or years between two dates.
 * <p>
 * <b>Sample Usage</b><br>
 * DATEDIF(DATE(1969, 7, 16), DATE(1969, 7, 24), "D")
 * DATEDIF(A1, A2, "YM")
 * DATEDIF("7/16/1969", "7/24/1969", "Y")
 */
public final class DateDifFunc extends Fixed3ArgFunction {

    public ValueEval evaluate(int srcRowIndex, int srcColumnIndex, ValueEval arg0, ValueEval arg1, ValueEval arg2) {
        double result;
        try {
            double startValue = singleOperandEvaluate(arg0, srcRowIndex, srcColumnIndex);
            double endValue = singleOperandEvaluate(arg1, srcRowIndex, srcColumnIndex);
            String unit = OperandResolver.coerceValueToString(OperandResolver.getSingleValue(arg2, srcRowIndex, srcColumnIndex));
            result = getDateDif(startValue, endValue, unit);
            checkValue(result);
        } catch (EvaluationException e) {
            return e.getErrorEval();
        }
        return new NumberEval(result);
    }

    private double singleOperandEvaluate(ValueEval arg, int srcRowIndex, int srcColumnIndex) throws EvaluationException {
        if (arg == null) {
            throw new IllegalArgumentException("arg must not be null");
        }
        ValueEval ve = OperandResolver.getSingleValue(arg, srcRowIndex, srcColumnIndex);
        double result = OperandResolver.coerceValueToDouble(ve);
        checkValue(result);
        return result;
    }

    /**
     * @throws EvaluationException (#NUM!) if {@code result} is {@code NaN} or {@code Infinity}
     */
    private void checkValue(double result) throws EvaluationException {
        if (Double.isNaN(result) || Double.isInfinite(result)) {
            throw new EvaluationException(ErrorEval.NUM_ERROR);
        }
    }

    /**
     * The input is a serial values represent dates in the Excel date format, where January 1, 1900 is day 1.
     * <p>
     * Accepted unit values are "Y","M","D" ,"MD","YM","YD".
     * "Y": the number of whole years between start_date and end_date.
     * "M": the number of whole months between start_date and end_date.
     * "D": the number of days between start_date and end_date.
     * "MD": the number of days between start_date and end_date after subtracting whole months.
     * "YM": the number of whole months between start_date and end_date after subtracting whole years.
     * "YD": the number of days between start_date and end_date, assuming start_date and end_date were no more than one year apart.
     *
     * @param startValue The start date to consider in the calculation. Must be a reference to a cell containing a DATE, a function returning a DATE type, or a number.
     * @param endValue   The end date to consider in the calculation. Must be a reference to a cell containing a DATE, a function returning a DATE type, or a number.
     * @param unit       A text abbreviation for unit of time
     * @return dif value
     */
    long getDateDif(double startValue, double endValue, String unit) {
        LocalDate startDate = LocalDate.of(1900, 1, 1).plusDays((int) startValue - 2);
        LocalDate endDate = LocalDate.of(1900, 1, 1).plusDays((int) endValue - 2);

        switch (unit.toUpperCase()) {
            case "Y":
                return Period.between(startDate, endDate).getYears();
            case "M":
                return Period.between(startDate, endDate).toTotalMonths();
            case "D":
                return ChronoUnit.DAYS.between(startDate, endDate);
            case "MD":
                return Period.between(startDate, endDate).getDays();
            case "YM":
                return Period.between(startDate, endDate).getMonths();
            case "YD":
                endDate = endDate.withYear(startDate.getYear());
                if (endDate.isBefore(startDate)) {
                    endDate = endDate.withYear(startDate.getYear() + 1);
                }
                return ChronoUnit.DAYS.between(startDate, endDate);
            default:
                return 0; // Handle unsupported units
        }
    }

}
