package com.nexla.parser.excel;

import lombok.Getter;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Getter
public class AdditionalRange {
    private final Optional<String> sheetName;
    private final List<AdditionalRangeNoSheet> additionalRangeNoSheetList;

    /**
     * 
     * @param rawInput should be in the format `sheetname`_`attributeNameCell1`:`valueCell1`|..|`attributeNameCellN`:`valueCellN`" 
     *                 If sheetname is absent, by default first one will be used. Specify sheetname to fetch cells from multiple sheets. 
     *                 If attributeNameCell is absent, we will generate attribute name for the specified value during data ingestion.
     *                 
     */
    public AdditionalRange(String rawInput) {
        String[] splitArr = rawInput.contains("_") ? rawInput.split("_", 2) : new String[] {null, rawInput};
        this.sheetName = RangeSheet.getTruncatedSheetName(splitArr[0]);
        
        List<AdditionalRangeNoSheet> additionalRangeNoSheetsList = List.of(splitArr[1].split("\\|"))
                .stream().map(AdditionalRangeNoSheet::new).collect(Collectors.toList());
        this.additionalRangeNoSheetList = additionalRangeNoSheetsList;
    }
    
    
    
}
