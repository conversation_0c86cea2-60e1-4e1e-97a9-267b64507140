package com.nexla.parser.excel;

import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

public class ExcelSheetReplacer {
	public void replaceSheet(final Workbook workbook, final Sheet srcSheet, final Sheet dstSheet) {
		final int sheetIdx = workbook.getSheetIndex(srcSheet);
		final String sheetName = srcSheet.getSheetName();
		workbook.removeSheetAt(sheetIdx);
		workbook.setSheetOrder(dstSheet.getSheetName(), sheetIdx);
		workbook.setSheetName(sheetIdx, sheetName);
	}
}
