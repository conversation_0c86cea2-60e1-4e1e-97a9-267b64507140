package com.nexla.parser.excel;

import lombok.Data;

import java.util.Optional;

@Data
public class RangeFromTo {

	private final Optional<String> column;
	private final Optional<Integer> row;		// 1-based row number

	public RangeFromTo(final String column, final Integer row) {
		this.column = Optional.ofNullable(column);
		this.row = Optional.ofNullable(row);
	}

	public RangeFromTo(String r) {
		StringBuffer colBuff = new StringBuffer();
		StringBuffer rowBuff = new StringBuffer();
		for (Character ch : r.toCharArray()) {
			if (Character.isDigit(ch)) {
				rowBuff.append(ch);
			} else {
				colBuff.append(ch);
			}
		}
		this.column = Optional.of(colBuff.toString())
			.filter(x -> x.length() > 0);
		this.row = Optional.of(rowBuff.toString())
			.filter(x -> x.length() > 0)
			.map(Integer::valueOf);
	}

	public Optional<Integer> getRowIdxZeroBased() {
		return row.map(this::toZeroBased);
	}

	public Optional<Integer> getColumnIdxZeroBased() {
		return column.map(this::columnLetterToInt)
					 .map(this::toZeroBased);
	}

	public RangeFromTo transposed() {
		return new RangeFromTo(
				row.map(this::columnIntToLetter).orElse(null),
				column.map(this::columnLetterToInt).orElse(null));
	}

	private int toZeroBased(final int value) {
		return value - 1;
	}

	private int columnLetterToInt(final String x) {
		char[] chars = x.toCharArray();
		int result = 0;
		for (int i = 0; i < chars.length; i++) {
			result += (chars[i] - 'A') + 1;
			if (i + 1 < chars.length) {
				result *= (('Z' - 'A') + 1);
			}
		}
		return result;
	}

	private String columnIntToLetter(int number) {
		final StringBuilder sb = new StringBuilder();
		while (number-- > 0) {
			sb.append((char) ('A' + (number % 26)));
			number /= 26;
		}
		return sb.reverse().toString();
	}

	@Override
	public String toString() {
		return column.orElse("") + row.map(String::valueOf).orElse("");
	}

}
