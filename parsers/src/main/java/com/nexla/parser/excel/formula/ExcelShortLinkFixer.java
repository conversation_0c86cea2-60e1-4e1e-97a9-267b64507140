package com.nexla.parser.excel.formula;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFTable;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * Workaround class to avoid apache.poi Table reference bug (for apache poi 5.2.2 and 5.2.3).
 * If formula contains short reference to other Table, like "VLOOKUP([@LocID];Table2;9;FALSE)", where Table2 is a short link,
 * Then apache.poi cannot resolve it and throws an error.
 * In order to locate table reference, we have to manually map it to apache.poi inner representation.
 * For the example above, this will look like Table2 -> Table2[[#This Row],[Name]].
 * <p>
 * !Important! for apache poi 5.2.2 and 5.2.3 [[#This Row],[Name]] isn't working correctly, poi is setting one
 * cell range, like A2:A2, instead of a full row, which leads to [#!REF] error for some formulas,
 * which tries to access other value than its cell. For example, VLOOKUP().
 * <p>
 * That's why I replace formula to Table2[#All]. See other variants at @see org.apache.poi.ss.formula.FormulaParser:603
 */
public class ExcelShortLinkFixer {

	/**
	 * Supported only for xlsx files.
	 */
	public void resolveShortTableNames(Sheet sheet, Workbook workbook) {
		if (!workbook.getClass().equals(XSSFWorkbook.class)) {
			return;
		}

		for (Row cells : sheet) {
			for (Cell cell : cells) {
				if (!cell.getCellType().equals(CellType.FORMULA)) {
					continue;
				}
				this.replaceShortTableName(cell, this.createShortNameToPoiName(workbook));
			}
		}

	}

	/**
	 * In order for Table links to be resolved, we have to re-assign a value to a cell which contains formula.
	 * Those with bugged short names, are replaced to the poi internal version.
	 * Non-bugged ones just reassign their values.
	 * <p>
	 * After the value is re-evaluated and set it back to Cell, apache.poi will recalculate its value.
	 * For the example above it will be:
	 * Table2 -> Table2[[#This Row],[Name]] -> VLOOKUP(LocProfile!A2:A2,'Source - Cafe Info Sheet'!A2:A2,6,FALSE)
	 *
	 * @param tableNameToResolvedName map of Table2, Table2[[#This Row],[Name]]
	 */
	private void replaceShortTableName(Cell cell, Map<String, String> tableNameToResolvedName) {
		if (!cell.getCellType().equals(CellType.FORMULA)) {
			return;
		}
		Sheet sheet = cell.getSheet();
		String originalFormula = cell.getCellFormula();
		if (cell.isPartOfArrayFormulaGroup()) {
			CellRangeAddress range = cell.getArrayFormulaRange();
			sheet.removeArrayFormula(cell);
			cell = sheet.getRow(range.getFirstRow()).getCell(range.getFirstColumn());
		}
	
		String newFormula = originalFormula;
		for (Map.Entry<String, String> entry : tableNameToResolvedName.entrySet()) {
			String shortTableName = entry.getKey();
			String poiTableName = entry.getValue();
			if (newFormula.contains(shortTableName)) {
				newFormula = newFormula.replace(shortTableName, poiTableName);
			}
		}
		cell.setCellFormula(newFormula);
	}

	private Map<String, String> createShortNameToPoiName(Workbook workbook) {
		List<XSSFTable> tables = new ArrayList<>();
		final int numberOfSheets = workbook.getNumberOfSheets();

		for (int sheetIdxPre = 0; sheetIdxPre < numberOfSheets; sheetIdxPre++) {
			XSSFSheet sheetPre = (XSSFSheet) workbook.getSheetAt(sheetIdxPre);
			tables.addAll(sheetPre.getTables());
		}

		return tables.stream()
				.map(XSSFTable::getName)
				.collect(Collectors.toMap(name -> name + "[]",
//							name -> name + "[[#This Row],[Name]]")); bug in 5.2.2 and 5.2.3 version
						name -> name + "[#All]"));
	}
}
