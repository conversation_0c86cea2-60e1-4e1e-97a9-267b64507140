package com.nexla.parser.excel;

import lombok.Data;

import java.util.Objects;
import java.util.Optional;

@Data
public class RangeSheet {

	private final Optional<String> sheetName;
	private final Optional<RangeNoSheet> range;

	public RangeSheet(final String sheetName, final RangeNoSheet range) {
		this.sheetName = getTruncatedSheetName(sheetName);
		this.range = Optional.ofNullable(range);
	}

	public RangeSheet(String r) {
		boolean hasSheetDelimiter = r.contains("!");
		boolean hasLeftRightDelimiter = r.contains(":");
		if (!hasSheetDelimiter && !hasLeftRightDelimiter) {
			this.sheetName = getTruncatedSheetName(r);
			this.range = Optional.empty();
		} else if (!hasSheetDelimiter && hasLeftRightDelimiter) {
			this.sheetName = Optional.empty();
			this.range = Optional.of(new RangeNoSheet(r));
		} else if (hasSheetDelimiter && hasLeftRightDelimiter) {
			String[] split = r.split("!");
			this.sheetName = getTruncatedSheetName(split[0]);
			this.range = Optional.of(new RangeNoSheet(split[1]));
		} else {
			throw new IllegalStateException();
		}
	}

	public RangeSheet transposed() {
		return new RangeSheet(
				sheetName.orElse(null),
				range.map(RangeNoSheet::transposed).orElse(null));
	}

	@Override
	public String toString() {
		return sheetName.map(x -> x + "!").orElse("") + range.map(Objects::toString).orElse("");
	}

	// truncates sheet name due to Excel 31 character limitation
	public static Optional<String> getTruncatedSheetName(String sheetName) {
		return Optional.ofNullable(sheetName)
				.map(it -> it.substring(0, Math.min(it.length(), 31)));
	}
}