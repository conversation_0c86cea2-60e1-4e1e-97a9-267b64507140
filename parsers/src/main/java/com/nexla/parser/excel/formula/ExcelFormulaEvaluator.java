package com.nexla.parser.excel.formula;

import java.util.Locale;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.eval.NotImplementedException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.Temporal;

import static org.apache.poi.ss.usermodel.CellType.*;
import static org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted;

@Slf4j
public class ExcelFormulaEvaluator {
	private static final Logger logger = LoggerFactory.getLogger(ExcelFormulaEvaluator.class);
	private final ExcelShortLinkFixer linkFixer = new ExcelShortLinkFixer();
	private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd-MMM-yyyy");
	private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd-MMM-yyyy HH:mm:ss");

	public Sheet evaluateAllFormulas(final Workbook workbook, final int sheetIdx) {
		final Sheet sheet = workbook.getSheetAt(sheetIdx);
		linkFixer.resolveShortTableNames(sheet, workbook);

		final FormulaEvaluator formulaEvaluator = workbook.getCreationHelper().createFormulaEvaluator();
		for (Row cells : sheet) {
			for (Cell cell : cells) {
				this.evaluateFormula(cell, formulaEvaluator);
			}
		}

		// result must be formatted after formulas are evaluated. Plz don't move to previous loop.
		for (Row cells : sheet) {
			for (Cell cell : cells) {
				this.formatResult(cell, cell.getCellType());
			}
		}
		return sheet;
	}

	@SneakyThrows
	private void evaluateFormula(Cell unevaluated, FormulaEvaluator formulaEvaluator) {
		if (!unevaluated.getCellType().equals(CellType.FORMULA)) {
			return;
		}
		try {
			formulaEvaluator.evaluateInCell(unevaluated);
		} catch (NotImplementedException e) {
			logger.warn("Error when evaluating formula: {}", e.getMessage());
		} catch (RuntimeException e) {
			logger.error("Error when evaluating formula: {}", e.getMessage());
			// We don't want to eat all runtime exceptions, but we want to ignore this one
			var externalWorkbookError = e.getMessage() != null && e.getMessage().startsWith("Could not resolve external workbook name");
			if (!externalWorkbookError) {
				throw e;
			}
		}
	}

	private String formatResult(Cell evaluated, CellType cellType) {
		switch (cellType) {
			case NUMERIC:
				if (isCellDateFormattedWrapped(evaluated)) {
					// cell is a valid excel date format and has cell style, render according to cell style
					DataFormatter formatter = new DataFormatter();
					evaluated.setCellValue(formatter.formatCellValue(evaluated));
					break;
				}
				evaluated.setCellValue(NumberToTextConverter.toText(evaluated.getNumericCellValue()));
				break;
			case BOOLEAN:
				evaluated.setCellValue(String.valueOf(evaluated.getBooleanCellValue()));
				break;
			case FORMULA:
				CellType cachedResultType = evaluated.getCachedFormulaResultType();
				if (cachedResultType != null && cachedResultType != _NONE && cachedResultType != BLANK) {
					evaluated.setCellType(cachedResultType);
					String fallbackRes = formatResult(evaluated, cachedResultType);
					evaluated.setCellValue(fallbackRes);
					log.warn("Formula wasn't calculated for: {}, falling back to: {}", evaluated, fallbackRes);
				} else {
					log.warn("Error while calculating Excel formula: {}", evaluated);
					evaluated.setCellValue("");
				}
				break;
			case _NONE:
			case BLANK:
			case ERROR:
				evaluated.setCellValue("");
				break;
		}
		return evaluated.getStringCellValue();
	}

	private Temporal mapToLocalDateTime(Cell unformattedValue) {
		try {
			return LocalDateTime.parse(unformattedValue.toString(), dateTimeFormatter);
		} catch (Exception e1) {
			try {
				return LocalDate.parse(unformattedValue.toString(), dateFormatter);
			} catch (Exception e2) {
				log.warn("Wasn't able to format given date {} as String {} to java.util.LocalDate or LocalDateTime.",
					unformattedValue.getNumericCellValue(), unformattedValue);
			}
		}
		return null;
	}

	private boolean isCellDateFormattedWrapped(Cell cell) {
		try {
			return isCellDateFormatted(cell);
		} catch (Exception e) {
			return false;
		}
	}
}
