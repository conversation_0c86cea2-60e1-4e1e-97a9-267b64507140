package com.nexla.parser.excel;

import lombok.Getter;

import java.util.Optional;

@Getter
public class AdditionalRangeNoSheet {
    private final Optional<String> attributeCell;
    private final String valueCell;
    
    public AdditionalRangeNoSheet(String rawInput) {
        String[] attributeValueArr = rawInput.contains(":") ? rawInput.split(":") 
                : new String[] { null, rawInput };
        this.attributeCell = Optional.ofNullable(attributeValueArr[0]);
        this.valueCell = attributeValueArr[1];
    }
}
