package com.nexla.parser.schemaaware;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.fge.jackson.JsonLoader;
import com.github.fge.jsonschema.core.tree.SimpleJsonTree;
import com.nexla.admin.client.NexlaSchema;
import com.nexla.common.SchemaUtils;
import com.nexla.common.StreamUtils;
import com.nexla.parser.avro.NexlaAvro2JsonSchemaProcessor;
import lombok.SneakyThrows;
import org.apache.avro.Schema;

import java.util.Iterator;
import java.util.Map;

public class AvroSchemaExtractor {
	private static final String REF = "$ref";
	private static final String ENUM = "enum";
	private static final String PROPERTIES = "properties";
	private static final String RECORD = "record";
	private static final String TYPE = "type";
	private static final String OBJECT = "object";
	private static final String DEFINITIONS = "definitions";
	private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
	private static final TypeReference<Map<String, JsonNode>> MAP_TYPE_REFERENCE = new TypeReference<Map<String, JsonNode>>() {
	};

	public static NexlaSchema getAvroSchema(Schema schema) throws Exception {
		String avroSchema = schema.toString();
		JsonNode curSchema = JsonLoader.fromString(avroSchema);
		JsonNode jsonNode = new NexlaAvro2JsonSchemaProcessor().rawProcess(new SimpleJsonTree(curSchema)).getNode();

		JsonNode jsonWithoutRef = removeReferences(jsonNode.get(DEFINITIONS), jsonNode.get(DEFINITIONS));
		Iterator<Map.Entry<String, JsonNode>> noRefIterator = jsonWithoutRef.fields();
		jsonWithoutRef = noRefIterator.next().getValue().get(PROPERTIES);

		ObjectNode jsonWithProps = JsonNodeFactory.instance.objectNode();
		jsonWithProps.set(PROPERTIES, jsonWithoutRef);

		Map<String, Object> result = OBJECT_MAPPER.convertValue(jsonWithProps, Map.class);
		Map<String, Object> schemaMetadata = SchemaUtils.addSchemaMetadata(result);
		ObjectNode schemaJson = OBJECT_MAPPER.valueToTree(schemaMetadata);
		schemaJson.put(TYPE, OBJECT);

		return StreamUtils.jsonUtil().stringToType(schemaJson.toString(), NexlaSchema.class);
	}

	@SneakyThrows
	private static Map<String, JsonNode> getRefNode(JsonNode jsonNode, JsonNode orgNode, String refName) {
		JsonNode node = jsonNode.get(refName);
		node = removeReferences(node, orgNode);
		String nodeAsString = node.toString();
		return OBJECT_MAPPER.readValue(nodeAsString, MAP_TYPE_REFERENCE);
	}

	private static void replaceRef(JsonNode jsonNode, Map<String, JsonNode> refNode) {
		refNode.forEach(((ObjectNode) jsonNode)::set);
	}

	private static JsonNode removeReferences(JsonNode jsonNode, JsonNode orgNode) {
		if (jsonNode.isObject()) {
			Iterator<Map.Entry<String, JsonNode>> iterator = jsonNode.fields();
			while (iterator.hasNext()) {
				Map.Entry<String, JsonNode> next = iterator.next();
				if (next.getKey().equals(REF)) {
					int refInd;
					String nextVal = next.getValue().asText("");
					if (nextVal.contains(RECORD)) {
						refInd = nextVal.indexOf(RECORD);
					} else {
						refInd = nextVal.indexOf(ENUM);
					}
					Map<String, JsonNode> refNode = getRefNode(orgNode, orgNode, nextVal.substring(refInd));
					replaceRef(jsonNode, refNode);

					((ObjectNode) jsonNode).remove(REF);
				}
				removeReferences(next.getValue(), orgNode);
			}
		} else if (jsonNode.isArray()) {
			for (JsonNode nextNode : jsonNode) {
				removeReferences(nextNode, orgNode);
			}
		}
		return jsonNode;
	}
}
