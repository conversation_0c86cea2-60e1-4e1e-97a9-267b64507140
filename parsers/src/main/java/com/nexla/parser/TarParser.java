package com.nexla.parser;

import com.nexla.common.NexlaMessage;
import com.nexla.common.exception.ParseError;
import com.nexla.common.parse.NexlaParser;
import com.nexla.parser.ParserUtilsExt.Result;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.io.FileUtils;
//import org.apache.tika.utils.ExceptionUtils;
import org.javatuples.Pair;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.parser.ParserUtilsExt.detectParser;

public class TarParser extends NexlaParser {
    
    @Override
    @SneakyThrows
    public StreamEx<Optional<NexlaMessage>> parseMessages(InputStream inputStream) {
        File tempDir = untarFilesToTempDir(inputStream);
        List<File> untarredFilesList = listFilesInDir(tempDir);

        StreamEx<Optional<NexlaMessage>> nexlaMessagesStream =  StreamEx.of(untarredFilesList)
                .map(file -> {
                    logger().info("Parsing inner file in TAR file: {}", file.getName());
                    return parseWithChildParser(file);
                })
                .map(parsedStream -> {
                    List<Pair<Optional<NexlaMessage>, String>> pairList = Collections.emptyList();
                    try {
                        pairList = parsedStream.toList();
                    } catch (Exception e) {
//                        logger().info("Unable to process underlying child file. Error occurred: {}", ExceptionUtils.getStackTrace(e));
                    } 
                    return pairList;
                })
                .flatMap(List::stream)
                .map(msgPair -> {
                    msgPair.getValue0().get().getNexlaMetaData().setSourceKey(msgPair.getValue1());
                    return msgPair.getValue0();
                });
        return nexlaMessagesStream;
    }
    
    @SneakyThrows
    private File untarFilesToTempDir(InputStream tarInputStream) {
        File tempTarFile = File.createTempFile("origin",".tar");
        tempTarFile.deleteOnExit();
        FileUtils.copyInputStreamToFile(tarInputStream, tempTarFile);
        File tempDir = com.google.common.io.Files.createTempDir();
        tempDir.deleteOnExit();
        new TarArchive().untar(tempTarFile, tempDir, false);
        return tempDir;
    }
    
    @SneakyThrows
    private StreamEx<Pair<Optional<NexlaMessage>, String>> parseWithChildParser(File file) {
        String childFileName = file.getName();
        InputStream tarInputStream = FileUtils.openInputStream(file);
        Result detection = detectParser(Optional.empty(), tarInputStream, Collections.emptyMap(),
                config, file.getName(), exceptionHandler, logger());

        detection.message().foreach(message -> fileParsingLog.put(childFileName, message));
        return detection.parser()
                .map(parser -> parser
                        .parseMessages(detection.restoredStream())
                        .onClose(() -> closeSilently(tarInputStream)))
                .map(optionalStreamEx -> optionalStreamEx
                        .map(msgOpt -> new Pair(msgOpt, childFileName))
                )
                .getOrElse(StreamEx::empty);
    }
    
    @SneakyThrows
    private List<File> listFilesInDir(File dirFile) {
        List<File> fList = Files.find(Paths.get(dirFile.getAbsolutePath()),
                        Integer.MAX_VALUE,
                        (filePath, fileAttr) -> fileAttr.isRegularFile())
                .map(path -> path.toFile())
                .collect(Collectors.toList());
        return fList;
    }

    @Override
    public TarParser exceptionHandler(BiConsumer<ParseError, Exception> exceptionHandler) {
        this.exceptionHandler = exceptionHandler;
        return this;
    }

}