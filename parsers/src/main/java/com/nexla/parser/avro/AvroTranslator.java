package com.nexla.parser.avro;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.github.fge.avro.MutableTree;
import com.github.fge.jackson.JacksonUtils;
import org.apache.avro.Schema;

public abstract class AvroTranslator {
	protected static final JsonNodeFactory FACTORY = JacksonUtils.nodeFactory();

	public AvroTranslator() {
	}

	public abstract void translate(Schema var1, MutableTree var2);
}
