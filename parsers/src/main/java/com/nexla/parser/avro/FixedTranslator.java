package com.nexla.parser.avro;

import com.github.fge.avro.MutableTree;
import com.github.fge.jackson.NodeType;
import org.apache.avro.Schema;

public class FixedTranslator extends NamedAvroTypeTranslator {
	private static final String BYTES_PATTERN = "^[\u0000-ÿ]*$";
	private static final AvroTranslator INSTANCE = new FixedTranslator();

	private FixedTranslator() {
		super(Schema.Type.FIXED);
	}

	public static AvroTranslator getInstance() {
		return INSTANCE;
	}

	protected void doTranslate(Schema avroSchema, MutableTree jsonSchema) {
		int size = avroSchema.getFixedSize();
		jsonSchema.setType(NodeType.STRING);
		jsonSchema.getCurrentNode().put("pattern", "^[\u0000-ÿ]*$").put("minLength", size).put("maxLength", size);
	}
}
