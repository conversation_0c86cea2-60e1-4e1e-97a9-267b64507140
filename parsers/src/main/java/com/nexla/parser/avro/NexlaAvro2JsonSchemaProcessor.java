package com.nexla.parser.avro;

import com.fasterxml.jackson.databind.JsonNode;
import com.github.fge.avro.IllegalAvroSchemaException;
import com.github.fge.avro.MutableTree;
import com.github.fge.jsonschema.core.exceptions.ProcessingException;
import com.github.fge.jsonschema.core.tree.CanonicalSchemaTree;
import com.github.fge.jsonschema.core.tree.JsonTree;
import com.github.fge.jsonschema.core.tree.SchemaTree;
import org.apache.avro.AvroRuntimeException;
import org.apache.avro.Schema;

/**
 * Custom class for {{@link com.github.fge.avro.Avro2JsonSchemaProcessor}} to remove org.codehaus.jackson vulnerability.
 * All classes in this avro package are part of the implementation that is necessary to convert avro schema to json schema
 * The vulnerability was in the RecordTranslator class and as this library doesn't have support anymore, the best to do
 * was to create a custom implementation on top of it.
 */

public class NexlaAvro2JsonSchemaProcessor {

	public NexlaAvro2JsonSchemaProcessor() {
	}

	public SchemaTree rawProcess(JsonTree input) throws ProcessingException {
		JsonNode node = input.getBaseNode();

		Schema avroSchema;
		try {
			String s = node.toString();
			avroSchema = (new Schema.Parser()).parse(s);
		} catch (AvroRuntimeException var7) {
			throw new IllegalAvroSchemaException(var7);
		}

		MutableTree tree = new MutableTree();
		Schema.Type avroType = avroSchema.getType();

		AvroTranslators.getTranslator(avroType).translate(avroSchema, tree);

		return new CanonicalSchemaTree(tree.getBaseNode());
	}

}