package com.nexla.parser.avro;

import com.github.fge.avro.MutableTree;
import com.github.fge.jackson.NodeType;
import org.apache.avro.Schema;

public final class SimpleTypeTranslator extends AvroTranslator {
	private final NodeType type;

	SimpleTypeTranslator(NodeType type) {
		this.type = type;
	}

	public void translate(Schema avroSchema, MutableTree jsonSchema){
		jsonSchema.setType(this.type);
	}
}
