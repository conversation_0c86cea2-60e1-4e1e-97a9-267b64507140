package com.nexla.parser.avro;

import com.github.fge.avro.MutableTree;
import com.github.fge.jackson.NodeType;
import org.apache.avro.Schema;

public class IntTranslator extends AvroTranslator {

	private static final AvroTranslator INSTANCE = new IntTranslator();

	private IntTranslator() {
	}

	static AvroTranslator getInstance() {
		return INSTANCE;
	}

	public void translate(Schema avroSchema, MutableTree jsonSchema) {
		jsonSchema.setType(NodeType.INTEGER);
		jsonSchema.getCurrentNode().put("minimum", Integer.MIN_VALUE).put("maximum", Integer.MAX_VALUE);
	}
}
