package com.nexla.parser.avro;

import com.github.fge.avro.MutableTree;
import com.github.fge.jackson.NodeType;
import org.apache.avro.Schema;

public class ByteTranslator extends AvroTranslator {

	private static final String BYTES_PATTERN = "^[\u0000-ÿ]*$";
	private static final AvroTranslator INSTANCE = new ByteTranslator();

	private ByteTranslator() {
	}

	public static AvroTranslator getInstance() {
		return INSTANCE;
	}

	public void translate(Schema avroSchema, MutableTree jsonSchema) {
		jsonSchema.setType(NodeType.STRING);
		jsonSchema.getCurrentNode().put("pattern", "^[\u0000-ÿ]*$");
	}
}
