package com.nexla.parser;

import com.nexla.common.io.CloseableInputStream;
import com.nexla.connector.config.FileEncryptConfig;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;

public class ASCEnryptUtils {

	private static final Logger log = LoggerFactory.getLogger(ASCEnryptUtils.class);
	private static final String PGP_EXTENSION = ".asc";

	public static EncryptEngine getEncryptionEngine(FileEncryptConfig config) {
		log.debug("Creating encryption engine for standard: {}", config.encryptStandard.get());

		switch (config.encryptStandard.get()) {
			case PGP:
				EncryptEngine engine = new PGPEngine();
				// Handle optional public key for decryption-only mode
				String publicKey = config.externalPublicKey.orElse("");
				log.debug("Initializing PGP engine with public key length: {}", publicKey.length());
				engine.init(config.privateKey.get(), publicKey, config.privatePassword.get(), config.encryptUserId.get(), config.externalUserId.get());

				((PGPEngine) engine)
					.initHashAlg(config.dataHashAlgorithm)
					.initEncryptAlg(config.dataEncryptionAlgorithm)
					.initCompressionAlg(config.dataCompressionAlgorithm);

				log.debug("PGP engine created successfully");
				return engine;
			default:
				throw new IllegalArgumentException("No such encryption algorithm");
		}
	}

	@SneakyThrows
	public static String encrypt(String outputPath, FileEncryptConfig fileEncryptConfig) {
		log.info("Encrypting file: {}", outputPath);

		EncryptEngine encryptEngine = getEncryptionEngine(fileEncryptConfig);
		String encryptedFileName = outputPath + PGP_EXTENSION;
		try (
			ByteArrayOutputStream encryptedStream = encryptEngine.encrypt(new FileInputStream(outputPath));
			OutputStream outputStream = new FileOutputStream(encryptedFileName)
		) {
			encryptedStream.writeTo(outputStream);
			log.info("File encrypted successfully: {}", encryptedFileName);
		}
		return encryptedFileName;
	}

	@SneakyThrows
	public static InputStream decrypt(InputStream inputStream, FileEncryptConfig fileEncryptConfig) {
		log.debug("Starting decryption of input stream");

		EncryptEngine encryptEngine = getEncryptionEngine(fileEncryptConfig);
		InputStream in = encryptEngine.readEncrypted(inputStream);

		File tempEncFile = Files.createTempFile("", "").toFile();
		try (FileOutputStream fileWriter = new FileOutputStream(tempEncFile)) {
			IOUtils.copyLarge(in, fileWriter);
		}
		log.debug("Decryption completed, temporary file size: {} bytes", tempEncFile.length());
		return new CloseableInputStream(new FileInputStream(tempEncFile)).onClose(() -> tempEncFile.delete());
	}

	/**
	 * Decrypts the input file and replaces it
	 */
	@SneakyThrows
	public static void decrypt(File inputFile, FileEncryptConfig fileEncryptConfig) {
		log.info("Decrypting file: {}", inputFile.getAbsolutePath());

		EncryptEngine encryptEngine = getEncryptionEngine(fileEncryptConfig);
		File tempFile = Files.createTempFile("", "").toFile();

		// Step 1. Decrypt file
		try (BufferedInputStream inputStream = new BufferedInputStream(Files.newInputStream(inputFile.toPath()));
			 InputStream decryptedInputStream = encryptEngine.readEncrypted(inputStream);
			 FileOutputStream fileWriter = new FileOutputStream(tempFile)) {
			IOUtils.copyLarge(decryptedInputStream, fileWriter);
		}
		log.debug("Decryption completed, temporary file size: {} bytes", tempFile.length());
		
		// Step 2. Replace the origin file
		if (!inputFile.delete() || !tempFile.renameTo(inputFile)) {
			log.error("Failed to replace original file with decrypted version");
			throw new IOException("Failed to persist encrypted file");
		}
		log.info("File decrypted and replaced successfully: {}", inputFile.getAbsolutePath());
	}
}
