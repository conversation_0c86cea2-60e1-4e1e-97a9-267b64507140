package com.nexla.parser;

import com.nexla.common.NexlaMessage;
import com.nexla.common.exception.ParseError;
import com.nexla.common.parse.NexlaParser;
import com.nexla.connector.config.FileEncryptConfig;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.parser.ParserUtilsExt.Result;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.zip.GZIPInputStream;

import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.parser.ParserUtilsExt.detectParser;
import static org.apache.commons.io.FilenameUtils.getExtension;
import static org.apache.commons.lang3.StringUtils.removeEnd;

public class AscParser extends NexlaParser {

	private final String innerFileName;
	private final Map<String, String> overriddenExtensions;
	private final FileEncryptConfig fileEncryptConfig;

	public AscParser(String fileNameOrFormat, FileEncryptConfig fileEncryptConfig, Map<String, String> overriddenExtensions) {
		String extension = Optional.ofNullable(getExtension(fileNameOrFormat)).orElse("");
		this.innerFileName = removeEnd(fileNameOrFormat, "." + extension);
		this.overriddenExtensions = overriddenExtensions;
		this.fileEncryptConfig = fileEncryptConfig;
	}

	@Override
	public StreamEx<Optional<NexlaMessage>> parseMessages(InputStream inputStream) {

		InputStream decrypted = ASCEnryptUtils.decrypt(inputStream, fileEncryptConfig);

		Result detection =
			detectParser(Optional.empty(), decrypted, overriddenExtensions, config, innerFileName, exceptionHandler, logger());

		detection.message().foreach(message -> fileParsingLog.put(innerFileName, message));

		return detection.parser()
			.map(parser -> parser
				.parseMessages(detection.restoredStream())
				.onClose(() -> closeSilently(decrypted)))
			.getOrElse(StreamEx::empty);
	}

	@Override
	public AscParser exceptionHandler(BiConsumer<ParseError, Exception> exceptionHandler) {
		this.exceptionHandler = exceptionHandler;
		return this;
	}

	@Override
	public StreamEx<String> readLines(InputStream inputStream) {
		InputStream decrypted = ASCEnryptUtils.decrypt(inputStream, fileEncryptConfig);
		return super.readLines(decrypted);
	}

	private GZIPInputStream toGzipInputStream(InputStream inputStream) {
		try {
			return new GZIPInputStream(inputStream);
		} catch (IOException e) {
			logger().error("Error while opening GZIP:", e);
			throw new RuntimeException(e);
		}
	}

}