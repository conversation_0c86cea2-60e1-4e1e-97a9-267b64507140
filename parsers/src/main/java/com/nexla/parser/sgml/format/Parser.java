package com.nexla.parser.sgml.format;

import lombok.Data;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class Parser {
    private static final Pattern ELEM = Pattern.compile("<[^/].+?>");

    private final String data;
    private final List<String> omitDocumentTypes;

    public Parser(String document, List<String> omitDocumentTypes) {
        this.data = document;
        this.omitDocumentTypes = omitDocumentTypes;
    }

    private static boolean eq(Format.Elem e, String tag) {
        return e.getName().equalsIgnoreCase(tag);
    }

    private static <T> Optional<T> opt() {
        return Optional.empty();
    }

    public SecDocument parse() {
        Segment root = next(data);
        if (root == null || !eq(Format.SEC_DOCUMENT, root.tag)) {
            return null;
        }

        Segment header = next(root.data);
        if (header == null || !eq(Format.SEC_HEADER, header.tag)) {
            return null;
        }

        return SecDocument.builder()
                .header(parseHeader(header.data))
                .documents(parseDocuments(data.substring(data.indexOf(header.tag) + header.data.length())))
                .build();
    }

    private SecDocument.Header parseHeader(String data) {
        return null;
    }

    private List<SecDocument.Document> parseDocuments(String data) {
        if (data.isBlank()) {
            return Collections.emptyList();
        }

        final List<SecDocument.Document> objects = new ArrayList<>();

        String rest = data;
        Segment document = null;
        while ((document = next(rest)) != null) {
            parseDocument(document.data)
                    .ifPresent(objects::add);

            int start = rest.indexOf(Format.DOCUMENT.getName());
            int len = Format.DOCUMENT.getName().length() + document.data.length() + Format.DOCUMENT.getClosingTag().length();

            rest = rest.substring(start + len);
        }

        return objects;
    }

    private Optional<SecDocument.Document> parseDocument(String data) {
        SecDocument.Document.DocumentBuilder builder = SecDocument.Document.builder();

        String rest = data;
        Segment descriptor = null;
        while ((descriptor = next(rest)) != null) {
            if (eq(Format.DOC_TYPE, descriptor.tag)) {
                if (omitDocumentTypes.contains(descriptor.getData().trim())) {
                    return Optional.empty();
                }

                builder.type(descriptor.getData().trim());
            } else if (eq(Format.SEQUENCE, descriptor.tag)) {
                builder.sequence(descriptor.getData().trim());
            } else if (eq(Format.FILENAME, descriptor.tag)) {
                builder.filename(descriptor.getData().trim());
            } else if (eq(Format.DESCRIPTION, descriptor.tag)) {
                builder.description(descriptor.getData().trim());
            } else if (eq(Format.DOC_TEXT, descriptor.tag)) {
                Segment format = next(descriptor.getData().trim());

                if (format != null) {
                    builder.format(format.tag.replaceAll("[<>]", ""));
                    builder.source(format.getData().trim());
                } else {
                    builder.source(descriptor.getData().trim());
                }
            }

            int len = rest.indexOf(descriptor.tag) + descriptor.tag.length() + descriptor.data.length();

            rest = rest.substring(len).trim();
        }

        return Optional.of(builder.build());
    }

    private Segment next(String data) {
        return nextElem(data)
                .filter(Format.SCHEMA::containsKey)
                .map(tag -> {
                    int valueStartPos = data.indexOf(tag) + tag.length();
                    if (!Format.SCHEMA.get(tag).hasClosingTag) {
                        int nextTagStart = nextElem(data.substring(valueStartPos))
                                .map(data::indexOf)
                                .orElse(data.length());

                        return new Segment(tag, data.substring(valueStartPos, nextTagStart).strip());
                    } else {
                        int endTagStart = data.indexOf(Format.SCHEMA.get(tag).getClosingTag(), valueStartPos);

                        return new Segment(tag, data.substring(valueStartPos, endTagStart));
                    }

                })
                .orElse(null);
    }

    private Optional<String> nextElem(String data) {
        Matcher matcher = ELEM.matcher(data);
        if (matcher.find()) {
            return Optional.of(matcher.group());
        }
        return Optional.empty();
    }

    @Data
    private static class Segment {
        private final String tag;
        private final String data;
    }

    private static final class Format {
        public static final Elem SEC_DOCUMENT = new Elem("<SEC-DOCUMENT>", true, false, null);
        public static final Elem SEC_HEADER = new Elem("<SEC-HEADER>", true, false, SEC_DOCUMENT);
        public static final Elem ACCEPTANCE_DATETIME = new Elem("<ACCEPTANCE-DATETIME>", false, false, SEC_HEADER);
        public static final Elem PRIVATE_TO_PUBLIC = new Elem("<PRIVATE-TO-PUBLIC>", false, false, SEC_HEADER);
        public static final Elem PUBLIC_REL_DATE = new Elem("<PUBLIC-REL-DATE>", false, false, SEC_HEADER);
        public static final Elem DOCUMENT = new Elem("<DOCUMENT>", true, true, SEC_DOCUMENT);
        public static final Elem DOC_TYPE = new Elem("<TYPE>", false, false, DOCUMENT);
        public static final Elem SEQUENCE = new Elem("<SEQUENCE>", false, false, DOCUMENT);
        public static final Elem FILENAME = new Elem("<FILENAME>", false, false, DOCUMENT);
        public static final Elem DESCRIPTION = new Elem("<DESCRIPTION>", false, false, DOCUMENT);
        public static final Elem DOC_TEXT = new Elem("<TEXT>", true, false, DOCUMENT);
        public static final Elem PDF = new Elem("<PDF>", true, false, DOC_TEXT);
        public static final Elem XML = new Elem("<XML>", true, false, DOC_TEXT);
        public static final Elem XBRL = new Elem("<XBRL>", true, false, DOC_TEXT);
        public static final Elem TABLE = new Elem("<TABLE>", true, false, DOC_TEXT);
        public static final Elem CAPTION = new Elem("<CAPTION>", false, false, DOC_TEXT);
        public static final Elem STUB = new Elem("<S>", false, false, DOC_TEXT);
        public static final Elem COLUMN = new Elem("<C>", false, false, DOC_TEXT);
        public static final Elem FOOTNOTES_SECTION = new Elem("<FN>", false, false, DOC_TEXT);

        public static final List<Elem> ELEMS = List.of(
                SEC_DOCUMENT,
                SEC_HEADER,
                ACCEPTANCE_DATETIME,
                PRIVATE_TO_PUBLIC,
                PUBLIC_REL_DATE,
                DOCUMENT,
                DOC_TYPE,
                SEQUENCE,
                FILENAME,
                DESCRIPTION,
                DOC_TEXT,
                PDF,
                XML,
                XBRL,
                TABLE,
                CAPTION,
                STUB,
                COLUMN,
                FOOTNOTES_SECTION
        );

        public static final Map<String, Elem> SCHEMA = ELEMS
                .stream()
                .collect(Collectors.toMap(Elem::getName, Function.identity()));

        public static List<Elem> childrenOf(String t) {
            return ELEMS
                    .stream()
                    .filter(e -> e.parent != null && e.parent.getName().equals(t))
                    .collect(Collectors.toList());
        }

        @Data
        private static final class Elem {
            private final String name;
            private final boolean hasClosingTag;
            private final boolean repeats;
            private final Elem parent;

            public String getClosingTag() {
                return this.name.replace("<", "</");
            }
        }
    }

}
