package com.nexla.parser.sgml.format;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class SecDocument {
    private final Header header;
    private List<Document> documents;

    @Data
    @Builder
    static
    class Header {
        private final String acceptanceDatetime;
        private final String privateToPublic;
        private final String publicRelDate;
    }

    @Data
    @Builder
    public static class Document {
        private final String filename;
        private final String type;
        private final String sequence;
        private final String description;
        private final String format;
        private final String source;
    }
}
