package com.nexla.parser;

import lombok.SneakyThrows;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.zip.GZIPInputStream;

public class TarArchive {

	@SneakyThrows
	public void untar(File tarFile, File destDir, boolean gzipped) {
		InputStream inputStream = null;
		try {
			if (gzipped) {
				inputStream = new BufferedInputStream(new GZIPInputStream(new FileInputStream(tarFile)));
			} else {
				inputStream = new BufferedInputStream(new FileInputStream(tarFile));
			}
			try (TarArchiveInputStream tis = new TarArchiveInputStream(inputStream)) {
				for (TarArchiveEntry entry = tis.getNextTarEntry(); entry != null; ) {
					unpackEntries(tis, entry, destDir);
					entry = tis.getNextTarEntry();
				}
			}
		} finally {
			if (inputStream != null) {
				inputStream.close();
			}
		}
	}

	@SneakyThrows
	private void unpackEntries(TarArchiveInputStream tis,
							   TarArchiveEntry entry,
							   File outputDir) {
		File target = new File(outputDir, entry.getName());
		if (entry.isDirectory()) {
			target.mkdirs();
			for (TarArchiveEntry e : entry.getDirectoryEntries()) {
				unpackEntries(tis, e, target);
			}
		} else if (entry.isFile()) {
			try (BufferedOutputStream outputStream = new BufferedOutputStream(new FileOutputStream(target))) {
				IOUtils.copy(tis, outputStream);
			}
		}
	}

}
