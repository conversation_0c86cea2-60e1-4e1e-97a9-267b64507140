package com.nexla.parser;

import com.fasterxml.jackson.databind.MappingIterator;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvParser;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.nexla.common.NexlaMessage;
import com.nexla.common.exception.ExceptionIterator;
import com.nexla.common.exception.ParseError;
import com.nexla.common.parse.FilePropertiesDetector;
import com.nexla.common.parse.NexlaParser;
import com.nexla.util.TypeDeducingStatefulFunction;
import lombok.SneakyThrows;
import lombok.val;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.io.input.BOMInputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.nexla.common.ConfigUtils.optInt;
import static com.nexla.common.FileUtils.*;
import static com.nexla.common.StreamUtils.*;
import static com.nexla.common.parse.FilePropertiesDetector.CsvCharacteristics;
import static com.nexla.common.parse.ParserConfigs.Csv.*;
import static java.util.Arrays.asList;
import static java.util.Collections.singletonMap;
import static java.util.Optional.*;
import static org.apache.commons.lang3.StringUtils.*;
import static org.joor.Reflect.on;

public class DelimitedTextParser extends NexlaParser {

	static class Params {
		boolean scalarCoercion = false;
		Boolean useDetectedDelimiter = false;
		List<String> configuredSchema;
		String schemaDetection = HEADER;
		boolean csvColumnNumberValidation;

		Optional<String> delimiter = empty();
		Optional<Character> escapeChar = of('\\');
		Optional<Character> quote = empty();
		boolean enableQuoteDetection = true;

		// removing quotes as additional step after parsing
		// needed for case where it is necessary to disable quotes for correct parsing like

		// ID,CODE
		// "1","VACUUM" PACK

		// desired output is
		//
		// 1,"VACUUM" PACK

		// but if quote is enable, parser will treat end of "VACUUM" as end of the column and fail to find column separator ','
		boolean quoteRemoveForced = false;

		boolean quoteDisabled = false;

		Optional<Integer> skipFirstLines = empty();
		Optional<Integer> skipLastLines = empty();
		
		boolean includeSkippedLines = false;
	}

	public Charset usedCharset;

	public static final Map<Character, Map<String, String>> DEFAULT_OPTIONS = new HashMap<>();
	public static final Map<Character, Map<String, Object>> WRITER_DEFAULT_OPTIONS = new HashMap<>();
	
	private static final String NX_SKIPPED_LINES = "nx_skipped_lines";

	static {
		DEFAULT_OPTIONS.put(';',
			map(
				CSV_DELIMITER, ";",
				CSV_QUOTE_CHAR, "\""
			));

		DEFAULT_OPTIONS.put(',',
			map(
				CSV_DELIMITER, ",",
				CSV_QUOTE_CHAR, "\""
			));

		DEFAULT_OPTIONS.put('\t',
			map(
				CSV_DELIMITER, "\t",
				CSV_QUOTE_CHAR, null
			));

		DEFAULT_OPTIONS.put('|',
			map(
				CSV_DELIMITER, "|",
				CSV_QUOTE_CHAR, null
			));

		WRITER_DEFAULT_OPTIONS.put(',',
			map(
				CSV_DELIMITER, ",",
				CSV_QUOTE_CHAR, "\""
			));

		WRITER_DEFAULT_OPTIONS.put('\t',
			map(
				CSV_DELIMITER, "\t",
				CSV_QUOTE_CHAR, null
			));

		WRITER_DEFAULT_OPTIONS.put('|',
			map(
				CSV_DELIMITER, "|",
				CSV_QUOTE_CHAR, null
			));
	}

	final Params parserConfig = new Params();

	public DelimitedTextParser() {
	}

	public DelimitedTextParser option(String key, String value) {
		switch (key) {
			case CSV_DELIMITER:
				parserConfig.delimiter = ofNullable(value);
				break;
			case CSV_SCHEMA_DETECTION:
				parserConfig.schemaDetection = value;
				break;
			case CSV_SCALAR_COERCION:
				parserConfig.scalarCoercion = !trimToEmpty(value).isEmpty() && Boolean.parseBoolean(value);;
				break;
			case CSV_SCHEMA:
				parserConfig.configuredSchema = asList(value.split(","));
				break;
			case CSV_QUOTE_CHAR:
				parserConfig.quote = ofNullable(trimToNull(value)).map(a -> a.charAt(0));
				break;
			case CSV_VALIDATE_NUMBER_OF_COLUMNS:
				parserConfig.csvColumnNumberValidation = trimToEmpty(value).isEmpty() ? false : Boolean.valueOf(value);
				break;
			case CSV_ENABLE_QUOTE_DETECTION:
				parserConfig.enableQuoteDetection = Boolean.valueOf(value);
				break;
			case CSV_SKIP_FIRST_LINES:
				parserConfig.skipFirstLines = optInt(value).filter(lines -> lines > 0);
				break;
			case CSV_SKIP_LAST_LINES:
				parserConfig.skipLastLines = optInt(value).filter(lines -> lines > 0);
				break;
			case CSV_QUOTE_DISABLED:
				parserConfig.quoteDisabled = Boolean.valueOf(value);
				break;
			case CSV_QUOTE_REMOVE_FORCED:
				parserConfig.quoteRemoveForced = Boolean.valueOf(value);
				break;
			case CSV_ESCAPE_CHAR:
				parserConfig.escapeChar = ofNullable(trimToNull(value)).map(a -> a.charAt(0));
				break;
			case CSV_USE_DETECTED_DELIMITER:
				parserConfig.useDetectedDelimiter = Boolean.valueOf(value);
				break;
			case CSV_INCLUDE_SKIPPED_LINES:
				parserConfig.includeSkippedLines = Boolean.valueOf(value);
				break;
			default:
				super.option(key, value);
		}
		return this;
	}

	@Override
	@SneakyThrows
	public StreamEx<Optional<NexlaMessage>> parseMessages(InputStream inputStream) {
		DelimitedTextReadResult recordStream = readMessagesAndSchema(inputStream);
		return parserConfig.skipLastLines
			.map(lines -> skipLastElements(recordStream.getResult(), lines))
			.orElse(recordStream.getResult());
	}

	public DelimitedTextReadResult readMessagesAndSchema(InputStream inputStream) {
		byte[] sample = FilePropertiesDetector.readSample(inputStream);
		FilePropertiesDetector detector = new FilePropertiesDetector(sample, formatDetectionMaxLines);
		AtomicLong lineNumber = new AtomicLong();
		Charset charset = detector.detectCharset(defaultCharset, charsetDetectionConfidenceThreshold);
		InputStream restoredStream = new BOMInputStream(joinStream(sample, inputStream));
		BufferedReader reader = new BufferedReader(new InputStreamReader(restoredStream, charset));

		this.usedCharset = detector.getDetectedCharset();
		Optional<String> skippedLinesOpt = parserConfig.skipFirstLines.map(
				linesToSkip -> skipFirstLines(reader, linesToSkip, parserConfig.includeSkippedLines)
		).filter(Predicate.not(String::isBlank));

		// no delimiter, extracting messages in format { value : [line] }
		if (parserConfig.delimiter.isPresent() && "".equals(parserConfig.delimiter.get())) {
			StreamEx<Optional<NexlaMessage>> emptyResult = StreamEx.of(reader.lines())
				.map(line -> of(new NexlaMessage(new LinkedHashMap<>(singletonMap("value", line)))));
			return new DelimitedTextReadResult(emptyResult, Arrays.asList("value"));
		}

		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		CsvCharacteristics detected = detector.detectTextDelimiter();
		logger().info("FILE CHARACTERISTICS DETECTION TIME={}", stopWatch.toString());

		CsvMapper mapper = new CsvMapper().enable(CsvParser.Feature.TRIM_SPACES);

		char delimiter;
		// by default false, as it is old behaviour
		if (parserConfig.useDetectedDelimiter) {
			delimiter = ofNullable(detected.delimiter).or(() -> parserConfig.delimiter.map(d -> d.charAt(0))).get();
		} else {
			delimiter = parserConfig.delimiter.map(d -> d.charAt(0)).orElse(detected.delimiter);
		}

		CsvSchema.Builder schemaBuilder = CsvSchema.builder()
			.setColumnSeparator(delimiter);

		char quoteChar = parserConfig.quote
			.orElseGet(() -> detected.quote
				.orElseGet(() -> ofNullable(DEFAULT_OPTIONS.get(delimiter))
					.map(m -> m.get(CSV_QUOTE_CHAR))
					.map(v -> v.charAt(0))
					.orElse('\"')));

		if (parserConfig.quoteDisabled) {
			schemaBuilder.disableQuoteChar();
		} else if ((parserConfig.quote.isPresent() || parserConfig.enableQuoteDetection && detected.quote.isPresent())) {
			schemaBuilder.setQuoteChar(quoteChar);
		} else {
			schemaBuilder.disableQuoteChar();
		}

		parserConfig.escapeChar.ifPresent(schemaBuilder::setEscapeChar);

		DelimitedTextReadResult parseToRecordStreamResult = parseToRecordStream(lineNumber, reader, mapper, schemaBuilder, quoteChar);
		
		if (skippedLinesOpt.isPresent()) {
			StreamEx<Optional<NexlaMessage>> parseToRecordStreamResultStr = StreamEx.of(parseToRecordStreamResult.getResult()
					.filter(Optional::isPresent)
					.map(originalMsgOpt -> {
						originalMsgOpt.get().getRawMessage().put(NX_SKIPPED_LINES, skippedLinesOpt.get());
						return originalMsgOpt;
					}).collect(Collectors.toList()));
			parseToRecordStreamResult = new DelimitedTextReadResult(parseToRecordStreamResultStr, parseToRecordStreamResult.getColumns());
		}

		return parseToRecordStreamResult;
	}

	@SneakyThrows
	private DelimitedTextReadResult parseToRecordStream(
		AtomicLong lineNumber,
		BufferedReader reader,
		CsvMapper mapper,
		CsvSchema.Builder schemaBuilder,
		char quoteChar
	) {
		val scalarMappingFn = mapToScalarsIfNeeded();
		switch (parserConfig.schemaDetection) {

			case HEADER: {
				CsvSchema schema = schemaBuilder.setUseHeader(true).build();
				MappingIterator<LinkedHashMap<String, Object>> iterator = mapper.readerFor(Map.class).with(schema).readValues(reader);
				CsvSchema.Column[] columns = on(iterator.getParserSchema()).get("_columns");
				StreamEx<Optional<LinkedHashMap<String, Object>>> stream = StreamEx.of(new ExceptionIterator<>(iterator, exceptionHandler))
					.peek(m -> lineNumber.incrementAndGet())
					.map(v -> validateLine(v, columns.length, lineNumber, exceptionHandler));

				StreamEx<Optional<NexlaMessage>> result = removeQuotesIfNecessary(quoteChar, stream)
					.map(v -> v.map(scalarMappingFn))
					.map(v -> v.map(NexlaMessage::new))
					.onClose(() -> closeSilently(iterator));

				List<String> columnNames = StreamEx.of(columns)
					.map(CsvSchema.Column::getName)
					.toList();

				return new DelimitedTextReadResult(result, columnNames);
			}

			case GENERATED: {
				mapper.enable(CsvParser.Feature.WRAP_AS_ARRAY);
				CsvSchema schema = schemaBuilder.setUseHeader(false).build();
				MappingIterator<String[]> iterator = mapper.readerFor(String[].class).with(schema).readValues(reader);

				// number of columns can vary from row to row as there is no schema specified
				StreamEx<Optional<LinkedHashMap<String, Object>>> stream = StreamEx
					.of(new ExceptionIterator<>(iterator, exceptionHandler))
					.map(this::validateLine)
					.map(v -> v.map(this::generateAttrMap));

				CsvSchema.Column[] columns = on(iterator.getParserSchema()).get("_columns");
				List<String> columnNames = StreamEx.of(columns)
					.map(CsvSchema.Column::getName)
					.toList();

				StreamEx<Optional<NexlaMessage>> result = removeQuotesIfNecessary(quoteChar, stream)
					.map(v -> v.map(scalarMappingFn))
					.map(v -> v.map(NexlaMessage::new))
					.onClose(() -> closeSilently(iterator));

				return new DelimitedTextReadResult(result, columnNames);
			}

			case CONFIGURED: {
				parserConfig.configuredSchema.forEach(schemaBuilder::addColumn);
				CsvSchema schema = schemaBuilder.setUseHeader(false).build();
				MappingIterator<LinkedHashMap<String, Object>> iterator = mapper.readerFor(Map.class).with(schema).readValues(reader);

				int columnCount = parserConfig.configuredSchema.size();
				StreamEx<Optional<LinkedHashMap<String, Object>>> stream = StreamEx
					.of(new ExceptionIterator<>(iterator, exceptionHandler))
					.peek(m -> lineNumber.incrementAndGet())
					.map(v -> validateLine(v, columnCount, lineNumber, exceptionHandler));

				CsvSchema.Column[] columns = on(iterator.getParserSchema()).get("_columns");
				List<String> columnNames = StreamEx.of(columns)
					.map(CsvSchema.Column::getName)
					.toList();

				StreamEx<Optional<NexlaMessage>> result = removeQuotesIfNecessary(quoteChar, stream)
					.map(v -> v.map(scalarMappingFn))
					.map(v -> v.map(NexlaMessage::new))
					.onClose(() -> closeSilently(iterator));

				return new DelimitedTextReadResult(result, columnNames);
			}

			default:
				throw new IllegalArgumentException();
		}
	}

	private Function<LinkedHashMap<String, Object>, LinkedHashMap<String, Object>> mapToScalarsIfNeeded() {
		return parserConfig.scalarCoercion
				? new TypeDeducingStatefulFunction()
				: Function.identity();
	}

	private StreamEx<Optional<LinkedHashMap<String, Object>>> removeQuotesIfNecessary(
		char quoteChar, StreamEx<Optional<LinkedHashMap<String, Object>>> stream
	) {
		return parserConfig.quoteRemoveForced
			? stream.map(v -> removeQuotes(v, quoteChar))
			: stream;
	}

	private Optional<LinkedHashMap<String, Object>> removeQuotes(
		Optional<LinkedHashMap<String, Object>> objectMap, char quoteChar
	) {
		return objectMap
			.map(EntryStream::of)
			.map(es -> toLinkedHashMap(es.mapValues(value -> {
				if (value == null) {
					return null;
				} else {
					String strValue = value.toString();
					if (isSafeToRemoveQuotes(strValue, quoteChar)) {
						return strValue.substring(1, strValue.length() - 1);
					} else {
						return value;
					}
				}
			})));
	}

	public static boolean isSafeToRemoveQuotes(String strValue, char quoteChar) {
		if (strValue.isEmpty()) {
			return false;
		} else {
			// starts and ends with quoteChar
			if (strValue.length() >= 2 &&
				strValue.charAt(0) == quoteChar &&
				strValue.charAt(strValue.length() - 1) == quoteChar
			) {
				// will removing from start and end lead to quote impairment?
				String removedBoundaryQuotes = strValue.substring(1, strValue.length() - 1);
				int nextQuoteCharIndex = removedBoundaryQuotes.indexOf(quoteChar);
				if (nextQuoteCharIndex == -1) {
					return true;
				} else {
					if (nextQuoteCharIndex + 1 <= removedBoundaryQuotes.length() - 1) {
						char nextCharAfterQuote = removedBoundaryQuotes.charAt(nextQuoteCharIndex + 1);
						return !Character.isWhitespace(nextCharAfterQuote);
					} else {
						return false;
					}
				}

			} else {
				return false;
			}
		}
	}

	private Optional<LinkedHashMap<String, Object>> validateLine(
		Optional<LinkedHashMap<String, Object>> v,
		int expectedNumberOfColumns,
		AtomicLong messageNumber,
		BiConsumer<ParseError, Exception> exceptionHandler
	) {
		return v.flatMap(a -> {
			if (parserConfig.csvColumnNumberValidation) {
				if (a.size() != expectedNumberOfColumns) {
					Exception exception = new Exception("Invalid number of columns. expected=" + expectedNumberOfColumns + ", actual=" + a.size());

					exceptionHandler.accept(
						new ParseError(messageNumber.get(), of(messageNumber.get()), a.toString()),
						exception);

					return empty();
				}
			}

			for (Object value : a.values()) {
				if (isNotBlank((String) value)) {
					return of(a);
				}
			}

			return empty();
		});
	}

	private Optional<String[]> validateLine(Optional<String[]> v) {
		return v.map(line -> {
			for (String columnValue : line) {
				if (isNotBlank(columnValue)) {
					return line;
				}
			}
			return null;
		});
	}

	private LinkedHashMap<String, Object> generateAttrMap(String[] row) {
		return toLinkedHashMap(
			IntStream.range(1, row.length + 1).boxed(),
			index -> "attribute" + index,
			index -> row[index - 1]);
	}
	
}