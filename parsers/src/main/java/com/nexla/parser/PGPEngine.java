package com.nexla.parser;

import name.neuhalfen.projects.crypto.bouncycastle.openpgp.BouncyGPG;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.algorithms.PGPAlgorithmSuite;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.algorithms.PGPCompressionAlgorithms;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.algorithms.PGPHashAlgorithms;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.algorithms.PGPSymmetricEncryptionAlgorithms;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.keys.callbacks.KeyringConfigCallbacks;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.keys.keyrings.KeyringConfig;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.keys.keyrings.KeyringConfigs;
import org.apache.commons.io.IOUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openpgp.PGPException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.security.GeneralSecurityException;
import java.security.Security;
import java.util.Optional;
import java.util.Set;

public class PGPEngine implements EncryptEngine {

	private static final Logger log = LoggerFactory.getLogger(PGPEngine.class);

	static {
		if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
			Security.addProvider(new BouncyCastleProvider());
		}
	}

	private String secretKeyRing;
	private String pubKeyRing;
	private String secKeyRingPassword;
	private String userId;
	private String recipientId;

	private PGPHashAlgorithms hashAlgorithm = PGPHashAlgorithms.SHA_256;
	private PGPSymmetricEncryptionAlgorithms encryptionAlgorithm = PGPSymmetricEncryptionAlgorithms.AES_256;
	private PGPCompressionAlgorithms compressionAlgorithm = PGPCompressionAlgorithms.BZIP2;

	public void init(String secretKeyRing, String pubKeyRing, String secKeyRingPassword, String userId, String recipientId) {
		this.secretKeyRing = secretKeyRing;
		this.pubKeyRing = pubKeyRing;
		this.secKeyRingPassword = secKeyRingPassword;
		this.userId = userId;
		this.recipientId = recipientId;
	}

	public PGPEngine initHashAlg(Optional<String> hashAlgorithm) {
		this.hashAlgorithm = PGPHashAlgorithms.valueOf(hashAlgorithm.orElse("SHA_256"));
		return this;
	}

	public PGPEngine initEncryptAlg(Optional<String> encryptionAlgorithm) {
		this.encryptionAlgorithm = PGPSymmetricEncryptionAlgorithms.valueOf(encryptionAlgorithm.orElse("AES_256"));
		return this;
	}

	public PGPEngine initCompressionAlg(Optional<String> compressionAlgorithm) {
		this.compressionAlgorithm = PGPCompressionAlgorithms.valueOf(compressionAlgorithm.orElse("BZIP2"));
		return this;
	}

	public InputStream readEncrypted(InputStream streamToDecrypt) throws IOException {
		log.info("PGPEngine.readEncrypted - userId: {}, recipientId: {}", userId, recipientId);
		
		// If public key is empty, user wants decryption-only mode with system GPG
		if (pubKeyRing == null || pubKeyRing.trim().isEmpty()) {
			log.debug("Public key is empty - using decryption-only mode with system GPG 2.4+");
			return decryptWithSystemGPG(streamToDecrypt);
		}
		
		// Original BouncyCastle implementation for backward compatibility
		InputStream streamToReturn;
		try (
			InputStream secretKey = new ByteArrayInputStream(secretKeyRing.getBytes(StandardCharsets.UTF_8));
			InputStream publicKey = new ByteArrayInputStream(pubKeyRing.getBytes(StandardCharsets.UTF_8))
		) {
			try {
				KeyringConfig encryptionKeyring = KeyringConfigs.withKeyRingsFromStreams(publicKey,
					secretKey, KeyringConfigCallbacks.withPassword(secKeyRingPassword));

				streamToReturn = BouncyGPG
					.decryptAndVerifyStream()
					.withConfig(encryptionKeyring)
					.andIgnoreSignatures()
					.fromEncryptedInputStream(streamToDecrypt);
			} catch (GeneralSecurityException | PGPException e) {
				log.error("Can't decrypt stream", e);
				throw new IllegalArgumentException(e);
			}
		}
		return streamToReturn;
	}

	/**
	 * Decrypt using system GPG 2.4+ for decryption-only mode
	 */
	private InputStream decryptWithSystemGPG(InputStream streamToDecrypt) throws IOException {
		log.debug("Starting system GPG 2.4+ decryption");
		
		// Create temporary directory for GPG home
		File tempGpgHome = new File(System.getProperty("java.io.tmpdir"), "gpg_home_" + System.currentTimeMillis());
		if (!tempGpgHome.mkdirs()) {
			throw new IOException("Failed to create temporary GPG home directory: " + tempGpgHome.getAbsolutePath());
		}

		// Set proper permissions on the GPG home directory (700 - rwx------)
		try {
			Set<PosixFilePermission> permissions = PosixFilePermissions.fromString("rwx------");
			Files.setPosixFilePermissions(tempGpgHome.toPath(), permissions);
			log.debug("Set POSIX permissions 700 on GPG home directory");
		} catch (Exception e) {
			log.debug("POSIX permissions not supported, using Java File API");
			tempGpgHome.setReadable(false, false);
			tempGpgHome.setWritable(false, false);
			tempGpgHome.setExecutable(false, false);
			tempGpgHome.setReadable(true, true);
			tempGpgHome.setWritable(true, true);
			tempGpgHome.setExecutable(true, true);
		}

		File tempInput = File.createTempFile("pgp_input_", ".gpg");
		File tempOutput = File.createTempFile("pgp_output_", ".dec");
		File tempKey = File.createTempFile("pgp_key_", ".asc");

		log.debug("Created temporary files: input={}, output={}, key={}, gpg_home={}",
			tempInput.getAbsolutePath(), tempOutput.getAbsolutePath(), tempKey.getAbsolutePath(), tempGpgHome.getAbsolutePath());

		try {
			// Write input stream to temp file using IOUtils for binary data integrity
			log.debug("Writing encrypted data to temporary file");
			try (FileOutputStream fos = new FileOutputStream(tempInput)) {
				long totalBytes = IOUtils.copyLarge(streamToDecrypt, fos);
				fos.flush(); // Ensure all data is written
				fos.getFD().sync(); // Force OS to flush to disk before GPG reads
				log.debug("Wrote {} bytes to temporary input file", totalBytes);
			}

			// Write private key to temp file
			log.debug("Writing private key to temporary file");
			try (FileOutputStream fos = new FileOutputStream(tempKey)) {
				fos.write(secretKeyRing.getBytes(StandardCharsets.UTF_8));
			}
			log.debug("Private key written to temporary file");

			// Import key and decrypt using GPG with temporary home directory
			log.debug("Importing private key using GPG");
			ProcessBuilder pb = new ProcessBuilder(
					"gpg", "--batch", "--yes", "--pinentry-mode", "loopback",
					"--homedir", tempGpgHome.getAbsolutePath(),
					"--passphrase", secKeyRingPassword,
					"--import", tempKey.getAbsolutePath());

			// Clear environment variables that might interfere with GPG
			pb.environment().remove("GNUPGHOME");
			pb.environment().remove("GPG_AGENT_INFO");

			Process process = pb.start();
			int importResult = process.waitFor();

			if (importResult != 0) {
				log.warn("GPG key import failed with exit code: {}, trying without import", importResult);
			} else {
				log.debug("GPG key import successful");
			}

			// Decrypt the file with AEAD-compatible parameters for GPG 2.4+
			log.debug("Decrypting file using GPG with AEAD compatibility flags");
			pb = new ProcessBuilder(
					"gpg", "--batch", "--yes", "--pinentry-mode", "loopback",
					"--homedir", tempGpgHome.getAbsolutePath(),
					"--passphrase", secKeyRingPassword,
					"--output", tempOutput.getAbsolutePath(),
					"--trust-model", "always",           // Skip trust validation
					"--ignore-mdc-error",                // Ignore modification detection errors  
					"--allow-non-selfsigned-uid",        // Allow non-self-signed UIDs
					"--disable-cipher-algo", "IDEA",     // Disable problematic legacy cipher
					"--debug-level", "basic",            // Add debug info for troubleshooting
					"--decrypt", tempInput.getAbsolutePath());

			// Clear environment variables that might interfere with GPG
			pb.environment().remove("GNUPGHOME");
			pb.environment().remove("GPG_AGENT_INFO");

			process = pb.start();
			int decryptResult = process.waitFor();

			if (decryptResult != 0) {
				// Read both stderr and stdout for comprehensive error info
				StringBuilder errorOutput = new StringBuilder();
				StringBuilder standardOutput = new StringBuilder();
				
				try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
				     BufferedReader outputReader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
					
					String line;
					while ((line = errorReader.readLine()) != null) {
						errorOutput.append(line).append("\n");
					}
					while ((line = outputReader.readLine()) != null) {
						standardOutput.append(line).append("\n");
					}
				}
				
				String errorMessage = errorOutput.toString();
				String outputMessage = standardOutput.toString();
				
				log.error("GPG decryption failed with exit code {}", decryptResult);
				log.error("GPG stderr: {}", errorMessage);
				if (!outputMessage.trim().isEmpty()) {
					log.error("GPG stdout: {}", outputMessage);
				}
				
				// Provide specific guidance for common errors
				if (errorMessage.contains("partial length invalid for packet type")) {
					log.error("This error typically indicates a corrupted or incompatible GPG file format");
				} else if (errorMessage.contains("bad passphrase")) {
					log.error("Incorrect passphrase provided for GPG key");
				} else if (errorMessage.contains("no valid OpenPGP data found")) {
					log.error("File does not contain valid GPG encrypted data");
				}
				
				throw new IOException("GPG decryption failed with exit code: " + decryptResult + ". Error: " + errorMessage);
			}

			// Validate that decryption actually produced output
			if (!tempOutput.exists() || tempOutput.length() == 0) {
				log.error("GPG decryption completed with exit code 0 but produced no output file or empty file");
				throw new IOException("GPG decryption produced no valid output despite successful exit code");
			}

			log.debug("GPG decryption successful, output file size: {} bytes", tempOutput.length());

			// Return the decrypted content as input stream
			return new FileInputStream(tempOutput) {
				@Override
				public void close() throws IOException {
					super.close();
					// Clean up temp files
					log.debug("Cleaning up temporary files");
					tempInput.delete();
					tempOutput.delete();
					tempKey.delete();
					// Clean up GPG home directory
					deleteDirectory(tempGpgHome);
				}
			};

		} catch (InterruptedException e) {
			// Handle thread interruption
			Thread.currentThread().interrupt();
			log.debug("Cleaning up temporary files due to interruption");
			tempInput.delete();
			tempOutput.delete();
			tempKey.delete();
			deleteDirectory(tempGpgHome);
			throw new IOException("GPG decryption was interrupted", e);
		} catch (Exception e) {
			// Clean up temp files on error
			log.debug("Cleaning up temporary files due to error");
			tempInput.delete();
			tempOutput.delete();
			tempKey.delete();
			deleteDirectory(tempGpgHome);
			throw new IOException("GPG decryption failed: " + e.getMessage(), e);
		}
	}

	/**
	 * Recursively delete a directory and its contents
	 */
	private void deleteDirectory(File directory) {
		if (directory.exists()) {
			File[] files = directory.listFiles();
			if (files != null) {
				for (File file : files) {
					if (file.isDirectory()) {
						deleteDirectory(file);
					} else {
						file.delete();
					}
				}
			}
			directory.delete();
		}
	}

	public ByteArrayOutputStream encrypt(InputStream streamToEncrypt) throws IOException {
		log.info("PGPEngine.encrypt - userId: {}, recipientId: {}", userId, recipientId);
		
		// Check if public key is available for encryption
		if (pubKeyRing == null || pubKeyRing.trim().isEmpty()) {
			log.error("Public key is required for encryption operations. This configuration is for decryption-only mode.");
			throw new IllegalArgumentException("Public key is required for encryption operations. This configuration is for decryption-only mode.");
		}
		
		ByteArrayOutputStream streamToReturn;
		try (
			InputStream secretKey = new ByteArrayInputStream(secretKeyRing.getBytes(StandardCharsets.UTF_8));
			InputStream publicKey = new ByteArrayInputStream(pubKeyRing.getBytes(StandardCharsets.UTF_8))
		) {
			try {
				KeyringConfig encryptionKeyring = KeyringConfigs.withKeyRingsFromStreams(publicKey,
					secretKey, KeyringConfigCallbacks.withPassword(secKeyRingPassword));
				streamToReturn = new ByteArrayOutputStream();
				OutputStream encryptionStream = BouncyGPG
					.encryptToStream()
					.withConfig(encryptionKeyring)
					.withAlgorithms(new PGPAlgorithmSuite(hashAlgorithm, encryptionAlgorithm, compressionAlgorithm))
					.toRecipient(recipientId)
					.andDoNotSign()
					.binaryOutput()
					.andWriteTo(streamToReturn);

				IOUtils.copyLarge(streamToEncrypt, encryptionStream);
				encryptionStream.close();
			} catch (PGPException | IOException | GeneralSecurityException e) {
				log.error("Can't encrypt stream", e);
				throw new IllegalArgumentException(e);
			}
		}
		return streamToReturn;
	}

}
