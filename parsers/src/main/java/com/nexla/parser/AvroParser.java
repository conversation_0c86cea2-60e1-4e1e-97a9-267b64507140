package com.nexla.parser;

import com.nexla.admin.client.NexlaSchema;
import com.nexla.common.NexlaMessage;
import com.nexla.common.StreamUtils;
import com.nexla.common.parse.NexlaParser;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.avro.AvroRuntimeException;
import org.apache.avro.file.DataFileStream;
import org.apache.avro.generic.GenericDatumReader;
import org.apache.avro.generic.GenericRecord;

import java.io.IOException;
import java.io.InputStream;
import java.util.LinkedHashMap;
import java.util.Optional;

import static com.nexla.parser.schemaaware.AvroSchemaExtractor.getAvroSchema;

/**
 * AvroParser to parse avro encoded data.
 */
public class AvroParser extends NexlaParser implements SchemaAware {
	private NexlaSchema schema;

	@SneakyThrows
	@Override
	public StreamEx<Optional<NexlaMessage>> parseMessages(InputStream inputStream) {
		DataFileStream<GenericRecord> reader = avroReader(inputStream);
		this.schema = getAvroSchema(reader.getSchema());
		return StreamEx.produce(consumer -> {
					try {
						if (reader.hasNext()) {
							consumer.accept(reader.next());
							return true;
						}
					} catch (AvroRuntimeException e) {
						// Invalid sync! - see NEX-5019
						logger().warn("Exception while requesting next block", e);
					}

					return false;
				})
				.map(record -> (LinkedHashMap<String, Object>) StreamUtils.jsonUtil().jsonToMap(record.toString()))
				.map(v -> Optional.of(new NexlaMessage(v)));
	}

	private DataFileStream<GenericRecord> avroReader(InputStream inputStream) {
		try {
			return new DataFileStream<>(inputStream, new GenericDatumReader<>());
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	@SneakyThrows
	@Override
	public StreamEx<String> readLines(InputStream inputStream) {
		DataFileStream<GenericRecord> reader = avroReader(inputStream);
		this.schema = getAvroSchema(reader.getSchema());
		return StreamEx.of(reader).map(Object::toString);
	}

	@Override
	public NexlaSchema getSchema() {
		return this.schema;
	}
}
