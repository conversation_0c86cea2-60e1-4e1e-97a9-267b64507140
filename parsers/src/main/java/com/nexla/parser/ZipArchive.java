package com.nexla.parser;

import lombok.SneakyThrows;
import net.lingala.zip4j.io.inputstream.ZipInputStream;
import net.lingala.zip4j.model.LocalFileHeader;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.Optional;

public class ZipArchive {
	private final int BUFFER_SIZE = 4096;

	@SneakyThrows
	public void unzip(File file, File outputDir, Optional<String> zipPassword) {
		LocalFileHeader entry;
		int readLen;
		byte[] readBuffer = new byte[BUFFER_SIZE];

		FileInputStream fileInputStream = new FileInputStream(file);
		try(ZipInputStream nativeZis = zipPassword.isPresent() ? new ZipInputStream(fileInputStream, zipPassword.get().toCharArray())
				: new ZipInputStream(fileInputStream))
		{
			while ((entry = nativeZis.getNextEntry()) != null) {
				File entryDestination = new File(outputDir, entry.getFileName());
				if (entry.isDirectory()) {
					entryDestination.mkdirs();
				} else {
					entryDestination.getParentFile().mkdirs();
					try (OutputStream outputStream = new FileOutputStream(entryDestination)) {
						while ((readLen = nativeZis.read(readBuffer)) != -1) {
							outputStream.write(readBuffer, 0, readLen);
						}
					}
				}
			}
		}
	}

}
