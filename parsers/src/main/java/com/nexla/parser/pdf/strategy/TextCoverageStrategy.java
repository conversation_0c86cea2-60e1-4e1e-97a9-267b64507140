package com.nexla.parser.pdf.strategy;

import com.nexla.common.parse.BatchParser;
import com.nexla.parser.pdf.strategy.model.Block;
import com.nexla.parser.pdf.strategy.parse.PDText;
import com.nexla.parser.pdf.strategy.parse.geom.Rect;
import lombok.SneakyThrows;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * This strategy is not part of the public API and is used for internal testing.
 * <p>
 * The goal was to find optimal ratio of the area covered by parsed text to the total area of the page.
 * This ratio is used in Hybrid* strategies to determine if the page should be processed by OCR strategies.
 */
public class TextCoverageStrategy implements BatchingStrategy {
    private static final Logger LOGGER = LoggerFactory.getLogger(TextCoverageStrategy.class);

    @Override
    public Stream<Block> execute(InputStream inputStream) {
        return executeBatch(Collections.singletonList(BatchParser.NexlaStreamDescriptor.from(inputStream)));
    }


    @SneakyThrows
    @Override
    public Stream<Block> executeBatch(List<BatchParser.NexlaStreamDescriptor> iss) {
        return Flux.fromIterable(iss)
                .index()
                .parallel()
                .runOn(Schedulers.boundedElastic())
                .map(two -> parseSingleFile(two.getT2(), two.getT1().intValue()))
                .sequential()
                .collectList()
                .map(xs -> xs.stream().flatMap(x -> x))
                .block();
    }

    @SneakyThrows
    private Stream<Block> parseSingleFile(BatchParser.NexlaStreamDescriptor is, int id) {
        List<Pair<Integer, Double>> pageAreas = new ArrayList<>();
        try {

            Stream<Block> stream = new DefaultStrategy("", Collections.emptyList()) {
                @SneakyThrows
                @Override
                protected Stream<? extends Block> parsePage(PDDocument document, PDPage p) {
                    List<? extends Block> blocks = super.parsePage(document, p).collect(Collectors.toList());
                    PDRectangle pageBox = p.getBBox();
                    double pageArea = Rect.area(Rect.of(pageBox.getLowerLeftX(), pageBox.getLowerLeftY(), pageBox.getUpperRightX(), pageBox.getUpperRightY()));

                    double textArea = blocks.stream()
                            .map(b -> (PDText) b)
                            .map(PDText::getBoundingBox)
                            .mapToDouble(Rect::area)
                            .sum();

                    pageAreas.add(Pair.with(pageAreas.size(), textArea / pageArea));

                    return blocks.stream();
                }
            }.execute(is.is).collect(Collectors.toList()).stream();

            LOGGER.info("Page areas: {}", pageAreas);
            return stream;
        } catch (Exception e) {
            LOGGER.error("Failed to parse document", e);
            LOGGER.error("Page areas: {}", pageAreas);

            return Stream.empty();
        }
    }
}
