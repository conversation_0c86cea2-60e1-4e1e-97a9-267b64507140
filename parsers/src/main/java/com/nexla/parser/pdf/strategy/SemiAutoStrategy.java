package com.nexla.parser.pdf.strategy;

import com.nexla.parser.pdf.strategy.config.TableConfig;
import com.nexla.parser.pdf.strategy.model.Block;
import com.nexla.parser.pdf.strategy.parse.BoundBlock;
import com.nexla.parser.pdf.strategy.parse.PDTable;
import com.nexla.parser.pdf.strategy.parse.PDText;
import com.nexla.parser.pdf.strategy.parse.TextCollector;
import com.nexla.parser.pdf.strategy.parse.geom.Rect;
import com.nexla.parser.pdf.strategy.parse.table.Horizontal;
import com.nexla.parser.pdf.strategy.parse.table.Vertical;
import com.nexla.parser.pdf.strategy.parse.util.Lists;
import com.nexla.parser.pdf.strategy.parse.util.TextPositions;
import lombok.SneakyThrows;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.text.TextPosition;
import org.javatuples.Pair;

import java.awt.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class SemiAutoStrategy extends AbstractStrategy {
    private final boolean emitText;
    private final List<TableConfig> tableConfigs;

    public SemiAutoStrategy(String password, List<Integer> pages, boolean emitText, List<TableConfig> tableConfigs) {
        super(password, pages);
        this.emitText = emitText;
        this.tableConfigs = tableConfigs;
    }

    public Stream<? extends Block> parse(PDDocument document, PDPage page) {
        return parsePage(document, page);
    }

    @SneakyThrows
    protected Stream<? extends Block> parsePage(PDDocument document, PDPage p) {
        List<PDTable> tables = new ArrayList<>();
        for (TableConfig tableConfig : this.tableConfigs) {
            List<PDTable> table = tableConfig.getHeader() == TableConfig.Header.ROW
                    ? new Horizontal(Collections.singletonList(tableConfig), document).detect(p)
                    : new Vertical(Collections.singletonList(tableConfig), document).detect(p);

            tables.addAll(table);
        }

        if (!emitText) {
            return tables.stream();
        }

        int pageIndex = Lists.indexOf(document.getPages(), p::equals);
        List<TextPosition> textPositions = TextCollector.builder()
                .document(document)
                .pageNumber(pageIndex)
                .build()
                .collect();

        List<PDText> pdTextList = TextPositions.collapse(textPositions, p.getCropBox())
                .stream()
                .filter(r -> tables.stream().noneMatch(t -> t.getBoundingBox().contains(r)))
                .sorted(Comparator.comparingDouble(Rectangle::getY).reversed().thenComparing(Rectangle::getX))
                .map(r -> {
                    String textString = textPositions.stream()
                            .map(t -> Pair.with(t, TextPositions.translate(t, p.getCropBox())))
                            .filter(t -> r.contains(t.getValue1()))
                            .sorted(Comparator.comparing(t -> t.getValue1().getX()))
                            .map(pair -> pair.getValue0().getUnicode())
                            .collect(Collectors.joining());

                    return new PDText(textString, r, pageIndex + 1);
                })
                .collect(Collectors.toList());

        List<BoundBlock> blocks = new ArrayList<>();
        blocks.addAll(tables);
        blocks.addAll(pdTextList);

        blocks.sort(
                Comparator
                        .comparingDouble((BoundBlock bb) -> bb.getBoundingBox().getY()).reversed()
                        .thenComparing((BoundBlock bb) -> bb.getBoundingBox().getX())
        );

        List<Block> result = new ArrayList<>();
        PDText currentText = null;
        for (BoundBlock block: blocks) {
            if (block instanceof PDText) {
                if (currentText == null) {
                    currentText = (PDText) block;
                    continue;
                }

                PDText pdText = (PDText) block;

                String newText = Rect.withinY(currentText.getBoundingBox(), pdText.getBoundingBox())
                        ? currentText.getText() + pdText.getText()
                        : currentText.getText() + "\n" + pdText.getText();

                Rectangle newBoundingBox = Rect.merge(currentText.getBoundingBox(), pdText.getBoundingBox());

                currentText = new PDText(newText, newBoundingBox, currentText.getPageNumber());
            } else {
                if (currentText != null) {
                    result.add(currentText);

                    currentText = null;
                }

                result.add(block);
            }
        }

        if (currentText != null) {
            result.add(currentText);
        }

        return result.stream();
    }
}
