package com.nexla.parser.pdf.strategy;

import com.nexla.common.parse.BatchParser;
import com.nexla.parser.pdf.strategy.model.Block;
import com.nexla.parser.pdf.strategy.parse.PDText;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.SneakyThrows;
import net.sourceforge.tess4j.Tesseract;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;
import java.util.stream.Stream;

/**
 * Strategy that uses Tesseract to extract text from PDFs.
 * <p>
 * To run this strategy, you need to have Tesseract installed on your system.
 * For Mac, you can install it using `brew install tesseract`.
 */
public class TesseractStrategy extends AbstractStrategy implements BatchingStrategy {
    private static final Logger LOGGER = LoggerFactory.getLogger(TesseractStrategy.class);

    @Override
    public Stream<Block> executeBatch(List<BatchParser.NexlaStreamDescriptor> inputStream) {
        return inputStream.stream()
                .flatMap(is -> execute(is.is).map(b -> new BatchedBlock(b, is)));
    }

    private static class Tesseract0 {
        private static final String TESSERACT_LIB_PATH = "/usr/local/lib";
        private static final String TESSERACT_DATA_PATH = "/usr/local/share/tessdata";
        public static final String JNA_LIBRARY_PATH_SETTING = "jna.library.path";

        private static boolean isMac() {
            return System.getProperty("os.name").toLowerCase().contains("mac");
        }

        private static boolean isWindows() {
            return System.getProperty("os.name").toLowerCase().contains("windows");
        }

        private static Tesseract mkTesseract() {
            Tesseract tesseract = new Tesseract();

            // developer's zone
            if (isMac() || isWindows()) {
                if (isMac()) {
                    if (!new File(TESSERACT_LIB_PATH, "libtesseract.dylib").exists()) {
                        throw new IllegalStateException(
                                "Tesseract library not found in " + TESSERACT_LIB_PATH + ". Please install tesseract using `brew install tesseract`."
                        );
                    }

                    String libs = System.getProperty(JNA_LIBRARY_PATH_SETTING);
                    if (libs != null) {
                        libs = libs + File.pathSeparator + TESSERACT_LIB_PATH;
                    } else {
                        libs = TESSERACT_LIB_PATH;
                    }

                    System.setProperty(JNA_LIBRARY_PATH_SETTING, libs);

                    if (!new File(TESSERACT_DATA_PATH).exists()) {
                        throw new IllegalStateException(
                                "Tesseract data not found in " + TESSERACT_DATA_PATH + ". Please Download the data from `https://github.com/tesseract-ocr/tessdata/archive/refs/tags/`. See Dockerfile for a reference."
                        );
                    }

                    tesseract.setDatapath(TESSERACT_DATA_PATH);
                }

                if (isWindows()) {
                    // windows is ok.
                }
            } else {
                System.setProperty(JNA_LIBRARY_PATH_SETTING, TESSERACT_LIB_PATH);

                tesseract.setDatapath(TESSERACT_DATA_PATH);
            }

            return tesseract;
        }
    }

    public TesseractStrategy(String password, List<Integer> pages) {
        super(password, pages);
    }

    @Data
    @AllArgsConstructor
    private static class ImageWithPage {
        public final BufferedImage image;
        public final int pageNumber;
    }

    @SneakyThrows
    protected Stream<Block> parseDocument(PDDocument document) {
        return Flux.fromStream(Stream.iterate(0, i -> i < document.getNumberOfPages(), i -> i + 1))
                .flatMap(pageNr -> {
                    try {
                        return Mono.just(new ImageWithPage(new PDFRenderer(document).renderImage(pageNr, 1.25f), pageNr + 1));
                    } catch (Throwable e) {
                        LOGGER.error("Failed to render page {}", pageNr, e);
                        return Mono.empty();
                    }
                })
                .parallel()
                .runOn(Schedulers.boundedElastic())
                .flatMap(image -> {
                    try {
                        return Mono.just(new PDText(Tesseract0.mkTesseract().doOCR(image.image), null, image.pageNumber + 1));
                    } catch (Throwable e) {
                        LOGGER.error("Failed to render page {}", image.pageNumber, e);
                        return Mono.<Block>empty();
                    }
                })
                .sequential()
                .collectList()
                .block()
                .stream();

    }
}
