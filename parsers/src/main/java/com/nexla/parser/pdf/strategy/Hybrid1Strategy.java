package com.nexla.parser.pdf.strategy;

import com.nexla.common.parse.BatchParser;
import com.nexla.parser.pdf.PdfParser;
import com.nexla.parser.pdf.strategy.model.Block;
import com.nexla.parser.pdf.strategy.parse.PDText;
import com.nexla.parser.pdf.strategy.parse.geom.Rect;
import com.nexla.parser.pdf.strategy.parse.util.Lists;
import com.nexla.parser.pdf.strategy.textract.TextractStrategy;
import lombok.Data;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Uses PDF box to extract text from PDFs.
 * If ratio of text area to page area is less than a certain threshold, the page is processed by Textract strategy.
 */
public class Hybrid1Strategy implements BatchingStrategy {
    private static final Logger LOGGER = LoggerFactory.getLogger(Hybrid1Strategy.class);
    private final Map<String, String> options;

    private final double ratio;

    public Hybrid1Strategy(double ratio, Map<String, String> options) {
        this.ratio = ratio;
        this.options = options;
    }

    @Override
    public Stream<Block> execute(InputStream inputStream) {
        return executeBatch(Collections.singletonList(BatchParser.NexlaStreamDescriptor.from(inputStream)));
    }

    @Data
    private static class PageContext {
        private final int pageNr;
        private final int originalPageNr;
        private final List<? extends Block> blocks;
        private final boolean isFullyParsed;
    }

    @Data
    private static class DocumentContext {
        private final int id;
        private final PDDocument document;
        private final File nok;
        private final BatchParser.NexlaStreamDescriptor descriptor;
        private final List<PageContext> pages;

    }

    @SneakyThrows
    @Override
    public Stream<Block> executeBatch(List<BatchParser.NexlaStreamDescriptor> iss) {
        List<DocumentContext> documentContexts = Flux.fromIterable(iss)
                .index()
                .parallel()
                .runOn(Schedulers.boundedElastic())
                .map(two -> parseSingleFile(two.getT2(), two.getT1().intValue()))
                .sequential()
                .collectList()
                .block();

        List<DocumentContext> toTextract = documentContexts
                .stream()
                .filter(dc -> {
                    boolean everythingParsed = dc.getPages().stream().allMatch(PageContext::isFullyParsed);
                    if (!everythingParsed) {
                        LOGGER.info("Document {} is not fully parsed", dc.getDescriptor().getId());
                    }

                    return !everythingParsed;
                })
                .collect(Collectors.toList());

        List<BatchParser.NexlaStreamDescriptor> fileList = toTextract.stream().map(documentContext -> {
            try {
                return new BatchParser.NexlaStreamDescriptor(documentContext.getDescriptor().getId(), Files.newInputStream(documentContext.getNok().toPath()));
            } catch (IOException e) {
                LOGGER.error("Failed to open file", e);
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());

        boolean parseText = Optional.ofNullable(options.get(PdfParser.Options.STRATEGY_AUTO_1_TEXT))
                .map(Boolean::parseBoolean)
                .orElse(true);

        boolean parseTables = Optional.ofNullable(options.get(PdfParser.Options.STRATEGY_AUTO_1_TABLES))
                .map(Boolean::parseBoolean)
                .orElse(false);

        Stream<Block> blockStream = new TextractStrategy(options, parseText, parseTables)
                .executeBatch(fileList);

        StreamEx<Block> defaultStrategyParsed = StreamEx.of(documentContexts)
                .flatMap(dc -> {
                    return dc.getPages().stream()
                            .map(PageContext::getBlocks)
                            .flatMap(xs -> xs.stream().map(x -> new BatchedBlock(x, dc.getDescriptor())));
                });

        return Stream.concat(defaultStrategyParsed, blockStream);
    }

    @SneakyThrows
    private DocumentContext parseSingleFile(BatchParser.NexlaStreamDescriptor is, int id) {
        PDDocument pdDocument = new PDDocument();
        File nok = File.createTempFile("hyb1-pdf", ".pdf"); // this is tmp file

        LOGGER.info("Going to save unparsed files in " + nok.getAbsolutePath());

        DocumentContext documentContext = new DocumentContext(id, pdDocument, nok, is, new ArrayList<>());

        try {
            long ignored = new DefaultStrategy(options.get(PdfParser.Options.PASSWORD), Collections.emptyList()) {
                @SneakyThrows
                @Override
                protected Stream<? extends Block> parsePage(PDDocument document, PDPage p) {
                    List<? extends Block> blocks = super.parsePage(document, p).collect(Collectors.toList());
                    PDRectangle pageBox = p.getBBox();
                    double pageArea = Rect.area(Rect.of(pageBox.getLowerLeftX(), pageBox.getLowerLeftY(), pageBox.getUpperRightX(), pageBox.getUpperRightY()));

                    double textArea = blocks.stream()
                            .map(b -> (PDText) b)
                            .map(PDText::getBoundingBox)
                            .mapToDouble(Rect::area)
                            .sum();

                    if (textArea / pageArea < ratio) {
                        documentContext.getPages().add(
                                new PageContext(pdDocument.getNumberOfPages(), Lists.indexOf(document.getPages(), p::equals) + 1, new ArrayList<>(), false)
                        );

                        // save this page to a new document
                        pdDocument.importPage(p);
                        return StreamEx.empty();
                    } else {
                        documentContext.getPages().add(
                                new PageContext(-1, Lists.indexOf(document.getPages(), p::equals) + 1, blocks, true)
                        );
                    }
                    return Stream.of();
                }
            }.execute(is.is, () -> {
                try {
                    pdDocument.save(nok);
                } catch (Exception e) {
                    LOGGER.error("Failed to save unparsed document", e);
                }
            }).count();

            pdDocument.close();
        } catch (Exception e) {
            LOGGER.error("Failed to parse document", e);
            pdDocument.close();
        }

        return documentContext;
    }
}
