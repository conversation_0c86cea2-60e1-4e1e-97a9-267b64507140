package com.nexla.parser.pdf.strategy;

import com.nexla.parser.pdf.strategy.model.Block;
import lombok.SneakyThrows;
import org.apache.pdfbox.io.RandomAccessBufferedFileInputStream;
import org.apache.pdfbox.pdfparser.PDFParser;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public abstract class AbstractStrategy implements Strategy {
    private final String password;
    private final List<Integer> pages;

    public AbstractStrategy(String password, List<Integer> pages) {
        this.password = password;
        this.pages = pages;
    }

    protected PDDocument loadPDF(InputStream file) throws IOException {
        return loadPDF(file, password);
    }

    public static PDDocument loadPDF(InputStream file, String password) throws IOException {
        PDFParser parser = new PDFParser(new RandomAccessBufferedFileInputStream(file), password);
        parser.parse();
        return parser.getPDDocument();
    }

    @SneakyThrows
    @Override
    public Stream<Block> execute(InputStream inputStream) {
        return execute(inputStream, () -> {});
    }

    @SneakyThrows
    public Stream<Block> execute(InputStream inputStream, Runnable onCompletion) {
        try (PDDocument document = loadPDF(inputStream)) {
            // TODO: handle streaming, currently it collects to properly close the document
            Stream<Block> stream = parseDocument(document).collect(Collectors.toList()).stream();
            onCompletion.run();
            return stream;
        }
    }

    protected Stream<Block> parseDocument(PDDocument document) {
        int numberOfPages = document.getNumberOfPages();
        if (pages == null || pages.isEmpty()) {
            return Stream.iterate(0, i -> i < numberOfPages, i -> i + 1)
                    .flatMap(pageNumber -> parsePage(document, document.getPage(pageNumber)));
        }

        return pages.stream()
                .map(pageNumber -> pageNumber < 0 ? numberOfPages + pageNumber : pageNumber)
                .filter(p -> p >= 0 && p < numberOfPages)
                .flatMap(pageNumber -> parsePage(document, document.getPage(pageNumber)));
    }

    protected Stream<? extends Block> parsePage(PDDocument document, PDPage page) {
        return Stream.empty();
    }
}
