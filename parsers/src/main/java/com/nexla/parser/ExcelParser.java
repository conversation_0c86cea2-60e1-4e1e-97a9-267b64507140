package com.nexla.parser;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.exception.ParseError;
import com.nexla.common.parse.MultiPartDocument;
import com.nexla.common.parse.NexlaParser;
import com.nexla.parser.excel.*;
import com.nexla.parser.excel.formula.ExcelFormulaEvaluator;
import com.nexla.parser.excel.functions.DateDifFunc;
import com.nexla.parser.xml.DomParser;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.NotImplementedException;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.common.config.ConfigException;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.formula.WorkbookEvaluator;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellAddress;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.apache.poi.util.XMLHelper;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.eventusermodel.XSSFSheetXMLHandler;
import org.apache.poi.xssf.model.StylesTable;
import org.apache.poi.xssf.usermodel.XSSFComment;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xml.sax.ContentHandler;
import org.xml.sax.InputSource;
import org.xml.sax.XMLReader;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.bazaarvoice.jolt.JsonUtils.stringToType;
import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.NexlaConstants.DATASET_CUSTOM_NAME;
import static com.nexla.common.StreamUtils.lhm;
import static com.nexla.common.parse.ParserConfigs.Excel.*;
import static com.nexla.writer.NexlaFileWriter.ONE_MB_FLUSH_THRESHOLD;
import static java.lang.Boolean.parseBoolean;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyMap;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.*;
import static org.apache.commons.collections.CollectionUtils.addAll;
import static org.apache.poi.ss.usermodel.CellType.BOOLEAN;
import static org.apache.poi.ss.usermodel.CellType.NUMERIC;
import static org.apache.poi.ss.usermodel.CellType.STRING;
import static org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted;

public class ExcelParser extends NexlaParser implements MultiPartDocument {

	private static final Logger logger = LoggerFactory.getLogger(ExcelParser.class);

	public static final String SHEET_MESSAGE_NUMBER = "sheet.message.number";
	public static final String SHEET_NAME = "sheet.name";

	private static final int INITIAL_ATTRIBUTES = 1000;
	private static final double ZIP_INFLATE_FILE_MIN_RATIO = 0.0001; // 100 times more lenient than the default

	private static final String ATTRIBUTE = "attribute";
	private static final String COMMENT = "comment";
	private static final String DEFAULT_DATE = "31-Dec-1899";
	private static final String FORMULA = "formula";
	private static final String HYPERLINK = "hyperlink";
	private static final String NULL = "null";
	private static final String VALUE = "value";

	private static final String DUPLICATED_SHEET_NAME_MESSAGE = "Duplicated sheet name %s provided in `%s` parameter.";
	public static final String NULL_AS_EMPTY_STR = "null.as.empty.string";
	public static final String PRESERVE_EMPTY_STRINGS = "preserve.empty.strings";
	public static final String PRESERVE_NULL_STRINGS = "preserve.null.strings";

	public static final String RENAME_DUPLICATE_HEADERS = "rename.duplicate.headers";

	private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormat.forPattern("HH:mm:ss");
	private static final ExcelTransposer excelTransposer = new ExcelTransposer();
	private static final ExcelFormulaEvaluator EXCEL_FORMULA_EVALUATOR = new ExcelFormulaEvaluator();
	private List<String> configuredSchema;
	private Map<Optional<String>, RangeSheet> ranges = Maps.newHashMap();
	private Map<Optional<String>, AdditionalRange> additionalRanges = Maps.newHashMap();

	private String excelType;
	private String mode = CONCISE;
	private String schemaDetection = HEADER;
	private String excelFileName;

	private boolean skipMergedCells = false;
	private boolean transpose = false;

	private boolean streamed = false;

	private boolean renameDuplicateHeaders = false;

	private boolean preserveEmptyStrings = false;
	private boolean preserveNullStrings = false;
	private boolean nullAsEmptyString = false;

	static {
		try {
			// some Excel files have a very big expansion ratio, this prevents the Zip bomb prevention from throwing
			ZipSecureFile.setMinInflateRatio(ZIP_INFLATE_FILE_MIN_RATIO);
			// list of functions that POI can evaluate
			Collection<String> supportedFuncs = WorkbookEvaluator.getSupportedFunctionNames();
			if (!supportedFuncs.contains("DATEDIF")) {
				logger.info("registering 'DATEDIF' function with the formula evaluator...");
				WorkbookEvaluator.registerFunction("DATEDIF", new DateDifFunc());
			}
		} catch (Exception e) {
			logger.error("functions were not registered with the formula evaluator: {}", e.getMessage());
		}
	}

	private static boolean isCellMerged(Sheet sheet, int row, int col) {
		for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
			CellRangeAddress range = sheet.getMergedRegion(i);
			if (range.isInRange(row, col)) {
				return true;
			}
		}
		return false;
	}

	private static boolean isRowMerged(Sheet sheet, int row, int col) {
		return isCellMerged(sheet, row, col) &&
			(isCellMerged(sheet, row, col + 1) || isCellMerged(sheet, row, col - 1));
	}

	@Override
	public ExcelParser option(String key, String value) {
		super.option(key, value);
		switch (key) {
			case EXCEL_TYPE:
				this.excelType = value;
				break;
			case RANGES:
				String[] splitSheets = value.split(",");

				List<Optional<String>> sheetNames = StreamEx.of(splitSheets)
					.map(RangeSheet::new)
					.map(RangeSheet::getSheetName)
					.collect(toList());

				validateDuplicatedKeys(sheetNames, RANGES);

				this.ranges = StreamEx.of(splitSheets)
					.map(RangeSheet::new)
					.toMap(RangeSheet::getSheetName, x -> x);
				break;
			case ADDITIONAL_EXCEL_RANGE:
				String[] splitAdditional = value.split(",");

				List<Optional<String>> additionalSheetNames = StreamEx.of(splitAdditional)
					.map(AdditionalRange::new)
					.map(AdditionalRange::getSheetName)
					.collect(toList());

				validateDuplicatedKeys(additionalSheetNames, ADDITIONAL_EXCEL_RANGE);

				this.additionalRanges = StreamEx.of(splitAdditional)
					.map(AdditionalRange::new)
					.toMap(AdditionalRange::getSheetName, additionalRange -> additionalRange);
				break;
			case EXCEL_SCHEMA_DETECTION:
				this.schemaDetection = value;
				break;
			case EXCEL_SCHEMA:
				this.configuredSchema = asList(value.split(","));
				break;
			case EXCEL_MODE:
				this.mode = value;
				break;
			case EXCEL_SKIP_MERGED_CELLS:
				this.skipMergedCells = parseBoolean(value);
				break;
			case EXCEL_TRANSPOSE:
				this.transpose = parseBoolean(value);
				break;
			case EXCEL_STREAMED:
				this.streamed = parseBoolean(value);
				break;
			case EXCEL_FILE_NAME:
				this.excelFileName = value;
				break;
			case PRESERVE_EMPTY_STRINGS:
				this.preserveEmptyStrings = parseBoolean(value);
				break;
			case PRESERVE_NULL_STRINGS:
				this.preserveNullStrings = parseBoolean(value);
				break;
			case RENAME_DUPLICATE_HEADERS:
				this.renameDuplicateHeaders = parseBoolean(value);
				break;
			case NULL_AS_EMPTY_STR:
				this.nullAsEmptyString = parseBoolean(value);
				break;
		}
		return this;
	}

	private void validateDuplicatedKeys(List<Optional<String>> sheetNames, String paramName) {
		List<String> duplicatedKeys = sheetNames
			.stream()
			.collect(groupingBy(Function.identity()))
			.values()
			.stream()
			.filter(names -> names.size() > 1)
			.flatMap(Collection::stream)
			.filter(Optional::isPresent)
			.map(Optional::get)
			.collect(Collectors.toList());

		if (CollectionUtils.isNotEmpty(duplicatedKeys)) {
			throw new ConfigException(String.format(DUPLICATED_SHEET_NAME_MESSAGE, duplicatedKeys, paramName));
		}
	}

	@SneakyThrows
	private StreamEx<StreamEx<Optional<NexlaMessage>>> parseMessagesInMemory(InputStream inputStream) {
		// Hide class-level 'ranges' field, use transposed range values instead
		final Map<Optional<String>, RangeSheet> ranges = transpose
			? EntryStream.of(this.ranges).mapValues(RangeSheet::transposed).toMap()
			: this.ranges;

		boolean doInjectAdditionalCells = additionalRanges.size() > 0;

		try (WorkbookWrapper workbook = createWorkbook(inputStream)) {
			Set<String> sheetNames = StreamEx.of(ranges.keySet())
				.filter(Optional::isPresent)
				.map(Optional::get)
				.toSet();

			Set<String> availableSheets = getAllAvailableSheets(workbook.workbook);

			validateSheetNames(sheetNames, availableSheets);

			boolean defaultSheetRange = ranges.size() == 1 && ranges.keySet().iterator().next().isEmpty();

			return StreamEx.of(IntStream.range(0, workbook.workbook.getNumberOfSheets()).boxed())
				.filter(sheetNumber -> {
					Sheet sheet = workbook.workbook.getSheetAt(sheetNumber);
					return ranges.isEmpty() ||
						sheetNames.contains(sheet.getSheetName()) ||
						(sheetNumber == 0 && defaultSheetRange);

				})
				.peek(sheetNumber -> {
					EXCEL_FORMULA_EVALUATOR.evaluateAllFormulas(workbook.workbook, sheetNumber);
				})
				.peek(sheetNumber -> {
					if (transpose) {
						excelTransposer.transposeSheet(workbook.workbook, sheetNumber, true);
					}
				})
				.map(sheetNumber -> {
					Optional<String> optSheetName = Optional.ofNullable(workbook.workbook.getSheetAt(sheetNumber).getSheetName());
					RangeSheet currentRange = ranges.get(optSheetName);
					if (currentRange == null) {
						currentRange = ranges.get(empty());
					}

					StreamEx<Optional<NexlaMessage>> parseSheetResult = parseSheet(workbook, sheetNumber, currentRange);

					if (doInjectAdditionalCells) {
						List<NexlaMessage> additionalRangeData = getAdditionalPathMessages(workbook.workbook);
						parseSheetResult = StreamEx.of(parseSheetResult
							.filter(Optional::isPresent)
							.map(originalMsgOpt -> {
								additionalRangeData.forEach(nexlaMessage ->
									originalMsgOpt.get().getRawMessage().putAll(nexlaMessage.getRawMessage()));
								return originalMsgOpt;
							}).collect(toList()));
					}
					return parseSheetResult;
				});
		}
	}

	@SneakyThrows
	private StreamEx<Optional<NexlaMessage>> parseSheet(WorkbookWrapper workbook, int sheetNumber,
																											RangeSheet currentRange) {

		Sheet sheet = workbook.workbook.getSheetAt(sheetNumber);

		boolean headerSet = false;
		List<String> headers = Lists.newArrayList();

		// Initializing headers for Generated or Configured option
		switch (schemaDetection) {
			case HEADER:
				break;
			case GENERATED:
				headers = generateAttributes();
				headerSet = true;
				break;
			case CONFIGURED:
				headerSet = true;
				addAll(headers, configuredSchema.iterator());
				break;
		}

		File file = Files.createTempFile("temp", ".sheet").toFile();

		RowParseContext ctx = new RowParseContext(headerSet, headers, sheet, file);

		final int fromRow = ofNullable(currentRange)
			.flatMap(RangeSheet::getRange)
			.map(RangeNoSheet::getFrom)
			.flatMap(RangeFromTo::getRowIdxZeroBased)
			.orElse(0);
		final int toRow = ofNullable(currentRange)
			.flatMap(RangeSheet::getRange)
			.map(RangeNoSheet::getTo)
			.flatMap(RangeFromTo::getRowIdxZeroBased)
			.orElse(Integer.MAX_VALUE);
		final int fromCol = ofNullable(currentRange)
			.flatMap(RangeSheet::getRange)
			.map(RangeNoSheet::getFrom)
			.flatMap(RangeFromTo::getColumnIdxZeroBased)
			.orElse(0);
		final int toCol = ofNullable(currentRange)
			.flatMap(RangeSheet::getRange)
			.map(RangeNoSheet::getTo)
			.flatMap(RangeFromTo::getColumnIdxZeroBased)
			.orElse(Integer.MAX_VALUE);

		String sheetName = ctx.sheet.getSheetName();

		for (Row row : sheet) {
			try {
				if (row.getRowNum() >= fromRow && row.getRowNum() <= toRow) {
					parseRow(row, ctx, fromCol, toCol);
				}
			} catch (Exception e) {
				// skip erroneous row and proceed to next
				if (exceptionHandler != null) {
					// in order to keep things congruent - in ExceptionIterator we start counting from 1
					long messageNumber = ctx.sheetMessageNumber + 1;
					exceptionHandler.accept(new ParseError(messageNumber, empty(), "sheet:" + sheetName), e);
				}
			}
		}
		ctx.close();

		if (workbook.opcPackage != null) {
			workbook.opcPackage.revert();
		}
		if (workbook.tempFile != null) {
			workbook.tempFile.delete();
		}

		return StreamEx.of(Files.lines(file.toPath()))
				.map(line -> stringToType(line, NexlaMessage.class))
				.map(nexlaMessage -> {
					NexlaMetaData metaData = nexlaMessage.getNexlaMetaData();
					Map<String, Object> tags = (metaData == null || metaData.getTags() == null)
							? emptyMap()
							: metaData.getTags();
					tags.put(DATASET_CUSTOM_NAME, customDatasetName(sheetName, excelFileName, ofNullable(currentRange)));
					metaData.getTags().putAll(tags);
					return nexlaMessage;
				})
				.map(Optional::of)
				.onClose(file::delete);
	}

	@SneakyThrows
	private void parseRow(Row row, RowParseContext ctx, int fromCol, int toCol) {
		int rowCnt = row.getRowNum();
		int numCols = 0;
		int emptyCols = 0;
		List<String> currentRow = Lists.newArrayList();
		boolean visitFlag = false;
		LinkedHashMap<String, Object> innerMap = Maps.newLinkedHashMap();
		Iterator<String> headerIterator = ctx.headers.iterator();

		for (Cell cell : row) {
			int colIndex = cell.getColumnIndex();
			if (colIndex < fromCol || colIndex > toCol) {
				continue;
			}

			String potentialValue = getCellValue(cell);
			String rawValue;

			if ((cell.getCellType() != STRING && cell.getCellType() != CellType.FORMULA && cell.getCellType() != CellType.ERROR)
				&& isCellDateFormattedWrapped(cell) && DEFAULT_DATE.equals(potentialValue)) {
				rawValue = TIMESTAMP_FORMATTER.print(cell.getDateCellValue().getTime());
			} else {
				rawValue = potentialValue;
			}

			int colDifference = (colIndex - fromCol) - numCols;
			numCols++;

			int numEmptyCols = Math.abs(colDifference);
			emptyCols += numEmptyCols;
			numCols += numEmptyCols;

			if (StringUtils.isEmpty(rawValue)) {
				numEmptyCols++;
			}

			for (int i = 0; i < numEmptyCols; i++) {
				if (ctx.headerSet && headerIterator.hasNext()) {
					String head = headerIterator.next();
					if (!NULL.equals(head)) {
						if (!preserveEmptyStrings) {
							innerMap.put(head, NULL);
						} else innerMap.put(head, StringUtils.EMPTY);
					}
				}
				currentRow.add(NULL);
			}

			// Increment emptyCol to track number of empty columns in this row
			// Add null if cell is empty
			// If row is merged, ignore cell
			boolean isSkipped = skipMergedCells && isRowMerged(ctx.sheet, rowCnt, cell.getColumnIndex());

			if (StringUtils.isEmpty(rawValue) || isSkipped) {
				emptyCols++;
				continue;
			}

			String value = escapeString(rawValue);
			currentRow.add(value);
			visitFlag = true;

			// Assign determined values to headers depending on mode
			if (ctx.headerSet && headerIterator.hasNext() && (preserveNullStrings || !NULL.equals(value))) {
				switch (mode) {
					case CONCISE:
						innerMap.put(headerIterator.next(), value);
						break;
					case VERBOSE:
						Map<String, String> dataMap = new LinkedHashMap<>();
						addVerboseData(dataMap, value, getFormula(cell), getComments(cell), getHyperlink(cell));
						innerMap.put(headerIterator.next(), dataMap);
						break;
				}
			}
			if (NULL.equals(value) && !preserveNullStrings) {
				headerIterator.next();
			}
		}

		//finish processing empty cells
		while (headerIterator.hasNext() && HEADER.equals(schemaDetection)) {
			String head = headerIterator.next();
			if (!NULL.equals(head)) {
				if (!preserveEmptyStrings) {
					innerMap.put(head, NULL);
				} else innerMap.put(head, StringUtils.EMPTY);
			}
			numCols++;
			emptyCols++;
			currentRow.add(NULL);
		}

		ctx.headerSet = true;

		// Delete comma at the end of row
		if (!currentRow.isEmpty()) {
			// Check number of contiguous columns for header detection
			// If a new row is discovered that is wider than any previous row,
			// we consider this to be the new header and discard all previous data
			int contiguousCol = numCols - emptyCols;
			if (contiguousCol > ctx.widestRow) {

				if (ctx.writer != null) {
					ctx.writer.close();
					ctx.file.delete();
				}
				ctx.writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(ctx.file), StandardCharsets.UTF_8), ONE_MB_FLUSH_THRESHOLD);
				ctx.sheetMessageNumber = 0;

				if (HEADER.equals(schemaDetection)) {
					if (renameDuplicateHeaders) {
						ctx.headers = makeUniqueHeaderFromRow(currentRow);
					} else {
						ctx.headers = currentRow;
					}
					innerMap.clear();
				}
			}

			// Add to main string if at least one cell is present(visitFlag is true)
			if (visitFlag && !innerMap.isEmpty()) {
				// Update 'null' values with empty string
				if(nullAsEmptyString) {
					innerMap.forEach((key, value) -> {
						if (NULL.equals(value)) {
							innerMap.put(key, StringUtils.EMPTY);
						}
					});
				}

				NexlaMessage message = new NexlaMessage(innerMap);
				ctx.sheetMessageNumber = ctx.sheetMessageNumber + 1;

				Map<String, Object> tags = new HashMap<>();
				tags.put(SHEET_NAME, ctx.sheet.getSheetName());
				tags.put(SHEET_MESSAGE_NUMBER, ctx.sheetMessageNumber.toString());
				message.getNexlaMetaData().setTags(tags);
				ctx.writer.write(toJsonString(message));
				ctx.writer.newLine();
			}

			ctx.widestRow = Math.max(ctx.widestRow, contiguousCol);
		}
	}

	private Set<String> getAllAvailableSheets(Workbook workbook) {
		return StreamEx.of(IntStream.range(0, workbook.getNumberOfSheets()).boxed())
			.map(index -> workbook.getSheetAt(index).getSheetName())
			.collect(Collectors.toSet());
	}

	private void validateSheetNames(Set<String> sheetNames, Set<String> availableSheets) {
		logger.info("M=validateSheetNames, availableSheets={}, sheetNames={}", availableSheets, sheetNames);
		List<String> notFoundSheets = sheetNames.stream()
			.filter(it -> !availableSheets.contains(it))
			.collect(toList());

		if (CollectionUtils.isNotEmpty(notFoundSheets)) {
			var message = String.format("Could not find Sheet(s) %s references in Source configuration property `sheets`. The available sheets are %s.",
				notFoundSheets,
				availableSheets);
			throw new ConfigException(message);
		}
	}

	private static List<String> makeUniqueHeaderFromRow(List<String> currentRow) {
		Map<String, Integer> lastFreeIdByHeader = Maps.newHashMap();
		List<String> uniqueHeader = Lists.newArrayList();
		for (String header : currentRow) {
			Integer lastFreeId = lastFreeIdByHeader.getOrDefault(header, 0);
			if (lastFreeId == 0) {
				uniqueHeader.add(header);
			} else {
				uniqueHeader.add(header + "." + lastFreeId);
			}
			lastFreeIdByHeader.put(header, lastFreeId + 1);
		}
		return uniqueHeader;
	}

	private List<NexlaMessage> getAdditionalPathMessages(Workbook workbook) {
		Set<String> sheetNames = StreamEx.of(additionalRanges.keySet())
			.filter(Optional::isPresent)
			.map(Optional::get)
			.toSet();

		Set<String> availableSheets = getAllAvailableSheets(workbook);

		validateSheetNames(sheetNames, availableSheets);

		boolean defaultSheetRange = additionalRanges.size() == 1 && additionalRanges.keySet().iterator().next().isEmpty();

		Queue<String> generatedAttributesQueue = new LinkedList<>(generateAttributes());

		return StreamEx.of(IntStream.range(0, workbook.getNumberOfSheets()).boxed())
			.filter(sheetNumber -> {
				Sheet sheet = workbook.getSheetAt(sheetNumber);
				return additionalRanges.isEmpty() ||
					sheetNames.contains(sheet.getSheetName()) ||
					(sheetNumber == 0 && defaultSheetRange);
			})
			.flatMap(sheetNumber -> {
				Optional<String> optSheetName = Optional.ofNullable(workbook.getSheetAt(sheetNumber).getSheetName());
				AdditionalRange currentRange = additionalRanges.get(optSheetName);
				if (currentRange == null) {
					currentRange = additionalRanges.get(empty());
				}
				Sheet sheet = workbook.getSheetAt(sheetNumber);
				return StreamEx.of(readCellFromAdditionalRange(currentRange, sheet, generatedAttributesQueue));
			}).collect(toList());
	}

	private List<NexlaMessage> readCellFromAdditionalRange(AdditionalRange additionalRange, Sheet sheet, Queue<String> attributesQueue) {
		return StreamEx.of(additionalRange.getAdditionalRangeNoSheetList())
			.map(addRangeNoSheetList -> {
				Optional<String> rangeAttributeOpt = addRangeNoSheetList.getAttributeCell();
				Optional<String> attributeName = rangeAttributeOpt
					.filter(not(String::isBlank))
					.map(s -> getCellByReference(sheet, s))
					.orElseGet(() -> Optional.of(attributesQueue.remove()));

				String value = getCellByReference(sheet, addRangeNoSheetList.getValueCell()).orElse("");
				return new NexlaMessage(lhm(attributeName.orElseGet(attributesQueue::remove), value));
			}).collect(toList());
	}

	@SneakyThrows
	private Optional<String> getCellByReference(Sheet sheet, String referenceStr) {
		Optional<CellAddress> address = Optional.empty();
		try {
			address = Optional.of(new CellAddress(referenceStr)).filter(x -> x.getRow() >= 0 && x.getColumn() >= 0);
		} catch (Exception ignored) {
			logger.error("incorrect format of the cell reference: {}", referenceStr);
		}
		Optional<Row> row = address.map(a -> sheet.getRow(a.getRow()));
		Optional<Cell> cell = address.flatMap(a -> row.map(r -> r.getCell(a.getColumn(), Row.MissingCellPolicy.RETURN_BLANK_AS_NULL)));
		return cell.map(ExcelParser::getCellValue);
	}

	// isCellDateFormatted throws an Exception when callen on BOOLEAN cell
	private static boolean isCellDateFormattedWrapped(Cell cell) {
		try {
			return isCellDateFormatted(cell);
		} catch (Exception e) {
			return false;
		}

	}

	@SneakyThrows
	private WorkbookWrapper createWorkbook(InputStream inputStream) throws IOException {
		switch (excelType) {
			case EXCEL_XLS:
				return new WorkbookWrapper(new HSSFWorkbook(inputStream), null, null);
			case EXCEL_XLSX:
			default:
				// copy input stream to file to reduce memory footprint while reading OPCPackage
				File file = Files.createTempFile("temp", ".xlsx").toFile();
				try (FileOutputStream outputStream = new FileOutputStream(file)) {
					IOUtils.copyLarge(inputStream, outputStream);
				}
				try {
					OPCPackage opcPackage = OPCPackage.open(file);
					XSSFWorkbook wBook = new XSSFWorkbook(opcPackage);
					return new WorkbookWrapper(wBook, file, opcPackage);
				} catch (Exception e) {
					file.delete();
					throw e;
				}
		}
	}

	private static String customDatasetName(String sheetName, String excelFileName, Optional<RangeSheet>  rangeSheet) {
		String rangeStr = rangeSheet
				.flatMap(RangeSheet::getRange)
				.map(RangeNoSheet::toString)
				.orElse(null);
		String multiSheetSuffix = Stream.of(sheetName, rangeStr)
				.filter(s -> s != null && !s.isEmpty())
				.collect(Collectors.joining("_", "[", "]"));

		return Stream.of(excelFileName, multiSheetSuffix)
				.filter(s -> s != null && !s.isEmpty())
				.collect(Collectors.joining(" "));
	}

	static class SheetContentsHandler implements XSSFSheetXMLHandler.SheetContentsHandler {
		private Map<Short,String> headerMap = new HashMap<>();
		private boolean isHeaderSet = false;
		private boolean isHeaderRow = false;

		private LinkedHashMap<String,Object> rawMessage;
		private final List<NexlaMessage> output;

		private final String schemaDetection;
		private final Optional<RangeSheet> rangeSheet;
		private final String fileName;
		private final String sheetName;
		private final boolean preserveEmptyStrings;
		private final boolean renameDuplicateHeaders;

		private int longestRow = 0;
		private Map<Short,String> currentRow;
		private boolean rowHasData = false;
		private short prevCol;

		private int sheetMessageNumber = 0;

		public SheetContentsHandler(List<NexlaMessage> output, String schemaDetection, Optional<RangeSheet> rangeSheet, List<String> configuredSchema, boolean preserveEmptyStrings, boolean renameDuplicateHeaders, String fileName, String sheetName) {
			this.output = output;
			this.schemaDetection = schemaDetection;
			this.rangeSheet = rangeSheet;
			this.preserveEmptyStrings = preserveEmptyStrings;
			this.renameDuplicateHeaders = renameDuplicateHeaders;
			this.fileName = fileName;
			this.sheetName = sheetName;

			switch (schemaDetection) {
				case GENERATED:
					List<String> headers = generateAttributes();
					for (int i = 0; i < headers.size(); ++i) {
						this.headerMap.put((short) i, headers.get(i));
					}
					isHeaderSet = true;
					break;
				case CONFIGURED:
					for (int i = 0; i < configuredSchema.size(); ++i) {
						this.headerMap.put((short) i, configuredSchema.get(i));
					}
					isHeaderSet = true;
					break;
				case HEADER:
				default:
					break;
			}
		}

		@Override
		public void startRow(int i) {
			currentRow = new HashMap<>();

			prevCol = -1;
			if (HEADER.equals(schemaDetection) && !isHeaderSet) {
				isHeaderRow = true;
				return;
			}

			rawMessage = new LinkedHashMap<>();
			if (HEADER.equals(schemaDetection) || CONFIGURED.equals(schemaDetection)) {
				for (String header : headerMap.values()) {
					if (preserveEmptyStrings) {
						rawMessage.put(header, StringUtils.EMPTY);
					} else {
						rawMessage.put(header, NULL);
					}
				}
			}

			rowHasData = false;
		}

		@Override
		public void endRow(int i) {
			if (HEADER.equals(schemaDetection) && !isHeaderRow && rawMessage.size() > longestRow) {
				isHeaderRow = true;
				headerMap = currentRow;
				output.clear();
			} else if (GENERATED.equals(schemaDetection) && rawMessage.size() > longestRow) {
				longestRow = rawMessage.size();
				output.clear();
			}

			if (isHeaderRow) {
				if (renameDuplicateHeaders) {
					List<Map.Entry<Short, String>> sortedHeaders = headerMap.entrySet().stream()
							.sorted(Map.Entry.comparingByKey())
							.collect(Collectors.toList());
					List<String> uniqueHeaders = makeUniqueHeaderFromRow(
							sortedHeaders.stream().map(Map.Entry::getValue).collect(Collectors.toList())
					);

					headerMap.clear();
					for (int idx = 0; idx < uniqueHeaders.size(); ++idx) {
						short col = sortedHeaders.get(idx).getKey();
						headerMap.put(col, uniqueHeaders.get(idx));
					}
				}

				isHeaderSet = true;
				isHeaderRow = false;
				longestRow = headerMap.size();
			} else if (!rawMessage.isEmpty() && rowHasData) {
				NexlaMessage message = new NexlaMessage(rawMessage);

				Map<String,Object> tags = new HashMap<>() {{
					put(SHEET_MESSAGE_NUMBER, String.valueOf(sheetMessageNumber++));
					put(SHEET_NAME, sheetName);
					put(DATASET_CUSTOM_NAME, customDatasetName(sheetName, fileName, rangeSheet));
				}};
				message.getNexlaMetaData().setTags(tags);

				output.add(message);
			}
		}

		@Override
		public void cell(String cellRef, String value, XSSFComment xssfComment) {
			CellReference cellReference = new CellReference(cellRef);

			currentRow.put(cellReference.getCol(), escapeString(value));

			// Fill in empty cells
			if ((cellReference.getCol() - prevCol) > 1 && prevCol != -1) {
				for (int i = prevCol + 1; i < cellReference.getCol(); ++i) {
					if (isInRange(rangeSheet, new CellReference(cellReference.getRow(), i))) {
						if (preserveEmptyStrings) {
							rawMessage.put(headerMap.get((short) i), StringUtils.EMPTY);
						} else {
							rawMessage.put(headerMap.get((short) i), NULL);
						}
					}
				}
			}

			prevCol = cellReference.getCol();

			if (!isInRange(rangeSheet, cellReference)) {
				return;
			}

			if (isHeaderRow) {
				headerMap.put(cellReference.getCol(), escapeString(value));
			} else {
				if (value.equals("TRUE")) {
					value = "true";
				} else if (value.equals("FALSE")) {
					value = "false";
				} else if (value.startsWith("ERROR:")) {
					value = NULL;
				} else if (value.isEmpty() && !preserveEmptyStrings) {
					value = NULL;
				}

				rawMessage.put(headerMap.get(cellReference.getCol()), escapeString(value));
				rowHasData = true;
			}
		}

		private static boolean isInRange(Optional<RangeSheet> rangeSheet, CellReference cellReference) {
			return rangeSheet.flatMap(sheet -> sheet.getRange().map(rangeNoSheet -> {
				final int fromRow = rangeNoSheet.getFrom()
						.getRowIdxZeroBased()
						.orElse(0);
				final int toRow = rangeNoSheet.getTo()
						.getRowIdxZeroBased()
						.orElse(Integer.MAX_VALUE);
				final int fromCol = rangeNoSheet.getFrom()
						.getColumnIdxZeroBased()
						.orElse(0);
				final int toCol = rangeNoSheet.getTo()
						.getColumnIdxZeroBased()
						.orElse(Integer.MAX_VALUE);

				return fromRow <= cellReference.getRow() && cellReference.getRow() <= toRow
						&& fromCol <= cellReference.getCol() && cellReference.getCol() <= toCol;
			})).orElse(true);
		}
	}

	@SneakyThrows
	private StreamEx<StreamEx<Optional<NexlaMessage>>> parseMessagesStreamed(InputStream inputStream) {
		switch (excelType) {
			case EXCEL_XLS:
				throw new NotImplementedException("XLS format is not supported for streamed parsing.");
			case EXCEL_XLSX:
			default:
				File file = Files.createTempFile("temp", ".xlsx").toFile();
				try (FileOutputStream outputStream = new FileOutputStream(file)) {
					IOUtils.copyLarge(inputStream, outputStream);
				}

				List<StreamEx<Optional<NexlaMessage>>> output = new ArrayList<>();

				try (OPCPackage opcPackage = OPCPackage.open(file, PackageAccess.READ)) {
					ReadOnlySharedStringsTable sharedStrings = new ReadOnlySharedStringsTable(opcPackage);
					XSSFReader reader = new XSSFReader(opcPackage);
					StylesTable styles = reader.getStylesTable();

					String workbookDataXml = new String(reader.getWorkbookData().readAllBytes());
					List<Map<String,Object>> workbookData = DomParser.parse(new StringReader(workbookDataXml), "").collect(Collectors.toList());
					Set<String> availableSheets = workbookData.stream()
							.map(workbookInfo -> (Map<String, Object>) workbookInfo.get("workbook"))
							.map(workbook -> (Map<String, Object>) workbook.get("sheets"))
							.flatMap(sheets -> {
								Object sheetObj = sheets.get("sheet");
								if (sheetObj instanceof List) {
									return ((List<Map<String, String>>) sheetObj).stream();
								} else if (sheetObj instanceof Map) {
									return Stream.of((Map<String, String>) sheetObj);
								} else {
									return Stream.empty();
								}
							})
							.map(sheet -> sheet.get("-name"))
							.collect(Collectors.toSet());

					Set<String> requestedSheets = ranges.keySet().stream()
							.filter(Optional::isPresent)
							.map(Optional::get)
							.collect(Collectors.toSet());
					validateSheetNames(requestedSheets, availableSheets);

					boolean isFirstSheet = true;

					XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) reader.getSheetsData();
					while (iter.hasNext()) {
						try (InputStream stream = iter.next()) {
							if (
									!ranges.isEmpty()
											&& !(isFirstSheet && ranges.containsKey(Optional.empty()))
											&& !ranges.containsKey(Optional.of(iter.getSheetName()))
							) {
								continue;
							}

							InputSource inputSource = new InputSource(stream);
							XMLReader parser = XMLHelper.newXMLReader();

							List<NexlaMessage> sheetOutput = new ArrayList<>();

							XSSFSheetXMLHandler.SheetContentsHandler handler = new SheetContentsHandler(
									sheetOutput,
									schemaDetection,
									Optional.ofNullable(Optional.ofNullable(ranges.get(Optional.of(iter.getSheetName()))).orElse(ranges.get(Optional.empty()))),
									configuredSchema,
									preserveEmptyStrings,
									renameDuplicateHeaders,
									excelFileName,
									iter.getSheetName()
							);

							DataFormatter dataFormatter = new DataFormatter() {
								@Override
								public String formatRawCellContents(double value, int formatIndex, String formatString) {
									// format dates according to cell style, ensures we don't have any loss of time-of-day
									// other numerics should just preserve the value

									boolean isDate = DateUtil.isADateFormat(formatIndex, formatString);
									if (isDate) {
										return super.formatRawCellContents(value, formatIndex, formatString);
									} else {
										return NumberToTextConverter.toText(value);
									}
								}
							};

							ContentHandler contentHandler = new XSSFSheetXMLHandler(styles, null, sharedStrings, handler, dataFormatter, false);
							parser.setContentHandler(contentHandler);
							parser.parse(inputSource);

							output.add(StreamEx.of(sheetOutput.stream().map(Optional::of)));
							isFirstSheet = false;
						}
					}
				} finally {
					file.delete();
				}
				return StreamEx.of(output);
		}
	}

	protected static List<String> generateAttributes() {
		return IntStream.rangeClosed(1, INITIAL_ATTRIBUTES)
			.boxed()
			.map(i -> ATTRIBUTE + i)
			.collect(toList());
	}

	private static String escapeString(String value) {
		return value.trim()
			.replace('\n', ' ')
			.replace("\r\n", " ");
	}

	private static void addVerboseData(Map<String, String> dataMap, String value, String formula, String comment, String hyperlink) {
		dataMap.put(VALUE, value);
		if (StringUtils.isNotEmpty(formula)) {
			dataMap.put(FORMULA, formula);
		}
		if (StringUtils.isNotEmpty(comment)) {
			dataMap.put(COMMENT, comment);
		}
		if (StringUtils.isNotEmpty(hyperlink)) {
			dataMap.put(HYPERLINK, hyperlink);
		}
	}

	private static String getCellValue(Cell cell) {
		String value = "";
		CellType cellType = cell.getCellType();
		try {
			if (cellType == NUMERIC) {
				value = NumberToTextConverter.toText(cell.getNumericCellValue());
			} else if (cellType == STRING) {
				value = getStringData(cell);
			} else if (cellType == BOOLEAN) {
				value = String.valueOf(cell.getBooleanCellValue());
			} else if (cellType == CellType.FORMULA) {
				logger.error("Formula: {} should be evaluated up to this point.", cell.getCellFormula());
			}
			try {
				if (isCellDateFormatted(cell)) {
					value = cell.toString();
				}
			} catch (Exception ignored) {
			}
		} catch (Exception ignored) {
			return null;
		}
		return value;
	}

	private static String getStringData(Cell cell) {
		try {
			return cell.getStringCellValue();
		} catch (Exception ignored) {
			return null;
		}
	}

	private static String getFormula(Cell cell) {
		try {
			return cell.getCellFormula();
		} catch (Exception ignored) {
			return null;
		}
	}

	private static String getComments(Cell cell) {
		try {
			return cell.getCellComment().toString();
		} catch (Exception ignored) {
			return null;
		}
	}

	private static String getHyperlink(Cell cell) {
		try {
			return cell.getHyperlink().getAddress();
		} catch (Exception ignored) {
			return null;
		}
	}

	@Override
	public StreamEx<StreamEx<Optional<NexlaMessage>>> parseMultiPartMessages(InputStream inputStream) {
		boolean useStreamed = streamed;
		if (streamed && transpose) {
			logger.warn("Streamed Excel parsing does not support transposed files. Falling back to non-streamed parsing.");
			useStreamed = false;
		}
		if (streamed && !additionalRanges.isEmpty()) {
			logger.warn("Streamed Excel parsing does not support additional ranges. Falling back to non-streamed parsing.");
			useStreamed = false;
		}
		if (streamed && EXCEL_XLS.equals(excelType)) {
			// line limit of xls means streamed parsing is unnecessary
			logger.warn("Streamed parsing is unnecessary for XLS files. Falling back to non-streamed parsing.");
			useStreamed = false;
		}
		// additionally the streamed implementation cannot use formulae that reference other rows or sheets
		// but there's not a good way to automatically detect that, which is why it is opt-in

		logger.info("Using streamed parsing: {}. For Excel file {}", useStreamed, excelFileName);

		if (useStreamed) {
			return parseMessagesStreamed(inputStream);
		} else {
			return parseMessagesInMemory(inputStream);
		}
	}

	@Override
	public StreamEx<Optional<NexlaMessage>> parseMessages(InputStream inputStream) {
		return parseMultiPartMessages(inputStream)
				.reduce(StreamEx.empty(), (a, b) -> StreamEx.of(Stream.concat(a, b)));
	}

	@AllArgsConstructor
	public static class WorkbookWrapper implements AutoCloseable {
		public final Workbook workbook;
		public final File tempFile;
		public final OPCPackage opcPackage;

		@Override
		public void close() throws Exception {
			if (workbook != null) {
				workbook.close();
			}
			if (opcPackage != null) {
				opcPackage.close();
			}
			if (tempFile != null && tempFile.exists()) {
				tempFile.delete();
			}
		}
	}

	public static class RowParseContext {
		private boolean headerSet;
		private List<String> headers;
		private Sheet sheet;
		private File file;
		private int widestRow = 0;
		private Integer sheetMessageNumber = 0;
		private BufferedWriter writer = null;

		public RowParseContext(boolean headerSet, List<String> headers, Sheet sheet, File file) {
			this.headerSet = headerSet;
			this.headers = headers;
			this.sheet = sheet;
			this.file = file;
		}

		@SneakyThrows
		public void close() {
			if (writer != null) {
				writer.flush();
				writer.close();
			}
		}
	}
}