package com.nexla.parser;

import com.ddtek.xmlconverter.Converter;
import com.ddtek.xmlconverter.ConverterFactory;
import com.nexla.common.NexlaMessage;
import com.nexla.common.io.SequenceInputStream;
import com.nexla.common.parse.NexlaParser;
import com.nexla.parser.xml.DomParser;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;

import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Optional;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.ConfigUtils.optBoolean;
import static com.nexla.common.parse.ParserConfigs.Edi.EDI_OPTIONS;
import static com.nexla.common.parse.ParserConfigs.Edi.EDI_SKIP_ADVANCE_TO_ISA;
import static com.nexla.common.parse.ParserConfigs.Edi.EDI_XPATH;
import static java.util.Arrays.asList;

public class EdiParser extends NexlaParser {

	public static final List<String> DOCUMENT_START_OPTIONS = asList("ISA*", "ISA:", "ISA+");

	String xpath;

	Optional<String> options = Optional.empty();

	boolean advanceToDocStart = true;

	public EdiParser option(String key, String value) {
		switch (key) {
			case EDI_XPATH:
				this.xpath = value;
				break;
			case EDI_OPTIONS:
				this.options = opt(value);
				break;
			case EDI_SKIP_ADVANCE_TO_ISA:
				this.advanceToDocStart = optBoolean(value).orElse(true);
				break;
			default:
				super.option(key, value);
		}
		return this;
	}

	@SneakyThrows
	@Override
	public StreamEx<Optional<NexlaMessage>> parseMessages(InputStream inputStream) {
		return Optional.of(inputStream)
			.flatMap(inStream -> advanceToDocStart ? advanceToDocumentStart(inStream) : Optional.of(inStream))
			.map(this::parseEdi)
			.orElse(StreamEx.empty());
	}

	@SneakyThrows
	private StreamEx<Optional<NexlaMessage>> parseEdi(InputStream inStream) {

		Converter toXML = new ConverterFactory().newConvertToXML(
			"converter:EDI:opt=yes:typ=false" +
			options.map(opt -> ":" + opt).orElse(""));

		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		StreamResult result = new StreamResult(outputStream);
		toXML.convert(new StreamSource(inStream), result);

		InputStreamReader reader = new InputStreamReader(new ByteArrayInputStream(outputStream.toByteArray()));
		return DomParser.parse(reader, xpath).map(v -> Optional.of(new NexlaMessage(v)));
	}

	@SneakyThrows
	private Optional<InputStream> advanceToDocumentStart(InputStream inputStream) {
		logger().debug("Advancing EDI document to first occurrence of " + DOCUMENT_START_OPTIONS);

		int b;
		StringBuilder sb = new StringBuilder();
		int maxLen = 1000;

		while ((b = inputStream.read()) != -1) {
			char c = (char) b;
			sb.append(c);
			for (String docStart : DOCUMENT_START_OPTIONS) {
				if (sb.lastIndexOf(docStart) != -1) {
					logger().debug("Document start successfully found");
					return Optional.of(new SequenceInputStream(new ByteArrayInputStream(docStart.getBytes()), inputStream));
				}
			}
			if (sb.length() > maxLen) {
				sb.delete(0, sb.length() - DOCUMENT_START_OPTIONS.get(0).length());
			}
		}
		logger().debug("Advancing to document start failed, skipping document");
		return Optional.empty();
	}
}
