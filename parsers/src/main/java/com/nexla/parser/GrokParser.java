package com.nexla.parser;

import com.nexla.common.NexlaMessage;
import com.nexla.common.parse.Grokker;
import com.nexla.common.parse.NexlaParser;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.LinkedHashMap;
import java.util.Optional;

import static com.nexla.common.parse.ParserConfigs.Grok.GROK_PATTERN;
import static org.apache.commons.lang3.StringUtils.trimToNull;

public class GrokParser extends NexlaParser {

	private String pattern;

	public GrokParser option(String key, String value) {
		super.option(key, value);
		switch (key) {
			case GROK_PATTERN:
				this.pattern = trimToNull(value);
				break;
		}
		return this;
	}

	@Override
	@SneakyThrows
	public StreamEx<Optional<NexlaMessage>> parseMessages(InputStream inputStream) {
		InputStreamReader isr = new InputStreamReader(inputStream);
		BufferedReader br = new BufferedReader(isr);
		return StreamEx.of(br.lines())
			.map(line -> Grokker.INSTANCE.parseLog(line, pattern))
			.map(map -> Optional.of(new NexlaMessage(new LinkedHashMap<>(map))));
	}

}
