package com.nexla.parser;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexla.common.NexlaMessage;

import java.io.InputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import com.nexla.common.exception.ParseError;
import com.nexla.common.parse.NexlaParser;
import com.nexla.parser.xml.AdditionalPathsParser;
import com.prowidesoftware.swift.io.parser.SwiftParserConfiguration;
import com.prowidesoftware.swift.model.*;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.io.IOUtils;

import static com.nexla.common.parse.ParserConfigs.Swift.*;
import static java.util.Optional.empty;

public class SwiftParser extends NexlaParser {
    
    private static final int SWIFT_DOC_BLOCK_COUNT = 5;
    private boolean lenient = true;
    private boolean parseTextBlock = true;
    private boolean parseTrailerBlock = true;
    private boolean parseUserBlock = true;
    
    public SwiftParser option(String key, String value) {
        super.option(key, value);
        switch (key) {
            case LENIENT:
                this.lenient = Boolean.parseBoolean(value);
                break;
            case PARSE_TEXT_BLOCK:
                this.parseTextBlock = Boolean.parseBoolean(value);
                break;
            case PARSE_TRAILER_BLOCK:
                this.parseTrailerBlock = Boolean.parseBoolean(value);
                break;
            case PARSE_USER_BLOCK:
                this.parseUserBlock = Boolean.parseBoolean(value);
                break;    
        }
        return this;
    }

    @Override
    @SneakyThrows
    public StreamEx<Optional<NexlaMessage>> parseMessages(InputStream inputStream) {
        SwiftParserConfiguration swiftParserConfiguration = initSwiftParserConfig();
        com.prowidesoftware.swift.io.parser.SwiftParser parser = new com.prowidesoftware.swift.io.parser.SwiftParser(inputStream);
        parser.setConfiguration(swiftParserConfiguration);

        SwiftMessage swiftMessage = parser.message();
        
        if (exceptionHandler != null) {
            AtomicLong errorMessageNumber = new AtomicLong(1);
            StreamEx.of(parser.getErrors()).forEach(errorMessage -> {
                    exceptionHandler.accept(new ParseError(errorMessageNumber.getAndIncrement(), empty(), errorMessage), 
                            new Exception("error occurred while parsing SWIFT document: " + errorMessage));
                }
            );
        }
        
        return StreamEx.of(swiftToNexlaMessageList(swiftMessage));
    }


    private SwiftParserConfiguration initSwiftParserConfig() {
        SwiftParserConfiguration swiftParserConfiguration = new SwiftParserConfiguration();
        swiftParserConfiguration.setLenient(lenient);
        swiftParserConfiguration.setParseTextBlock(parseTextBlock);
        swiftParserConfiguration.setParseTrailerBlock(parseTrailerBlock);
        swiftParserConfiguration.setParseUserBlock(parseUserBlock);
        return swiftParserConfiguration;
    }

    private List<Optional<NexlaMessage>> swiftToNexlaMessageList(SwiftMessage swiftMessage) {
        List<Optional<NexlaMessage>> nexlaMessageList = new ArrayList<>();

        for (int i = 1; i <= SWIFT_DOC_BLOCK_COUNT; i++) {
            SwiftBlock block = swiftMessage.getBlock(i);

            if (block != null) {
                if (block.isTagBlock()) {
                    SwiftTagListBlock tagListBlock = (SwiftTagListBlock) block;
                    StreamEx.of(tagListBlock.tagIterator())
                            .forEach(tag -> {
                                LinkedHashMap<String, Object> data = new LinkedHashMap<>();
                                data.put(tag.getName(), tag.getValue());
                                nexlaMessageList.add(Optional.of(new NexlaMessage(data)));
                            });
                } else {
                    nexlaMessageList.addAll(blockToNexlaMessageOptList(block, i));
                }
            }
        }

        return nexlaMessageList;
    }
    
    /**
     * Extracts data from string JSON representation of the SwiftBlock  using delegate JSON parser 
     * and converts it to the NexlaMessage
     * 
     * @param block
     * @param blockNumber
     * @return list of NexlaMessage optionals
     */
    @SneakyThrows
    private List<Optional<NexlaMessage>> blockToNexlaMessageOptList(SwiftBlock block, int blockNumber) {
        AdditionalPathsParser delegateJsonParser = new AdditionalPathsParser(new JsonParser());
        String jsonMessage;

        switch (blockNumber) {
            case 1: 
                SwiftBlock1 block1 = (SwiftBlock1) block;
                jsonMessage = block1.toJson();
                break;
            case 2:
                SwiftBlock2 block2 = (SwiftBlock2) block;
                jsonMessage = block2.toJson();
                break;    
            default:
                return new ArrayList<>();
        }
        JsonNode blockData = new ObjectMapper().readTree(jsonMessage);
        String stringBlockData = blockData.toString();
        
        return delegateJsonParser.parseMessages(IOUtils.toInputStream(stringBlockData, "UTF-8")).collect(Collectors.toList());
    }
    
}
