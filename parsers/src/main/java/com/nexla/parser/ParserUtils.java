package com.nexla.parser;

import com.nexla.admin.client.*;
import com.nexla.common.NexlaDataCredentials;
import com.nexla.common.parse.FilePropertiesDetector;
import com.nexla.common.parse.NexlaParser;
import com.nexla.common.parse.SampleContentType;
import com.nexla.common.script.Language;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.nashorn.api.scripting.NashornScriptEngineFactory;
import com.nexla.parser.orc.OrcParser;
import com.nexla.parser.pb.ProtobufParser;
import com.nexla.parser.pdf.PdfParser;
import com.nexla.parser.xml.AdditionalPathsParser;
import com.nexla.parser.xml.XmlParser;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.python.jsr223.PyScriptEngineFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.script.ScriptEngineManager;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

import static com.google.common.io.Files.getFileExtension;
import static com.nexla.common.FileUtils.joinStream;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.StreamUtils.lhm;
import static com.nexla.common.parse.FilePropertiesDetector.MAX_LINES;
import static com.nexla.common.parse.FilePropertiesDetector.readSample;
import static com.nexla.common.parse.ParserConfigs.Excel.*;
import static com.nexla.common.parse.ParserConfigs.FixedWidth.FIELD_LENGTHS;
import static com.nexla.common.parse.ParserConfigs.FixedWidth.PARQUET_INT96_AS_TIMESTAMP;
import static com.nexla.common.parse.ParserConfigs.Swift.*;
import static com.nexla.common.parse.ParserConfigs.UnstructuredAi.*;
import static com.nexla.common.parse.ParserConfigs.Zip.EMPTY_PASSWORD;
import static com.nexla.common.parse.ParserConfigs.Zip.ZIP_PASSWORD;
import static com.nexla.common.parse.SampleContentType.BINARY;
import static com.nexla.connector.config.file.FileSourceConnectorConfig.*;
import static com.nexla.parser.DelimitedTextParser.DEFAULT_OPTIONS;
import static java.util.Optional.ofNullable;

public class ParserUtils {

	public static final Logger logger = LoggerFactory.getLogger(ParserUtils.class);

	private static final ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
	static {
		scriptEngineManager.registerEngineName(Language.JAVASCRIPT.getName(), new NashornScriptEngineFactory());
		scriptEngineManager.registerEngineName(Language.PYTHON.getName(), new PyScriptEngineFactory());
	}

	public static final LinkedHashMap<String, String> MIME_TO_EXTENSION = lhm(
		MIME_GOOGLE_SPREADSHEET, "xlsx",
		MIME_EXCEL, "xlsx",
		MIME_ZIP, "zip",
		MIME_GZIP, "gz",
		MIME_TAR, "tar",
		MIME_CSV, "csv",
		MIME_TEXT, "txt",
		MIME_JSON, "json",
		MIME_XML, "xml",
		MIME_TEXT_XML, "xml");

	public static final LinkedHashMap<String, String> EXTENSION_TO_MIME = lhm(
		"xlsx", MIME_EXCEL,
		"zip", MIME_ZIP,
		"gz", MIME_GZIP,
		"tar", MIME_TAR,
		"csv", MIME_CSV,
		"txt", MIME_TEXT,
		"json", MIME_JSON,
		"xml", MIME_TEXT_XML);

	public static Optional<NexlaParser> getParserByFormat(
		Optional<String> mimeType,
		String fileNameOrFormat,
		Map<String, String> overriddenExtensions,
		Map<String, String> config
	) {
		String fileExtension = mimeType.map(MIME_TO_EXTENSION::get)
			.orElseGet(() -> getFileExtension("." + fileNameOrFormat).toLowerCase());

		// archive and pgp parsers will internally handle extension overrides and delegation
		// if not an archive or pgp file, we must check and apply overrides
		final Optional<String> extensionOverride =
			isArchiveType(fileExtension) || isEncryptedType(fileExtension)
				? Optional.empty()
				: ofNullable(overriddenExtensions.get(OVERRIDDEN_EXTENSIONS_ANY))
				.or(() -> ofNullable(overriddenExtensions.get(fileExtension)));

		Optional<NexlaParser> optParser = getNexlaParserByExtension(fileNameOrFormat, overriddenExtensions, config,
			extensionOverride.orElse(fileExtension)
		);

		if ("true".equals(config.get(POST_PROCESSOR))) {
			optParser = optParser.map(TransformingParserWrapper::new);
		}

		return optParser
			.map(parser -> {
				parser.config(config);
				return parser;
			});
	}

	private static Optional<NexlaParser> getNexlaParserByExtension(
		String fileNameOrFormat,
		Map<String, String> overriddenExtensions,
		Map<String, String> config,
		String extension
	) {

		switch (extension) {
			case UNSTRUCTURED:
				return Optional.of(new UnstructuredDataParser(fileNameOrFormat));
			case ASC:
			case GPG:
			case PGP:
				FileSourceConnectorConfig sourceConfig = new FileSourceConnectorConfig(config);
				return sourceConfig
					.fileEncryptConfig
					.map(encCfg -> new AscParser(fileNameOrFormat, encCfg, overriddenExtensions));

			case EXCEL_XLSX:
			case EXCEL_XLS:
				return Optional.of(new ExcelParser().options(Map.of(EXCEL_TYPE, extension,
					EXCEL_FILE_NAME, fileNameOrFormat)
				));
			case BINARY_EXTENSION:
				return Optional.of(new BinaryParser(fileNameOrFormat));
			case "zip":
				return Optional.of(new ZipParserUtils().overriddenExtensions(overriddenExtensions)
					.option(ZIP_PASSWORD, config.getOrDefault(ZIP_PASSWORD, EMPTY_PASSWORD)));
			case "gz": {
				return Optional.of(new GzipParser(fileNameOrFormat, overriddenExtensions));
			}
			case "csv": {
				return Optional.of(new DelimitedTextParser().options(DEFAULT_OPTIONS.get(',')));
			}
			case "tsv":
				return Optional.of(new DelimitedTextParser().options(DEFAULT_OPTIONS.get('\t')));
			case "psv":
				return Optional.of(new DelimitedTextParser().options(DEFAULT_OPTIONS.get('|')));
			case "txt": {
				return Optional.of(config.containsKey(FIELD_LENGTHS) ? new FixedWidthParser() : new DelimitedTextParser());
			}
			case "onx":
			case "xml":
				return Optional.of(new AdditionalPathsParser(new XmlParser()));
			case "parquet": {
				String parquetDecimalAsDate = config.get(PARQUET_INT96_AS_TIMESTAMP);
				if (parquetDecimalAsDate != null) {
					boolean decimalDate = false;
					try {
						decimalDate = Boolean.parseBoolean(parquetDecimalAsDate);
						return Optional.of(new ParquetParser(decimalDate));
					} catch (Exception ignored) {
						// noop
					}
				} else {
					return Optional.of(new ParquetParser(false));
				}
			}
			case "orc":
				return Optional.of(new OrcParser());
			case "dat":
			case "edi":
			case "x12":
				return Optional.of(new EdiParser());
			case "json":
				return Optional.of(new AdditionalPathsParser(new JsonParser()));
			case "avro":
				return Optional.of(new AvroParser());
			case "pdf":
				return Optional.of(new PdfParser(config));
			case "log":
				return Optional.of(new GrokParser());
			case "pb":
				return Optional.of(new ProtobufParser());
			case "fw":
				return Optional.of(new FixedWidthParser());
			case "tar":
				return Optional.of(new TarParser());
			case SWIFT:
			case FIN:
			case OUT:
			case IN:
				return Optional.of(new SwiftParser());
			default:
				return Optional.empty();
		}
	}

	private static boolean isArchiveType(String extension) {
		return "gz".equalsIgnoreCase(extension) || "zip".equalsIgnoreCase(extension) || "tar".equalsIgnoreCase(extension);
	}

	private static boolean isEncryptedType(String extension) {
		return "pgp".equalsIgnoreCase(extension);
	}

	@SneakyThrows
	public static BufferedReader reader(InputStream inputStream, String charset, int bufferSize) {
		return new BufferedReader(new InputStreamReader(inputStream, charset), bufferSize);
	}

	@AllArgsConstructor
	public static class GetParserResult {
		public final Optional<NexlaParser> parser;
		public final Optional<byte[]> sampleUsed;
		public final InputStream restoredStream;
		public final Optional<SampleContentType> contentType;
	}

	public static GetParserResult tryGetParserByExtOrContent(
		Optional<String> mimeType,
		InputStream is,
		Map<String, String> overriddenExtensions,
		Map<String, String> config,
		String fullPath
	) {
		Optional<NexlaParser> parserByExtension = getParserByFormat(mimeType, fullPath, overriddenExtensions, config);
		if (parserByExtension.isPresent()) {
			return new GetParserResult(parserByExtension, Optional.empty(), is, Optional.empty());
		} else {
			logger.info("Unknown extension for file, trying to detect by content: {}", fullPath);

			byte[] sample = readSample(is);
			FilePropertiesDetector detector = new FilePropertiesDetector(sample, MAX_LINES);

			Optional<SampleContentType> sampleContentType = detector.detectContentType();
			logger.info("Detected format '{}' for file={}", sampleContentType, fullPath);

			Optional<NexlaParser> parser = sampleContentType
				.filter(f -> f != BINARY)
				.flatMap(type -> getParserByFormat(mimeType, type.name(), overriddenExtensions, config));

			return new GetParserResult(parser, Optional.of(sample), joinStream(sample, is), sampleContentType);
		}
	}

	public static Optional<NexlaParser> createCustomParser(AdminApiClient adminApiClient, Integer dataSourceId,
																												 Map<String, Object> metadata, String decryptKey) {
		try {
			DataSource dataSource = adminApiClient.getDataSource(dataSourceId).get();
			Integer codeContainerId = dataSource.getCodeContainerId();
			Optional<NexlaParser> customParser = Optional.empty();
			Integer runtimeDataCredsId = null;
			logger.info("Source {} has code container {}", dataSourceId, codeContainerId);
			if (codeContainerId != null) {
				CodeContainer codeContainer = adminApiClient.getCodeContainer(codeContainerId);
				RuntimeDataCredentials runtimeDataCredentials = codeContainer.getRuntimeDataCredentials();
				Map<Object, Object> args = new HashMap<>();
				if (runtimeDataCredentials != null) {
					runtimeDataCredsId = runtimeDataCredentials.getId();
					DataCredentials dataCredentials = adminApiClient.getDataCredentials(runtimeDataCredsId).get();
					if (dataCredentials.getCredentialsType() == DataCredentials.NonConnectionTypeCredentialType.GENERIC) {
						Map<String, String> sensitiveArgs = NexlaDataCredentials.getCreds(decryptKey, dataCredentials.getCredentialsEnc(), dataCredentials.getCredentialsEncIv());
						args.putAll(sensitiveArgs);
					}
				}

				customParser = ParserUtils.createCustomParser(codeContainer, metadata, args);
			}

			if (customParser.isPresent()) {
				logger.info("Using custom parser {} in source {} with runtime data credentials {}",
					codeContainerId, dataSourceId, runtimeDataCredsId);
			} else {
				logger.info("No customer parser created for source {}", dataSourceId);
			}

			return customParser;
		} catch (Exception e) {
			logger.warn("Exception while creating custom parser for source {}: {}", dataSourceId, e);
			return Optional.empty();
		}
	}

	public static Optional<NexlaParser> createCustomParser(CodeContainer config, Map<String, Object> metadata, Map<Object, Object> args) {
		String code = config.getCode();
		if (StringUtils.isEmpty(code)) {
			return Optional.empty();
		}

		Language language = Language.valueOfIgnoreCase(config.getCodeType());
		if ("base64".equalsIgnoreCase(config.getCodeEncoding())) {
			code = new String(Base64.getDecoder().decode(code.replace("\n", "")));
		}
		switch (language) {
			case PYTHON:
				return Optional.of(new PythonCustomParser(code, scriptEngineManager.getEngineByName(Language.PYTHON.getName()), metadata, args));
			case JAVASCRIPT:
				return Optional.of(new JavaScriptCustomParser(code, scriptEngineManager.getEngineByName(Language.JAVASCRIPT.getName()), metadata, args));
			default:
				return Optional.empty();
		}
	}

}
