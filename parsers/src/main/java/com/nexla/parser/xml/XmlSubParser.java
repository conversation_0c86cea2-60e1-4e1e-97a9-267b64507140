package com.nexla.parser.xml;

import lombok.SneakyThrows;
import org.w3c.dom.Node;

import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.StringWriter;

public interface XmlSubParser {

	DocumentBuilderFactory DOCUMENT_BUILDER_FACTORY = DocumentBuilderFactory.newInstance();
	TransformerFactory TRANSFORMER_FACTORY = TransformerFactory.newInstance();

	static Transformer getTransformer() throws TransformerConfigurationException {
		Transformer transformer = TRANSFORMER_FACTORY.newTransformer();
		transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
		return transformer;
	}

	@SneakyThrows
	static String nodeToString(Node node, Transformer t) {
		StringWriter sw = new StringWriter();
		t.transform(new DOMSource(node), new StreamResult(sw));
		return sw.toString();
	}
}
