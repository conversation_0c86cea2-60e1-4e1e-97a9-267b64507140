package com.nexla.parser.xml;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Supplier;
import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.exception.ParseError;
import com.nexla.common.parse.NexlaParser;
import one.util.streamex.StreamEx;

import java.io.InputStream;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;

import static com.bazaarvoice.jolt.JsonUtils.stringToType;
import static org.apache.commons.lang3.StringUtils.trimToNull;

public class AdditionalPathsParser extends NexlaParser {

	public static final TypeReference<List<String>> LIST_TYPE_REFERENCE = new TypeReference<List<String>>() {
	};
	public static final String NO_XPATH = "";
	public static final BiConsumer<ParseError, Exception> NOOP_EXCEPTION_CONSUMER = (parseError, e) -> {
	};

	private Optional<String> path = Optional.empty();
	private List<String> additionalPath = Collections.emptyList();

	private final DocumentParser docParser;

	public AdditionalPathsParser(DocumentParser docParser) {
		this.docParser = docParser;
	}

	public AdditionalPathsParser option(String key, String value) {
		super.option(key, value);
		docParser.option(key, value);
		if (docParser.getPathParameter().equalsIgnoreCase(key)) {
			this.path = Optional.ofNullable(trimToNull(value));
		}
		if (docParser.getAdditionalPathParameter().equalsIgnoreCase(key)) {
			this.additionalPath = Optional.ofNullable(trimToNull(value))
				.map(val -> stringToType(val, LIST_TYPE_REFERENCE))
				.orElse(Collections.emptyList());
		}
		return this;
	}

	@Override
	public StreamEx<Optional<NexlaMessage>> parseMessages(Supplier<InputStream> isSupplier, boolean autoClose) {
		// turn off exc handler after additional paths processing
		docParser.exceptionHandler(NOOP_EXCEPTION_CONSUMER);
		Map<String, Object> additionalData = StreamEx
			.of(additionalPath)
			.flatMap(xPath -> {
				docParser.option(docParser.getPathParameter(), xPath);
				return docParser.parseMessages(isSupplier, autoClose);
			})
			.flatMap(StreamEx::of)
			.flatMapToEntry(NexlaMessage::getRawMessage)
			.toMap((o, o2) -> o);

		// turn on exc handler after additional paths processing
		docParser.exceptionHandler(exceptionHandler);

		String newPath = path.orElse(NO_XPATH);
		docParser.option(docParser.getPathParameter(), newPath);
		StreamEx<LinkedHashMap<String, Object>> result = docParser.parseMessages(isSupplier, autoClose)
			.filter(Optional::isPresent)
			.map(res -> res.get().getRawMessage());

		return createDocumentParseResult(additionalData, result);
	}

	@Override
	public StreamEx<Optional<NexlaMessage>> parseMessages(InputStream inputStream) {
		if (!additionalPath.isEmpty()) {
			throw new IllegalStateException("For additional paths use `parseMessages(Supplier<InputStream> isSupplier)`");
		}
		return this.parseMessages(() -> inputStream, false);
	}

	public static StreamEx<Optional<NexlaMessage>> createDocumentParseResult(
		Map<String, Object> augmentData,
		StreamEx<LinkedHashMap<String, Object>> read
	) {
		return read.map(recordMap -> {
			LinkedHashMap<String, Object> result = Maps.newLinkedHashMap(augmentData);
			result.putAll(recordMap);
			return Optional.of(new NexlaMessage(result));
		});
	}

	public AdditionalPathsParser exceptionHandler(BiConsumer<ParseError, Exception> exceptionHandler) {
		this.exceptionHandler = exceptionHandler;
		return this;
	}

}
