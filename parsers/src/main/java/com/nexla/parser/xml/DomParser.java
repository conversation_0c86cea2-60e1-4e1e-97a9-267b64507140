package com.nexla.parser.xml;

import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathFactory;
import java.io.File;
import java.io.Reader;
import java.io.StringReader;
import java.util.LinkedHashMap;
import java.util.List;

import static com.nexla.parser.xml.XmlSubParser.getTransformer;
import static com.nexla.parser.xml.XmlSubParser.nodeToString;
import static java.util.stream.Collectors.toList;
import static java.util.stream.IntStream.range;
import static org.apache.commons.lang3.StringUtils.isBlank;

public class DomParser implements XmlSubParser {

	@SneakyThrows
	public static void main(String... ag) {
		String filepath = "/Users/<USER>/work/netsuite.wsdl.xml";
		String filepath2 = "/Users/<USER>/work/netsuite2.wsdl.xml";

		Document doc = DocumentBuilderFactory.newInstance().newDocumentBuilder().parse(
			new InputSource(filepath));

		XPath xpath = XPathFactory.newInstance().newXPath();
		NodeList nodes = (NodeList) xpath.evaluate("//employee/name[text()='old']", doc, XPathConstants.NODESET);

		for (int idx = 0; idx < nodes.getLength(); idx++) {
			nodes.item(idx).setTextContent("new value");
		}

		Transformer xformer = TransformerFactory.newInstance().newTransformer();
		xformer.transform(new DOMSource(doc), new StreamResult(new File("data_new.xml")));


//		DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
//		DocumentBuilder docBuilder = docFactory.newDocumentBuilder();
//		Document doc = docBuilder.parse(filepath);
//
//		// Get the root element
//		Node company = doc.getFirstChild();
//
//		// Get the staff element , it may not working if tag has spaces, or
//		// whatever weird characters in front...it's better to use
//		// getElementsByTagName() to get it directly.
//		// Node staff = company.getFirstChild();
//
//		// Get the staff element by tag name directly
//		Node staff = doc.getElementsByTagName("types").item(0);
//
//		// update staff attribute
//		NamedNodeMap attr = staff.getAttributes();
//		Node nodeAttr = attr.getNamedItem("id");
//		nodeAttr.setTextContent("2");
//
//		// append a new node to staff
//		Element age = doc.createElement("age");
//		age.appendChild(doc.createTextNode("28"));
//		staff.appendChild(age);
//
//		// loop the staff child node
//		NodeList list = staff.getChildNodes();
//
//		for (int i = 0; i < list.getLength(); i++) {
//
//			Node node = list.item(i);
//
//			// get the salary element, and update the value
//			if ("salary".equals(node.getNodeName())) {
//				node.setTextContent("2000000");
//			}
//
//			//remove firstname
//			if ("firstname".equals(node.getNodeName())) {
//				staff.removeChild(node);
//			}
//
//		}
//
//		// write the content into xml file
//		TransformerFactory transformerFactory = TransformerFactory.newInstance();
//		Transformer transformer = transformerFactory.newTransformer();
//		DOMSource source = new DOMSource(doc);
//
//		StreamResult result = new StreamResult(new File(filepath2));
//		transformer.transform(source, result);
//
//		System.out.println("Done");

	}

	@SneakyThrows
	public static StreamEx<LinkedHashMap<String, Object>> parse(Reader reader, String dataPath) {

		if (isBlank(dataPath)) {
			return StreamEx.of(XmlUtils.toRawMessage(reader));
		} else {
			DocumentBuilder builder = DOCUMENT_BUILDER_FACTORY.newDocumentBuilder();
			// ignoring dtd files
			builder.setEntityResolver((publicId, systemId) ->
				systemId.toLowerCase().endsWith(".dtd")
					? new InputSource(new StringReader(""))
					: null);

			Document doc = builder.parse(new InputSource(reader));

			XPathFactory xPathFactory = XPathFactory.newInstance();
			XPath xpath = xPathFactory.newXPath();
			XPathExpression expr = xpath.compile(dataPath);

			NodeList result = (NodeList) expr.evaluate(doc, XPathConstants.NODESET);

			Transformer transformer = getTransformer();

			List<LinkedHashMap<String, Object>> items =
				range(0, result.getLength()).boxed()
					.map(index -> XmlUtils.toRawMessage(nodeToString(result.item(index), transformer)))
					.collect(toList());

			return StreamEx.of(items);
		}
	}
}