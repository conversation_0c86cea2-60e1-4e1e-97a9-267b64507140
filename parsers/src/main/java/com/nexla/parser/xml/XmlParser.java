package com.nexla.parser.xml;

import com.nexla.common.NexlaMessage;
import com.nexla.common.exception.ParseError;
import com.nexla.common.io.SourceAwareInputStream;
import com.nexla.common.parse.FilePropertiesDetector;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.input.BOMInputStream;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.StringReader;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Optional;
import java.util.stream.Stream;

import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.common.FileUtils.joinStream;
import static com.nexla.common.StreamUtils.isInstance;
import static com.nexla.common.parse.ParserConfigs.Unstructured.MODE_ENTIRE_FILE;
import static com.nexla.common.parse.ParserConfigs.Unstructured.MODE_ROW;
import static com.nexla.common.parse.ParserConfigs.Xml.ADDITIONAL_XML_XPATHS;
import static com.nexla.common.parse.ParserConfigs.Xml.XML_MODE;
import static com.nexla.common.parse.ParserConfigs.Xml.XML_XPATH;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.trimToNull;

public class XmlParser extends DocumentParser {

	static class Params {
		Optional<String> mode = empty();
		Optional<String> xpath = empty();
	}

	private final Params params = new Params();

	public XmlParser option(String key, String value) {

		super.option(key, value);

		switch (key) {
			case XML_MODE:
				params.mode = ofNullable(trimToNull(value));
				break;
			case XML_XPATH:
				params.xpath = ofNullable(trimToNull(value));
				break;
		}
		return this;
	}

	@Override
	public StreamEx<Optional<NexlaMessage>> parseMessages(InputStream inputStream) {
		byte[] sample = FilePropertiesDetector.readSample(inputStream);

		FilePropertiesDetector detector = new FilePropertiesDetector(sample, formatDetectionMaxLines);
		String readMode = params.mode.orElseGet(() -> detector.isEntireFileXml() ? MODE_ENTIRE_FILE : MODE_ROW);
		Optional<SourceAwareInputStream> sourceAware = isInstance(of(inputStream), SourceAwareInputStream.class);

		Charset charset = detector.detectCharset(defaultCharset, charsetDetectionConfidenceThreshold);

		InputStream restoredStream = new BOMInputStream(joinStream(sample, inputStream));
		BufferedReader reader = new BufferedReader(new InputStreamReader(restoredStream, charset));

		switch (readMode) {

			case MODE_ROW:
				return StreamEx.of(reader.lines())
					.zipWith(StreamEx.iterate(1L, v -> v + 1))
					.flatMap(entry -> {
						String line = entry.getKey();
						Long lineNumber = entry.getValue();
						try {
							return parseWithXpath(new StringReader(line), detector.getDetectedCharset(), line, sourceAware)
								.map(v -> of(new NexlaMessage(v)));
						} catch (Exception e) {
							Optional.ofNullable(exceptionHandler)
									.ifPresentOrElse(
											h -> h.accept(new ParseError(lineNumber, of(lineNumber), line), e),
											() -> logger().error("Couldn't parse XML and exceptionHandler is null", e)
									);
							return Stream.empty();
						}
					});

			case MODE_ENTIRE_FILE:
				return parseWithXpath(reader, detector.getDetectedCharset(), null, sourceAware)
					.map(v -> of(new NexlaMessage(v)));
			default:
				throw new IllegalArgumentException();
		}
	}

	@SneakyThrows
	private BufferedReader createReader(Charset charset, InputStream is) {
		return new BufferedReader(new InputStreamReader(new BOMInputStream(is), charset));
	}

	private StreamEx<LinkedHashMap<String, Object>> parseWithXpath(
		Reader reader, Charset charset, String xmlOpt, Optional<SourceAwareInputStream> sourceAware
	) {
		return params.xpath
			.map(xpath -> trySaxThenDom(reader, charset, xpath, xmlOpt, sourceAware))
			.orElseGet(() -> DomParser.parse(reader, null));
	}

	@SneakyThrows
	private StreamEx<LinkedHashMap<String, Object>> trySaxThenDom(
		Reader reader,
		Charset charset,
		String xpath,
		String xmlLine,
		Optional<SourceAwareInputStream> sourceAware
	) {
		StaxParser staxParser = new StaxParser();

		if (staxParser.supports(xpath)) {

			final File tempFile;
			final Reader currReader;
			final FileInputStream currFileStream;
			if (xmlLine == null && !sourceAware.isPresent()) {
				tempFile = dumpToTempFile(reader);
				currFileStream = new FileInputStream(tempFile);
				currReader = createReader(charset, currFileStream);
			} else {
				tempFile = null;
				currReader = reader;
				currFileStream = null;
			}

			StreamEx<LinkedHashMap<String, Object>> messages = staxParser.parse(currReader, xpath);
			Iterator<LinkedHashMap<String, Object>> iterator = messages.iterator();

			// if there is data, it was able to parse it
			if (iterator.hasNext()) {
				return parseWithStax(tempFile, currFileStream, messages, iterator);
			} else {
				closeSilently(messages, currFileStream);
				return parseWithDom(charset, xpath, xmlLine, sourceAware, tempFile);
			}

		} else {
			// fall back to dom if xpath is not supported
			logger().error("Falling back to DOM parser for xpath: {}", xpath);
			return DomParser.parse(reader, xpath);
		}
	}

	private StreamEx<LinkedHashMap<String, Object>> parseWithDom(
		Charset charset,
		String xpath,
		String xmlLine,
		Optional<SourceAwareInputStream> sourceAware,
		File tempFile
	) {
		// otherwise fall back to dom parser

		if (xmlLine != null) {
			return DomParser.parse(new StringReader(xmlLine), xpath);
		} else {
			InputStream newStream = sourceAware
				.<InputStream>map(SourceAwareInputStream::recreateFromSource)
				.orElseGet(() -> newFileInputStream(tempFile));

			BufferedReader r = createReader(charset, newStream);
			return DomParser
				.parse(r, xpath)
				.onClose(() -> {
					closeSilently(r, newStream);
					deleteFile(tempFile);
				});
		}
	}

	private StreamEx<LinkedHashMap<String, Object>> parseWithStax(
		File tempFile,
		FileInputStream currFileStream,
		StreamEx<LinkedHashMap<String, Object>> messages,
		Iterator<LinkedHashMap<String, Object>> iterator
	) {
		LinkedHashMap<String, Object> firstMessage = iterator.next();
		return StreamEx
			.of(firstMessage)
			.append(StreamEx.of(iterator))
			.onClose(() -> {
				closeSilently(messages, currFileStream);
				deleteFile(tempFile);
			});
	}

	private FileInputStream newFileInputStream(File tempFile) {
		try {
			return new FileInputStream(tempFile);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	private void deleteFile(File tempFile) {
		if (tempFile != null) {
			tempFile.delete();
		}
	}

	private File dumpToTempFile(Reader reader) throws IOException {
		File tempFile = Files.createTempFile("", "").toFile();
		try (FileWriter fileWriter = new FileWriter(tempFile)) {
			IOUtils.copyLarge(reader, fileWriter);
		}
		return tempFile;
	}

	@Override
	public String getPathParameter() {
		return XML_XPATH;
	}

	@Override
	public String getAdditionalPathParameter() {
		return ADDITIONAL_XML_XPATHS;
	}

}
