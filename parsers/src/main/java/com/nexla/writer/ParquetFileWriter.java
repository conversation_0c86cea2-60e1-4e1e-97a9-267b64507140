package com.nexla.writer;

import com.fasterxml.jackson.databind.JsonNode;
import com.nexla.util.HdfsUtil;
import com.nexla.writer.base.JsonUtil;
import lombok.SneakyThrows;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;
import org.apache.parquet.avro.AvroParquetWriter;
import org.apache.parquet.hadoop.ParquetWriter;
import org.apache.parquet.hadoop.metadata.CompressionCodecName;

import java.io.File;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

import static com.nexla.connector.config.file.FileSinkConnectorConfig.COMPRESSION_TYPE;
import static com.nexla.util.HdfsUtil.hadoopConfiguration;

public class ParquetFileWriter extends AvroParquetBase {

	public static final String PARQUET_GZ = "parquet.gz";
	public static final String PARQUET_SNAPPY = "parquet.snappy";
	public static final String SNAPPY = "snappy";
	public static final String GZIP = "gzip";

	@SneakyThrows
	@Override
	protected void writeData(Iterator<LinkedHashMap<String, Object>> recordIterator, Schema jsonSchema, GenericData model) {
		try (ParquetWriter<GenericData.Record> parquetWriter = createWriter(outputPath, jsonSchema)) {
			while (recordIterator.hasNext()) {
				Map<String, Object> record = recordIterator.next();
				JsonNode jsonNode = OBJECT_MAPPER.valueToTree(record);
				GenericData.Record genericRecord = (GenericData.Record) JsonUtil.convertToAvro(model, jsonNode, jsonSchema);
				parquetWriter.write(genericRecord);
			}
		}
	}

	@SneakyThrows
	private ParquetWriter<GenericData.Record> createWriter(String outputPath, Schema jsonSchema) {
		deleteFileIfExists(outputPath);
		return AvroParquetWriter
			.<GenericData.Record>builder(HdfsUtil.path(outputPath))
			.withCompressionCodec(CompressionCodecName.fromConf(getCompressionType()))
			.withSchema(jsonSchema)
			.withConf(hadoopConfiguration())
			.build();
	}

	@Override
	public String getExtension() {
		return getCompressionType().equalsIgnoreCase(SNAPPY) ? PARQUET_SNAPPY : PARQUET_GZ;
	}

	private String getCompressionType() {
		return allConfigs.map(m -> m.getOrDefault(COMPRESSION_TYPE, GZIP))
				.orElse(GZIP)
				.toString();
	}

	private void deleteFileIfExists(final String outputPath) {
		final File file = new File(outputPath);
		if(file.exists()) {
			file.delete();
		}
	}
}
