package com.nexla.writer;

import com.fasterxml.jackson.databind.JsonNode;
import com.nexla.writer.base.JsonUtil;
import lombok.SneakyThrows;
import org.apache.avro.Schema;
import org.apache.avro.file.DataFileWriter;
import org.apache.avro.generic.GenericData;
import org.apache.avro.generic.GenericDatumWriter;
import org.apache.avro.generic.GenericRecord;
import org.apache.avro.io.DatumWriter;

import java.io.File;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

public class AvroFileWriter extends AvroParquetBase {

	public static final String AVRO_EXTENSION = "avro";

	@SneakyThrows
	@Override
	protected void writeData(Iterator<LinkedHashMap<String, Object>> recordIterator, Schema jsonSchema, GenericData model) {
		try (DataFileWriter<GenericRecord> dataFileWriter = createWriter(jsonSchema)) {
			while (recordIterator.hasNext()) {
				Map<String, Object> record = recordIterator.next();
				JsonNode jsonNode = OBJECT_MAPPER.valueToTree(record);

				GenericData.Record genericRecord = (GenericData.Record) JsonUtil.convertToAvro(model, jsonNode, jsonSchema);
				dataFileWriter.append(genericRecord);
			}
		}
	}

	@SneakyThrows
	private DataFileWriter<GenericRecord> createWriter(Schema jsonSchema) {
		DatumWriter<GenericRecord> writer = new GenericDatumWriter<>(jsonSchema);
		DataFileWriter<GenericRecord> dataFileWriter = new DataFileWriter<>(writer);

		dataFileWriter.create(jsonSchema, new File(outputPath));
		return dataFileWriter;
	}

	@Override
	public String getExtension() {
		return AVRO_EXTENSION;
	}
}
