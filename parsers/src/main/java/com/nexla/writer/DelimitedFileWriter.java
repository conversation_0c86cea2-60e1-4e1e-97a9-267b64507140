package com.nexla.writer;

import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.nexla.common.transform.Flattener;
import lombok.SneakyThrows;

import java.io.*;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.parse.ParserConfigs.Csv.*;
import static java.nio.charset.StandardCharsets.UTF_8;

public class DelimitedFileWriter extends NexlaFileWriter {

	private static final CsvMapper OBJECT_MAPPER = new CsvMapper();

	private boolean useHeader = true;
	private char delimiter = ',';
	private boolean append = false;
	private BufferedWriter writer;
	private ObjectWriter csvWriter;
	private CsvSchema schema;
	private Optional<Character> quote = Optional.empty();

	@SneakyThrows
	@Override
	public int append(Map<String, Object> record) {
		record = Flattener.INSTANCE.flatten(record);
		if (writer == null) {
			initWriter();
			writeCustomHeader(writer, record);
		}
		if (schema == null) {
			this.schema = getSchema(record);
			this.csvWriter = OBJECT_MAPPER.writer(schema);
			writeCsvHeader();
		}
		String value = csvWriter.writeValueAsString(record);
		writer.append(value);
		return value.getBytes().length;
	}

	private void writeCsvHeader() throws IOException {
		if (useHeader) {
			String header = generateHeader(schema);
			writer.append(header);
		}
	}

	@SneakyThrows
	@Override
	public void flush() {
		if (writer != null) {
			writer.flush();
		}
	}

	@Override
	public boolean isClosed() {
		return writer == null;
	}

	@Override
	public void close() throws Exception {
		if (this.writer != null) {
			this.writer.close();
		}
		this.writer = null;
		this.append = true;
	}

	@SneakyThrows
	private String generateHeader(CsvSchema schema) {
		return OBJECT_MAPPER.writer(schema.withHeader()).writeValueAsString(null);
	}

	@Override
	public DelimitedFileWriter useTempFile() {
		this.outputPath = "/tmp/" + UUID.randomUUID().toString() + ".csv";
		return this;
	}

	@Override
	public DelimitedFileWriter withFileName(String fileName) {
		this.outputPath = fileName;
		return this;
	}

	private void initWriter() throws IOException {
		File file = new File(outputPath);
		FileOutputStream fos = new FileOutputStream(file, append);
		if (!append) {
			// Explicitly truncate the file. On linux and OS X this appears to
			// happen anyway when opening with FileOutputStream but that behavior is not
			// actually documented or specified anywhere so let's be rigorous about it.
			fos.getChannel().truncate(0);
		}
		this.writer = new BufferedWriter(new OutputStreamWriter(fos, UTF_8), HUNDRED_KB_FLUSH_THRESHOLD);
	}

	@Override
	public DelimitedFileWriter option(String key, Object value) {

		super.option(key, value);

		switch (key) {
			case WRITE_HEADER:
				this.useHeader = Boolean.valueOf(value.toString());
				break;
			case CSV_DELIMITER:
				this.delimiter = value.toString().charAt(0);
				break;
			case CSV_QUOTE_CHAR:
				this.quote = opt(value).map(v -> v.charAt(0));
				break;
		}
		return this;
	}

	private CsvSchema getSchema(Map<String, Object> rawMessage) {

		CsvSchema.Builder schemaBuilder = CsvSchema.builder()
			.setColumnSeparator(delimiter)
			.setUseHeader(false);

		if (!quote.isPresent()) {
			schemaBuilder.disableQuoteChar();
		} else {
			schemaBuilder.setQuoteChar(quote.get());
		}

		rawMessage.keySet().forEach(schemaBuilder::addColumn);

		return schemaBuilder.build();
	}

}
