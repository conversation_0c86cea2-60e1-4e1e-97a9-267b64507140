package com.nexla.writer;

import com.google.common.base.Suppliers;
import com.google.common.collect.Sets;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.time.VarUtils.VarInfo;
import com.nexla.connector.config.MappingConfig;
import com.nexla.parser.Writers;
import com.nexla.writer.orc.OrcFileWriter;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.Writer;
import java.util.*;
import java.util.function.Supplier;

import static com.google.common.io.Files.getFileExtension;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.parse.ParserConfigs.Excel.EXCEL_XLSX;
import static com.nexla.common.parse.ParserConfigs.Unstructured.HEADER_DATE_FORMAT;
import static com.nexla.common.parse.ParserConfigs.Unstructured.HEADER_TEMPLATE;
import static com.nexla.common.time.VarUtils.DATE_VAR;
import static com.nexla.common.time.VarUtils.processStringWithVars;
import static com.nexla.common.time.VarUtils.replaceVars;
import static com.nexla.parser.DelimitedTextParser.WRITER_DEFAULT_OPTIONS;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;

public abstract class NexlaFileWriter implements AutoCloseable {

	private com.google.common.base.Supplier<NexlaLogger> logger =
		Suppliers.memoize(() -> new NexlaLogger(LoggerFactory.getLogger(getClass()), ""));

	public static final Set<String> EDI_EXTENSIONS = Sets.newHashSet("edi", "x12");

	public static final Map<String, Supplier<NexlaFileWriter>> WRITERS = new HashMap<String, Supplier<NexlaFileWriter>>() {{
		put("parquet", ParquetFileWriter::new);
		put("avro", AvroFileWriter::new);
		put("csv", () -> new DelimitedFileWriter().options(WRITER_DEFAULT_OPTIONS.get(',')));
		put("tsv", () -> new DelimitedFileWriter().options(WRITER_DEFAULT_OPTIONS.get('\t')));
		put(EXCEL_XLSX, ExcelFileWriter::new);
		put("xml", XmlFileWriter::new);
		put("json", JsonFileWriter::new);
		put("edi", EdiFileWriter::new);
		put("x12", EdiFileWriter::new);
		put("orc", OrcFileWriter::new);
		put("pb", ProtobufFileWriter::new);
		put("delta", JsonFileWriter::new);
		put("fw", FixedWidthFileWriter::new);
	}};

	static {
		if (!WRITERS.keySet().equals(Writers.WRITER_NAMES)) {
			throw new IllegalArgumentException();
		}
	}

	public static final int ONE_MB_FLUSH_THRESHOLD = 1024 * 1024;

	public static final int HUNDRED_KB_FLUSH_THRESHOLD = 100 * 1024;

	public String outputPath;
	// this is arbitrary header which user can put in the file. Have nothing common with CSV / Excel headers
	public Optional<VarInfo> customHeader = empty();
	public Optional<DateTimeFormatter> formatter = of(DateTimeFormat.forPattern("yyyy-MM-dd"));

	protected Optional<Map<String, Object>> allConfigs = Optional.empty();

	/**
	 * Workaround method to pass mapping config. Override in child classes if needed.
	 */
	public int append(Map<String, Object> record, Optional<MappingConfig> mappingConfig) {
		return append(record);
	}

	public abstract int append(Map<String, Object> record);

	public abstract void flush();

	public abstract NexlaFileWriter useTempFile();

	public abstract NexlaFileWriter withFileName(String fileName);

	public void delete() {
		new File(outputPath).delete();
	}

	public boolean finish() {
		return true;
	}

	public NexlaFileWriter option(String key, Object value) {
		switch (key) {
			case HEADER_TEMPLATE:
				this.customHeader = of(processStringWithVars(value.toString()));
				break;
			case HEADER_DATE_FORMAT:
				this.formatter = of(DateTimeFormat.forPattern(value.toString()));
				break;
		}
		return this;
	}

	public NexlaFileWriter options(Map<String, Object> options) {
		this.allConfigs = Optional.of(options);
		options.forEach(this::option);
		return this;
	}

	public String getOutputPath() {
		return outputPath;
	}

	public static NexlaFileWriter forExt(String fileNameOrFormat) {
		String ext = getFileExtension("." + fileNameOrFormat).toLowerCase();
		return ofNullable(WRITERS.get(ext))
			.orElseThrow(() -> new IllegalArgumentException("Writer not found for file name or extension=" + ext))
			.get();
	}

	public abstract boolean isClosed();

	public NexlaFileWriter withLoggerPrefix(Object... prefixParts) {
		this.logger.get().setPrefix(prefixParts);
		return this;
	}

	protected Logger logger() {
		return logger.get();
	}

	@SneakyThrows
	protected void writeCustomHeader(Writer writer, Map<String, Object> rawRecord) {
		customHeader.ifPresent(varInfo -> {
			Map<String, String> record = EntryStream.of(rawRecord)
					.filterValues(Objects::nonNull)
					.mapValues(Object::toString)
					.toMap();

			formatter.ifPresent(format -> record.put(DATE_VAR, format.print(getNow())));
			String resultHeader = replaceVars(varInfo, record);
			appendWriter(writer, resultHeader);
			appendWriter(writer, "\n");
		});
	}

	@SneakyThrows
	private void appendWriter(Writer writer, String resultHeader) {
		writer.append(resultHeader);
	}

	// for tests
	protected DateTime getNow() {
		return nowUTC();
	}

}
