package com.nexla.writer;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.writer.base.BaseXmlJsonFileWriter;
import lombok.SneakyThrows;

import java.util.Map;

import static com.nexla.common.parse.ParserConfigs.Json.JSON_MODE;
import static com.nexla.common.parse.ParserConfigs.Json.JSON_OUTPUT_TEMPLATE;

public class JsonFileWriter extends BaseXmlJsonFileWriter {

	public static final String FILE_EXTENSION = "json";

	@Override
	protected String buildRecordLine(Map<String, Object> record) {
		return JsonUtils.toJsonString(record);
	}

	protected String getFileExtension() {
		return FILE_EXTENSION;
	}

	@SneakyThrows
	@Override
	protected void printRecordSeparator() {
		writer.append(',');
		writer.newLine();
	}

	@Override
	public JsonFileWriter option(String key, Object value) {
		super.option(key, value);

		switch (key) {
			case JSON_MODE:
				this.mode = value.toString();
				break;
			case JSON_OUTPUT_TEMPLATE:
				this.outputTemplate = parseOutputTemplate(value.toString());
				break;
		}
		return this;
	}

}
