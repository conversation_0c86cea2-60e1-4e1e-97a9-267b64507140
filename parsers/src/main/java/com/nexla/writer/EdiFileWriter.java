package com.nexla.writer;

import com.ddtek.xmlconverter.Converter;
import com.ddtek.xmlconverter.ConverterFactory;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.SneakyThrows;

import javax.xml.transform.Result;
import javax.xml.transform.Source;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.parse.ParserConfigs.Edi.EDI_OPTIONS;

public class EdiFileWriter extends NexlaFileWriter {

	private static final LoadingCache<String, ObjectWriter> XML_ROOT_WRITERS;

	static {
		XmlMapper xmlMapper = new XmlMapper();
		XML_ROOT_WRITERS = CacheBuilder.newBuilder()
			.maximumSize(5)
			.build(new CacheLoader<String, ObjectWriter>() {
				@Override
				public ObjectWriter load(String root) {
					return xmlMapper.writer().withRootName(root);
				}
			});
	}

	private final ConverterFactory factory = new ConverterFactory();

	Optional<String> options = Optional.empty();

	private BufferedWriter writer;

	private boolean append = false;

	public EdiFileWriter option(String key, String value) {
		super.option(key, value);

		switch (key) {
			case EDI_OPTIONS:
				this.options = opt(value);
				break;
			default:
				super.option(key, value);
		}
		return this;
	}

	@SneakyThrows
	@Override
	public int append(Map<String, Object> record) {
		if (writer == null) {
			initWriter();
		}

		String root;
		// if key set has single element, it means it is X12 or other EDI root
		if (record.keySet().size() == 1) {
			root = record.keySet().iterator().next();
			// extracting what is under the root to write it as XML with the root
			record = (Map<String, Object>) record.get(root);
		} else {
			// otherwise assume it should be X12 root
			root = "X12";
		}

		String xmlString = XML_ROOT_WRITERS.get(root).writeValueAsString(record);
		logger().debug("EDI -> XML:\n{}", xmlString);

		Source converterSource = new StreamSource(new StringReader(xmlString));
		Result converterResult = new StreamResult(writer); // write to file

		Converter fromXml = factory.newConvertFromXML("converter:EDI" + options.map(opt -> ":" + opt).orElse(""));
		fromXml.convert(converterSource, converterResult);
		return xmlString.getBytes().length;
	}

	private void initWriter() throws IOException {
		FileOutputStream fos = new FileOutputStream(new File(outputPath), append);
		if (!append) {
			// Explicitly truncate the file. On linux and OS X this appears to
			// happen anyway when opening with FileOutputStream but that behavior is not
			// actually documented or specified anywhere so let's be rigorous about it.
			fos.getChannel().truncate(0);
		}
		this.writer = new BufferedWriter(new OutputStreamWriter(fos, StandardCharsets.UTF_8), ONE_MB_FLUSH_THRESHOLD);
	}

	@SneakyThrows
	@Override
	public void flush() {
		writer.flush();
	}

	@Override
	public EdiFileWriter useTempFile() {
		this.outputPath = "/tmp/" + UUID.randomUUID().toString() + ".x12";
		return this;
	}

	@Override
	public EdiFileWriter withFileName(String fileName) {
		this.outputPath = fileName;
		return this;
	}

	@Override
	public boolean isClosed() {
		return writer == null;
	}

	@Override
	public void close() throws Exception {
		if (writer != null) {
			writer.close();
			writer = null;
		}
		this.append = true;
	}
}
