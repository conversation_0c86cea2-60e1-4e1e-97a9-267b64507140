package com.nexla.writer.base;

import com.google.common.collect.Maps;
import com.nexla.common.time.VarUtils;
import com.nexla.writer.NexlaFileWriter;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedWriter;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static com.bazaarvoice.jolt.JsonUtils.jsonToMap;
import static com.nexla.common.StreamUtils.stringifyMap;
import static com.nexla.common.parse.ParserConfigs.Unstructured.*;
import static com.nexla.common.time.VarUtils.*;
import static com.nexla.writer.base.OutputTemplate.DATA_VAR;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Optional.empty;
import static java.util.Optional.of;

abstract public class BaseXmlJsonFileWriter extends NexlaFileWriter {

	protected BufferedWriter writer;
	protected boolean append = false;
	protected Optional<VarInfo> customMetadata = empty();
	protected String mode = MODE_ROW;

	private ModeWriter modeWriter;

	protected OutputTemplate outputTemplate;
	private boolean firstRecordWritten = false;

	@SneakyThrows
	@Override
	public int append(Map<String, Object> record) {

		if (writer == null) {
			initWriter();
			modeWriter.writeHeader(record);
		}

		Map<String, Object> withMeta = addMetadataHeaders(record);
		String payload = buildRecordLine(withMeta);
		return modeWriter.writeLine(payload);
	}

	@Override
	public boolean finish() {
		if (!isClosed() && mode.equals(MODE_ENTIRE_FILE)) {
			outputTemplate.footer.ifPresent(this::append);
		}
		return true;
	}

	@SneakyThrows
	private Writer append(String footer) {
		return writer.append(footer);
	}

	protected abstract String buildRecordLine(Map<String, Object> record);

	protected abstract String getFileExtension();

	protected abstract void printRecordSeparator();

	private Map<String, Object> addMetadataHeaders(Map<String, Object> record) {
		return customMetadata
			.map(varInfo -> {
				Map<String, String> replace = stringifyMap(record);
				formatter.ifPresent(format -> replace.put(DATE_VAR, format.print(getNow())));
				String resultHeaderJson = replaceVars(varInfo, replace);
				Map<String, Object> headerMap = jsonToMap(resultHeaderJson);
				Map<String, Object> result = Maps.newHashMap(record);
				result.putAll(headerMap);
				return result;
			})
			.orElse(record);
	}

	@SneakyThrows
	private void initWriter() {
		FileOutputStream fos = new FileOutputStream(outputPath, append);
		if (!append) {
			// Explicitly truncate the file. On linux and OS X this appears to
			// happen anyway when opening with FileOutputStream but that behavior is not
			// actually documented or specified anywhere so let's be rigorous about it.
			fos.getChannel().truncate(0);
		}
		this.writer = new BufferedWriter(new OutputStreamWriter(fos, UTF_8), ONE_MB_FLUSH_THRESHOLD);
		this.modeWriter = createModeWriter(mode);
	}

	private ModeWriter createModeWriter(String mode) {
		switch (mode) {

			case MODE_ROW:
				return new ModeWriter() {
					@Override
					public void writeHeader(Map<String, Object> record) {
						writeCustomHeader(writer, record);
					}

					@SneakyThrows
					@Override
					public int writeLine(String payload) {
						writer.append(payload);
						writer.newLine();
						return payload.getBytes().length + 1;
					}
				};

			case MODE_ENTIRE_FILE:
				return new ModeWriter() {
					@Override
					public void writeHeader(Map<String, Object> record) {
						writeOutputTemplate();
					}

					@SneakyThrows
					@Override
					public int writeLine(String payload) {
						if (firstRecordWritten) {
							printRecordSeparator();
							writer.append(payload);
						} else {
							BaseXmlJsonFileWriter.this.firstRecordWritten = true;
							writer.append(payload);
						}
						return payload.getBytes().length;
					}
				};

			default:
				throw new IllegalArgumentException();
		}
	}

	@Override
	public NexlaFileWriter withFileName(String fileName) {
		this.outputPath = fileName;
		return this;
	}

	@Override
	public boolean isClosed() {
		return writer == null;
	}

	@Override
	public void close() throws Exception {
		if (writer != null) {
			writer.flush();
			writer.close();
			this.writer = null;
		}
		this.append = true;
	}

	@SneakyThrows
	@Override
	public void flush() {
		if (writer != null) {
			writer.flush();
		}
	}

	@Override
	public NexlaFileWriter useTempFile() {
		this.outputPath = "/tmp/" + UUID.randomUUID().toString() + "." + getFileExtension();
		return this;
	}

	@Override
	public BaseXmlJsonFileWriter option(String key, Object value) {

		super.option(key, value);

		switch (key) {
			case HEADER_META_TEMPLATE:
				this.customMetadata = of(processStringWithVars(value.toString()));
				break;
		}
		return this;
	}

	protected static OutputTemplate parseOutputTemplate(String value) {
		int dataVarIndex = value.indexOf(DATA_VAR);
		if (dataVarIndex == -1) {
			throw new IllegalArgumentException(DATA_VAR + " is not found");
		} else {
			Optional<String> header = of(value.substring(0, dataVarIndex)).filter(StringUtils::isNotEmpty);
			Optional<VarInfo> varInfo = header.map(VarUtils::processStringWithVars);
			Optional<String> footer = of(value.substring(dataVarIndex + DATA_VAR.length())).filter(StringUtils::isNotEmpty);
			return new OutputTemplate(header, footer, varInfo);
		}
	}

	@SneakyThrows
	private void writeOutputTemplate() {
		outputTemplate.header.ifPresent(h -> {
			String val = outputTemplate.metadata
					.map(metadata -> {
						Map<String, String> record = new HashMap<>();
						formatter.ifPresent(format -> record.put(DATE_VAR, format.print(getNow())));
						return replaceVars(metadata, record);
					})
					.orElse(h);

			this.append(val);
		});
	}

}
