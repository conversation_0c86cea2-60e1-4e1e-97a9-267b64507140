package com.nexla.writer.base;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.connector.TransformTraverse;
import com.nexla.writer.JsonFileWriter;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.avro.Schema;
import org.apache.commons.collections4.MapUtils;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.StreamUtils.OBJECT_MAPPER;
import static com.nexla.writer.base.JsonUtil.inferSchemaWithMaps;
import static java.nio.charset.StandardCharsets.UTF_8;

public class WriterUtils {

	public static final int SCHEMA_DETECTION_MAX_MESSAGES = 50;

	@SneakyThrows
	public static Optional<Schema> inferAvroSchemaFromFile(TransformTraverse transformTraverse, JsonFileWriter jsonFileWriter) {
		return inferAvroSchemaFromFile(transformTraverse, jsonFileWriter, SCHEMA_DETECTION_MAX_MESSAGES);
	}

	@SneakyThrows
	public static Optional<Schema> inferAvroSchemaFromFile(TransformTraverse transformTraverse, JsonFileWriter jsonFileWriter, int maxMessages) {
		try (
			FileInputStream inputStream = new FileInputStream(jsonFileWriter.getOutputPath());
			BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, UTF_8));
			StreamEx<LinkedHashMap<String, Object>> messageStream =
				readMessagesFromFile(bufferedReader)
					.limit(maxMessages)
		) {
			StreamEx<LinkedHashMap<String, Object>> tx = messageStream.map(transformTraverse::transform);
			return inferAvroSchema(tx);
		}
	}

	private static Optional<Schema> inferAvroSchema(StreamEx<LinkedHashMap<String, Object>> messages) {
		return messages
			.map(message -> inferSchemaWithMaps(OBJECT_MAPPER.valueToTree(message), "schema"))
			.reduce(org.kitesdk.data.spi.SchemaUtil::merge);
	}

	public static StreamEx<LinkedHashMap<String, Object>> readMessagesFromFile(BufferedReader bufferedReader) {
		return StreamEx.of(bufferedReader.lines())
			.map(json -> (LinkedHashMap<String, Object>) JsonUtils.jsonToMap(json))
			.peek(WriterUtils::cleanupEmptyOrNullValues)
			.filter(MapUtils::isNotEmpty);
	}

	public static void cleanupEmptyOrNullValues(Object mapOrList) {

		Iterator iterator = (mapOrList instanceof Map)
			? ((Map) mapOrList).values().iterator()
			: ((List) mapOrList).iterator();

		while (iterator.hasNext()) {
			Object value = iterator.next();
			if (value == null) {
				iterator.remove();
			} else if (value instanceof Map) {
				Map m = (Map) value;
				if (m.isEmpty()) {
					iterator.remove();
				} else {
					cleanupEmptyOrNullValues(m);
					if (m.isEmpty()) {
						iterator.remove();
					}
				}
			} else if (value instanceof List) {
				List list = (List) value;
				if (list.isEmpty()) {
					iterator.remove();
				} else {
					cleanupEmptyOrNullValues(list);
					if (list.isEmpty()) {
						iterator.remove();
					}
				}
			}
		}
	}

}
