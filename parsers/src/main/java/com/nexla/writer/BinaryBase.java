package com.nexla.writer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexla.writer.base.WriterUtils;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static com.nexla.common.parse.ParserConfigs.SchemaDetection.SCHEMA_DETECTION_MAX_MESSAGES;
import static com.nexla.writer.base.WriterUtils.readMessagesFromFile;
import static java.nio.charset.StandardCharsets.UTF_8;

public abstract class BinaryBase extends NexlaFileWriter {

	protected final Logger logger = LoggerFactory.getLogger(this.getClass());

	protected static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

	protected final JsonFileWriter jsonFileWriter = new JsonFileWriter();

	@Override
	public int append(Map<String, Object> record) {
		return jsonFileWriter.append(record);
	}

	@Override
	public void flush() {
		jsonFileWriter.flush();
	}

	@Override
	public NexlaFileWriter withFileName(String fileName) {
		jsonFileWriter.useTempFile();
		this.outputPath = fileName;
		return this;
	}

	@Override
	public boolean isClosed() {
		return jsonFileWriter.isClosed();
	}

	@Override
	public void close() throws Exception {
		jsonFileWriter.close();
	}

	@Override
	public void delete() {
		super.delete();
		jsonFileWriter.delete();
	}

	protected abstract void writeStreamOnFinish(StreamEx<LinkedHashMap<String, Object>> messageStream);

	public abstract String getExtension();

	@SneakyThrows
	@Override
	public boolean finish() {
		try {
			if (!jsonFileWriter.isClosed()) {
				jsonFileWriter.close();
			}

			File jsonInputFile = new File(jsonFileWriter.getOutputPath());
			if (!jsonInputFile.exists() || jsonInputFile.length() == 0) {
				logger.warn("File={} does not exist or empty", jsonInputFile.getAbsolutePath());
				return false;
			}

			try (
				FileInputStream inputStream = new FileInputStream(jsonFileWriter.getOutputPath());
				BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, UTF_8));
				StreamEx<LinkedHashMap<String, Object>> messageStream = readMessagesFromFile(bufferedReader)
			) {
				writeStreamOnFinish(messageStream);
				return true;
			}
		} finally {
			jsonFileWriter.delete();
		}
	}

	@Override
	public NexlaFileWriter useTempFile() {
		jsonFileWriter.useTempFile();
		this.outputPath = "/tmp/" + UUID.randomUUID().toString() + "." + getExtension();
		return this;
	}
}
