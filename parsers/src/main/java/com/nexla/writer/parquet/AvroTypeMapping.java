package com.nexla.writer.parquet;

import com.nexla.common.logging.NexlaLogger;
import org.apache.avro.LogicalTypes;
import org.apache.avro.Schema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.function.Function;

enum AvroTypeMapping {
	BOOLEAN(
			(sourceFieldOptions, targetFieldOptions) -> nullableSchema(Schema.Type.BOOLEAN),
			(sourceFieldOptions, targetFieldOptions) -> AvroTypeMapping::mapToBoolean),
	INT(
			(sourceFieldOptions, targetFieldOptions) -> nullableSchema(Schema.Type.INT),
			(sourceFieldOptions, targetFieldOptions) -> AvroTypeMapping::mapToInt),
	LONG(
			(sourceFieldOptions, targetFieldOptions) -> nullableSchema(Schema.Type.LONG),
			(sourceFieldOptions, targetFieldOptions) -> AvroTypeMapping::mapToLong),
	FLOAT(
			(sourceFieldOptions, targetFieldOptions) -> nullableSchema(Schema.Type.FLOAT),
			(sourceFieldOptions, targetFieldOptions) -> AvroTypeMapping::mapToFloat),
	DOUBLE(
			(sourceFieldOptions, targetFieldOptions) -> nullableSchema(Schema.Type.DOUBLE),
			(sourceFieldOptions, targetFieldOptions) -> AvroTypeMapping::mapToDouble),
	STRING(
			(sourceFieldOptions, targetFieldOptions) -> nullableSchema(Schema.Type.STRING),
			(sourceFieldOptions, targetFieldOptions) -> AvroTypeMapping::mapToString),
	TIMESTAMP(
			(sourceFieldOptions, targetFieldOptions) -> nullableSchema(timestampSchema(targetFieldOptions)),
			(sourceFieldOptions, targetFieldOptions) -> obj -> mapToTimestamp(sourceFieldOptions, targetFieldOptions, obj)),
	TIME(
			(sourceFieldOptions, targetFieldOptions) -> nullableSchema(timeSchema(targetFieldOptions)),
			(sourceFieldOptions, targetFieldOptions) -> obj -> mapToTime(sourceFieldOptions, targetFieldOptions, obj)),
	DATE(
			(sourceFieldOptions, targetFieldOptions) -> nullableSchema(dateSchema()),
			(sourceFieldOptions, targetFieldOptions) -> obj -> mapToDate(sourceFieldOptions, targetFieldOptions, obj)),
	DECIMAL(
			(sourceFieldOptions, targetFieldOptions) -> nullableSchema(getDecimalSchema(targetFieldOptions)),
			(sourceFieldOptions, targetFieldOptions) -> obj -> mapToDecimal(sourceFieldOptions, targetFieldOptions, obj));

	private static final Logger LOGGER = new NexlaLogger(LoggerFactory.getLogger(AvroTypeMapping.class));
	private static final String DEFAULT_TARGET_FIELD_OPTION_UNIT = "millis";
	private static final String DEFAULT_TARGET_FIELD_OPTION_PRECISION = "32";
	private static final String DEFAULT_TARGET_FIELD_OPTION_SCALE = "16";

	// BiFunction<sourceFieldOptions, targetFieldOptions, Schema>
	private final BiFunction<Map<String, String>, Map<String, String>, Schema> schemaTypeSupplier;

	// BiFunction<sourceFieldOptions, targetFieldOptions, Function<originValue, mappedValue>>
	private final BiFunction<
			Map<String, String>,
			Map<String, String>,
			Function<Object, Object>> valueMapperSupplier;

	AvroTypeMapping(
			BiFunction<Map<String, String>, Map<String, String>, Schema> schemaTypeSupplier,
			BiFunction<
					Map<String, String>,
					Map<String, String>,
					Function<Object, Object>> valueMapperSupplier) {
		this.schemaTypeSupplier = schemaTypeSupplier;
		this.valueMapperSupplier = (sourceFieldOptions, targetFieldOptions) ->
				obj -> {
					Object result = obj == null ? null : valueMapperSupplier.apply(sourceFieldOptions, targetFieldOptions).apply(obj);
					traceParseResult(obj, result, this, sourceFieldOptions, targetFieldOptions);
					return result;
				};
	}

	public Schema getSchemaType(Map<String, String> sourceFieldOptions, Map<String, String> targetFieldOptions) {
		return schemaTypeSupplier.apply(sourceFieldOptions, targetFieldOptions);
	}

	public BiFunction<Map<String, String>, Map<String, String>, Function<Object, Object>> getValueMapperSupplier() {
		return valueMapperSupplier;
	}

	// --- Field mappers and schema builders

	static Schema nullableSchema(Schema.Type i) {
		return Schema.createUnion(Schema.create(Schema.Type.NULL), Schema.create(i));
	}

	static Schema nullableSchema(Schema schema) {
		return Schema.createUnion(Schema.create(Schema.Type.NULL), schema);
	}

	static Boolean mapToBoolean(Object obj) {
		if (obj instanceof Boolean) {
			return (Boolean) obj;
		}
		if (obj instanceof String) {
			if ("true".equalsIgnoreCase((String) obj)) {
				return Boolean.TRUE;
			}
			if ("false".equalsIgnoreCase((String) obj)) {
				return Boolean.FALSE;
			}
		}
		return null;
	}

	static Integer mapToInt(Object obj) {
		if (obj instanceof Number) {
			return ((Number) obj).intValue();
		}
		if (obj instanceof String) {
			try {
				return Integer.parseInt((String) obj);
			} catch (Exception ignored) {
			}
		}
		return null;
	}

	static Long mapToLong(Object obj) {
		if (obj instanceof Number) {
			return ((Number) obj).longValue();
		}
		if (obj instanceof String) {
			try {
				return Long.parseLong((String) obj);
			} catch (Exception ignored) {
			}
		}
		return null;
	}

	static Float mapToFloat(Object obj) {
		if (obj instanceof Number) {
			return ((Number) obj).floatValue();
		}
		if (obj instanceof String) {
			try {
				return Float.parseFloat((String) obj);
			} catch (Exception ignored) {
			}
		}
		return null;
	}

	static Double mapToDouble(Object obj) {
		if (obj instanceof Number) {
			return ((Number) obj).doubleValue();
		}
		if (obj instanceof String) {
			try {
				return Double.parseDouble((String) obj);
			} catch (Exception ignored) {
			}
		}
		return null;
	}

	static String mapToString(Object obj) {
		if (obj instanceof String) {
			return (String) obj;
		}
		return obj == null ? null : String.valueOf(obj);
	}

	// --- * Timestamp

	static Schema timestampSchema(Map<String, String> targetFieldOptions) {
		String unit = targetFieldOptions.getOrDefault("unit", DEFAULT_TARGET_FIELD_OPTION_UNIT).toLowerCase();
		switch (unit) {
			case "micros":
				return LogicalTypes.timestampMicros().addToSchema(Schema.create(Schema.Type.LONG));
			case "millis":
				return LogicalTypes.timestampMillis().addToSchema(Schema.create(Schema.Type.LONG));
			default:
				throw new IllegalArgumentException("Not supported Timestamp unit: " + unit);
		}
	}

	static Long mapToTimestamp(
			Map<String, String> sourceFieldOptions,
			Map<String, String> targetFieldOptions,
			Object obj) {
		Long tsMs;

		String format = sourceFieldOptions.get("format");
		if (format == null) {
			tsMs = mapToLong(obj);
			return tsMs == null ? null : AvroTypeMapping.mapToTimestamp(targetFieldOptions, tsMs);
		}

		if (!(obj instanceof String)) {
			return null;
		}

		try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format).withZone(ZoneId.of("UTC"));
			ZonedDateTime dateTime = ZonedDateTime.parse((String) obj, formatter);
			tsMs = dateTime.toInstant().toEpochMilli();
		} catch (Exception e) {
			return null;
		}
		return AvroTypeMapping.mapToTimestamp(targetFieldOptions, tsMs);
	}

	static Long mapToTimestamp(Map<String, String> targetFieldOptions, Long tsMs) {
		String unit = targetFieldOptions.getOrDefault("unit", DEFAULT_TARGET_FIELD_OPTION_UNIT).toLowerCase();
		switch (unit) {
			case "micros":
				return TimeUnit.MILLISECONDS.toMicros(tsMs);
			case "millis":
				return tsMs;
			default:
				throw new IllegalArgumentException("Not supported Timestamp unit: " + unit);
		}
	}

	// --- * Time

	static Schema timeSchema(Map<String, String> targetFieldOptions) {
		String unit = targetFieldOptions.getOrDefault("unit", DEFAULT_TARGET_FIELD_OPTION_UNIT).toLowerCase();
		switch (unit) {
			case "micros":
				return LogicalTypes.timeMicros().addToSchema(Schema.create(Schema.Type.LONG));        // Caution: return Long
			case "millis":
				return LogicalTypes.timeMillis().addToSchema(Schema.create(Schema.Type.INT));        // Caution: return Int
			default:
				throw new IllegalArgumentException("Not supported Time unit: " + unit);
		}
	}

	static Number mapToTime(
			Map<String, String> sourceFieldOptions,
			Map<String, String> targetFieldOptions,
			Object obj) {
		Long timeMs;

		String format = sourceFieldOptions.get("format");
		if (format == null) {
			timeMs = mapToLong(obj);
			return timeMs == null ? null : mapToTime(targetFieldOptions, timeMs);
		}

		if (!(obj instanceof String)) {
			return null;
		}

		try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format).withZone(ZoneId.of("UTC"));
			LocalTime time = LocalTime.parse((String) obj, formatter);
			timeMs = TimeUnit.NANOSECONDS.toMillis(time.toNanoOfDay());
		} catch (Exception e) {
			return null;
		}
		return mapToTime(targetFieldOptions, timeMs);
	}

	static Number mapToTime(Map<String, String> targetFieldOptions, Long timeMs) {
		if (timeMs < 0 || timeMs > TimeUnit.HOURS.toMillis(24)) {
			return null;
		}
		String unit = targetFieldOptions.getOrDefault("unit", DEFAULT_TARGET_FIELD_OPTION_UNIT).toLowerCase();
		switch (unit) {
			case "micros":
				return TimeUnit.MILLISECONDS.toMicros(timeMs);                // Caution: return Long
			case "millis":
				return timeMs.intValue();                                    // Caution: return Int
			default:
				throw new IllegalArgumentException("Not supported Time unit: " + unit);
		}
	}

	// --- * Date

	static Schema dateSchema() {
		return LogicalTypes.date().addToSchema(Schema.create(Schema.Type.INT));
	}

	@SuppressWarnings("unused")
	static Integer mapToDate(
			Map<String, String> sourceFieldOptions,
			Map<String, String> targetFieldOptions,
			Object obj) {
		String format = sourceFieldOptions.get("format");
		if (format == null) {
			return mapToInt(obj);
		}

		if (!(obj instanceof String)) {
			return null;
		}

		try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
			LocalDate date = LocalDate.parse((String) obj, formatter);
			return ((Long) date.toEpochDay()).intValue();
		} catch (Exception ignored) {
		}
		return null;
	}

	// --- * Decimal

	static Schema getDecimalSchema(Map<String, String> targetFieldOptions) {
		return LogicalTypes
				.decimal(
						Integer.parseInt(targetFieldOptions.getOrDefault("precision", DEFAULT_TARGET_FIELD_OPTION_PRECISION)),
						Integer.parseInt(targetFieldOptions.getOrDefault("scale", DEFAULT_TARGET_FIELD_OPTION_SCALE)))
				.addToSchema(Schema.create(Schema.Type.BYTES));
	}

	@SuppressWarnings("unused")
	static byte[] mapToDecimal(
			Map<String, String> sourceFieldOptions,
			Map<String, String> targetFieldOptions,
			Object obj) {
		BigDecimal bigDecimal = null;
		if (obj instanceof Number) {
			if (obj instanceof Double || obj instanceof Float) {
				bigDecimal = BigDecimal.valueOf(((Number) obj).doubleValue());
			}
			if (obj instanceof Long || obj instanceof Integer || obj instanceof Short) {
				bigDecimal = BigDecimal.valueOf(((Number) obj).longValue());
			}
		}
		if (obj instanceof String) {
			try {
				bigDecimal = new BigDecimal((String) obj);
			} catch (Exception e) {
				return null;
			}
		}
		if (bigDecimal == null) {
			return null;
		}
		int targetScale = Integer.parseInt(targetFieldOptions.getOrDefault("scale", DEFAULT_TARGET_FIELD_OPTION_SCALE));
		int targetPrecision = Integer.parseInt(targetFieldOptions.getOrDefault("precision", DEFAULT_TARGET_FIELD_OPTION_PRECISION));
		bigDecimal = bigDecimal.setScale(targetScale, RoundingMode.DOWN);
		if (bigDecimal.precision() > targetPrecision) {
			return null;
		}
		return bigDecimal.unscaledValue().toByteArray();
	}

	private static void traceParseResult(
			Object obj,
			Object parseResult,
			AvroTypeMapping type,
			Map<String, String> sourceFieldOptions,
			Map<String, String> targetFieldOptions) {
		if (parseResult == null) {
			LOGGER.trace("Failed to parse {} value '{}', sourceFieldOptions: {}, targetFieldOptions: {}", type, obj, sourceFieldOptions, targetFieldOptions);
		}
	}
}