package com.nexla.writer;

import com.nexla.common.parse.ParserConfigs.SchemaDetection;
import com.nexla.connector.config.MappingConfig;
import com.nexla.writer.parquet.AutoAvroSchemaMapper;
import com.nexla.writer.parquet.AvroSchemaMapper;
import com.nexla.writer.parquet.ManualAvroSchemaMapper;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;
import org.kitesdk.data.spi.DataModelUtil;

import java.io.File;
import java.io.IOException;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

public abstract class AvroParquetBase extends BinaryBase {
	private int schemaDetectionMaxMessages;

	private AvroSchemaMapper avroSchemaMapper = null;

	@Override
	public NexlaFileWriter option(String key, Object value) {
		if (SchemaDetection.SCHEMA_DETECTION_MAX_MESSAGES.equals(key)) {
			this.schemaDetectionMaxMessages = Integer.parseInt(value.toString());
		}

		return super.option(key, value);
	}

	@Override
	public int append(Map<String, Object> record, Optional<MappingConfig> mappingConfig) {
		if (avroSchemaMapper == null && mappingConfig.map(MappingConfig::isManual).orElse(false)) {
			this.avroSchemaMapper = new ManualAvroSchemaMapper(mappingConfig.get());
		}
		return this.append(record);
	}

	@Override
	public int append(Map<String, Object> record) {
		if (avroSchemaMapper == null) {
			this.avroSchemaMapper = new AutoAvroSchemaMapper(schemaDetectionMaxMessages);
		}
		avroSchemaMapper.introduceRecord(record);
		return super.append(record);
	}

	@Override
	@SneakyThrows
	protected void writeStreamOnFinish(StreamEx<LinkedHashMap<String, Object>> messageStreamOrig) {
		if (this.avroSchemaMapper == null || this.avroSchemaMapper.getSchema() == null) {
			logger.warn("No AvroSchemaMapper set OR no schema detected for file: {}", new File(jsonFileWriter.getOutputPath()).getAbsolutePath());
			return;
		}
		writeData(
				messageStreamOrig
						.map(record -> avroSchemaMapper.mapRecord(record))
						.iterator(),
				this.avroSchemaMapper.getSchema(),
				DataModelUtil.getDataModelForType(GenericData.Record.class));
	}

	protected abstract void writeData(Iterator<LinkedHashMap<String, Object>> recordIterator, Schema jsonSchema, GenericData model) throws IOException;

}
