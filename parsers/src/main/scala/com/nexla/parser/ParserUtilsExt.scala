package com.nexla.parser

import java.io.InputStream
import java.util.Optional
import java.util.function.BiConsumer

import com.nexla.common.exception.ParseError
import com.nexla.common.parse.NexlaParser
import com.nexla.common.parse.SampleContentType.BINARY
import com.nexla.parser.ParserUtils.{getParserByFormat, tryGetParserByExtOrContent}
import org.slf4j.Logger

import scala.compat.java8.OptionConverters._

object ParserUtilsExt {

  case class Result(parser: Option[NexlaParser],
                    restoredStream: InputStream,
                    message: Option[String])

  def detectParser[T](mimeType: Optional[String],
                      is: InputStream,
                      overriddenExtensions: java.util.Map[String, String],
                      config: java.util.Map[String, String],
                      fileName: String,
                      exceptionHandler: BiConsumer[ParseError, Exception],
                      logger: Logger): Result = {

    val parserResult = tryGetParserByExtOrContent(mimeType, is, overriddenExtensions, config, fileName)
    val detectedContentType = parserResult.contentType.asScala

    var message: Option[String] = None

    val parserOpt =
      (parserResult.parser.asScala,
        detectedContentType,
        parserResult.sampleUsed
      ) match {
        case (parserOpt@Some(_), _, _) => parserOpt
        case (_, Some(contentType), _) if contentType != BINARY =>
          getParserByFormat(mimeType, contentType.name(), overriddenExtensions, config).asScala
            .orElse({
              message = Some("Unknown format, fallback to text parser")
              logger.warn(s"${message.orNull}, file=$fileName")
              Some(new DelimitedTextParser())
            })

        case _ =>
          message = Some(s"Could not detect file format")
          logger.error(s"${message.orNull}, file=$fileName")
          None
      }

    Result(
      parserOpt
        .map(_
          .config(config)
          .exceptionHandler(exceptionHandler)),
      parserResult.restoredStream,
      message)
  }

}
