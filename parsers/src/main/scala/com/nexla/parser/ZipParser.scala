package com.nexla.parser

import com.nexla.common.exception.ParseError
import com.nexla.common.parse.NexlaParser
import com.nexla.parser.ParserUtilsExt.detectParser
import net.lingala.zip4j.exception.ZipException
import net.lingala.zip4j.model.LocalFileHeader
import one.util.streamex.StreamEx
import org.slf4j.Logger

import java.io.{File, FileOutputStream, InputStream}
import java.util
import java.util.function.BiConsumer
import java.util.Optional
import scala.collection.JavaConverters._
import scala.util.Try

trait ZipParser[I <: InputStream, E] {
  private val UNZIP_BUFFER_SIZE = 4096
  protected val zipStream: I
  protected val logger: Logger
  protected val zipPassword: String

  protected def getEntryFileName(entry: E): String

  protected def isDirectory(entry: E): Boolean

  protected def getNextFileEntry: E

  protected def getNextEntry: E

  protected def validate(): Unit

  def validateEntry(): Unit

  private def parseEntryWithChildParser[T](createFileStreamFunction: (String, NexlaParser, InputStream) => StreamEx[T],
                                           entry: E,
                                           overriddenExtensions: util.Map[String, String],
                                           config: util.Map[String, String],
                                           exceptionHandler: BiConsumer[ParseError, Exception],
                                           fileParsingLog: util.LinkedHashMap[String, String]): StreamEx[T] = {
    val fileName: String = getEntryFileName(entry)
    val detection = detectParser(Optional.empty(), zipStream, overriddenExtensions,
      config, fileName, exceptionHandler, logger)

    detection.message.foreach(fileParsingLog.put(fileName, _))

    detection.parser
      .map(parser => createFileStreamFunction.apply(fileName, parser, detection.restoredStream))
      .getOrElse(StreamEx.empty())
  }

  def parseStreamWithChildParser[T](createFileStreamFn: Function3[String, NexlaParser, InputStream, StreamEx[T]],
                                    overriddenExtensions: util.Map[String, String],
                                    config: util.Map[String, String],
                                    exceptionHandler: BiConsumer[ParseError, Exception],
                                    fileParsingLog: util.LinkedHashMap[String, String]): Iterator[StreamEx[T]] = {
    validate()

    val entries = StreamEx
      .iterate(null.asInstanceOf[E], (_: E) => getNextFileEntry)
      .skip(1)
      .takeWhile(v => v != null)

    entries
      .iterator().asScala
      .map(zipEntry => parseEntryWithChildParser(createFileStreamFn, zipEntry,
        overriddenExtensions,
        config,
        exceptionHandler,
        fileParsingLog))
  }

  def unzip(outputDir: File): Unit = {
    validate()
    val readBuffer = new Array[Byte](UNZIP_BUFFER_SIZE)

    var entry: E = getNextEntry
    while (entry != null) {
      val entryDestination = new File(outputDir, getEntryFileName(entry))

      if (isDirectory(entry)) {
        entryDestination.mkdirs()
      } else {
        entryDestination.getParentFile.mkdirs()

        var outputStream: FileOutputStream = null
        try {
          outputStream = new FileOutputStream(entryDestination)
          var readLen = zipStream.read(readBuffer)
          while (readLen != -1) {
            outputStream.write(readBuffer, 0, readLen)
            readLen = zipStream.read(readBuffer)
          }
        } finally {
          if (outputStream != null) outputStream.close()
        }
      }

      entry = getNextEntry
    }
  }
}
