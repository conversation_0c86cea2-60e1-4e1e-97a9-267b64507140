package com.nexla.parser

import com.nexla.common.FileUtils._
import com.nexla.common.{NexlaMessage, NexlaMetaData}
import com.nexla.common.parse.NexlaParser
import com.nexla.common.parse.ParserConfigs.Zip.{EMPTY_PASSWORD, ZIP_PASSWORD}
import net.lingala.zip4j.exception.ZipException
import one.util.streamex.StreamEx

import java.io.{File, FileInputStream, FileOutputStream, InputStream}
import java.nio.file.{Files, Path}
import java.time.Instant
import java.util
import java.util.concurrent.atomic.AtomicReference
import java.util.stream.{Collectors, Stream}
import java.util.{Collections, Optional}
import scala.language.implicitConversions

class ZipParserUtils extends NexlaParser {

  implicit def funToFunction[T, R](fun: T => R) = new java.util.function.Function[T, R] {
    override def apply(t: T): R = fun(t)
  }

  var overriddenExtensions: util.Map[String, String] = Collections.emptyMap[String, String]
  var zipPassword: String = EMPTY_PASSWORD


  override def option(key: String, value: String): ZipParserUtils = {
    super.option(key, value)
    if (key.equals(ZIP_PASSWORD)) {
      this.zipPassword = value
    }
    this
  }

  def overriddenExtensions(overriddenExtensions: util.Map[String, String]): ZipParserUtils = {
    this.overriddenExtensions = overriddenExtensions
    this
  }

  override def parseMessages(inputStream: InputStream): StreamEx[Optional[NexlaMessage]] = {
    parse(inputStream, (file, p, stream) => {
      p.parseMessages(stream).map { optMess =>
        optMess.map(message => {
          val metaData = message.getNexlaMetaData
          metaData.setSourceKey(file)
          val tags = Option(metaData.getTags).getOrElse(new util.HashMap[String, Object]())
          tags.put(NexlaMetaData.METADATA_ARCHIVE_SOURCE_FILENAME, file)
          metaData.setTags(tags)
          message
        })
      }
    })
  }

  override def readLines(inputStream: InputStream): StreamEx[String] = {
    parse(inputStream, (file, p, stream) => StreamEx.of(p.readLines(stream)))
  }

  private def createTempFileFromInputStream(inputStream: InputStream): File = {
    val timestamp = Instant.now().toEpochMilli
    val threadId = Thread.currentThread().getId
    val tempFile: Path = Files.createTempFile("tempFile"+timestamp+threadId, null)
    val outputStream = new FileOutputStream(tempFile.toFile)
    try {
      inputStream.transferTo(outputStream)
    } finally {
      outputStream.close()
    }
    tempFile.toFile
  }

  private def parse[T](inputStream: InputStream,
                       createFileStreamFn: (String, NexlaParser, InputStream) => StreamEx[T]): StreamEx[T] = {
    val tempFile = createTempFileFromInputStream(inputStream)
    try {
      new Zip4jZipParser(
        new FileInputStream(tempFile),
        logger,
        zipPassword
      ).validateEntry()

      executeParse(new Zip4jZipParser(
        new FileInputStream(tempFile),
        logger,
        zipPassword
      ).parseStreamWithChildParser(createFileStreamFn,
        overriddenExtensions,
        config,
        exceptionHandler,
        fileParsingLog))
    } catch {
      case e: ZipException =>
        logger().warn("Error when tried to use Zip4j in order to read compressed files. Trying to use Apache Commons lib.")
        executeParse(new ApacheCommonsZipParser(
          new FileInputStream(tempFile),
          logger,
          zipPassword
        ).parseStreamWithChildParser(createFileStreamFn,
          overriddenExtensions,
          config,
          exceptionHandler,
          fileParsingLog))
    } finally {
      tempFile.delete()
    }
  }

  private def executeParse[T](fileStream: Iterator[StreamEx[T]]): StreamEx[T] = {
    val currStream = new AtomicReference[Stream[T]]()
    val iterator: util.Iterator[T] = new java.util.Iterator[T]() {
      var currStreamIterator: java.util.Iterator[T] = _

      @Override
      def hasNext: Boolean = {
        val continue =
          if (currStream.get() == null) {
            if (fileStream.hasNext) {
              currStream.set(fileStream.next())
              currStreamIterator = currStream.get().iterator()
              true
            } else {
              false
            }
          } else {
            true
          }

        val result = if (continue) {
          if (!currStreamIterator.hasNext) {
            // every substream is a stream of messages in file.
            // To read some types of files like Parquet it might be required to create temporary files
            // and use external resources, that need to be closed.
            // Substream has a delegate that should be called after reading file fully or partially.
            currStream.get().close()
            currStream.set(null)

            hasNext
          } else {
            true
          }
        } else {
          false
        }

        result
      }

      @Override
      def next: T = currStreamIterator.next()
    }

    StreamEx.of(iterator).onClose(() => closeSilently(currStream.get()))
  }
}
