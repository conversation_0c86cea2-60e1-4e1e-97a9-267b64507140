package com.nexla.parser

import com.nexla.common.exception.ParseError
import com.nexla.common.parse.NexlaParser
import com.nexla.common.parse.ParserConfigs.Zip.EMPTY_PASSWORD
import net.lingala.zip4j.exception.ZipException
import net.lingala.zip4j.io.inputstream.ZipInputStream
import net.lingala.zip4j.model.LocalFileHeader
import one.util.streamex.StreamEx
import org.slf4j.Logger

import java.io.{File, InputStream}
import java.util
import java.util.function.BiConsumer

class Zip4jZipParser(
                      inputStream: InputStream,
                      override protected val logger: Logger,
                      override protected val zipPassword: String = EMPTY_PASSWORD
                    ) extends ZipParser[ZipInputStream, LocalFileHeader] {
  override protected val zipStream: ZipInputStream =
    if (zipPassword.nonEmpty) new ZipInputStream(inputStream, zipPassword.toCharArray)
    else new ZipInputStream(inputStream)

  override protected def getNextFileEntry: LocalFileHeader = {
    var entry = this.zipStream.getNextEntry
    while ((entry != null) && entry.isDirectory) {
      entry = this.zipStream.getNextEntry
    }
    entry
  }

  override protected def getNextEntry: LocalFileHeader = {
    this.zipStream.getNextEntry
  }

  override protected def getEntryFileName(entry: LocalFileHeader): String = {
    entry.getFileName
  }

  override protected def isDirectory(entry: LocalFileHeader): Boolean = {
    entry.isDirectory
  }

  override protected def validate(): Unit = { }

  override def validateEntry(): Unit = {
    val entry: LocalFileHeader = getNextFileEntry
    logger.debug("Validating Zip4jZipParser compression method: " + entry.getCompressionMethod)
  }

  @throws[ZipException]
  override def parseStreamWithChildParser[T](createFileStreamFn: Function3[String, NexlaParser, InputStream, StreamEx[T]],
                                    overriddenExtensions: util.Map[String, String],
                                    config: util.Map[String, String],
                                    exceptionHandler: BiConsumer[ParseError, Exception],
                                    fileParsingLog: util.LinkedHashMap[String, String]): Iterator[StreamEx[T]] = {
    super.parseStreamWithChildParser(createFileStreamFn, overriddenExtensions, config, exceptionHandler, fileParsingLog)
  }

  @throws[ZipException]
  override def unzip(outputDir: File): Unit = {
    super.unzip(outputDir)
  }
}