package com.nexla.writer.parquet;

import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.nexla.connector.config.MappingConfig;
import com.nexla.test.UnitTests;
import com.nexla.writer.NexlaFileWriter;
import junitparams.JUnitParamsRunner;
import junitparams.Parameters;
import org.apache.avro.Schema;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.junit.runner.RunWith;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;

@RunWith(JUnitParamsRunner.class)
@Category(UnitTests.class)
public class ManualAvroSchemaMapperTest {
	private static final int TOTAL_FIELDS_IN_MAPPING = 12;

	/**
	 * Test that schema is built correctly and the record is mapped to a proper values
	 */
	@Test
	@Parameters
	public void mapAllFieldTypesRecordTest(
			final MappingConfig mappingConfig,
			final LinkedHashMap<String, Object> inputData,
			final LinkedHashMap<String, Object> expectedData,
			final String expectedSchema) {
		final ManualAvroSchemaMapper manualAvroSchemaMapper = new ManualAvroSchemaMapper(mappingConfig);

		final Schema actualSchema = manualAvroSchemaMapper.getSchema();
		assertEquals(actualSchema.toString(), expectedSchema);

		// Compare date Maps. Because Map contains byte arrays, the logic is more complex
		final Map<String, Object> actualData = manualAvroSchemaMapper.mapRecord(inputData);
		assertRecordsMap(expectedData, actualData);
	}

	private Object[] parametersForMapAllFieldTypesRecordTest() {
		return new Object[]{
				goodCase(),
				someValuesMissed(),
				someValuesSkipped()
		};
	}

	private static void assertRecordsMap(final Map<String, Object> expectedData, final Map<String, Object> actualData) {
		final MapDifference<String, Object> difference = Maps.difference(actualData, expectedData);
		assertEquals(0, difference.entriesOnlyOnLeft().size());
		assertEquals(0, difference.entriesOnlyOnRight().size());
		assertEquals(TOTAL_FIELDS_IN_MAPPING - 2, difference.entriesInCommon().size());    // -2 for decimal fields. They will be compared separately

		final MapDifference.ValueDifference<Object> decimalSmallDifference = difference.entriesDiffering().get("decimal_value_small");
		assertNotNull(decimalSmallDifference);
		assertArrayEquals((byte[]) decimalSmallDifference.leftValue(), (byte[]) decimalSmallDifference.rightValue());

		final MapDifference.ValueDifference<Object> decimalBigDifference = difference.entriesDiffering().get("decimal_value_big");
		assertNotNull(decimalBigDifference);
		assertArrayEquals((byte[]) decimalBigDifference.leftValue(), (byte[]) decimalBigDifference.rightValue());
	}

	@Test
	@Parameters
	public void writeFileTest(
			final MappingConfig mappingConfig,
			final LinkedHashMap<String, Object> inputData,
			final LinkedHashMap<String, Object> expectedData,
			final String expectedSchema) throws Exception {
		final NexlaFileWriter writer = NexlaFileWriter.forExt("parquet").useTempFile();
		writer.append(inputData, Optional.of(mappingConfig));
		writer.close();
		writer.finish();
		writer.delete();
		// FIXME file data is not validated. Current implementation of Nexla Parquet parser looses the information about field types (like int, float, decimal)
	}

	private Object[] parametersForWriteFileTest() {
		return new Object[]{
				goodCase(),
				someValuesMissed(),
				someValuesSkipped()
		};
	}

	private Object[] goodCase() {
		final LinkedHashMap<String, Object> inputData = new LinkedHashMap<>();
		inputData.put("boolean_value", true);
		inputData.put("long_value", 356L);
		inputData.put("integer_value", 256);
		inputData.put("float_value", 13.56f);
		inputData.put("double_value", 27.45d);
		inputData.put("string_value", "abcdf");
		inputData.put("timestamp_value", "1/2/1991 12:45:16");
		inputData.put("time_value", "13:42:16");
		inputData.put("time_micros_value", "13:42:16");
		inputData.put("date_value", "1991-02-01");
		inputData.put("decimal_value_small", "0.123456789098765432101234567890987654321123456789098765432101234567890987654321");
		inputData.put("decimal_value_big", "123456789098765432101234567890987654321123456789098765432101234567890987654321");

		final LinkedHashMap<String, Object> expectedData = new LinkedHashMap<>();
		expectedData.put("boolean_value", true);
		expectedData.put("long_value", 356L);
		expectedData.put("integer_value", 256);
		expectedData.put("float_value", 13.56f);
		expectedData.put("double_value", 27.45d);
		expectedData.put("string_value", "abcdf");
		expectedData.put("timestamp_value", 665412316000L);
		expectedData.put("time_value", (int) (TimeUnit.SECONDS.toMillis(16) + TimeUnit.MINUTES.toMillis(42) + TimeUnit.HOURS.toMillis(13)));
		expectedData.put("time_micros_value", TimeUnit.SECONDS.toMicros(16) + TimeUnit.MINUTES.toMicros(42) + TimeUnit.HOURS.toMicros(13));
		expectedData.put("date_value", 7701);
		expectedData.put(
				"decimal_value_small",
				new BigDecimal("0.123456789098765432101234567890987654321123456789098765432101234567890987654321")
						.setScale(100, RoundingMode.DOWN)
						.unscaledValue()
						.toByteArray());
		expectedData.put("decimal_value_big",
				new BigDecimal("123456789098765432101234567890987654321123456789098765432101234567890987654321")
						.setScale(0, RoundingMode.DOWN)
						.unscaledValue()
						.toByteArray());

		return new Object[]{getMappingConfig(), inputData, expectedData, getExpectedSchema()};
	}

	private Object[] someValuesMissed() {
		final LinkedHashMap<String, Object> inputData = new LinkedHashMap<>();
		inputData.put("boolean_value", true);
		inputData.put("long_value", 356L);

		inputData.put("integer_value", null);
		inputData.put("float_value", null);

		// No double_value, string_value

		inputData.put("time_value", "13:42:16");
		inputData.put("time_micros_value", "13:42:16");
		inputData.put("date_value", "1991-02-01");
		inputData.put("decimal_value_small", "0.123456789098765432101234567890987654321123456789098765432101234567890987654321");
		inputData.put("decimal_value_big", "123456789098765432101234567890987654321123456789098765432101234567890987654321");

		final LinkedHashMap<String, Object> expectedData = new LinkedHashMap<>();
		expectedData.put("boolean_value", true);
		expectedData.put("long_value", 356L);
		expectedData.put("integer_value", null);
		expectedData.put("float_value", null);
		expectedData.put("double_value", null);
		expectedData.put("string_value", null);
		expectedData.put("timestamp_value", null);
		expectedData.put("time_value", (int) (TimeUnit.SECONDS.toMillis(16) + TimeUnit.MINUTES.toMillis(42) + TimeUnit.HOURS.toMillis(13)));
		expectedData.put("time_micros_value", TimeUnit.SECONDS.toMicros(16) + TimeUnit.MINUTES.toMicros(42) + TimeUnit.HOURS.toMicros(13));
		expectedData.put("date_value", 7701);
		expectedData.put(
				"decimal_value_small",
				new BigDecimal("0.123456789098765432101234567890987654321123456789098765432101234567890987654321")
						.setScale(100, RoundingMode.DOWN)
						.unscaledValue()
						.toByteArray());
		expectedData.put("decimal_value_big",
				new BigDecimal("123456789098765432101234567890987654321123456789098765432101234567890987654321")
						.setScale(0, RoundingMode.DOWN)
						.unscaledValue()
						.toByteArray());

		return new Object[]{getMappingConfig(), inputData, expectedData, getExpectedSchema()};
	}

	private Object[] someValuesSkipped() {
		final LinkedHashMap<String, Object> inputData = new LinkedHashMap<>();
		inputData.put("skipped_boolean_value", true);
		inputData.put("skipped_string_value", "abcdf");
		inputData.put("skipped_timestamp_value", "1/2/1991 12:45:16");

		inputData.put("boolean_value", true);
		inputData.put("long_value", 356L);
		inputData.put("integer_value", 256);
		inputData.put("float_value", 13.56f);
		inputData.put("double_value", 27.45d);
		inputData.put("string_value", "abcdf");
		inputData.put("timestamp_value", "1/2/1991 12:45:16");
		inputData.put("time_value", "13:42:16");
		inputData.put("time_micros_value", "13:42:16");
		inputData.put("date_value", "1991-02-01");
		inputData.put("decimal_value_small", "0.123456789098765432101234567890987654321123456789098765432101234567890987654321");
		inputData.put("decimal_value_big", "123456789098765432101234567890987654321123456789098765432101234567890987654321");

		final LinkedHashMap<String, Object> expectedData = new LinkedHashMap<>();
		expectedData.put("boolean_value", true);
		expectedData.put("long_value", 356L);
		expectedData.put("integer_value", 256);
		expectedData.put("float_value", 13.56f);
		expectedData.put("double_value", 27.45d);
		expectedData.put("string_value", "abcdf");
		expectedData.put("timestamp_value", 665412316000L);
		expectedData.put("time_value", (int) (TimeUnit.SECONDS.toMillis(16) + TimeUnit.MINUTES.toMillis(42) + TimeUnit.HOURS.toMillis(13)));
		expectedData.put("time_micros_value", TimeUnit.SECONDS.toMicros(16) + TimeUnit.MINUTES.toMicros(42) + TimeUnit.HOURS.toMicros(13));
		expectedData.put("date_value", 7701);
		expectedData.put(
				"decimal_value_small",
				new BigDecimal("0.123456789098765432101234567890987654321123456789098765432101234567890987654321")
						.setScale(100, RoundingMode.DOWN)
						.unscaledValue()
						.toByteArray());
		expectedData.put("decimal_value_big",
				new BigDecimal("123456789098765432101234567890987654321123456789098765432101234567890987654321")
						.setScale(0, RoundingMode.DOWN)
						.unscaledValue()
						.toByteArray());

		return new Object[]{getMappingConfig(), inputData, expectedData, getExpectedSchema()};
	}

	private MappingConfig getMappingConfig() {
		final MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MappingConfig.MODE_MANUAL);
		{
			final LinkedHashMap<String, Map<String, String>> mapping = mappingConfig.getMapping();
			mapping.put("boolean_value_origin", Map.of("boolean_value", "BOOLEAN"));
			mapping.put("long_value_origin", Map.of("long_value", "LONG"));
			mapping.put("integer_value_origin", Map.of("integer_value", "INT"));
			mapping.put("float_value_origin", Map.of("float_value", "FLOAT"));
			mapping.put("double_value_origin", Map.of("double_value", "DOUBLE"));
			mapping.put("string_value_origin", Map.of("string_value", "STRING"));
			mapping.put("timestamp_value_origin", Map.of("timestamp_value", "TIMESTAMP"));

			final LinkedHashMap<String, String> timeValuesMapping = new LinkedHashMap<>();
			timeValuesMapping.put("time_value", "TIME");
			timeValuesMapping.put("time_micros_value", "TIME");
			mapping.put("time_value_origin", timeValuesMapping);

			mapping.put("date_value_origin", Map.of("date_value", "DATE"));

			final LinkedHashMap<String, String> decimalValuesMapping = new LinkedHashMap<>();
			decimalValuesMapping.put("decimal_value_small", "DECIMAL");
			decimalValuesMapping.put("decimal_value_big", "DECIMAL");
			mapping.put("decimal_value_origin", decimalValuesMapping);
		}
		{
			final LinkedHashMap<String, Map<String, String>> sourceFieldOptions = mappingConfig.getSourceFieldOptions();
			sourceFieldOptions.put("timestamp_value_origin", Map.of("format", "d/M/yyyy HH:mm:ss"));
			sourceFieldOptions.put("time_value_origin", Map.of("format", "HH:mm:ss"));
			sourceFieldOptions.put("date_value_origin", Map.of("format", "yyyy-MM-dd"));
		}
		{
			final LinkedHashMap<String, Map<String, String>> targetFieldOptions = mappingConfig.getTargetFieldOptions();
			targetFieldOptions.put("timestamp_value", Map.of("unit", "millis"));
			targetFieldOptions.put("time_value", Map.of("unit", "millis"));
			targetFieldOptions.put("time_micros_value", Map.of("unit", "micros"));
			targetFieldOptions.put("decimal_value_small", Map.of("precision", "100", "scale", "100"));
			targetFieldOptions.put("decimal_value_big", Map.of("precision", "100", "scale", "0"));
		}
		return mappingConfig;
	}

	private static String getExpectedSchema() {
		return "" +
				"{\"type\":\"record\"," +
				"\"name\":\"custom\"," +
				"\"fields\":[" +
				"{\"name\":\"boolean_value\",\"type\":[\"null\",\"boolean\"]}," +
				"{\"name\":\"long_value\",\"type\":[\"null\",\"long\"]}," +
				"{\"name\":\"integer_value\",\"type\":[\"null\",\"int\"]}," +
				"{\"name\":\"float_value\",\"type\":[\"null\",\"float\"]}," +
				"{\"name\":\"double_value\",\"type\":[\"null\",\"double\"]}," +
				"{\"name\":\"string_value\",\"type\":[\"null\",\"string\"]}," +
				"{\"name\":\"timestamp_value\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"timestamp-millis\"}]}," +
				"{\"name\":\"time_value\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"time-millis\"}]}," +
				"{\"name\":\"time_micros_value\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"time-micros\"}]}," +
				"{\"name\":\"date_value\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}]}," +
				"{\"name\":\"decimal_value_small\",\"type\":[\"null\",{\"type\":\"bytes\",\"logicalType\":\"decimal\",\"precision\":100,\"scale\":100}]}," +
				"{\"name\":\"decimal_value_big\",\"type\":[\"null\",{\"type\":\"bytes\",\"logicalType\":\"decimal\",\"precision\":100,\"scale\":0}]}" +
				"]}";
	}
}