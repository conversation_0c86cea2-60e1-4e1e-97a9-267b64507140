package com.nexla.parser.sgml;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexla.common.NexlaMessage;
import com.nexla.parser.TarTest;
import com.nexla.test.UnitTests;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import java.io.IOException;
import java.io.InputStream;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;

@Category(UnitTests.class)
public class NexlaSgmlParserTest {

    @Test
    public void parseMessages10Q() throws IOException {
        try (InputStream is = stream("sgml/10-Q/0000320193-21-000010.txt")) {
            List<NexlaMessage> messages = new NexlaSgmlParser().parseMessages(is)
                    .map(Optional::get)
                    .collect(Collectors.toList());

            assertEquals(3, messages.size());

            for (NexlaMessage message : messages) {
                Map<String, String> report = (Map<String, String>) message.getRawMessage().get("report");

                try (InputStream actual = stream("sgml/10-Q/actual/0000320193-21-000010/" + report.get("HtmlFileName").replace(".htm", ".json"))) {
                    LinkedHashMap expected = new ObjectMapper().readValue(
                            actual,
                            LinkedHashMap.class
                    );

                    assertEquals(expected, message.getRawMessage());
                }
            }
        }

    }

    @Test
    public void parseMessages10KA() throws IOException {
        try (InputStream is = stream("sgml/10-K_A/0001477932-21-001410.txt")) {
            List<NexlaMessage> messages = new NexlaSgmlParser().parseMessages(is)
                    .map(Optional::get)
                    .collect(Collectors.toList());

            assertEquals(3, messages.size());

            for (NexlaMessage message : messages) {
                Map<String, String> report = (Map<String, String>) message.getRawMessage().get("report");

                try (InputStream actual = stream("sgml/10-K_A/actual/0001477932-21-001410/" + report.get("HtmlFileName").replace(".htm", ".json"))) {
                    LinkedHashMap expected = new ObjectMapper().readValue(
                            actual,
                            LinkedHashMap.class
                    );

                    assertEquals(expected, message.getRawMessage());
                }
            }

        }
    }

    @Test
    public void parseMessages10K() throws IOException {
        try (InputStream is = stream("sgml/10-K/0001459417-21-000003.txt")) {
            List<NexlaMessage> messages = new NexlaSgmlParser().parseMessages(is)
                    .map(Optional::get)
                    .collect(Collectors.toList());

            assertEquals(2, messages.size());

            for (NexlaMessage message : messages) {
                Map<String, String> report = (Map<String, String>) message.getRawMessage().get("report");

                try (InputStream actual = stream("sgml/10-K/actual/0001459417-21-000003/" + report.get("HtmlFileName").replace(".htm", ".json"))) {
                    LinkedHashMap expected = new ObjectMapper().readValue(
                            actual,
                            LinkedHashMap.class
                    );

                    assertEquals(expected, message.getRawMessage());
                }
            }

        }
    }

    @Test
    public void parseMessages10KNoSummary() throws IOException {
        try (InputStream is = stream("sgml/10-K/0001213900-21-009907.txt")) {
            List<NexlaMessage> messages = new NexlaSgmlParser().parseMessages(is)
                    .map(Optional::get)
                    .collect(Collectors.toList());

            assertEquals(0, messages.size());
        }
    }

    private InputStream stream(String name) {
        return TarTest.getResourceAsStreaz(this.getClass(), name);
    }
}