package com.nexla.parser;

import com.beust.jcommander.internal.Lists;
import com.nexla.test.UnitTests;
import net.lingala.zip4j.exception.ZipException;
import one.util.streamex.StreamEx;
import org.apache.commons.lang.StringUtils;
import org.apache.cxf.helpers.FileUtils;
import org.assertj.core.util.Sets;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

import static com.nexla.common.parse.ParserConfigs.Zip.EMPTY_PASSWORD;
import static org.apache.commons.io.FileUtils.listFiles;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;

@Category(UnitTests.class)
public class ZipTest {
	private static final Logger logger = LoggerFactory.getLogger(ZipTest.class);

	@Test
	public void testUnzip() throws FileNotFoundException, ZipException {
		File tempDir = FileUtils.createTmpDir();
		File zipFile = new File(TarTest.getResource(this.getClass(), "zipfolder.zip").getFile());

		new Zip4jZipParser(
			new FileInputStream(zipFile),
			logger,
			EMPTY_PASSWORD
		).unzip(tempDir);

		HashSet<String> expected = Sets.newHashSet(Lists.newArrayList("/3/1", "/3/2", "1", "2"));
		Set<String> actual = StreamEx.of(listFiles(tempDir, null, true))
			.map(f ->
				Optional.ofNullable(
						StringUtils.trimToNull(
							StringUtils.removeStart(f.getParent(), tempDir.getAbsolutePath())))
					.map(xx -> xx + "/")
					.orElse("") + f.getName())
			.toSet();

		assertEquals(expected, actual);
	}

	@Test
	public void testUnzipWithPassword() throws FileNotFoundException, ZipException {
		File tempDir = FileUtils.createTmpDir();
		File zipFile = new File(TarTest.getResource(this.getClass(), "zip_folder_password_protected.zip").getFile());
		String password = "samplepass";

		new Zip4jZipParser(
			new FileInputStream(zipFile),
			logger,
			password
		).unzip(tempDir);

		HashSet<String> expected = Sets.newHashSet(Lists.newArrayList("/3/1", "/3/2", "1", "2"));
		Set<String> actual = StreamEx.of(listFiles(tempDir, null, true))
			.map(f ->
				Optional.ofNullable(
						StringUtils.trimToNull(
							StringUtils.removeStart(f.getParent(), tempDir.getAbsolutePath())))
					.map(xx -> xx + "/")
					.orElse("") + f.getName())
			.toSet();

		assertEquals(expected, actual);
	}


	@Test
	public void testDeflate64Unzip() throws FileNotFoundException {
		File tempDir = FileUtils.createTmpDir();
		File zipFile = new File(TarTest.getResource(this.getClass(), "test_deflate64.zip").getFile());

		try {
			new Zip4jZipParser(
				new FileInputStream(zipFile),
				logger,
				EMPTY_PASSWORD
			).unzip(tempDir);
		} catch (ZipException e) {
			new ApacheCommonsZipParser(
				new FileInputStream(zipFile),
				logger,
				EMPTY_PASSWORD
			).unzip(tempDir);
		}

		HashSet<String> expected = Sets.newHashSet(Lists.newArrayList("users.csv"));
		Set<String> actual = StreamEx.of(listFiles(tempDir, null, true))
			.map(f ->
				Optional.ofNullable(
						StringUtils.trimToNull(
							StringUtils.removeStart(f.getParent(), tempDir.getAbsolutePath())))
					.map(xx -> xx + "/")
					.orElse("") + f.getName())
			.toSet();

		assertEquals(expected, actual);
	}

	@Test
	public void testDeflate64UnzipWithPassword() throws FileNotFoundException {
		File tempDir = FileUtils.createTmpDir();
		File zipFile = new File(TarTest.getResource(this.getClass(), "test_deflate64.zip").getFile());
		String password = "samplepass";


		Exception exception = assertThrows(Exception.class, () -> {
			try {
				new Zip4jZipParser(
					new FileInputStream(zipFile),
					logger,
					password
				).unzip(tempDir);
			} catch (ZipException e) {
				new ApacheCommonsZipParser(
					new FileInputStream(zipFile),
					logger,
					password
				).unzip(tempDir);
			}
		});
		assertEquals("Apache commons zip parser does not support password/encryption.", exception.getMessage());
	}
}
