package com.nexla.parser.excel;

import com.nexla.test.UnitTests;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import static org.junit.Assert.*;

@Category(UnitTests.class)
public class RangeNoSheetTest {
    @Test
    public void transposedTest() {
        final RangeNoSheet range = new RangeNoSheet(
                new RangeFromTo("A", 2),
                new RangeFromTo("C", 4));
        final RangeNoSheet transposed = range.transposed();

        assertEquals("B", transposed.getFrom().getColumn().get());
        assertEquals(1, (int) transposed.getFrom().getRow().get());
        assertEquals("D", transposed.getTo().getColumn().get());
        assertEquals(3, (int) transposed.getTo().getRow().get());
    }
}