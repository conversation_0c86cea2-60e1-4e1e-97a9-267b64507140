package com.nexla.parser.excel;

import com.nexla.test.UnitTests;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import static org.junit.Assert.assertEquals;

@Category(UnitTests.class)
public class RangeSheetTest {

    @Test
    public void transposedTest() {
        final RangeSheet rangeSheet = new RangeSheet(
                "name1",
                new RangeNoSheet(
                        new RangeFromTo("A", 2),
                        new RangeFromTo("C", 4)));
        final RangeSheet transposed = rangeSheet.transposed();

        assertEquals("name1", transposed.getSheetName().get());

        assertEquals("B",transposed.getRange().get().getFrom().getColumn().get());
        assertEquals(1, (int) transposed.getRange().get().getFrom().getRow().get());
        assertEquals("D",transposed.getRange().get().getTo().getColumn().get());
        assertEquals(3, (int) transposed.getRange().get().getTo().getRow().get());
    }
}