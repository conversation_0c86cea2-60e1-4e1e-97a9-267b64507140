package com.nexla.parser.excel;

import com.nexla.parser.excel.formula.ExcelFormulaEvaluator;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Ignore;
import org.junit.Test;

import java.time.LocalDate;
import java.time.Period;

import static java.lang.String.format;
import static org.assertj.core.api.Java6Assertions.assertThat;

public class ExcelFormulaEvaluatorTest {
	private static final ExcelFormulaEvaluator EXCEL_FORMULA_EVALUATOR = new ExcelFormulaEvaluator();

	@Test
	@SneakyThrows
	public void testStringFormula() {
		try (Workbook workbook = new XSSFWorkbook()) {
			String sheetName = "String formula";
			Sheet sheet = workbook.createSheet(sheetName);
			Row row = sheet.createRow(0);
			Cell cell = row.createCell(0);
			cell.setCellFormula("\"Hello\" & \"World\"");

			Sheet sheetResult = EXCEL_FORMULA_EVALUATOR.evaluateAllFormulas(workbook,
					workbook.getSheetIndex(workbook.getSheet(sheetName)));
			assertThat(sheetResult.getRow(0).getCell(0).getStringCellValue())
					.isEqualTo("HelloWorld");
		}
	}

	@Test
	@SneakyThrows
	public void testSheetNameMaxSize() {
		try (Workbook workbook = new XSSFWorkbook()) {
			String sheetName = "This sheet name is 31 chars lon";
			Sheet sheet = workbook.createSheet(sheetName);
			Row row = sheet.createRow(0);
			Cell cell = row.createCell(0);
			cell.setCellFormula("\"Hello\" & \"World\"");

			Sheet sheetResult = EXCEL_FORMULA_EVALUATOR.evaluateAllFormulas(workbook,
					workbook.getSheetIndex(workbook.getSheet(sheetName)));
			assertThat(sheetResult.getRow(0).getCell(0).getStringCellValue())
					.isEqualTo("HelloWorld");
		}
	}

	@Test
	@SneakyThrows
	public void testBooleanFormula() {
		try (Workbook workbook = new XSSFWorkbook()) {
			String sheetName = "Boolean formula";
			Sheet sheet = workbook.createSheet(sheetName);
			Row row = sheet.createRow(0);
			Cell cell0 = row.createCell(0);
			Cell cell1 = row.createCell(1);
			Cell cell2 = row.createCell(2);

			cell0.setCellValue(10.0);
			cell1.setCellValue(2.0);
			cell2.setCellFormula("A1>B1");

			Sheet sheetResult = EXCEL_FORMULA_EVALUATOR.evaluateAllFormulas(workbook,
					workbook.getSheetIndex(workbook.getSheet(sheetName)));
			assertThat(sheetResult.getRow(0).getCell(2).getStringCellValue())
					.isEqualTo("true");
		}
	}

	@Test
	@SneakyThrows
	public void testNumericFormula() {
		try (Workbook workbook = new XSSFWorkbook()) {
			String sheetName = "Numeric formula";
			Sheet sheet = workbook.createSheet(sheetName);
			Row row = sheet.createRow(0);
			Cell cell0 = row.createCell(0);
			Cell cell1 = row.createCell(1);
			Cell cell2 = row.createCell(2);

			cell0.setCellValue(10.0);
			cell1.setCellValue(2.0);
			cell2.setCellFormula("A1+B1");

			Sheet sheetResult = EXCEL_FORMULA_EVALUATOR.evaluateAllFormulas(workbook,
					workbook.getSheetIndex(workbook.getSheet(sheetName)));
			assertThat(sheetResult.getRow(0).getCell(2).getStringCellValue())
					.isEqualTo("12");
		}
	}

	@Test
	@SneakyThrows
	public void testFormulaChain() {
		try (Workbook workbook = new XSSFWorkbook()) {
			String sheetName = "Chain formula";
			Sheet sheet = workbook.createSheet(sheetName);
			Row row = sheet.createRow(0);
			Cell cell0 = row.createCell(0);
			Cell cell1 = row.createCell(1);
			Cell cell2 = row.createCell(2);

			cell0.setCellValue(10.0);
			cell1.setCellValue(2.0);
			cell2.setCellFormula("(A1+B1)*2");

			Sheet sheetResult = EXCEL_FORMULA_EVALUATOR.evaluateAllFormulas(workbook,
					workbook.getSheetIndex(workbook.getSheet(sheetName)));
			assertThat(sheetResult.getRow(0).getCell(2).getStringCellValue())
					.isEqualTo("24");
		}
	}

	@Test
	@SneakyThrows
	public void testComplexFormula() {
		try (Workbook workbook = new XSSFWorkbook()) {
			String sheetName = "Pythagorean theorem";
			Sheet sheet = workbook.createSheet(sheetName);
			Row row = sheet.createRow(0);
			Cell cell0 = row.createCell(0);
			Cell cell1 = row.createCell(1);
			Cell cell2 = row.createCell(2);

			cell0.setCellValue(3.0);
			cell1.setCellValue(4.0);
			cell2.setCellFormula("SQRT((A1^2)+(B1^2))");

			Sheet sheetResult = EXCEL_FORMULA_EVALUATOR.evaluateAllFormulas(workbook,
					workbook.getSheetIndex(workbook.getSheet(sheetName)));
			assertThat(sheetResult.getRow(0).getCell(2).getStringCellValue())
					.isEqualTo("5");
		}
	}

	@Test
	@SneakyThrows
	public void testDateSupported() {
		final String formulaTemplate = "TODAY()-%s>180";
		try (Workbook workbook = new XSSFWorkbook()) {
			String sheetName = "Dates function supported";
			Sheet sheet = workbook.createSheet(sheetName);
			Row row = sheet.createRow(0);
			Cell cell0 = row.createCell(0);
			Cell cell1 = row.createCell(1);
			Cell cellValueTrue = row.createCell(2);
			Cell cellValueFalse = row.createCell(3);

			cell0.setCellValue(LocalDate.now().minus(Period.of(0, 0, 179)));
			cell1.setCellValue(LocalDate.now().minus(Period.of(0, 0, 181)));
			cellValueTrue.setCellFormula(format(formulaTemplate, "A1"));
			cellValueFalse.setCellFormula(format(formulaTemplate, "B1"));

			Sheet sheetResult = EXCEL_FORMULA_EVALUATOR.evaluateAllFormulas(workbook,
					workbook.getSheetIndex(workbook.getSheet(sheetName)));
			assertThat(sheetResult.getRow(0).getCell(2).getStringCellValue())
					.isEqualTo("false");
			assertThat(sheetResult.getRow(0).getCell(3).getStringCellValue())
					.isEqualTo("true");
		}
	}

	@Test
	@Ignore("Because poi can decide it's date only if workbook has it stated in the cache." +
			"@see  org.apache.poi.ss.usermodel.DateUtil#isADateFormat(int formatIndex, String formatString)")
	@SneakyThrows
	public void testDateFormatted() {
		final String formulaTemplate = "DATE(2000,10,20)";

		try (Workbook workbook = new XSSFWorkbook()) {
			String sheetName = "Dates function formatted";
			Sheet sheet = workbook.createSheet(sheetName);
			Row row = sheet.createRow(0);
			Cell cell0 = row.createCell(0);

			cell0.setCellFormula(formulaTemplate);

			Sheet sheetResult = EXCEL_FORMULA_EVALUATOR.evaluateAllFormulas(workbook,
					workbook.getSheetIndex(workbook.getSheet(sheetName)));

			assertThat(LocalDate.parse(sheetResult.getRow(0).getCell(0).getStringCellValue()))
					.isEqualTo(LocalDate.of(2000, 10, 20));
		}
	}
}
