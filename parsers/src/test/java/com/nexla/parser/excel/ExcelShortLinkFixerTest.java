package com.nexla.parser.excel;

import com.nexla.parser.TarTest;
import com.nexla.parser.excel.formula.ExcelShortLinkFixer;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;

import java.io.IOException;
import java.io.InputStream;

import static org.assertj.core.api.Java6Assertions.assertThat;

public class ExcelShortLinkFixerTest {

	private static final ExcelShortLinkFixer EXCEL_SHORT_LINK_FIXER = new ExcelShortLinkFixer();

	@Test
	@SneakyThrows
	public void testTableNameResolved() {
		try (Workbook workbook = this.readWorkBook()) {
			Sheet sheet = workbook.getSheetAt(0);
			String formulaBefore = sheet.getRow(2).getCell(1).getCellFormula();
			assertThat(formulaBefore)
					.isEqualTo("IF(ISBLANK(VLOOKUP(LocProfile!$A3,Table2[],6,FALSE)),\"\",\"Y\")");

			EXCEL_SHORT_LINK_FIXER.resolveShortTableNames(sheet, workbook);
			String formulaAfter = sheet.getRow(2).getCell(1).getCellFormula();
			assertThat(formulaAfter)
					.isEqualTo("IF(ISBLANK(VLOOKUP(LocProfile!$A3," +
							"'Source - Cafe Info Sheet'!A1:AB112,6,FALSE)),\"\",\"Y\")");
		}
	}

	@Test
	@SneakyThrows
	public void testTableNameResolvedWithArrayFormula() {
		try (Workbook workbook = this.readWorkBook()) {
			Sheet sheet = workbook.getSheetAt(0);
			String formulaBefore = sheet.getRow(1).getCell(1).getCellFormula();
			assertThat(formulaBefore)
					.isEqualTo("IF(ISBLANK(VLOOKUP(LocProfile!$A2,Table2[],6,FALSE)),\"\",\"Y\")");

			EXCEL_SHORT_LINK_FIXER.resolveShortTableNames(sheet, workbook);
			String formulaAfter = sheet.getRow(1).getCell(1).getCellFormula();
			assertThat(formulaAfter)
					.isEqualTo("IF(ISBLANK(VLOOKUP(LocProfile!$A2," +
							"'Source - Cafe Info Sheet'!A1:AB112,6,FALSE)),\"\",\"Y\")");
		}
	}

	@Test
	@SneakyThrows
	public void testSeveralTableNamesResolved() {
		try (Workbook workbook = this.readWorkBook()) {
			Sheet sheet = workbook.getSheetAt(0);
			String formulaBefore = sheet.getRow(2).getCell(2).getCellFormula();
			assertThat(formulaBefore)
					.isEqualTo("IF(VLOOKUP(LocProfile!$A3,Table2[],9,FALSE)=0,\"\"," +
							"VLOOKUP(LocProfile!$A3,Table2[],9,FALSE))");

			EXCEL_SHORT_LINK_FIXER.resolveShortTableNames(sheet, workbook);
			String formulaAfter = sheet.getRow(2).getCell(2).getCellFormula();
			assertThat(formulaAfter)
					.isEqualTo("IF(VLOOKUP(LocProfile!$A3,'Source - Cafe Info Sheet'!A1:AB112,9,FALSE)=0,\"\"," +
							"VLOOKUP(LocProfile!$A3,'Source - Cafe Info Sheet'!A1:AB112,9,FALSE))");
		}
	}

	private XSSFWorkbook readWorkBook() throws IOException {
		try (final InputStream file = TarTest.getResourceAsStreaz(this.getClass(),
				"customer_data_with_multi_page_formulas_and_dates.xlsx")) {
			return new XSSFWorkbook(file);
		}
	}
}
