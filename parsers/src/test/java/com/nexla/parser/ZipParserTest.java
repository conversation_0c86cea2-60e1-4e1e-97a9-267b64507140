package com.nexla.parser;

import com.nexla.common.NexlaMessage;
import com.nexla.test.UnitTests;
import lombok.SneakyThrows;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import java.io.InputStream;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

import static com.google.common.collect.ImmutableMap.of;
import static com.nexla.common.parse.ParserConfigs.Zip.ZIP_PASSWORD;
import static java.util.Arrays.asList;
import static java.util.stream.Collectors.toList;
import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

@RunWith(Parameterized.class)
@Category(UnitTests.class)
public class ZipParserTest {

	private String fileName;
	private List<Map<String, String>> values;
	private int length;
	private int columnCount;

	@Parameters
	public static Collection<Object[]> data() {
		return asList(new Object[][]{
			{
				"test.zip",
				asList(
					of("key", "key1"),
					of("column1", "1", "column2", "2"),
					of("column1", "3", "column2", "4")),
				0,
				2
			},
			{
				"test_bigger.zip", null, 704, 6
			},
			{
				"test_deflate64.zip", null, 1215, 3
			}
		});
	}

	public ZipParserTest(String fileName, List<Map<String, String>> values, int length, int columnCount) {
		this.fileName = fileName;
		this.values = values;
		this.length = length;
		this.columnCount = columnCount;
	}

	@SneakyThrows
	@Test
	public void getMessages() {

		try (InputStream inputStream = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {

			ZipParserUtils zipParserUtils = new ZipParserUtils();
			Stream<Optional<NexlaMessage>> stream = zipParserUtils.parseMessages(inputStream);
			List<NexlaMessage> messages = stream.map(Optional::get).collect(toList());
			stream.close();

			int actualColCount = 0;
			List<HashMap<String, Object>> actual = messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList());
			for (HashMap<String, Object> row : actual) {
				actualColCount = Math.max(actualColCount, row.size());
			}

			assertEquals(columnCount, actualColCount);
			if (values != null) {
				assertEquals(values, actual);
			} else {
				assertThat(length, is(actual.size()));
			}
		}
	}


	@SneakyThrows
	@Test
	public void getMessagesPasswordProtected() {
		String zipPasswordProtected = "zip_parser_password_protected.zip";
		int columnCountOfZip = 2;
		int customLength = 3;

		try (InputStream inputStream = TarTest.getResourceAsStreaz(this.getClass(), zipPasswordProtected)) {

			ZipParserUtils zipParserUtils = new ZipParserUtils().option(ZIP_PASSWORD, "zippass123");
			Stream<Optional<NexlaMessage>> stream = zipParserUtils.parseMessages(inputStream);
			List<NexlaMessage> messages = stream.map(Optional::get).collect(toList());
			stream.close();

			int actualColCount = 0;
			List<HashMap<String, Object>> actual = messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList());
			for (HashMap<String, Object> row : actual) {
				actualColCount = Math.max(actualColCount, row.size());
			}

			assertEquals(columnCountOfZip, actualColCount);
			if (values != null) {
				assertEquals(actual, values);
			} else {
				assertEquals(actual.size(), customLength);
			}
		}
	}
}