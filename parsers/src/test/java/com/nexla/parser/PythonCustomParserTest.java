package com.nexla.parser;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexla.common.NexlaMessage;
import com.nexla.test.UnitTests;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import javax.script.ScriptEngineManager;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Category(UnitTests.class)
public class PythonCustomParserTest {

	private String getCustomParserCode(String filename) throws IOException {
		try (InputStream reader = TarTest.getResourceAsStreaz(this.getClass(), filename)) {
			return IOUtils.toString(reader, StandardCharsets.UTF_8);
		}
	}

	@SneakyThrows
	private List<Optional<NexlaMessage>> getExpectedData(String dataFile) {
		ObjectMapper mapper = new ObjectMapper();
		List<Optional<NexlaMessage>> result;
		try (BufferedReader br = new BufferedReader(new InputStreamReader(TarTest.getResourceAsStreaz(this.getClass(), dataFile)))){
			result = br.lines().map(x -> {
				try {
					return Optional.of(new NexlaMessage(mapper.readValue(x, LinkedHashMap.class)));
				} catch (JsonProcessingException e) {
					throw new RuntimeException();
				}
			}).collect(Collectors.toList());
		}
		return result;
	}

	@Test
	public void parseMessagesUsingPythonFileObject() {
		ScriptEngineManager scriptEngineManager = new ScriptEngineManager();

		try(InputStream inputStream = TarTest.getResourceAsStreaz(this.getClass(), "aes_encrypted_data")){
			PythonCustomParser pythonParser = new PythonCustomParser(getCustomParserCode("aes_custom_parser.py"),
				scriptEngineManager.getEngineByName("python"),
				Collections.emptyMap(), Map.of("password", "super-secret-password", "salt", "super-random-salt",
					"algorithm", "AES", "mode", "ECB", "padding", "PKCS5PADDING"));

			StreamEx<Optional<NexlaMessage>> result = pythonParser.parseMessages(inputStream);

			List<Optional<NexlaMessage>> actual = result.collect(Collectors.toList());
			List<Optional<NexlaMessage>> expectedData = getExpectedData("aes_decrypted_data.json");
			Assert.assertEquals(expectedData, actual);
		} catch (IOException e) {
			Assert.fail("Error reading test resources");
		}
	}

	@Test
	public void parseMessagesUsingDelegateExcelParser() {
		ScriptEngineManager scriptEngineManager = new ScriptEngineManager();

		try(InputStream inputStream = TarTest.getResourceAsStreaz(this.getClass(), "test.xlsx")){
			PythonCustomParser pythonParser = new PythonCustomParser(getCustomParserCode("custom_delegate_parser.py"),
				scriptEngineManager.getEngineByName("python"),
				Collections.emptyMap(), Map.of("excel.type", "xlsx", "sheets", "Sheet1", 
					"excel.schema.detection", "header"));

			pythonParser.options(Map.of("delegate", "excel"));

			StreamEx<Optional<NexlaMessage>> result = pythonParser.parseMessages(inputStream);

			List<Map<String, Object>> actual = result
					.filter(Optional::isPresent)
					.map(e -> e.get().getRawMessage())
					.collect(Collectors.toList());

			List<Map<String, Object>> expectedData = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("number", "1");
			map.put("weight", "100");
			map.put("description", "pig");
			expectedData.add(map);

			map = new HashMap<>();
			map.put("number", "2");
			map.put("weight", "200");
			map.put("description", "cow");
			expectedData.add(map);

			map = new HashMap<>();
			map.put("number", "3");
			map.put("weight", "150");
			map.put("description", "dog");
			expectedData.add(map);
			
			Assert.assertEquals(expectedData, actual);
		} catch (IOException e) {
			Assert.fail("Error reading test resources");
		}
	}


	@Test
	public void parseMessagesUsingDelegateJsonParser() {
		ScriptEngineManager scriptEngineManager = new ScriptEngineManager();

		try(InputStream inputStream = TarTest.getResourceAsStreaz(this.getClass(), "testJsonPath.json")){
			PythonCustomParser pythonParser = new PythonCustomParser(getCustomParserCode("custom_delegate_parser.py"),
					scriptEngineManager.getEngineByName("python"),
					Collections.emptyMap(), Map.of("json.path", "$.features[*].properties"));

			pythonParser.options(Map.of("delegate", "json"));

			StreamEx<Optional<NexlaMessage>> result = pythonParser.parseMessages(inputStream);

			List<Map<String, Object>> actual = result
					.filter(Optional::isPresent)
					.map(e -> e.get().getRawMessage())
					.collect(Collectors.toList());

			List<Map<String, Object>> expectedData = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("MAPBLKLOT", "0001001");
			expectedData.add(map);

			map = new HashMap<>();
			map.put("MAPBLKLOT", "0002001");
			expectedData.add(map);

			Assert.assertEquals(expectedData, actual);
		} catch (IOException e) {
			Assert.fail("Error reading test resources");
		}
	}

	@Test
	public void parseMessagesUsingDelegateFixedWidthParser() {
		ScriptEngineManager scriptEngineManager = new ScriptEngineManager();

		try(InputStream inputStream = TarTest.getResourceAsStreaz(this.getClass(), "fixed_width_test_header_detection.txt")){
			PythonCustomParser pythonParser = new PythonCustomParser(getCustomParserCode("custom_delegate_parser.py"),
					scriptEngineManager.getEngineByName("python"),
					Collections.emptyMap(), Map.of("field.lengths", "8,16,16,12,14,16,7", "padding.character", " ",
					"line.separator.detection.enabled", "true", "fixed.width.schema.detection.mode", "header"));

			pythonParser.options(Map.of("delegate", "fixed-width"));

			StreamEx<Optional<NexlaMessage>> result = pythonParser.parseMessages(inputStream);

			List<Map<String, Object>> actual = result
					.filter(Optional::isPresent)
					.map(e -> e.get().getRawMessage())
					.collect(Collectors.toList());

			List<Map<String, Object>> expectedData = new ArrayList<>();
			
			Map<String, Object> map1 = new HashMap<>();
			map1.put("Account", 101);
			map1.put("LastName", "Reeves");
			map1.put("FirstName", "Keanu");
			map1.put("Balance", 9315.45);
			map1.put("CreditLimit", 10000.00);
			map1.put("AccountCreated", "1/17/1998");
			map1.put("Rating", "A");
			expectedData.add(map1);

			Map<String, Object> map2 = new HashMap<>();
			map2.put("Account", 312);
			map2.put("LastName", "Butler");
			map2.put("FirstName", "Gerard");
			map2.put("Balance", 90.00);
			map2.put("CreditLimit", 1000.00);
			map2.put("AccountCreated", "8/6/2003");
			map2.put("Rating", "B");
			expectedData.add(map2);

			Assert.assertEquals(expectedData, actual);
		} catch (IOException e) {
			Assert.fail("Error reading test resources");
		}
	}

	@Test
	public void parseMessagesUsingCipherInputStream() {
		ScriptEngineManager scriptEngineManager = new ScriptEngineManager();

		try(InputStream inputStream = TarTest.getResourceAsStreaz(this.getClass(), "aes_encrypted_data")){
			PythonCustomParser pythonParser = new PythonCustomParser(getCustomParserCode("aes_custom_parser_cipher_is.py"),
				scriptEngineManager.getEngineByName("python"),
				Collections.emptyMap(), Map.of("password", "super-secret-password", "salt", "super-random-salt",
					"algorithm", "AES", "mode", "ECB", "padding", "PKCS5PADDING"));

			StreamEx<Optional<NexlaMessage>> result = pythonParser.parseMessages(inputStream);

			List<Optional<NexlaMessage>> actual = result.collect(Collectors.toList());
			List<Optional<NexlaMessage>> expectedData = getExpectedData("aes_decrypted_data.json");
			Assert.assertEquals(expectedData, actual);
		} catch (IOException e) {
			Assert.fail("Error reading test resources");
		}
	}
}
