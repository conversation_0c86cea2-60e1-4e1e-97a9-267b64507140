package com.nexla.parser;

import com.nexla.common.NexlaMessage;
import com.nexla.test.UnitTests;
import junitparams.JUnitParamsRunner;
import junitparams.Parameters;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import org.apache.kafka.common.config.ConfigException;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;

import java.io.InputStream;
import java.nio.file.FileSystems;
import java.time.LocalDate;
import java.util.*;

import static com.nexla.common.NexlaConstants.DATASET_CUSTOM_NAME;
import static com.nexla.common.StreamUtils.map;
import static com.nexla.common.parse.ParserConfigs.Excel.*;
import static com.nexla.parser.ExcelParser.*;
import static java.util.Arrays.asList;
import static java.util.stream.Collectors.toList;
import static org.assertj.core.api.Java6Assertions.assertThat;
import static org.junit.Assert.assertEquals;

@RunWith(JUnitParamsRunner.class)
@Category(UnitTests.class)
public class ExcelParserTest {

	private final ExcelParser excelParser = new ExcelParser();

	private String getFileName(final String name, final boolean isTransposedTest){
		return isTransposedTest
				? "excel_transposed" + FileSystems.getDefault().getSeparator() + name
				: name;
	}

	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void withHeader(boolean isTransposed, boolean useStreamed) throws Exception {
		String fileName = getFileName("test.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "Sheet1")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("number", "1");
			map.put("weight", "100");
			map.put("description", "pig");
			testList.add(map);

			map = new HashMap<>();
			map.put("number", "2");
			map.put("weight", "200");
			map.put("description", "cow");
			testList.add(map);

			map = new HashMap<>();
			map.put("number", "3");
			map.put("weight", "150");
			map.put("description", "dog");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@Test
	@Parameters({"true", "false"})
	public void withAdditionalRangeSingleSheet(boolean useStreamed) throws Exception {
		String fileName = getFileName("test_range.xlsx", false);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					//	A1:B2 refers to the first two cells in the top two rows of the first visible sheet.
					.option(RANGES, "A1:B2")
					// C3:A2|C4:A4 will create two KV pairs based on according cell values (C3 -> A2, C4 -> A4) and inject them to original records
					.option(ADDITIONAL_EXCEL_RANGE, "C3:A2|C4:A4")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

//			messages.forEach(msg -> System.out.println(">>>" + msg.getRawMessage()));

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("number", "1");
			map.put("weight", "100");
			map.put("cow", "1");
			map.put("dog", "3");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@Test
	@Parameters({"true", "false"})
	public void emptyValuesTest(boolean useStreamed) throws Exception {
		String fileName = getFileName("empty_values.xlsx", false);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "Sheet1")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(PRESERVE_EMPTY_STRINGS, "true")
					.option(PRESERVE_NULL_STRINGS, "true")
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("col1", "1");
			map.put("col2", "hello");
			map.put("col3", "");
			map.put("col4", "bye");
			testList.add(map);

			map = new HashMap<>();
			map.put("col1", "2");
			map.put("col2", "test");
			map.put("col3", "no test");
			map.put("col4", "null");
			testList.add(map);

			map = new HashMap<>();
			map.put("col1", "3");
			map.put("col2", "");
			map.put("col3", "more");
			map.put("col4", "less");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@Test
	@Parameters({"true", "false"})
	public void emptyAndNullValuesTestCombined(boolean useStreamed) throws Exception {
		String fileName = getFileName("empty_nulls_combined.xlsx", false);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(PRESERVE_EMPTY_STRINGS, "true")
					.option(PRESERVE_NULL_STRINGS, "true")
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("Name", "Check regular row");
			map.put("Comments", "A comment");
			map.put("Extra field", "100");
			testList.add(map);

			map = new HashMap<>();
			map.put("Name", "Check nulls");
			map.put("Comments", "null");
			map.put("Extra field", "null");
			testList.add(map);

			map = new HashMap<>();
			map.put("Name", "Check null + empty");
			map.put("Comments", "null");
			map.put("Extra field", "");
			testList.add(map);

			map = new HashMap<>();
			map.put("Name", "Check empty values");
			map.put("Comments", "Some comment");
			map.put("Extra field", "");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@Test
	@Parameters({"true", "false"})
	public void withAdditionalRangeNoAttributeNames(boolean useStreamed) throws Exception {
		String fileName = getFileName("test_range.xlsx", false);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					//	A1:B2 refers to the first two cells in the top two rows of the first visible sheet.
					.option(RANGES, "A1:B2")
					// will generate attribute names for A2 and A4 cells, will treat C4:A4 as normal KV pair
					.option(ADDITIONAL_EXCEL_RANGE, "A2|A4|C4:A4")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("number", "1");
			map.put("weight", "100");
			map.put("attribute1", "1");
			map.put("attribute2", "3");
			map.put("dog", "3");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@Test
	@Parameters({"true", "false"})
	public void withAdditionalRangeMultiSheet(boolean useStreamed) throws Exception {
		String fileName = getFileName("test_range.xlsx", false);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					//	A1:B2 refers to the first two cells in the top two rows of the first visible sheet.
					.option(RANGES, "A1:B2")
					// will fetch KV Pair (C4 -> A4) from Sheet1, generate attribute name for A3, and fetch C4 -> A4 pair from Sheet2.
					.option(ADDITIONAL_EXCEL_RANGE, "Sheet1_C4:A4|A3,Sheet2_C4:A4")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("number", "1");
			map.put("weight", "100");
			map.put("attribute1", "2");
			map.put("dog2", "6");
			map.put("dog", "3");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}


	@Test
	@Parameters({"true", "false"})
	public void dateFormatParsing(boolean useStreamed) throws Exception {
		String fileName = getFileName("SerialDates.xlsx", false);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					//	Sheet1!A:C refers to the all the cells in the first two rows of Sheet1.
					.option(RANGES, "Manual Entries!A:C")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new LinkedHashMap<>();
			map.put("CarrierShortName", "GERBR");
			map.put("StatementDate", "01172024");
			map.put("PolicyNumber", "DummyPolicy");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@Test
	@Parameters({"true", "false"})
	public void withAdditionalRangeEmptyCell(boolean useStreamed) throws Exception {
		String fileName = getFileName("test_range.xlsx", false);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					//	A1:B2 refers to the first two cells in the top two rows of the first visible sheet.
					.option(RANGES, "A1:B2")
					// since C3 cell value is empty, will generate attribute name for A2 value, also will generate it for C2 cell value
					.option(ADDITIONAL_EXCEL_RANGE, "Sheet2_C3:A2|C2")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("number", "1");
			map.put("weight", "100");
			map.put("attribute1", "4");
			map.put("attribute2", "pig2");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@Test
	@Parameters({"true", "false"})
	public void incorrectAdditionalRangeCells(boolean useStreamed) throws Exception {
		String fileName = getFileName("test_range.xlsx", false);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "A1:B2")
					// here we use | instead of , before Sheet2_A1 and make this address invalid
					.option(ADDITIONAL_EXCEL_RANGE, "Sheet1_C1:C2|Sheet1_A1:A3,Sheet2_B1:B3")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			// Sheet1_A1 is not the right cell name, so we have attribute1 instead of it
			map.put("attribute1", "2");
			map.put("description", "pig");
			map.put("number", "1");
			map.put("weight", "300");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void withSheetRange1(boolean isTransposed, boolean useStreamed) throws Exception {
		String fileName = getFileName("test_range.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					//	A1:B2 refers to the first two cells in the top two rows of the first visible sheet.
					.option(RANGES, "A1:B2")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("number", "1");
			map.put("weight", "100");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void withSheetRange2(boolean isTransposed, boolean useStreamed) throws Exception {
		String fileName = getFileName("test_range.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					//	Sheet1!1:2 refers to the all the cells in the first two rows of Sheet1.
					.option(RANGES, isTransposed ? "Sheet2!A:C" : "Sheet2!1:3")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("number", "4");
			map.put("weight", "null");
			map.put("description", "pig2");
			testList.add(map);

			map = new HashMap<>();
			map.put("number", "null");
			map.put("weight", "300");
			map.put("description", "null");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void generatedHeader(boolean isTransposed, boolean useStreamed) throws Exception {
		String fileName = getFileName("test.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "Sheet1")
					.option(EXCEL_SCHEMA_DETECTION, GENERATED)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();

			Map<String, Object> map = new HashMap<>();
			map.put("attribute1", "number");
			map.put("attribute2", "weight");
			map.put("attribute3", "description");
			testList.add(map);

			map = new HashMap<>();
			map.put("attribute1", "1");
			map.put("attribute2", "100");
			map.put("attribute3", "pig");
			testList.add(map);

			map = new HashMap<>();
			map.put("attribute1", "2");
			map.put("attribute2", "200");
			map.put("attribute3", "cow");
			testList.add(map);

			map = new HashMap<>();
			map.put("attribute1", "3");
			map.put("attribute2", "150");
			map.put("attribute3", "dog");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void configuredHeader(boolean isTransposed, boolean useStreamed) throws Exception {
		String fileName = getFileName("test.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "Sheet1")
					.option(EXCEL_SCHEMA, "header1,footer2,attribute3")
					.option(EXCEL_SCHEMA_DETECTION, CONFIGURED)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();

			Map<String, Object> map = new HashMap<>();
			map.put("header1", "number");
			map.put("footer2", "weight");
			map.put("attribute3", "description");
			testList.add(map);

			map = new HashMap<>();
			map.put("header1", "1");
			map.put("footer2", "100");
			map.put("attribute3", "pig");
			testList.add(map);

			map = new HashMap<>();
			map.put("header1", "2");
			map.put("footer2", "200");
			map.put("attribute3", "cow");
			testList.add(map);

			map = new HashMap<>();
			map.put("header1", "3");
			map.put("footer2", "150");
			map.put("attribute3", "dog");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@Test
	@Parameters({"false, false", "false, true"})
	// FIXME: fix failure on JPEG media when transposing, transposed cases are temporarily removed
	public void realLifeData(boolean isTransposed, boolean useStreamed) throws Exception {
		String fileName = getFileName("realtest.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "Location List")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("Installation", "14000");
			map.put("Marketing Description", "This spectacular wall in downtown Miami is highly visible to commuters" +
					" heading North on I-95 expressway- the main interstate and busiest highway" +
					" in South Florida. Motorists can\'t miss this unit as they travel" +
					" northbound from affluent neighborhoods in Coral Gables, Coconut Grove, " +
					"Brickell, Key Biscayne and the Financial District. Nearby hip & trendy " +
					"areas such as Mary Brickell Village and the Art and Cultural District are " +
					"popular spots for shopping, dining and night life.");
			map.put("Location Description", "I-95 @ SW 2nd Avenue F/SW");
			map.put("Copy Size", "40\'x165\'");
			map.put("Inventory #", "MI-7056W");
			map.put("IMP 18+ Weekly", "593452");
			map.put("Media", "Wallscape");
			map.put("Period", "4 Week");
			map.put("Duration", "4 Weeks");
			map.put("Latitude", "25.77339");
			map.put("TAB Panel ID", "30609908");
			map.put("Longitude", "-80.19744");
			map.put("D.E.C.", "100");
			map.put("Net Amount per Period", "50000");
			map.put("Start Date", "4/3/2017");
			map.put("Map #", "1");
			map.put("Production", "7900");
			map.put("Current Copy", "Swire Properties Inc");
			map.put("Illumination", "12 HRS");
			map.put("IMP 18+ 4 Week", "2373808");
			map.put("Rate Card Value", "80000");

			map = escapeMap(map);

			testList.add(map);

			map = new HashMap<>();
			map.put("Installation", "9000");
			map.put("Marketing Description", "This spectacular wall mural is visible to commuters heading South on the" +
					" I-95 expressway, the busiest highway in South Florida. The location of " +
					"this wall is perfect for advertisers looking to target locals heading " +
					"towards the most affluent neighborhoods in Miami such as Brickell," +
					" Coconut Grove, Coral Gables and Key Biscayne.");
			map.put("Location Description", "I-95 @ NW 2nd Street F/N  Note: March Promo Rate Location $30,000 with " +
					"production & installation 4-Weeks");
			map.put("Copy Size", "30\'x65\'");
			map.put("Inventory #", "MI-7187W");
			map.put("IMP 18+ Weekly", "753928");
			map.put("Media", "Wallscape");
			map.put("Period", "4 Week");
			map.put("Duration", "8 Weeks");
			map.put("Latitude", "25.776389");
			map.put("TAB Panel ID", "30716219");
			map.put("Longitude", "-80.199033");
			map.put("D.E.C.", "105.88");
			map.put("Net Amount per Period", "45000");
			map.put("Start Date", "3/6/2017");
			map.put("Map #", "2");
			map.put("Production", "2500");
			map.put("Current Copy", "Shire");
			map.put("Illumination", "18 HRS");
			map.put("IMP 18+ 4 Week", "3015712");
			map.put("Rate Card Value", "55000");

			map = escapeMap(map);

			testList.add(map);

			List<HashMap<String, Object>> result = messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList());
			assertEquals(testList, result);
		}
	}

	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void withFormula_numericFormula(boolean isTransposed, boolean useStreamed) throws Exception {
		String fileName = getFileName("different_formulas.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<HashMap<String, Object>> result = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "NumericFormula")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is)
					.map(Optional::orElseThrow)
					.map(m -> new HashMap<>(m.getRawMessage()))
					.collect(toList());

			List<Map<String, Object>> expectedResult = new ArrayList<>();
			Map<String, Object> column1 = new HashMap<>();
			column1.put("Value", "7999");
			column1.put("Minus499", "7500");

			Map<String, Object> column2 = new HashMap<>();
			column2.put("Value", "8999");
			column2.put("Minus499", "8500");

			expectedResult.add(escapeMap(column1));
			expectedResult.add(escapeMap(column2));
			assertEquals(expectedResult, result);
		}
	}

	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void withFormula_stringFormula(boolean isTransposed, boolean useStreamed) throws Exception {
		String fileName = getFileName("different_formulas.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<HashMap<String, Object>> result = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "StringFormula")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is)
					.map(Optional::orElseThrow)
					.map(m -> new HashMap<>(m.getRawMessage()))
					.collect(toList());

			List<Map<String, Object>> expectedResult = new ArrayList<>();
			Map<String, Object> column1 = new HashMap<>();
			column1.put("Value", "capital");
			column1.put("UpperCase", "CAPITAL");
			column1.put("SelfConcat", "capitalcapital");

			Map<String, Object> column2 = new HashMap<>();
			column2.put("Value", "sNaKe");
			column2.put("UpperCase", "SNAKE");
			column2.put("SelfConcat", "sNaKesNaKe");

			expectedResult.add(escapeMap(column1));
			expectedResult.add(escapeMap(column2));
			assertEquals(expectedResult, result);
		}
	}

	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void withFormula_booleanFormula(boolean isTransposed, boolean useStreamed) throws Exception {
		String fileName = getFileName("different_formulas.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<HashMap<String, Object>> result = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "BooleanFormula")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is)
					.map(Optional::orElseThrow)
					.map(m -> new HashMap<>(m.getRawMessage()))
					.collect(toList());

			List<Map<String, Object>> expectedResult = new ArrayList<>();
			Map<String, Object> column1 = new HashMap<>();
			column1.put("Value", "20");
			column1.put("IsEven", "true");

			Map<String, Object> column2 = new HashMap<>();
			column2.put("Value", "21");
			column2.put("IsEven", "false");

			expectedResult.add(escapeMap(column1));
			expectedResult.add(escapeMap(column2));
			assertEquals(expectedResult, result);
		}
	}

	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void withFormula_errorFormula(boolean isTransposed, boolean useStreamed) throws Exception {
		String fileName = getFileName("different_formulas.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<HashMap<String, Object>> result = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "ErrorFormula")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is)
					.map(Optional::orElseThrow)
					.map(m -> new HashMap<>(m.getRawMessage()))
					.collect(toList());

			List<Map<String, Object>> expectedResult = new ArrayList<>();
			Map<String, Object> column1 = new HashMap<>();
			column1.put("Value", "20");
			column1.put("DivideBy0", "null");

			Map<String, Object> column2 = new HashMap<>();
			column2.put("Value", "-20");
			column2.put("DivideBy0", "null");

			expectedResult.add(escapeMap(column1));
			expectedResult.add(escapeMap(column2));
			assertEquals(expectedResult, result);
		}
	}

	@Test
	@Parameters({"true", "false"})
	@Ignore // TODO fix: broken in 3.2.0
	public void withFormula_clientFormula_withLinkToOtherTables_withDates(boolean useStreamed) throws Exception {
		String fileName = getFileName("customer_data_with_multi_page_formulas_and_dates.xlsx", false);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<Map<String, Object>> result = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "LocProfile")
					.option(EXCEL_SCHEMA_DETECTION,  HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(false))
					.parseMessages(is)
					.map(Optional::orElseThrow)
					.map(m -> new HashMap<>(m.getRawMessage()))
					.collect(toList());

			Map<String, Object> genericResult = new HashMap<>();
			genericResult.put("LocID", "AL016");
			genericResult.put("LocOpenDate", "11/15/21");
			genericResult.put("LocAGM", "null");
			genericResult.put("LocStable", "Y");
			genericResult.put("LocOpen", "Y");

			// TODO: we are not getting this result, why
			Map<String, Object> emptyResult = new HashMap<>();
			emptyResult.put("LocID", "AL055");
			emptyResult.put("LocOpenDate", "null");
			emptyResult.put("LocAGM", "null");
			emptyResult.put("LocStable", "null");
			emptyResult.put("LocOpen", "null");

			Map<String, Object> locAgmResult = new HashMap<>();
			locAgmResult.put("LocID", "AR024");
			locAgmResult.put("LocOpenDate", "4/21/16");
			locAgmResult.put("LocAGM", "Y");
			locAgmResult.put("LocStable", "Y");
			locAgmResult.put("LocOpen", "Y");

			HashMap<String, Object> nonStableResult = new HashMap<>();
			nonStableResult.put("LocID", "FL302");
			nonStableResult.put("LocOpenDate", LocalDate.now().toString());
			nonStableResult.put("LocAGM", null);
			nonStableResult.put("LocStable", null);
			nonStableResult.put("LocOpen", "Y");

			ArrayList<Map<String, Object>> actualResult = new ArrayList<>();
			actualResult.add(genericResult);
			actualResult.add(emptyResult);
			actualResult.add(locAgmResult);

			assertThat(result)
					.hasSize(111)
					.containsAll(actualResult);
		}
	}

	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void generatedHeader_withFormula(boolean isTransposed, boolean useStreamed) throws Exception {
		String fileName = getFileName("different_formulas.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<HashMap<String, Object>> result = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "BooleanFormula")
					.option(EXCEL_SCHEMA_DETECTION, GENERATED)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is)
					.map(Optional::orElseThrow)
					.map(m -> new HashMap<>(m.getRawMessage()))
					.collect(toList());

			List<Map<String, Object>> expectedResult = new ArrayList<>();
			Map<String, Object> column1 = new HashMap<>();
			column1.put("attribute1", "Value");
			column1.put("attribute2", "IsEven");

			Map<String, Object> column2 = new HashMap<>();
			column2.put("attribute1", "20");
			column2.put("attribute2", "true");

			Map<String, Object> column3 = new HashMap<>();
			column3.put("attribute1", "21");
			column3.put("attribute2", "false");

			expectedResult.add(escapeMap(column1));
			expectedResult.add(escapeMap(column2));
			expectedResult.add(escapeMap(column3));
			assertEquals(expectedResult, result);
		}
	}

	@SneakyThrows
	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void mergedCellsTest(boolean isTransposed, boolean useStreamed) {
		String fileName = getFileName("merged_cells.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "RSN Posting Report")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is)
					.map(Optional::get).collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("Spot Duration", "30");
			map.put("ISCII Code", "XMMM18172");
			testList.add(map);

			map = new HashMap<>();
			map.put("Spot Duration", "30");
			map.put("ISCII Code", "XMMM18173");
			testList.add(map);

			map = new HashMap<>();
			map.put("Spot Duration", "30");
			map.put("ISCII Code", "XMMM18174");
			testList.add(map);

			map = new HashMap<>();
			map.put("Spot Duration", "30");
			map.put("ISCII Code", "XMMM18175");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@SneakyThrows
	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void xlsTest(boolean isTransposed, boolean useStreamed) {
		String fileName = getFileName("test.xls", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLS)
					.option(RANGES, "Sheet1")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is).map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("number", "1");
			map.put("weight", "100");
			map.put("description", "pig");
			testList.add(map);

			map = new HashMap<>();
			map.put("number", "2");
			map.put("weight", "200");
			map.put("description", "cow");
			testList.add(map);

			map = new HashMap<>();
			map.put("number", "3");
			map.put("weight", "150");
			map.put("description", "dog");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@SneakyThrows
	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void escapedDataTest(boolean isTransposed, boolean useStreamed) {
		String fileName = getFileName("escaped_test.xls", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLS)
					.option(RANGES, "Sheet1")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is).map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();
			Map<String, Object> map = new HashMap<>();
			map.put("number", "1");
			map.put("weight", "100");
			map.put("description", "pig,ping");
			testList.add(map);

			map = new HashMap<>();
			map.put("number", "2");
			map.put("weight", "200");
			map.put("description", "cow,white");
			testList.add(map);

			map = new HashMap<>();
			map.put("number", "3");
			map.put("weight", "150");
			map.put("description", "dog,yellow");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@SneakyThrows
	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void generatedHeaderTest_varyingHeaderLength(boolean isTransposed, boolean useStreamed) {
		String fileName = getFileName("generated_test.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "Sheet1")
					.option(EXCEL_SCHEMA_DETECTION, GENERATED)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = new ArrayList<>();

			Map<String, Object> map = new HashMap<>();
			map.put("attribute1", "val1");
			map.put("attribute2", "val2");
			map.put("attribute3", "val3");
			testList.add(map);

			map = new HashMap<>();
			map.put("attribute1", "val4");
			map.put("attribute2", "val5");
			map.put("attribute3", "val6");
			testList.add(map);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@SneakyThrows
	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void generatedHeaderTest_varyingHeaderLength_multiSheet(boolean isTransposed, boolean useStreamed) {
		String fileName = getFileName("generated_test.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(EXCEL_SCHEMA_DETECTION, GENERATED)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is)
					.map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = asList(
					// sheet1
					map(
							"attribute1", "val1",
							"attribute2", "val2",
							"attribute3", "val3"
					),
					map(
							"attribute1", "val4",
							"attribute2", "val5",
							"attribute3", "val6"
					),

					// sheet2
					map(
							"attribute1", "val1",
							"attribute2", "val2",
							"attribute3", "val3"
					),
					map(
							"attribute1", "val8",
							"attribute2", "val9",
							"attribute3", "val10"
					)
			);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));

			assertEquals(
					asList(
							map(
									SHEET_MESSAGE_NUMBER, "1",
									SHEET_NAME, "Sheet1",
									DATASET_CUSTOM_NAME, "[Sheet1]"
							),
							map(
									SHEET_MESSAGE_NUMBER, "2",
									SHEET_NAME, "Sheet1",
									DATASET_CUSTOM_NAME, "[Sheet1]"
							),
							map(
									SHEET_MESSAGE_NUMBER, "1",
									SHEET_NAME, "Sheet2",
									DATASET_CUSTOM_NAME, "[Sheet2]"
							),
							map(
									SHEET_MESSAGE_NUMBER, "2",
									SHEET_NAME, "Sheet2",
									DATASET_CUSTOM_NAME, "[Sheet2]"
							)
					),
					messages.stream().map(m -> {
						return m.getNexlaMetaData().getTags();
					}).collect(toList()));
		}
	}

	@Test
	@SneakyThrows
	@Parameters({"true", "false"})
	public void testDuplicateColumnsArePreservedAndRenamed(boolean useStreamed) {
		String fileName = getFileName("dupcol.xlsx", false);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "Sheet1")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(RENAME_DUPLICATE_HEADERS, "true")
					.option(EXCEL_TRANSPOSE, String.valueOf(false))
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.parseMessages(is).map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = asList(
					map(
							"testing.1", "a",
							"testing.2", "2.5",
							"testing", "1",
							"testing.3", "2024-05-02"
					),
					map(
							"testing.1", "b",
							"testing.2", "3.5",
							"testing", "2",
							"testing.3", "2024-05-03"
					),
					map(
							"testing.1", "c",
							"testing.2", "4.5",
							"testing", "3",
							"testing.3", "2024-05-04"
					)
			);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@SneakyThrows
	@Test
	@Parameters({"true, false", "true, true", "false, false", "false, true"})
	public void emptyColumnsTest(boolean isTransposed, boolean useStreamed) {
		String fileName = getFileName("excel_empty_cols.xlsx", isTransposed);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			List<NexlaMessage> messages = excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
					.option(RANGES, "Sheet1")
					.option(EXCEL_SCHEMA_DETECTION, HEADER)
					.option(EXCEL_STREAMED, String.valueOf(useStreamed))
					.option(EXCEL_TRANSPOSE, String.valueOf(isTransposed))
					.parseMessages(is).map(Optional::get)
					.collect(toList());

			List<Map<String, Object>> testList = asList(
					map(
							"Spot_Title", "null",
							"Product_Name", "Auto Foreign/Factory",
							"Spot_Length", "30",
							"ProgramName", "News-WTVH 5-7a News M-F"
					),
					map(
							"Spot_Title", "null",
							"Product_Name", "Auto Dealer Group",
							"Spot_Length", "30",
							"ProgramName", "null"
					),
					map(
							"Spot_Title", "null",
							"Product_Name", "Auto Dealer Group",
							"Spot_Length", "30",
							"ProgramName", "null"
					)
			);

			assertEquals(
					testList,
					messages.stream().map(m -> new HashMap<>(m.getRawMessage())).collect(toList()));
		}
	}

	@Test
	@SneakyThrows
	@Parameters({"true", "false"})
	public void testInvalidTruncatedSheetsParamValue(boolean useStreamed) {
		String fileName = getFileName("dupcol.xlsx", false);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			ConfigException exception = Assertions.assertThrows(ConfigException.class, () ->
					excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
							.option(RANGES, "sheet_with_more_than_thirty_one_characters,sheet_with_less_than_31_chars")
							.option(EXCEL_SCHEMA_DETECTION, HEADER)
							.option(RENAME_DUPLICATE_HEADERS, "true")
							.option(EXCEL_TRANSPOSE, String.valueOf(false))
							.option(EXCEL_STREAMED, String.valueOf(useStreamed))
							.parseMessages(is).map(Optional::get)
							.collect(toList()));

			assertEquals("Could not find Sheet(s) [sheet_with_more_than_thirty_one, sheet_with_less_than_31_chars] references in Source configuration property `sheets`. The available sheets are [Sheet1].",
					exception.getMessage());
		}
	}

	@Test
	@SneakyThrows
	@Parameters({"true", "false"})
	public void testInvalidTruncatedAdditionalExcelRangeParamValue(boolean useStreamed) {
		String fileName = getFileName("dupcol.xlsx", false);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			ConfigException exception = Assertions.assertThrows(ConfigException.class, () ->
					excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
							.option(RANGES, "Sheet1")
							.option(ADDITIONAL_EXCEL_RANGE, "sheet with more than thirty one characters_C4:A4|A3,sheet with less than 31 chars_C4:A4")
							.option(EXCEL_SCHEMA_DETECTION, HEADER)
							.option(RENAME_DUPLICATE_HEADERS, "true")
							.option(EXCEL_TRANSPOSE, String.valueOf(false))
							.option(EXCEL_STREAMED, String.valueOf(useStreamed))
							.parseMessages(is).map(Optional::get)
							.collect(toList()));

			assertEquals("Could not find Sheet(s) [sheet with more than thirty one, sheet with less than 31 chars] references in Source configuration property `sheets`. The available sheets are [Sheet1].",
					exception.getMessage());
		}
	}

	@Test
	@SneakyThrows
	@Parameters({"true", "false"})
	public void testDuplicatedSheetsParamValue(boolean useStreamed) {
		String fileName = getFileName("dupcol.xlsx", false);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			ConfigException exception = Assertions.assertThrows(ConfigException.class, () ->
					excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
							.option(RANGES, "sheet_with_more_than_thirty_one_characters,sheet_with_more_than_thirty_one_characters_two,sheet_name,sheet_name")
							.option(EXCEL_SCHEMA_DETECTION, HEADER)
							.option(RENAME_DUPLICATE_HEADERS, "true")
							.option(EXCEL_TRANSPOSE, String.valueOf(false))
							.option(EXCEL_STREAMED, String.valueOf(useStreamed))
							.parseMessages(is).map(Optional::get)
							.collect(toList()));

			assertEquals("Duplicated sheet name [sheet_with_more_than_thirty_one, sheet_with_more_than_thirty_one, sheet_name, sheet_name] provided in `sheets` parameter.",
					exception.getMessage());
		}
	}

	@Test
	@SneakyThrows
	@Parameters({"true", "false"})
	public void testDuplicatedAdditionalExcelRangeParamValue(boolean useStreamed) {
		String fileName = getFileName("dupcol.xlsx", false);

		try (InputStream is = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
			ConfigException exception = Assertions.assertThrows(ConfigException.class, () ->
					excelParser.option(EXCEL_TYPE, EXCEL_XLSX)
							.option(RANGES, "Sheet1")
							.option(ADDITIONAL_EXCEL_RANGE, "sheet with more than thirty one characters_C4:A4|A3,sheet with more than thirty one characters two_C4:A4|A3,sheet name_C4:A4|A3,sheet name_C4:A4|A3")
							.option(EXCEL_SCHEMA_DETECTION, HEADER)
							.option(RENAME_DUPLICATE_HEADERS, "true")
							.option(EXCEL_TRANSPOSE, String.valueOf(false))
							.option(EXCEL_STREAMED, String.valueOf(useStreamed))
							.parseMessages(is).map(Optional::get)
							.collect(toList()));

			assertEquals("Duplicated sheet name [sheet name, sheet name, sheet with more than thirty one, sheet with more than thirty one] provided in `excel.range.additional` parameter.",
					exception.getMessage());
		}
	}

	private Map<String, Object> escapeMap(Map<String, Object> map) {
		return EntryStream.of(map)
				.mapValues(value -> (Object) value.toString().trim().replace('\n', ' ')).toMap();
	}
}
