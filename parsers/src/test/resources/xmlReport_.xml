<Report xmlns="http://schema.intuit.com/finance/v3"><Header><Time>2019-01-10T00:37:43-08:00</Time><ReportName>AccountList</ReportName><Currency>USD</Currency><Option><Name>NoReportData</Name><Value>false</Value></Option></Header><Columns><Column><ColTitle>Account</ColTitle><ColType>account_name</ColType></Column><Column><ColTitle>Type</ColTitle><ColType>account_type</ColType></Column><Column><ColTitle>Detail type</ColTitle><ColType>detail_acc_type</ColType></Column><Column><ColTitle>Description</ColTitle><ColType>account_desc</ColType></Column><Column><ColTitle>Balance</ColTitle><ColType>account_bal</ColType></Column></Columns><Rows><Row type="Data"><ColData value="Checking"></ColData><ColData value="Bank"></ColData><ColData value="Checking"></ColData><ColData value=""></ColData><ColData value="1201.00"></ColData></Row><Row type="Data"><ColData value="Savings"></ColData><ColData value="Bank"></ColData><ColData value="Savings"></ColData><ColData value=""></ColData><ColData value="800.00"></ColData></Row><Row type="Data"><ColData value="Accounts Receivable (A/R)"></ColData><ColData value="Accounts receivable (A/R)"></ColData><ColData value="Accounts Receivable (A/R)"></ColData><ColData value=""></ColData><ColData value="5281.52"></ColData></Row><Row type="Data"><ColData value="Inventory Asset"></ColData><ColData value="Other Current Assets"></ColData><ColData value="Inventory"></ColData><ColData value=""></ColData><ColData value="596.25"></ColData></Row><Row type="Data"><ColData value="Prepaid Expenses"></ColData><ColData value="Other Current Assets"></ColData><ColData value="Prepaid Expenses"></ColData><ColData value=""></ColData><ColData value=".00"></ColData></Row><Row type="Data"><ColData value="Uncategorized Asset"></ColData><ColData value="Other Current Assets"></ColData><ColData value="Other Current Assets"></ColData><ColData value=""></ColData><ColData value=".00"></ColData></Row><Row type="Data"><ColData value="Undeposited Funds"></ColData><ColData value="Other Current Assets"></ColData><ColData value="Undeposited Funds"></ColData><ColData value=""></ColData><ColData value="2062.52"></ColData></Row><Row type="Data"><ColData value="Truck"></ColData><ColData value="Fixed Assets"></ColData><ColData value="Vehicles"></ColData><ColData value=""></ColData><ColData value="13495.00"></ColData></Row><Row type="Data"><ColData value="Truck:Depreciation"></ColData><ColData value="Fixed Assets"></ColData><ColData value="Accumulated Depreciation"></ColData><ColData value=""></ColData><ColData value=".00"></ColData></Row><Row type="Data"><ColData value="Truck:Original Cost"></ColData><ColData value="Fixed Assets"></ColData><ColData value="Vehicles"></ColData><ColData value=""></ColData><ColData value="13495.00"></ColData></Row><Row type="Data"><ColData value="Accounts Payable (A/P)"></ColData><ColData value="Accounts payable (A/P)"></ColData><ColData value="Accounts Payable (A/P)"></ColData><ColData value=""></ColData><ColData value="-1602.67"></ColData></Row><Row type="Data"><ColData value="Mastercard"></ColData><ColData value="Credit Card"></ColData><ColData value="Credit Card"></ColData><ColData value=""></ColData><ColData value="-157.72"></ColData></Row><Row type="Data"><ColData value="Visa"></ColData><ColData value="Credit Card"></ColData><ColData value="Credit Card"></ColData><ColData value=""></ColData><ColData value=".00"></ColData></Row><Row type="Data"><ColData value="Arizona Dept. of Revenue Payable"></ColData><ColData value="Other Current Liabilities"></ColData><ColData value="Sales Tax Payable"></ColData><ColData value=""></ColData><ColData value=".00"></ColData></Row><Row type="Data"><ColData value="Board of Equalization Payable"></ColData><ColData value="Other Current Liabilities"></ColData><ColData value="Sales Tax Payable"></ColData><ColData value=""></ColData><ColData value="-370.94"></ColData></Row><Row type="Data"><ColData value="Loan Payable"></ColData><ColData value="Other Current Liabilities"></ColData><ColData value="Other Current Liabilities"></ColData><ColData value=""></ColData><ColData value="-4000.00"></ColData></Row><Row type="Data"><ColData value="Notes Payable"></ColData><ColData value="Long Term Liabilities"></ColData><ColData value="Other Long Term Liabilities"></ColData><ColData value=""></ColData><ColData value="-25000.00"></ColData></Row><Row type="Data"><ColData value="Opening Balance Equity"></ColData><ColData value="Equity"></ColData><ColData value="Opening Balance Equity"></ColData><ColData value=""></ColData><ColData value="9337.50"></ColData></Row><Row type="Data"><ColData value="Retained Earnings"></ColData><ColData value="Equity"></ColData><ColData value="Retained Earnings"></ColData><ColData value=""></ColData><ColData value=".00"></ColData></Row><Row type="Data"><ColData value="Billable Expense Income"></ColData><ColData value="Income"></ColData><ColData value="Service/Fee Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Design income"></ColData><ColData value="Income"></ColData><ColData value="Other Primary Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Discounts given"></ColData><ColData value="Income"></ColData><ColData value="Discounts/Refunds Given"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Fees Billed"></ColData><ColData value="Income"></ColData><ColData value="Service/Fee Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Landscaping Services"></ColData><ColData value="Income"></ColData><ColData value="Other Primary Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Landscaping Services:Job Materials"></ColData><ColData value="Income"></ColData><ColData value="Other Primary Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Landscaping Services:Job Materials:Decks and Patios"></ColData><ColData value="Income"></ColData><ColData value="Other Primary Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Landscaping Services:Job Materials:Fountains and Garden Lighting"></ColData><ColData value="Income"></ColData><ColData value="Other Primary Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Landscaping Services:Job Materials:Plants and Soil"></ColData><ColData value="Income"></ColData><ColData value="Other Primary Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Landscaping Services:Job Materials:Sprinklers and Drip Systems"></ColData><ColData value="Income"></ColData><ColData value="Other Primary Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Landscaping Services:Labor"></ColData><ColData value="Income"></ColData><ColData value="Other Primary Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Landscaping Services:Labor:Installation"></ColData><ColData value="Income"></ColData><ColData value="Other Primary Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Landscaping Services:Labor:Maintenance and Repair"></ColData><ColData value="Income"></ColData><ColData value="Other Primary Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Other Income"></ColData><ColData value="Income"></ColData><ColData value="Other Primary Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Pest Control Services"></ColData><ColData value="Income"></ColData><ColData value="Other Primary Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Refunds-Allowances"></ColData><ColData value="Income"></ColData><ColData value="Discounts/Refunds Given"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Sales of Product Income"></ColData><ColData value="Income"></ColData><ColData value="Sales of Product Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Services"></ColData><ColData value="Income"></ColData><ColData value="Service/Fee Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Unapplied Cash Payment Income"></ColData><ColData value="Income"></ColData><ColData value="Unapplied Cash Payment Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Uncategorized Income"></ColData><ColData value="Income"></ColData><ColData value="Service/Fee Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Cost of Goods Sold"></ColData><ColData value="Cost of Goods Sold"></ColData><ColData value="Supplies &amp; Materials - COGS"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Advertising"></ColData><ColData value="Expenses"></ColData><ColData value="Advertising/Promotional"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Automobile"></ColData><ColData value="Expenses"></ColData><ColData value="Auto"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Automobile:Fuel"></ColData><ColData value="Expenses"></ColData><ColData value="Auto"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Bank Charges"></ColData><ColData value="Expenses"></ColData><ColData value="Bank Charges"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Commissions &amp; fees"></ColData><ColData value="Expenses"></ColData><ColData value="Other Miscellaneous Service Cost"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Disposal Fees"></ColData><ColData value="Expenses"></ColData><ColData value="Other Miscellaneous Service Cost"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Dues &amp; Subscriptions"></ColData><ColData value="Expenses"></ColData><ColData value="Dues &amp; subscriptions"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Equipment Rental"></ColData><ColData value="Expenses"></ColData><ColData value="Equipment Rental"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Insurance"></ColData><ColData value="Expenses"></ColData><ColData value="Insurance"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Insurance:Workers Compensation"></ColData><ColData value="Expenses"></ColData><ColData value="Insurance"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Job Expenses"></ColData><ColData value="Expenses"></ColData><ColData value="Other Miscellaneous Service Cost"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Job Expenses:Cost of Labor"></ColData><ColData value="Expenses"></ColData><ColData value="Other Miscellaneous Service Cost"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Job Expenses:Cost of Labor:Installation"></ColData><ColData value="Expenses"></ColData><ColData value="Other Miscellaneous Service Cost"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Job Expenses:Cost of Labor:Maintenance and Repairs"></ColData><ColData value="Expenses"></ColData><ColData value="Other Miscellaneous Service Cost"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Job Expenses:Equipment Rental"></ColData><ColData value="Expenses"></ColData><ColData value="Equipment Rental"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Job Expenses:Job Materials"></ColData><ColData value="Expenses"></ColData><ColData value="Supplies &amp; Materials"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Job Expenses:Job Materials:Decks and Patios"></ColData><ColData value="Expenses"></ColData><ColData value="Supplies &amp; Materials"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Job Expenses:Job Materials:Fountain and Garden Lighting"></ColData><ColData value="Expenses"></ColData><ColData value="Supplies &amp; Materials"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Job Expenses:Job Materials:Plants and Soil"></ColData><ColData value="Expenses"></ColData><ColData value="Supplies &amp; Materials"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Job Expenses:Job Materials:Sprinklers and Drip Systems"></ColData><ColData value="Expenses"></ColData><ColData value="Supplies &amp; Materials"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Job Expenses:Permits"></ColData><ColData value="Expenses"></ColData><ColData value="Other Miscellaneous Service Cost"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Legal &amp; Professional Fees"></ColData><ColData value="Expenses"></ColData><ColData value="Legal &amp; Professional Fees"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Legal &amp; Professional Fees:Accounting"></ColData><ColData value="Expenses"></ColData><ColData value="Legal &amp; Professional Fees"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Legal &amp; Professional Fees:Bookkeeper"></ColData><ColData value="Expenses"></ColData><ColData value="Legal &amp; Professional Fees"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Legal &amp; Professional Fees:Lawyer"></ColData><ColData value="Expenses"></ColData><ColData value="Legal &amp; Professional Fees"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Maintenance and Repair"></ColData><ColData value="Expenses"></ColData><ColData value="Repair &amp; Maintenance"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Maintenance and Repair:Building Repairs"></ColData><ColData value="Expenses"></ColData><ColData value="Repair &amp; Maintenance"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Maintenance and Repair:Computer Repairs"></ColData><ColData value="Expenses"></ColData><ColData value="Repair &amp; Maintenance"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Maintenance and Repair:Equipment Repairs"></ColData><ColData value="Expenses"></ColData><ColData value="Repair &amp; Maintenance"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Meals and Entertainment"></ColData><ColData value="Expenses"></ColData><ColData value="Entertainment Meals"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Office Expenses"></ColData><ColData value="Expenses"></ColData><ColData value="Office/General Administrative Expenses"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Promotional"></ColData><ColData value="Expenses"></ColData><ColData value="Advertising/Promotional"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Purchases"></ColData><ColData value="Expenses"></ColData><ColData value="Supplies &amp; Materials"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Rent or Lease"></ColData><ColData value="Expenses"></ColData><ColData value="Rent or Lease of Buildings"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Stationery &amp; Printing"></ColData><ColData value="Expenses"></ColData><ColData value="Office/General Administrative Expenses"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Supplies"></ColData><ColData value="Expenses"></ColData><ColData value="Supplies &amp; Materials"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Taxes &amp; Licenses"></ColData><ColData value="Expenses"></ColData><ColData value="Taxes Paid"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Travel"></ColData><ColData value="Expenses"></ColData><ColData value="Travel"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Travel Meals"></ColData><ColData value="Expenses"></ColData><ColData value="Travel Meals"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Unapplied Cash Bill Payment Expense"></ColData><ColData value="Expenses"></ColData><ColData value="Unapplied Cash Bill Payment Expense"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Uncategorized Expense"></ColData><ColData value="Expenses"></ColData><ColData value="Other Miscellaneous Service Cost"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Utilities"></ColData><ColData value="Expenses"></ColData><ColData value="Utilities"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Utilities:Gas and Electric"></ColData><ColData value="Expenses"></ColData><ColData value="Utilities"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Utilities:Telephone"></ColData><ColData value="Expenses"></ColData><ColData value="Utilities"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Interest Earned"></ColData><ColData value="Other Income"></ColData><ColData value="Interest Earned"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Other Portfolio Income"></ColData><ColData value="Other Income"></ColData><ColData value="Other Miscellaneous Income"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Depreciation"></ColData><ColData value="Other Expense"></ColData><ColData value="Depreciation"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Miscellaneous"></ColData><ColData value="Other Expense"></ColData><ColData value="Other Miscellaneous Expense"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row><Row type="Data"><ColData value="Penalties &amp; Settlements"></ColData><ColData value="Other Expense"></ColData><ColData value="Penalties &amp; Settlements"></ColData><ColData value=""></ColData><ColData value=""></ColData></Row></Rows></Report>