{"Transaction": {"-CancelFlag": "false", "RetailStoreID": "1985", "OperatorID": "000401", "WorkstationID": 1, "SequenceNumber": 0, "BeginDateTime": "2020-09-16T12:49:00", "bbb:Transaction": {"bbb:TransactionTypeText": "Normal Sale", "bbb:NumberOfStrings": 22, "bbb:TransactionStatus": {}}, "RetailTransaction": {"Customer": {"Address": {}}, "LineItem": {"lineItem": [{}, {}, {"bbb:ZipCode": "00000"}, {"bbb:SecuredFunctionOverride": {"bbb:Function": "28", "bbb:AuthCode": "0103", "bbb:Status": {"bbb:ManagerIdPassword": "true", "bbb:ManagerKeyUsed": "true", "bbb:StatusValue": "05"}, "bbb:ManagerPassword": "00000", "bbb:ManagerId": "00000401"}}, {"bbb:SecuredFunctionOverride": {"bbb:Function": "28", "bbb:AuthCode": "0103", "bbb:Status": {"bbb:ManagerIdPassword": "true", "bbb:ManagerKeyUsed": "true", "bbb:StatusValue": "05"}, "bbb:ManagerPassword": "00000", "bbb:ManagerId": "00000401"}}, {"Sale": {"POSIdentity": [{"POSItemID": 88864349267, "-POSIDType": "UPC"}, {"POSItemID": 42754749, "-POSIDType": "SKU"}, {"POSItemID": 884, "-POSIDType": "POSDepartment"}], "Quantity": "3", "-ItemType": "00", "ExtendedAmount": "83.97", "bbb:Item": {"bbb:ItemDefinitionFlags": {"bbb:ItemDefinitionFlagsValue": "0000"}, "bbb:Reserved": "0100", "bbb:ItemTypeText": "RegularItem", "bbb:ItemType": "00", "bbb:ItemCost": 0, "bbb:ItemStatusFlags": {"bbb:ItemStatusFlagsValue": "000000", "bbb:PriceIsNegative": "0", "bbb:Scanned": "true"}}}, "-VoidFlag": "false", "-EntryMethod": "Scanned"}, {"Discount": {"bbb:Discount": {"bbb:ItemUPC": "88864349267", "bbb:ItemSKU": "000042754749"}}}, {"bbb:SpecialOrderDeposit": {}}, {"-VoidFlag": "false", "Tax": {"TaxableAmount": "83.97", "Amount": "7.35", "bbb:Tax": {"bbb:TaxA": "true"}}}, {"bbb:CreditCardAuth": {"bbb:KeySequence": 1449, "bbb:PS2000AuthResponse": "00", "bbb:EntryFlags": "01", "bbb:AuthSource": "H", "bbb:PaymentServiceIndicator": " ", "bbb:PS2000TransactionID": " ", "bbb:AuthCode": "OK3160", "bbb:MerchantCatagory": "0000", "bbb:AuthSourceText": "HOST", "bbb:MSRName": " ", "bbb:JournalKey": "NOPCI", "bbb:PS2000ValidationCode": " ", "bbb:ExpiryDate": "1225"}}, {"bbb:CreditCardAuth": {"bbb:KeySequence": 1450, "bbb:PS2000AuthResponse": "00", "bbb:EntryFlags": "01", "bbb:AuthSource": "H", "bbb:PaymentServiceIndicator": " ", "bbb:PS2000TransactionID": " ", "bbb:AuthCode": "OK3160", "bbb:MerchantCatagory": "0000", "bbb:AuthSourceText": "HOST", "bbb:MSRName": " ", "bbb:JournalKey": "NOPCI", "bbb:PS2000ValidationCode": " ", "bbb:ExpiryDate": "1225"}}, {"bbb:CreditCardAuth": {"bbb:KeySequence": 1451, "bbb:PS2000AuthResponse": "00", "bbb:EntryFlags": "01", "bbb:AuthSource": "H", "bbb:PaymentServiceIndicator": " ", "bbb:PS2000TransactionID": " ", "bbb:AuthCode": "OK3210", "bbb:MerchantCatagory": "0000", "bbb:AuthSourceText": "HOST", "bbb:MSRName": " ", "bbb:JournalKey": "NOPCI", "bbb:PS2000ValidationCode": " ", "bbb:ExpiryDate": "1225"}}, {"Tender": {"-TyoeCode": "Sale", "CreditDebit": {"ExpirationDate": "2025-12", "PrimaryAccountNumber": "****************"}, "-SubTenderType": "bbb:PLCC", "Check": {"AccountNumber": "****************"}, "bbb:Tender": {"bbb:TenderWasNegative": "false"}, "TenderID": "69", "GiftCard": {"CardNumber": "****************"}, "-TenderType": "CreditDebit"}}, {"Tender": {"-TyoeCode": "Sale", "CreditDebit": {"ExpirationDate": "2025-12", "PrimaryAccountNumber": "****************"}, "-SubTenderType": "bbb:PLCC", "Check": {"AccountNumber": "****************"}, "bbb:Tender": {"bbb:TenderWasNegative": "false"}, "TenderID": "69", "GiftCard": {"CardNumber": "****************"}, "-TenderType": "CreditDebit"}}, {"bbb:Change": {}, "-VoidFlag": "false"}, {"bbb:RVN": "019854724001091620"}, {"bbb:CAD": {"bbb:RedLocation": {}, "bbb:CPNAuth": {}, "bbb:NormalizedTimeStamp": "2020-09-16T12:49:00", "bbb:OfflineFlag": {}, "bbb:CPNEntry": {}, "bbb:CPNType": {}}}, {"bbb:PrIVULoto": {}}]}, "Total": 91.32}, "ReceiptNumber": "4724"}}