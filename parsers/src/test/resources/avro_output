{datacenter={oneOf=[{type=string}, {type=null}]}, demand={oneOf=[{type=null}, {additionalProperties=false, properties={advertiser={oneOf=[{additionalProperties=false, properties={id={oneOf=[{maximum=**********, minimum=-**********, type=integer}, {type=null}]}}, required=[id], type=object}, {type=null}]}, auction_type={oneOf=[{type=string}, {type=null}]}, bid_request_type={oneOf=[{type=string}, {type=null}]}, bidder={additionalProperties=false, properties={advertiser_name={oneOf=[{type=string}, {type=null}]}, buyeruid={oneOf=[{type=string}, {type=null}]}, creative_id={oneOf=[{type=string}, {type=null}]}, deal_account_managers={oneOf=[{type=string}, {type=null}]}, deal_id={oneOf=[{type=string}, {type=null}]}, deal_kargo_built_creative={oneOf=[{type=string}, {type=null}]}, deal_name={oneOf=[{type=string}, {type=null}]}, deal_priority={oneOf=[{type=string}, {type=null}]}, deal_regions={oneOf=[{type=string}, {type=null}]}, deal_sales={oneOf=[{type=string}, {type=null}]}, deal_type={oneOf=[{type=string}, {type=null}]}, esync_flag={oneOf=[{type=string}, {type=null}]}, id={oneOf=[{maximum=**********, minimum=-**********, type=integer}, {type=null}]}, seat_id={oneOf=[{type=string}, {type=null}]}}, required=[id, seat_id, creative_id, deal_id, deal_name, deal_sales, deal_account_managers, deal_regions, deal_kargo_built_creative, deal_type, deal_priority, buyeruid, esync_flag, advertiser_name], type=object}, campaign={additionalProperties=false, properties={id={maximum=**********, minimum=-**********, type=integer}, type={type=string}}, required=[id, type], type=object}, creative={additionalProperties=false, properties={ad_execution={oneOf=[{maximum=**********, minimum=-**********, type=integer}, {type=null}]}, can_track_clicks={type=boolean}, id={oneOf=[{maximum=**********, minimum=-**********, type=integer}, {type=null}]}, size={oneOf=[{type=string}, {type=null}]}, type={type=string}}, required=[id, type, size, can_track_clicks, ad_execution], type=object}, demand_type={oneOf=[{type=string}, {type=null}]}, landing_page_domain={oneOf=[{type=string}, {type=null}]}, line_item={additionalProperties=false, properties={id={maximum=**********, minimum=-**********, type=integer}, pricing_type={type=string}}, required=[id, pricing_type], type=object}, loads={oneOf=[{additionalProperties=false, properties={lload_campaign_imp_9h={type=number}, lload_click_7d={type=number}, lload_creative_imp_5m={type=number}}, required=[lload_creative_imp_5m, lload_campaign_imp_9h, lload_click_7d], type=object}, {type=null}]}}, required=[advertiser, campaign, line_item, creative, bidder, demand_type, auction_type, bid_request_type, landing_page_domain, loads], type=object}]}, details={additionalProperties=false, properties={request_ad_executions={oneOf=[{items={maximum=**********, minimum=-**********, type=integer}, type=array}, {type=null}]}, request_deals={oneOf=[{items={type=string}, type=array}, {type=null}]}, request_maxsize={oneOf=[{type=string}, {type=null}]}, request_minsize={oneOf=[{type=string}, {type=null}]}, request_sizes={items={type=string}, type=array}}, required=[request_sizes, request_ad_executions, request_deals, request_minsize, request_maxsize], type=object}, impression_id={type=string}, is_secure_request={type=boolean}, metadata={additionalProperties=false, properties={compression_ratio={oneOf=[{type=number}, {type=null}]}, coroutine_uuid={oneOf=[{type=string}, {type=null}]}, hostname={oneOf=[{type=string}, {type=null}]}}, required=[compression_ratio, coroutine_uuid, hostname], type=object}, request={additionalProperties=false, properties={datetime={type=string}, host={type=string}, ip={type=string}, referer={type=string}, request_uri={type=string}, uri={type=string}, useragent={type=string}}, required=[datetime, ip, host, uri, request_uri, referer, useragent], type=object}, request_deal_account_managers={oneOf=[{items={type=string}, type=array}, {type=null}]}, request_deal_ad_execution={oneOf=[{items={type=string}, type=array}, {type=null}]}, request_deal_ids={oneOf=[{items={type=string}, type=array}]}, request_deal_is_kargo_built_creative={oneOf=[{items={type=string}, type=array}, {type=null}]}, request_deal_names={oneOf=[{items={type=string}, type=array}]}, request_deal_priority={oneOf=[{items={type=string}, type=array}, {type=null}]}, request_deal_regions={oneOf=[{items={type=string}, type=array}, {type=null}]}, request_deal_sales={oneOf=[{items={type=string}, type=array}, {type=null}]}, request_deal_type={oneOf=[{items={type=string}, type=array}, {type=null}]}, request_deal_wseats={oneOf=[{items={type=string}, type=array}, {type=null}]}, supply={additionalProperties=false, properties={adtag_version={oneOf=[{type=string}, {type=null}]}, inventory_type={type=string}, kraken_auction_type={oneOf=[{type=string}, {type=null}]}, krux_segment={oneOf=[{items={type=string}, type=array}, {type=null}]}, lotame_segment={oneOf=[{items={type=string}, type=array}, {type=null}]}, mas_segment={oneOf=[{items={type=string}, type=array}, {type=null}]}, publisher={additionalProperties=false, properties={id={maximum=**********, minimum=-**********, type=integer}}, required=[id], type=object}, section={additionalProperties=false, properties={category={oneOf=[{items={type=string}, type=array}, {type=null}]}, expdir={oneOf=[{items={maximum=**********, minimum=-**********, type=integer}, type=array}, {type=null}]}, id={maximum=**********, minimum=-**********, type=integer}, iframebuster={oneOf=[{items={type=string}, type=array}, {type=null}]}, interstitial={oneOf=[{type=boolean}, {type=null}]}, position={maximum=**********, minimum=-**********, type=integer}, total_budget={oneOf=[{type=number}, {type=null}]}, type={oneOf=[{type=null}, {type=string}]}}, required=[id, category, type, interstitial, iframebuster, expdir, position, total_budget], type=object}, site={additionalProperties=false, properties={category={oneOf=[{items={type=string}, type=array}]}, domain={oneOf=[{type=string}, {type=null}]}, grapeshot_channel={oneOf=[{items={type=string}, type=array}]}, id={maximum=**********, minimum=-**********, type=integer}, is_mobile_optimized={oneOf=[{type=boolean}, {type=null}]}, page={oneOf=[{type=string}, {type=null}]}}, required=[id, category, domain, page, is_mobile_optimized, grapeshot_channel], type=object}}, required=[lotame_segment, krux_segment, mas_segment, inventory_type, publisher, kraken_auction_type, site, section, adtag_version], type=object}, timestamp={maximum=**********, minimum=-**********, type=integer}, user={additionalProperties=false, properties={agent={additionalProperties=false, properties={browser={additionalProperties=false, properties={name={type=string}, version={type=string}}, required=[name, version], type=object}, device={additionalProperties=false, properties={maker={type=string}, model={type=string}, type={type=string}}, required=[type, maker, model], type=object}, os={additionalProperties=false, properties={name={type=string}, version={type=string}}, required=[name, version], type=object}}, required=[device, os, browser], type=object}, geo={additionalProperties=false, properties={city={type=string}, country={type=string}, country_region={type=string}, dma={maximum=**********, minimum=-**********, type=integer}, region={type=string}, zip={type=string}}, required=[country, region, city, dma, zip, country_region], type=object}, ip={type=string}, kargo_id={oneOf=[{type=string}, {type=null}]}, language={type=string}}, required=[kargo_id, ip, language, geo, agent], type=object}, uuid={oneOf=[{type=string}, {type=null}]}}