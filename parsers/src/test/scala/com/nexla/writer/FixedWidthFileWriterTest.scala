package com.nexla.writer

import com.nexla.common.parse.ParserConfigs.FixedWidth.{FIELD_LENGTHS, PADDING_CHARACTER, REMOVE_QUOTES_FORCED, WRITE_HEADER}
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._
import scala.io.Source

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class FixedWidthFileWriterTest extends AnyFlatSpecLike with Matchers {

  it should "write message to fw file" in {

    val message: Map[String, AnyRef] = Map(
      "header1" -> "value1",
      "header2" -> "value2"
    )

    val writer = NexlaFileWriter.WRITERS.get("fw").get()
      .option(FIELD_LENGTHS, "10,10")
      .option(PADDING_CHARACTER, " ")
      .option(WRITE_HEADER, "true")
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    Source.fromFile(writer.getOutputPath).getLines().mkString("\n") shouldBe
      "header1   header2   " +
        "\n\"value1\"  \"value2\"  "
  }

  it should "write message headers mode disabled" in {

    val message: Map[String, AnyRef] = Map(
      "header1" -> "value1",
      "header2" -> "value2"
    )

    val writer = NexlaFileWriter.WRITERS.get("fw").get()
      .option(FIELD_LENGTHS, "10,10")
      .option(PADDING_CHARACTER, " ")
      .option(WRITE_HEADER, "false")
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    Source.fromFile(writer.getOutputPath).getLines().mkString("\n") shouldBe
      "\"value1\"  \"value2\"  "
  }

  it should "write message with headers written only once" in {

    val message1: Map[String, AnyRef] = Map(
      "header1" -> "value1",
      "header2" -> "value2"
    )

    val message2: Map[String, AnyRef] = Map(
      "header1" -> "value3",
      "header2" -> "value4"
    )

    val writer = NexlaFileWriter.WRITERS.get("fw").get()
      .option(FIELD_LENGTHS, "10,10")
      .option(PADDING_CHARACTER, " ")
      .option(WRITE_HEADER, "true")
      .useTempFile()

    writer.append(message1.asJava)
    writer.append(message2.asJava)
    writer.close()

    Source.fromFile(writer.getOutputPath).getLines().mkString("\n") shouldBe
      "header1   header2   " +
        "\n\"value1\"  \"value2\"  " +
        "\n\"value3\"  \"value4\"  "
  }

  it should "convert non-strings" in {
    val message1: Map[String, AnyRef] = Map(
      "header1" -> "String value",
      "header2" -> Integer.valueOf(69420)
    )

    val writer = NexlaFileWriter.WRITERS.get("fw").get()
      .option(FIELD_LENGTHS, "15,7")
      .option(WRITE_HEADER, "true")
      .option(PADDING_CHARACTER, " ")
      .useTempFile()

    writer.append(message1.asJava)
    writer.close()

    Source.fromFile(writer.getOutputPath).getLines().mkString("\n") shouldBe
      "header1        header2" +
        "\n\"String value\" 69420  "
  }

}
