package com.nexla.writer

import java.nio.file.{Files, Paths}
import java.util
import com.bazaarvoice.jolt.JsonUtils
import com.bazaarvoice.jolt.JsonUtils._
import com.nexla.parser.{Av<PERSON><PERSON><PERSON><PERSON>, ParquetParser, TarTest}
import org.scalatest.TagAnnotation
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._
import scala.io.Source

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class AvroFileWriterTest extends AnyFunSuite with Matchers {

  val useDecimalAsDate = false

  test("successful write") {

    val dataToOutput = Map[String, AnyRef]("company" -> "Nexla", "city" -> "San Francisco")
    val writer = NexlaFileWriter.forExt("avro").useTempFile()
    writer.append(dataToOutput.asJava)
    writer.close()
    writer.finish()

    val inputStream = Files.newInputStream(Paths.get(writer.getOutputPath))
    val writtenData = new AvroParser().readLines(inputStream).iterator().asScala.map(jsonToMap(_).asScala).toList.head

    inputStream.close()
    writer.delete()

    writtenData shouldEqual dataToOutput
  }

  test("replace dots in schema") {

    val dataToOutput = Map[String, AnyRef]("comp.any" -> "Nexla", "city" -> "San Francisco")
    val writer = NexlaFileWriter.forExt("avro").useTempFile()
    writer.append(dataToOutput.asJava)
    writer.close()
    writer.finish()

    val inputStream = Files.newInputStream(Paths.get(writer.getOutputPath))
    val writtenData = new AvroParser().readLines(inputStream).iterator().asScala.map(jsonToMap(_).asScala).toList.head

    inputStream.close()
    writer.delete()

    writtenData shouldEqual Map[String, AnyRef]("comp_any" -> "Nexla", "city" -> "San Francisco")
  }

  test("test exclude empty and null fields") {

    val writer = NexlaFileWriter.forExt("parquet").useTempFile()

    val messageParsed = JsonUtils.jsonToMap(TestConstants.message).get("rawMessage").asInstanceOf[util.LinkedHashMap[String, AnyRef]]
    writer.append(messageParsed)

    writer.close()
    writer.finish()

    val inputStream = Files.newInputStream(Paths.get(writer.getOutputPath))
    val writtenData = new ParquetParser(useDecimalAsDate).readLines(inputStream).iterator().asScala.map(jsonToMap(_).asScala).toList.head

    inputStream.close()
    writer.delete()

    writtenData.size shouldEqual 12
  }

  test("test schema detection successful") {

    val writer = NexlaFileWriter.forExt("avro").useTempFile()

    val inputStream = TarTest.getResourceAsStreaz(this.getClass(), "test_data.json")

    Source.fromInputStream(inputStream).getLines()
      .map(line => JsonUtils.jsonToMap(line).get("rawMessage").asInstanceOf[util.LinkedHashMap[String, AnyRef]])
      .foreach(data => writer.append(data))

    inputStream.close()
    writer.close()
    writer.finish()

    val writterFileInputStream = Files.newInputStream(Paths.get(writer.getOutputPath))
    val writtenData = new AvroParser().readLines(writterFileInputStream).iterator().asScala.map(jsonToMap(_).asScala).toList

    writterFileInputStream.close()
    writer.delete()

    writtenData.size shouldEqual 100
  }

  test("successful write with reopening") {

    val dataToOutput = Map[String, AnyRef]("company" -> "Nexla", "city" -> "San Francisco")
    val writer = NexlaFileWriter.forExt("avro").useTempFile()
    writer.append(dataToOutput.asJava)
    writer.close()

    val dataToOutput2 = Map[String, AnyRef]("company" -> "Apple", "city" -> "Cupertino")
    writer.append(dataToOutput2.asJava)

    writer.finish()

    val inputStream = Files.newInputStream(Paths.get(writer.getOutputPath))
    val writtenData = new AvroParser().readLines(inputStream).iterator().asScala.map(jsonToMap(_).asScala).toList

    inputStream.close()
    writer.delete()

    writtenData shouldEqual List(dataToOutput, dataToOutput2)
  }
}
