package com.nexla.writer.orc

import java.io.FileInputStream
import java.util
import java.util.Arrays.asList
import com.nexla.common.StreamUtils.lhm
import com.nexla.parser.orc.OrcParser
import org.apache.orc.OrcConf._
import org.apache.orc.OrcProto.CompressionKind._
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class OrcFileWriterTest extends AnyFlatSpecLike with Matchers {

  it should "write orc file" in {

    val writer = new OrcFileWriter
    writer
      .options(Map(COMPRESS.getAttribute -> LZO.name().asInstanceOf[AnyRef]).asJava)
      .useTempFile()

    val element: util.LinkedHashMap[String, AnyRef] = lhm(
      "intValue", Int.box(1),
      "doubleValue", new java.math.BigDecimal("111.23"),
      "mapOfLists", lhm("engineer", asList(1L, 2L, 3L))
    )

    writer.append(element)
    writer.finish()

    writer.outputPath.endsWith(".lzo.orc") shouldBe true

    val parser = new OrcParser
    val inputStream = new FileInputStream(writer.outputPath)
    val result = parser.parseMessages(inputStream).iterator().asScala.toList.map(_.get().getRawMessage)
    inputStream.close()
    writer.delete()

    result.head.asScala.toMap shouldBe element.asScala.toMap
  }

}
