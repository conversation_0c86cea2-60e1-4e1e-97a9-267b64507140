package com.nexla.writer

import java.io.FileInputStream
import com.nexla.common.StreamUtils.lhm
import com.nexla.common.parse.ParserConfigs.Csv._
import com.nexla.common.parse.ParserConfigs.Excel.{EXCEL_TYPE, EXCEL_XLSX, RANGES}
import com.nexla.common.parse.ParserConfigs.Unstructured.{HEADER_DATE_FORMAT, HEADER_TEMPLATE}
import com.nexla.connector.config.MappingConfig
import com.nexla.parser.ExcelParser
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import java.util.Optional
import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class ExcelFileWriterTest extends AnyFlatSpecLike with Matchers {

  behavior of "ExcelFileWriter"

  it should "writeMessage" in {

    val template =
      """date,{now}
        |header,{header1}""".stripMargin

    val message: Map[String, AnyRef] = Map(
      "header1" -> "key1",
      "header2" -> "value 1"
    )

    val writer = new ExcelFileWriter()
      .option(WRITE_HEADER, "true")
      .option(HEADER_TEMPLATE, template)
      .option(HEADER_DATE_FORMAT, "yyyy-MM-dd")
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    writer.append(message.asJava)
    writer.finish()

    val output = new ExcelParser()
      .option(EXCEL_TYPE, EXCEL_XLSX)
      .option(RANGES, "Sheet1")
      .parseMessages(new FileInputStream(writer.getOutputPath))
      .toList

    output.size() shouldBe 2
    output.get(0).get().getRawMessage shouldBe lhm("header1", "key1", "header2", "value 1")
    output.get(1).get().getRawMessage shouldBe lhm("header1", "key1", "header2", "value 1")

    writer.close()

  }

  it should "writeMessage with provided mapping" in {

    val template =
      """date,{now}
        |header,{header1}""".stripMargin

    val message1: Map[String, AnyRef] = Map(
      "header1" -> "key1",
      "header2" -> "value1"
    )

    val message2: Map[String, AnyRef] = Map(
      "header0" -> "zero2",                 // To be skipped
      "header1" -> "key2",
//      "header2" -> "value2"               // To be null
    )

    val writer = new ExcelFileWriter()
      .option(WRITE_HEADER, "true")
      .option(HEADER_TEMPLATE, template)
      .option(HEADER_DATE_FORMAT, "yyyy-MM-dd")
      .useTempFile()

    val mappingConfig = new MappingConfig
    mappingConfig.getFieldsOrder.add("header2")
    mappingConfig.getFieldsOrder.add("header1")

    writer.append(message1.asJava, Optional.of(mappingConfig))
    writer.append(message2.asJava, Optional.of(mappingConfig))
    writer.finish()
    writer.close()

    val output = new ExcelParser()
      .option(EXCEL_TYPE, EXCEL_XLSX)
      .option(RANGES, "Sheet1")
      .parseMessages(new FileInputStream(writer.getOutputPath))
      .toList

    output.size() shouldBe 2
    output.get(0).get().getRawMessage shouldBe lhm( "header1", "key1", "header2", "value1")
    output.get(1).get().getRawMessage shouldBe lhm( "header1", "key2", "header2", "null")
  }


  it should "writeMessage without mapping" in {

    val template =
      """date,{now}
        |header,{header1}""".stripMargin

    val message1: Map[String, AnyRef] = Map(
      "header1" -> "key1",
      "header2" -> "value1"
    )

    val message2: Map[String, AnyRef] = Map(
      "header0" -> "zero2",     // To be skipped
      "header1" -> "key2"
//      "header2" -> "value2"   // To be null
    )

    val writer = new ExcelFileWriter()
      .option(WRITE_HEADER, "true")
      .option(HEADER_TEMPLATE, template)
      .option(HEADER_DATE_FORMAT, "yyyy-MM-dd")
      .useTempFile()

    writer.append(message1.asJava, Optional.empty())
    writer.append(message2.asJava, Optional.empty())
    writer.finish()
    writer.close()

    val output = new ExcelParser()
      .option(EXCEL_TYPE, EXCEL_XLSX)
      .option(RANGES, "Sheet1")
      .parseMessages(new FileInputStream(writer.getOutputPath))
      .toList

    output.size() shouldBe 2
    output.get(0).get().getRawMessage shouldBe lhm("header1", "key1", "header2", "value1")
    output.get(1).get().getRawMessage shouldBe lhm("header1", "key2", "header2", "null")
  }

}
