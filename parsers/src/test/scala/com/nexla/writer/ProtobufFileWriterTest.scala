package com.nexla.writer

import com.nexla.common.StreamUtils.lhm
import com.nexla.parser.pb.ProtobufParser
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import java.io.FileInputStream
import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class ProtobufFileWriterTest extends AnyFlatSpecLike with Matchers {
  behavior of "ProtobufFileWriter"

  it should "writeMessage" in {
    val writer = new ProtobufFileWriter()
      .useTempFile()

    val writtenData: java.util.Map[String, AnyRef] = lhm(
      "int", Int.box(1),
      "intArray", List(1, 2, 3).asJava,
      "long", Long.box(Long.MaxValue),
      "double", Double.box(0.000001d),
      "string", "string",
      "stringArray", List("A", "B", "C").asJava
    )

    writer.append(writtenData)

    writer.finish()

    val parsed = new ProtobufParser()
      .parseMessages(new FileInputStream(writer.getOutputPath))
      .toList

    parsed.size shouldBe 1

    val message = parsed.get(0)
      .orElseThrow()


    // TODO getRawMessage != writtenData because all int* values are converted to doubles.
    message.getRawMessage shouldBe lhm(
      "int", Double.box(1),
      "intArray", List(1d, 2d, 3d).asJava,
      "long", Double.box(Long.MaxValue),
      "double", Double.box(0.000001d),
      "string", "string",
      "stringArray", List("A", "B", "C").asJava
    )
  }

  it should "write2Messages" in {
    val writer = new ProtobufFileWriter()
      .useTempFile()

    writer.append(lhm(
      "int", Int.box(1),
      "intArray", List(1, 2, 3).asJava,
      "long", Long.box(Long.MaxValue),
      "double", Double.box(0.000001d),
      "string", "string",
      "stringArray", List("A", "B", "C").asJava
    ))

    writer.append(lhm(
      "int", Int.box(-1),
      "intArray", List(-1, -2, -3).asJava,
      "long", Long.box(Long.MinValue),
      "double", Double.box(-0.000001d),
      "string", "-string",
      "stringArray", List("C", "B", "A").asJava
    ))

    writer.finish()

    val parsed = new ProtobufParser()
      .parseMessages(new FileInputStream(writer.getOutputPath))
      .toList

    parsed.size shouldBe 2
    // TODO again, int* values are converted to doubles.
    parsed.get(0).get().getRawMessage shouldBe lhm(
      "int", Double.box(1),
      "intArray", List(1d, 2d, 3d).asJava,
      "long", Double.box(Long.MaxValue),
      "double", Double.box(0.000001d),
      "string", "string",
      "stringArray", List("A", "B", "C").asJava
    )

    parsed.get(1).get().getRawMessage shouldBe lhm(
      "int", Double.box(-1),
      "intArray", List(-1d, -2d, -3d).asJava,
      "long", Double.box(Long.MinValue),
      "double", Double.box(-0.000001d),
      "string", "-string",
      "stringArray", List("C", "B", "A").asJava
    )
  }
}
