package com.nexla.writer

import java.util
import com.nexla.common.parse.ParserConfigs.Csv._
import com.nexla.common.parse.ParserConfigs.Unstructured.{HEADER_DATE_FORMAT, HEADER_TEMPLATE}
import org.joda.time.DateTime
import org.joda.time.DateTimeZone.UTC
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._
import scala.io.Source

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class DelimitedFileWriterTest extends AnyFlatSpecLike with Matchers {

  behavior of "DelimitedFileWriter"

  it should "writeMessage csv" in {

    val message: Map[String, AnyRef] = Map(
      "header1" -> "key1",
      "header2" -> new util.LinkedHashMap[String, AnyRef](Map("a1" -> "value 1", "a2" -> "value2").asJava)
    )

    val writer = NexlaFileWriter.WRITERS.get("csv").get()
      .option(WRITE_HEADER, "true")
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    Source.fromFile(writer.getOutputPath).getLines().mkString("\n") shouldBe
      """header1,header2.a1,header2.a2
        |key1,"value 1",value2""".stripMargin
  }

  it should "writeMessage tsv" in {

    val message: Map[String, AnyRef] = Map(
      "header1" -> "key1",
      "header2" -> "value 1"
    )

    val writer = NexlaFileWriter.WRITERS.get("tsv").get()
      .option(WRITE_HEADER, "false")
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    Source.fromFile(writer.getOutputPath).getLines().mkString("\n") shouldBe "key1\tvalue 1"
  }

  it should "writeMessage with header" in {

    val message: Map[String, AnyRef] = Map(
      "header1" -> "key1",
      "header2" -> "value1"
    )

    val template =
      """date,{now}
        |header,{header1}""".stripMargin
    val writer = new DelimitedFileWriter() {
      override protected def getNow: DateTime = DateTime.parse("2018-04-11T12:11:22.333Z").withZone(UTC)
    }
      .option(CSV_DELIMITER, ",")
      .option(WRITE_HEADER, "false")
      .option(HEADER_TEMPLATE, template)
      .option(HEADER_DATE_FORMAT, "yyyy-MM")
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    Source.fromFile(writer.getOutputPath).getLines().mkString("\n") shouldBe
      """date,2018-04
        |header,key1
        |key1,value1""".stripMargin
  }

  it should "writeMessage with header and default date format" in {

    val message: Map[String, AnyRef] = Map(
      "header1" -> "key1",
      "header2" -> "value1"
    )

    val template =
      """date,{now}
        |header,{header1}""".stripMargin
    val writer = new DelimitedFileWriter() {
      override protected def getNow: DateTime = DateTime.parse("2018-04-11T12:11:22.333Z").withZone(UTC)
    }
      .option(CSV_DELIMITER, ",")
      .option(WRITE_HEADER, "false")
      .option(HEADER_TEMPLATE, template)
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    Source.fromFile(writer.getOutputPath).getLines().mkString("\n") shouldBe
      """date,2018-04-11
        |header,key1
        |key1,value1""".stripMargin
  }

  it should "writeMessage with disabled quote" in {

    val message: Map[String, AnyRef] = Map(
      "A" -> "Value with wss",
      "B" -> "value1\tvalue1"
    )

    val writer = new DelimitedFileWriter() {
      override protected def getNow: DateTime = DateTime.parse("2018-04-11T12:11:22.333Z").withZone(UTC)
    }
      .option(CSV_DELIMITER, ",")
      .option(HEADER_DATE_FORMAT, "yyyy-MM-dd")
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    Source.fromFile(writer.getOutputPath).getLines().mkString("\n") shouldBe
      """A,B
        |Value with wss,value1	value1""".stripMargin
  }

  it should "writeMessage with disabled quote if CSV_QUOTE_CHAR is blank" in {

    val message: Map[String, AnyRef] = Map(
      "A" -> "Value with wss",
      "B" -> "value1  value1"
    )

    val writer = new DelimitedFileWriter()
      .option(CSV_DELIMITER, ",")
      .option(CSV_QUOTE_CHAR, "")
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    Source.fromFile(writer.getOutputPath).getLines().mkString("\n") shouldBe
      """A,B
        |Value with wss,value1  value1""".stripMargin
  }

  it should """writeMessage with " as quote char""" in {

    val message: Map[String, AnyRef] = Map(
      "A" -> "Value with wss",
      "B" -> "value1\tvalue1",
      "C" -> 1.asInstanceOf[AnyRef]
    )

    val writer = new DelimitedFileWriter()
      .option(CSV_DELIMITER, ",")
      .option(HEADER_DATE_FORMAT, "yyyy-MM-dd")
      .option(CSV_QUOTE_CHAR, "\"")
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    Source.fromFile(writer.getOutputPath).getLines().mkString("\n") shouldBe
      """A,B,C
         |"Value with wss","value1	value1",1""".stripMargin
  }
}
