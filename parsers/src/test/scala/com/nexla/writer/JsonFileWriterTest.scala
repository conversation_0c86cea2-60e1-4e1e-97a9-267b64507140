package com.nexla.writer

import com.bazaarvoice.jolt.JsonUtils.jsonToMap
import com.nexla.common.parse.ParserConfigs
import com.nexla.common.parse.ParserConfigs.Unstructured._
import org.joda.time.DateTime
import org.joda.time.DateTimeZone.UTC
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._
import scala.io.Source

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class JsonFileWriterTest
  extends AnyFlatSpecLike
    with Matchers {

  it should "writeMessage" in {

    val message: Map[String, AnyRef] = Map(
      "header1" -> "key1",
      "header2" -> "value 1"
    )

    val writer = new JsonFileWriter()
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    Source.fromFile(writer.getOutputPath).getLines().mkString("\n") shouldBe """{"header1":"key1","header2":"value 1"}"""
  }

  it should "writeMessage with metadata" in {

    val message: Map[String, AnyRef] = Map(
      "header1" -> "key1",
      "header2" -> "value 1"
    )

    val writer = new JsonFileWriter() {
      override protected def getNow: DateTime = DateTime.parse("2018-04-11T12:11:22.333Z").withZone(UTC)
    }
      .option(HEADER_META_TEMPLATE, """{"date": "{now}", "data": "{header1}"}""")
      .option(HEADER_TEMPLATE, """{"hdate": "{now}"}""")
      .option(HEADER_DATE_FORMAT, "yyyy-MM-dd")
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    val resultTextLines = Source.fromFile(writer.getOutputPath).getLines().toList

    val actual1 = jsonToMap(resultTextLines(0))
    val expected1 = jsonToMap("""{"hdate": "2018-04-11"}""")

    val actual2 = jsonToMap(resultTextLines(1))
    val expected2 = jsonToMap("""{"date": "2018-04-11", "header1": "key1", "header2": "value 1", "data": "key1"}""")

    actual1 shouldBe expected1
    actual2 shouldBe expected2
  }

  it should "writeMessage in entire file mode" in {

    val messages: List[Map[String, AnyRef]] = List(
      Map(
        "headerA" -> "key1",
        "headerB" -> "value1"
      ),
      Map(
        "headerA" -> "key2",
        "headerB" -> "value2"
      )
    )

    val writer = new JsonFileWriter()
      .useTempFile()
      .option(ParserConfigs.Json.JSON_MODE, MODE_ENTIRE_FILE)
      .option(ParserConfigs.Json.JSON_OUTPUT_TEMPLATE,
        """[
          |{DATA}
          |]""".stripMargin)

    messages.foreach(m => writer.append(m.asJava))

    writer.finish()
    writer.close()

    val resultTextLines = Source.fromFile(writer.getOutputPath).getLines().mkString("\n")

    resultTextLines shouldBe
      """[
        |{"headerA":"key1","headerB":"value1"},
        |{"headerA":"key2","headerB":"value2"}
        |]""".stripMargin
  }
}
