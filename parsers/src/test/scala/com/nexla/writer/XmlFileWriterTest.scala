package com.nexla.writer

import com.bazaarvoice.jolt.JsonUtils.jsonToMap
import com.nexla.common.parse.ParserConfigs
import com.nexla.common.parse.ParserConfigs.Unstructured._
import com.nexla.common.parse.ParserConfigs.Xml
import org.joda.time.DateTime
import org.joda.time.DateTimeZone.UTC
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._
import scala.io.Source
import scala.util.{Failure, Success, Using}

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class XmlFileWriterTest
  extends AnyFlatSpecLike
    with Matchers {

  it should "writeMessage" in {

    val message: Map[String, AnyRef] = Map(
      "header1" -> "key1",
      "header2" -> "value 1"
    )

    val writer = new XmlFileWriter()
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    Source.fromFile(writer.getOutputPath).getLines().mkString("\n") shouldBe
      "<root><header1>key1</header1><header2>value 1</header2></root>"
  }

  it should "writeMessage with metadata" in {

    val message: Map[String, AnyRef] = Map(
      "header1" -> "key1",
      "header2" -> "value 1"
    )

    val writer = new XmlFileWriter() {
      override protected def getNow: DateTime = DateTime.parse("2018-04-11T12:11:22.333Z").withZone(UTC)
    }
      .option(HEADER_META_TEMPLATE, """{"date": "{now}", "data": "{header1}"}""")
      .option(HEADER_TEMPLATE, "<hdate>{now}</hdate>")
      .option(HEADER_DATE_FORMAT, "yyyy-MM-dd")
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    val resultTextLines = Source.fromFile(writer.getOutputPath).getLines().toList
    resultTextLines(0) shouldBe "<hdate>2018-04-11</hdate>"
    resultTextLines(1) shouldBe "<root><date>2018-04-11</date><header2>value 1</header2><header1>key1</header1><data>key1</data></root>"
  }

  it should "writeMessage in entire file mode" in {

    val messages: List[Map[String, AnyRef]] = List(
      Map(
        "headerA" -> "key1",
        "headerB" -> "value1"
      ),
      Map(
        "headerA" -> "key2",
        "headerB" -> "value2"
      )
    )

    val writer = new XmlFileWriter()
      .useTempFile()
      .option(ParserConfigs.Xml.XML_MODE, MODE_ENTIRE_FILE)
      .option(ParserConfigs.Xml.XML_OUTPUT_TEMPLATE,
        """<result>
          |{DATA}
          |</result>""".stripMargin)

    messages.foreach(m => writer.append(m.asJava))

    writer.finish()
    writer.close()

    val resultTextLines = Source.fromFile(writer.getOutputPath).getLines().mkString("\n")

    resultTextLines shouldBe
      """<result>
        |<root><headerA>key1</headerA><headerB>value1</headerB></root>
        |<root><headerA>key2</headerA><headerB>value2</headerB></root>
        |</result>""".stripMargin
  }

  it should "writeMessage with xml.output.template (without root tag)" in {
    val message = jsonToMap(
      """{
        |  "tag" : {
        |    "-attr" : "1",
        |    "#text" : "value"
        |  }
        |}""".stripMargin
    )

    val writer = new XmlFileWriter()
      .useTempFile()
      .option(Xml.XML_OUTPUT_TEMPLATE,
        """<POSLog xmlns="http://www.nrf-arts.org/IXRetail/namespace/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bbb="http://www.bbb.com/IXRetail/namespace/" xmlns:jms="uri://SolutionCenter/JMS/namespace">
          |{DATA}
          |</POSLog>""".stripMargin
      )
      .option(Xml.XML_MODE, MODE_ENTIRE_FILE)
      .option(Xml.XML_ROOT_TAG, null)

    writer.append(message)
    writer.finish()
    writer.close()

    val actual = Source.fromFile(writer.getOutputPath).getLines().mkString("\n")
    actual shouldBe
      """<POSLog xmlns="http://www.nrf-arts.org/IXRetail/namespace/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bbb="http://www.bbb.com/IXRetail/namespace/" xmlns:jms="uri://SolutionCenter/JMS/namespace">
        |<tag attr="1">value</tag>
        |</POSLog>""".stripMargin
  }

  it should "writeMessage with xml.output.template (with root tag)" in {
    val message = jsonToMap(
      """{
        |  "tag" : {
        |    "-attr" : "1",
        |    "#text" : "value"
        |  }
        |}""".stripMargin
    )

    val writer = new XmlFileWriter() {
      override protected def getNow: DateTime = DateTime.parse("2024-01-18T12:11:22.333Z").withZone(UTC)
    }
      .useTempFile()
      .option(Xml.XML_OUTPUT_TEMPLATE, "<POSLog xmlns=\"http://www.nrf-arts.org/IXRetail/namespace/\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:bbb=\"http://www.bbb.com/IXRetail/namespace/\" xmlns:jms=\"uri://SolutionCenter/JMS/namespace\" extractDate=\"{now}\">\n{DATA}</POSLog>"
      )
      .option(Xml.XML_MODE, MODE_ENTIRE_FILE)
      .option(Xml.XML_ROOT_TAG, "root")
      .option(HEADER_DATE_FORMAT, "yyyy-MM-dd'T'HH:mm:ss")

    writer.append(message)
    writer.finish()
    writer.close()

    val actual = Source.fromFile(writer.getOutputPath).getLines().mkString("\n")
    actual shouldBe
      "<POSLog xmlns=\"http://www.nrf-arts.org/IXRetail/namespace/\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:bbb=\"http://www.bbb.com/IXRetail/namespace/\" xmlns:jms=\"uri://SolutionCenter/JMS/namespace\" extractDate=\"2024-01-18T12:11:22\">\n<root><tag attr=\"1\">value</tag></root></POSLog>"
  }

  it should "writeMessage with unknown namespaces" in {
    val expected = Source.fromResource("xml.test.expected.xml", this.getClass.getClassLoader).getLines().mkString("\n")

    val message = jsonToMap(
      Source.fromResource("xml.test.json", this.getClass.getClassLoader).getLines().mkString("\n")
    )

    val writer = new XmlFileWriter()
      .useTempFile()
      .option(Xml.XML_OUTPUT_TEMPLATE, "<POSLog xmlns=\"http://www.nrf-arts.org/IXRetail/namespace/\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:bbb=\"http://www.bbb.com/IXRetail/namespace/\" xmlns:jms=\"uri://SolutionCenter/JMS/namespace\">\n{DATA}</POSLog>")
      .option(Xml.XML_MODE, MODE_ENTIRE_FILE)
      .option(Xml.XML_ROOT_TAG, null)

    writer.append(message)
    writer.finish()
    writer.close()

    val actual = Source.fromFile(writer.getOutputPath).getLines().mkString("\n")
    actual shouldBe expected
  }

  it should "support nested text content" in {
    val message: Map[String, AnyRef] = Map(
      "header1" -> "key1",
      "header2" -> "value 1",
      "#text" -> "text content"
    )

    val writer = new XmlFileWriter()
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    val actual = Source.fromFile(writer.getOutputPath).getLines().mkString("\n")
    actual shouldBe
      """<root><header1>key1</header1><header2>value 1</header2>text content</root>"""
  }

  it should "support multiple attributes per elem" in {
    val message: Map[String, AnyRef] = Map(
      "header1" -> "key1",
      "header2" -> "value 1",
      "-first" -> "1",
      "-second" -> "2"
    )

    val writer = new XmlFileWriter()
      .useTempFile()

    writer.append(message.asJava)
    writer.close()

    val actual = Source.fromFile(writer.getOutputPath).getLines().mkString("\n")
    actual shouldBe
      """<root first="1" second="2"><header1>key1</header1><header2>value 1</header2></root>"""
  }

  it should "escape element names" in {
    val message = jsonToMap("""{"Author": "Melanie", "Book Title": "Dolor Company", "Rating": 4, "Cost": "$7,887"}""")

    val writer = new XmlFileWriter()
      .option(Xml.XML_ROOT_TAG, "root")
      .useTempFile()

    writer.append(message)
    writer.close()

    val actual = Source.fromFile(writer.getOutputPath).getLines().mkString("\n")
    actual shouldBe
      """<root><Author>Melanie</Author><Book_Title>Dolor Company</Book_Title><Rating>4</Rating><Cost>$7,887</Cost></root>"""
  }

  it should "not fail during xmlns attributes serialization" in {
    val message = jsonToMap(
      """
        |{
        |   "Log": {
        |       "-xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance",
        |       "-xmlns:jms": "uri://uri/namespace",
        |       "-xmlns:bbb": "http://h/p/namespace/",
        |       "TX": {
        |         "-CancelFlag": "false",
        |         "ID": "1985"
        |       },
        |       "-xmlns": "http://ns/namespace/"
        |   }
        |}
        |""".stripMargin
    )

    val writer = new XmlFileWriter()
      .useTempFile()

    writer.append(message)
    writer.close()

    val actual = Source.fromFile(writer.getOutputPath).getLines().mkString("\n")
    actual shouldBe
      """<root><Log xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jms="uri://uri/namespace" xmlns:bbb="http://h/p/namespace/" xmlns="http://ns/namespace/"><TX CancelFlag="false"><ID>1985</ID></TX></Log></root>"""
  }

  it should "optionally flatten lists of objects" in {
    val message = jsonToMap(
      """
        |{
        |  "Document": {
        |    "+Report": [
        |      {
        |        "Type1": {
        |          "Id": "123",
        |          "Account": [1,2,3]
        |        }
        |      },
        |      {
        |        "Type2": {
        |          "Id": "456",
        |          "Account": [1,2,3]
        |        }
        |      }
        |    ]
        |  }
        |}
        |""".stripMargin)

    val writer = new XmlFileWriter().useTempFile()

    writer.append(message)
    writer.close()

    Using(Source.fromFile(writer.getOutputPath)) { source =>
      val actual = source.getLines().mkString("\n")
      actual shouldEqual "<root><Document><Report><Type1><Id>123</Id><Account>1</Account><Account>2</Account><Account>3</Account></Type1><Type2><Id>456</Id><Account>1</Account><Account>2</Account><Account>3</Account></Type2></Report></Document></root>"
    }.get
  }

  it should "not flatten lists of primitives" in {
    val message = jsonToMap(
      """
        |{
        |  "Foo": {
        |    "+Bar": [1,2,3]
        |  }
        |}
        |""".stripMargin)

    val writer = new XmlFileWriter().useTempFile()

    writer.append(message)
    writer.close()

    Using(Source.fromFile(writer.getOutputPath)) { source =>
      val actual = source.getLines().mkString("\n")
      actual shouldBe "<root><Foo><Bar>1</Bar><Bar>2</Bar><Bar>3</Bar></Foo></root>"
    }.get
  }
}
