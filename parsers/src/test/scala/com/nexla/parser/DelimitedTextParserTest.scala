package com.nexla.parser

import java.io.ByteArrayInputStream
import com.nexla.common.parse.ParserConfigs.Csv._
import com.nexla.common.parse.ParserConfigs.DEFAULT_CHARSET
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._
import scala.io.Source

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class DelimitedTextParserTest extends AnyFlatSpecLike with Matchers {

  behavior of "DelimitedTextParserTest"


  it should "parse with escape char 2" in {

    val data = "article_id,descriptive_attributes\n179982,\"{\"\"value\"\":\"\"0.20\\\"\"\"\"}\""

    val parser = new DelimitedTextParser()
      .option(CSV_DELIMITER, ",")
      .option(CSV_SCHEMA_DETECTION, HEADER)
      .option(CSV_QUOTE_CHAR, "\"")
      .option(CSV_ESCAPE_CHAR, "")

    readMessages(data, parser) shouldBe List(
      Map("article_id" -> "179982", "descriptive_attributes" -> """{"value":"0.20\""}""")
    )

  }


  it should "parse with escape char" in {

    val data = "aaa\\\"bbb".stripMargin

    val parser = new DelimitedTextParser().option(CSV_SCHEMA_DETECTION, GENERATED)
    readMessages(data, parser) shouldBe List(
      Map("attribute1" -> "aaa\"bbb")
    )
  }

  it should "return entire line if delimiter is empty string" in {
    val data =
      """aaaaaaa
        |
        |bbbbbbbbbb
        |"Hello"""".stripMargin

    val parser = new DelimitedTextParser().option(CSV_DELIMITER, "")
    readMessages(data, parser) shouldBe List(
      Map("value" -> "aaaaaaa"),
      Map("value" -> ""),
      Map("value" -> "bbbbbbbbbb"),
      Map("value" -> """"Hello"""")
    )
  }

  it should "be parsed as a single item when delimiter is absent in its content" in {

    val data =
      "key1\tvalue1\n" +
        "key2"

    val parser = new DelimitedTextParser()
      .option(CSV_DELIMITER, "^")
      .option(CSV_SCHEMA_DETECTION, GENERATED)

    val maps = readMessages(data, parser)
    maps shouldBe List(
      Map("attribute1" -> "key1\tvalue1"),
      Map("attribute1" -> "key2")
    )
  }

  it should "parseMessages_schemaGenerated" in {

    val data =
      " \n" +
        " \t \n" +
        "key1\tvalue1\n" +
        "key2"

    val parser = new DelimitedTextParser()
      .option(CSV_DELIMITER, "\t")
      .option(CSV_SCHEMA_DETECTION, GENERATED)

    val actual = readMessages(data, parser)

    actual shouldBe List(
      Map("attribute1" -> "key1", "attribute2" -> "value1"),
      Map("attribute1" -> "key2")
    )
  }

  it should "parseMessages_schemaHeader" in {

    val data =
      "header1\theader2\n" +
        "  \t   \n" +
        "\t\n" +
        "  \n" +
        "\n" +
        "key1\tvalue1\n" +
        "key2\tvalue2"

    val parser = new DelimitedTextParser().option(CSV_DELIMITER, "\t").option(CSV_SCHEMA_DETECTION, HEADER)

    readMessages(data, parser) shouldBe List(
      Map("header1" -> "key1", "header2" -> "value1"),
      Map("header1" -> "key2", "header2" -> "value2")
    )
  }

  it should "parseMessages_forceRemoveQuotes" in {

    val data =
      """ID,CODE
        |1,VACUUM CODE NAME
        |"2","VACUUM CODE NAME"
        |"3","VACUUM "CODE" NAME"
        |"4","VACUUM" "CODE" "NAME"
        |"5","VACUUM" CODE NAME
      """.stripMargin

    val parser = new DelimitedTextParser()
      .option(CSV_DELIMITER, ",")
      .option(CSV_SCHEMA_DETECTION, HEADER)
      .option(CSV_SCALAR_COERCION, "true")
      .option(CSV_QUOTE_DISABLED, "true")
      .option(CSV_QUOTE_REMOVE_FORCED, "true")

    readMessages(data, parser) shouldBe List(
      Map("ID" -> 1, "CODE" -> "VACUUM CODE NAME"),
      Map("ID" -> 2, "CODE" -> "VACUUM CODE NAME"),
      Map("ID" -> 3, "CODE" -> """VACUUM "CODE" NAME"""),
      Map("ID" -> 4, "CODE" -> """"VACUUM" "CODE" "NAME""""),
      Map("ID" -> 5, "CODE" -> """"VACUUM" CODE NAME""")
    )
  }

  it should "parseMessages_withDisabledQuote" in {

    val data =
      """ID,CODE
        |1,"VACUUM CODE" NAME
      """.stripMargin

    val parser = new DelimitedTextParser()
      .option(CSV_DELIMITER, ",")
      .option(CSV_SCALAR_COERCION, "true")
      .option(CSV_SCHEMA_DETECTION, HEADER)
      .option(CSV_QUOTE_CHAR, "")

    readMessages(data, parser) shouldBe List(
      Map("ID" -> 1, "CODE" -> """"VACUUM CODE" NAME""")
    )
  }

  private def readMessages(data: String, parser: DelimitedTextParser, charset: String = "UTF-8") = {
    parser.parseMessages(new ByteArrayInputStream(data.getBytes(charset))).iterator().asScala
      .filter(_.isPresent)
      .map(_.get)
      .toList
      .map(_.getRawMessage.asScala)
  }

  it should "parseMessages_schemaConfigured" in {

    val data =
      "key1\tvalue1\r\n" +
        "key2\tvalue2"

    val parser = new DelimitedTextParser()
      .option(CSV_DELIMITER, "\t")
      .option(CSV_SCHEMA, "config1,config2")
      .option(CSV_SCHEMA_DETECTION, CONFIGURED)

    readMessages(data, parser) shouldBe List(
      Map("config1" -> "key1", "config2" -> "value1"),
      Map("config1" -> "key2", "config2" -> "value2")
    )
  }

  it should "parseMessages_skipBlankOrEmptyLine" in {

    val data =
      "key1\tvalue1\n" +
        "\n" +
        "key2\tvalue2"

    val parser = new DelimitedTextParser()
      .option(CSV_DELIMITER, "\t")
      .option(CSV_SCHEMA, "config1,config2")
      .option(CSV_SCHEMA_DETECTION, CONFIGURED)

    readMessages(data, parser) shouldBe List(
      Map("config1" -> "key1", "config2" -> "value1"),
      Map("config1" -> "key2", "config2" -> "value2")
    )
  }

  it should "parseMessages_nonUtf8" in {

    val data = "id,Name,City\n" +
      "1,Prescott Grant,Kungälv\n" +
      "2,Rhea Schroeder,Cambridge\n" +
      "3,Nathan Bernard,Trollhättan,\n" +
      "4,Wanda Flores,Montignoso,\n" +
      "5,McKenzie Roach,Aachen,\n" +
      "6,Judah Robles,Manukau,\n" +
      "7,Brendan Hester,Tczew,\n" +
      "8,Keegan Keller,Torun,\n" +
      "9,Kato Mckay,Rockford,\n" +
      "10,Oliver Wheeler,Strasbourg,"

    val parser = new DelimitedTextParser()
      .option(CSV_DELIMITER, ",")
      .option(CSV_SCHEMA_DETECTION, HEADER)
      .option(CSV_SCALAR_COERCION, "true")
      .option(DEFAULT_CHARSET, "ISO-8859-1")

    val messages = readMessages(data, parser, "ISO-8859-1")
    messages.size shouldBe 10
    messages.take(2) shouldBe List(
      Map("id" -> 1, "Name" -> "Prescott Grant", "City" -> "Kungälv"),
      Map("id" -> 2, "Name" -> "Rhea Schroeder", "City" -> "Cambridge")
    )
  }

  it should "parseMessages_withNoQuoteCharacter" in {
    val data =
      "id,Char\n" +
        "1,\"A\"\n" +
        "2,B"

    val parser = new DelimitedTextParser()
      .option(CSV_DELIMITER, ",")
      .option(CSV_ENABLE_QUOTE_DETECTION, "false")
      .option(CSV_SCALAR_COERCION, "true")
      .option(CSV_SCHEMA_DETECTION, HEADER)

    readMessages(data, parser) shouldBe List(
      Map("id" -> 1, "Char" -> "\"A\""),
      Map("id" -> 2, "Char" -> "B")
    )
  }

  it should "parseMessages_skipFirstLines" in {
    val data =
      "dfsgfdg\n" +
        "dsfgdfg\r\n" +
        "id,Char\n" +
        "1,\"A\"\n" +
        "2,B"

    val parser = new DelimitedTextParser()
      .option(CSV_DELIMITER, ",")
      .option(CSV_ENABLE_QUOTE_DETECTION, "false")
      .option(CSV_SCHEMA_DETECTION, HEADER)
      .option(CSV_SCALAR_COERCION, "true")
      .option(CSV_SKIP_FIRST_LINES, "2")

    readMessages(data, parser) shouldBe List(
      Map("id" -> 1, "Char" -> "\"A\""),
      Map("id" -> 2, "Char" -> "B")
    )
  }

  it should "parseMessages_skipFirstLines_and_include_skipped_lines" in {
    val data =
      "lineToSkip1\n" +
        "lineToSkip2\r\n" +
        "id,Char\n" +
        "1,\"A\"\n" +
        "2,B"
        
    val skippedLinesExpectedResult = "lineToSkip1" + System.lineSeparator() + "lineToSkip2";

    val parser = new DelimitedTextParser()
      .option(CSV_DELIMITER, ",")
      .option(CSV_ENABLE_QUOTE_DETECTION, "false")
      .option(CSV_SCHEMA_DETECTION, HEADER)
      .option(CSV_SCALAR_COERCION, "true")
      .option(CSV_SKIP_FIRST_LINES, "2")
      .option(CSV_INCLUDE_SKIPPED_LINES, "true")

    readMessages(data, parser) shouldBe List(
      Map("id" -> 1, "Char" -> "\"A\"", "nx_skipped_lines" -> skippedLinesExpectedResult),
      Map("id" -> 2, "Char" -> "B", "nx_skipped_lines" -> skippedLinesExpectedResult)
    )
  }

  it should "parseMessages_skipLastLines" in {
    val data =
      "id,Char\n" +
        "1,\"A\"\n" +
        "2,B\n" +
        "total:125"

    val parser = new DelimitedTextParser()
      .option(CSV_DELIMITER, ",")
      .option(CSV_ENABLE_QUOTE_DETECTION, "false")
      .option(CSV_SCHEMA_DETECTION, HEADER)
      .option(CSV_SCALAR_COERCION, "true")
      .option(CSV_SKIP_LAST_LINES, "1")

    readMessages(data, parser) shouldBe List(
      Map("id" -> 1, "Char" -> "\"A\""),
      Map("id" -> 2, "Char" -> "B")
    )
  }

  it should "readLines" in {

    val data =
      "key1\tvalue1\r\n" +
        "key2\tvalue2"

    val parser = new DelimitedTextParser()
      .option(CSV_DELIMITER, "\t")
      .option(CSV_SCHEMA, "config1,config2")
      .option(CSV_SCALAR_COERCION, "true")
      .option(CSV_SCHEMA_DETECTION, CONFIGURED)

    val lines = parser.readLines(new ByteArrayInputStream(data.getBytes())).iterator().asScala.toList

    lines shouldBe List("key1\tvalue1", "key2\tvalue2")
  }

  it should "deduce proper types for scalars" in {
    val data =
      s"""id,int,long,double,bdec,bool,ip,string\n""" +
      s"""1,${Int.MaxValue},${Long.MaxValue},${Double.MaxValue},6.02e1000,false,127.0.0.1,string\n""" +
      s"""1,${Int.MinValue},${Long.MinValue},${Double.MinValue},6.02e-1000,true,*******,\n""" +
      s""""1",,,,,,,\n"""

    val parser = new DelimitedTextParser()
      .option(CSV_DELIMITER, ",")
      .option(CSV_SCALAR_COERCION, "true")
      .option(CSV_SCHEMA_DETECTION, HEADER)

    val actual = readMessages(data, parser)

    actual.map(it => it.mapValues(_.getClass).toMap) shouldBe List(
      Map(
        "id" -> classOf[java.lang.Integer],
        "int" -> classOf[java.lang.Integer],
        "long" -> classOf[java.lang.Long],
        "double" -> classOf[java.lang.Double],
        "bdec" -> classOf[java.math.BigDecimal],
        "bool" -> classOf[java.lang.Boolean],
        "ip" -> classOf[java.lang.String],
        "string" -> classOf[java.lang.String],
      ),
      Map(
        "id" -> classOf[java.lang.Integer],
        "int" -> classOf[java.lang.Integer],
        "long" -> classOf[java.lang.Long],
        "double" -> classOf[java.lang.Double],
        "bdec" -> classOf[java.math.BigDecimal],
        "bool" -> classOf[java.lang.Boolean],
        "ip" -> classOf[java.lang.String],
        "string" -> classOf[java.lang.String],
      ),
      Map(
        "id" -> classOf[java.lang.String],
        "int" -> classOf[java.lang.String],
        "long" -> classOf[java.lang.String],
        "double" -> classOf[java.lang.String],
        "bdec" -> classOf[java.lang.String],
        "bool" -> classOf[java.lang.String],
        "ip" -> classOf[java.lang.String],
        "string" -> classOf[java.lang.String],
      )
    )

    actual shouldBe List(
      Map(
        "id" -> 1,
        "int" -> Int.MaxValue,
        "long" -> Long.MaxValue,
        "double" -> Double.MaxValue,
        "bdec" -> new java.math.BigDecimal("6.02e1000"),
        "bool" -> false,
        "ip" -> "127.0.0.1",
        "string" -> "string"
      ), Map(
        "id" -> 1,
        "int" -> Int.MinValue,
        "long" -> Long.MinValue,
        "double" -> Double.MinValue,
        "bdec" -> new java.math.BigDecimal("6.02e-1000"),
        "bool" -> true,
        "ip" -> "*******",
        "string" -> ""
      ), Map(
        "id" -> """"1"""",
        "int" -> "",
        "long" -> "",
        "double" -> "",
        "bdec" -> "",
        "bool" -> "",
        "ip" -> "",
        "string" -> ""
      ),
    )
  }
}