package com.nexla.parser

import java.io.{ByteArrayInputStream, InputStream}
import java.math.BigInteger
import java.util.function.BiConsumer
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import com.google.common.base.Supplier
import com.nexla.common.exception.ParseError
import com.nexla.common.parse.ParserConfigs.Json
import com.nexla.common.parse.ParserConfigs.Json._
import com.nexla.common.parse.ParserConfigs.Unstructured._
import com.nexla.parser.xml.AdditionalPathsParser
import org.jsfr.json.exception.JsonSurfingException
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.scalatest.{OneInstancePerTest, TagAnnotation}
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._
import scala.collection.mutable

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class JsonParserTest extends AnyFunSuite with Matchers with OneInstancePerTest {

  private val mapper = new ObjectMapper().registerModule(DefaultScalaModule)

  private def parser = new AdditionalPathsParser(new JsonParser())

  private val jsonPathList = List("$[*]", "*", "$.*")

  test("parse jsonPath: map") {

    val p = parser.option(JSON_PATH, "$.features[*].properties")

    val expected = List(
      map("MAPBLKLOT" -> "0001001"),
      map("MAPBLKLOT" -> "0002001"))

    mapper.writeValueAsString(expected) shouldEqual mapper.writeValueAsString(testData(p))
  }

  test("parse jsonPath: multiple JSON paths") {

    val p = parser
      .option(JSON_PATH, "$.features[*].properties")
      .option(ADDITIONAL_JSON_PATHS, "[\"$.header.type\", \"$.header2\"]")

    val expected = List(
      map("type" -> "headerType", "MAPBLKLOT" -> "0001001"),
      map("type" -> "headerType", "MAPBLKLOT" -> "0002001"))

    mapper.writeValueAsString(expected) shouldEqual mapper.writeValueAsString(testData(p))
  }

  test("parse jsonPath: multiple JSON paths ROW mode") {

    var exceptions = List[Exception]()
    val exceptionHandler = new BiConsumer[ParseError, Exception] {
      override def accept(t: ParseError, u: Exception): Unit = {
        exceptions = exceptions :+ u
      }
    }
    val p = parser
      .option(JSON_PATH, "$.features[*].properties")
      .option(ADDITIONAL_JSON_PATHS, "[\"$.header\", \"$.header2\"]")
      .exceptionHandler(exceptionHandler)

    val expected = List(
      map("type2" -> "headerType", "type" -> "headerType", "MAPBLKLOT" -> "0001001"),
      map("type2" -> "headerType", "type" -> "headerType", "MAPBLKLOT" -> "0002001"),
      map("type2" -> "headerType", "type" -> "headerType", "MAPBLKLOT" -> "0003001"),
      map("type2" -> "headerType", "type" -> "headerType", "MAPBLKLOT" -> "0004001")
    )

    mapper.writeValueAsString(testData(p, "testJsonPathInLines.json")) shouldEqual mapper.writeValueAsString(expected)
    exceptions.size shouldEqual 1
    exceptions.head.getCause.getMessage shouldEqual "Duplicate field 'type'\n at [Source: (String)\"{\"type\": \"FeatureCollection\", \"type\": \"FeatureCollection2\", \"features\": [{\"type\": \"Feature\", \"properties\": {\"MAPBLKLOT\": \"0003001\"}}, {\"type\": \"Feature\", \"properties\": {\"MAPBLKLOT\": \"0004001\"}}], \"feature space\": \"infinite\", \"header\": {\"type\": \"headerType\"}, \"header2\": {\"type2\": \"headerType\"}}\"; line: 1, column: 37]"
  }

  test("parse jsonPath: no JSON paths ROW mode - allow duplicates") {
    var exceptions = List[Exception]()
    val exceptionHandler = new BiConsumer[ParseError, Exception] {
      override def accept(t: ParseError, u: Exception): Unit = {
        exceptions = exceptions :+ u
      }
    }

    val expected = """[{"type":"FeatureCollection","features":[{"type":"Feature","properties":{"MAPBLKLOT":"0001001"}},{"type":"Feature","properties":{"MAPBLKLOT":"0002001"}}],"feature space":"infinite","header":{"type":"headerType"},"header2":{"type2":"headerType"}},{"type":"FeatureCollection2","features":[{"type":"Feature","properties":{"MAPBLKLOT":"0003001"}},{"type":"Feature","properties":{"MAPBLKLOT":"0004001"}}],"feature space":"infinite","header":{"type":"headerType"},"header2":{"type2":"headerType"}}]"""

    val p = parser.exceptionHandler(exceptionHandler)
    mapper.writeValueAsString(testData(p, "testJsonPathInLines.json")) shouldEqual expected
    exceptions.size shouldEqual 0
  }

  test("parse jsonPath with space in keys") {
    val p = parser.option(JSON_PATH, "$['feature space']")

    val expected = List(
      map("feature space" -> "infinite"))

    mapper.writeValueAsString(expected) shouldEqual mapper.writeValueAsString(testData(p))
  }

  test("parse jsonPath: array") {

    val p = parser.option(JSON_PATH, "$.features")

    val expected = List(
      map(
        "features" -> List(
          map(
            "type" -> "Feature",
            "properties" -> map("MAPBLKLOT" -> "0001001")),
          map(
            "type" -> "Feature",
            "properties" -> map("MAPBLKLOT" -> "0002001")))))

    mapper.writeValueAsString(expected) shouldEqual mapper.writeValueAsString(testData(p))
  }

  test("parse jsonPathWithArray entire file mode") {

    val testDataWithArray =
      """[
        |  {
        |    "type": "FeatureCollection1"
        |  },
        |  {
        |    "type": "FeatureCollection2"
        |  }
        |]""".stripMargin

    for (jsonPath <- jsonPathList) {
      val p = parser.option(JSON_PATH, jsonPath)
      val inputStream = new ByteArrayInputStream(testDataWithArray.getBytes())
      val result = parseToMap(p, inputStream)

      val expected = List(
        Map("type" -> "FeatureCollection1"),
        Map("type" -> "FeatureCollection2")
      )
      mapper.writeValueAsString(result) shouldEqual mapper.writeValueAsString(expected)
    }
  }

  test("parse jsonPathWithArray row mode") {

    val testDataWithArray =
      """[{"type": "FeatureCollection1"},{"type": "FeatureCollection2"}]
        |[{"type": "FeatureCollection3"},{"type": "FeatureCollection4"}]
      """.stripMargin

    for (jsonPath <- jsonPathList) {
      val p = parser.option(JSON_PATH, jsonPath)
      val inputStream = new ByteArrayInputStream(testDataWithArray.getBytes())
      val result = parseToMap(p, inputStream)

      val expected = List(
        Map("type" -> "FeatureCollection1"),
        Map("type" -> "FeatureCollection2"),
        Map("type" -> "FeatureCollection3"),
        Map("type" -> "FeatureCollection4")
      )
      mapper.writeValueAsString(result) shouldEqual mapper.writeValueAsString(expected)
    }
  }

  test("parse entire file without jsonPath") {
    val p = parser.option(Json.JSON_MODE, MODE_ENTIRE_FILE)

    val expected = List(
      map(
        "type" -> "FeatureCollection",
        "features" -> List(
          map(
            "type" -> "Feature",
            "properties" -> map("MAPBLKLOT" -> "0001001")),
          map(
            "type" -> "Feature",
            "properties" -> map("MAPBLKLOT" -> "0002001"))),
        "feature space" -> "infinite",
        "header" -> map(
          "type" -> "headerType",
          "value" -> "value"
        ))
    )

    mapper.writeValueAsString(expected) shouldEqual mapper.writeValueAsString(testData(p))
  }

  test("parsing failed in entire file mode") {

    assertThrows[JsonSurfingException] {
      val malformedJson = "}12314"
      parser.option(Json.JSON_MODE, MODE_ENTIRE_FILE)
        .parseMessages(new ByteArrayInputStream(malformedJson.getBytes))
        .iterator().asScala.toArray
        .map(_.get.getRawMessage)
    }
  }

  test("parsing failed in row-based mode") {

    val handlerMock = mock(classOf[BiConsumer[ParseError, Exception]])
    val p = parser.option(Json.JSON_MODE, MODE_ROW)
    p.option(JSON_MODE, MODE_ROW).exceptionHandler(handlerMock)
    p.parseMessages(new ByteArrayInputStream("not valid json".getBytes)).iterator.asScala.toArray
    verify(handlerMock, times(1)).accept(any(), any())
  }

  test("parsing really long integer in entire file mode") {
    val testDataWithBigInt =
      """{
        | "value": 12345678901234567890
        |}""".stripMargin

    val expected = List(
      Map("value" -> new BigInteger("12345678901234567890"))
    )

    val inputStream = new ByteArrayInputStream(testDataWithBigInt.getBytes())
    val result = parseToMap(parser.option(Json.JSON_MODE, MODE_ENTIRE_FILE), inputStream)
    inputStream.close()

    result shouldBe expected
  }

  test("parsing really long integer in row mode") {
    val testDataWithBigInt =
      """{"value": 12345678901234567890}""".stripMargin

    val expected = List(
      Map("value" -> new BigInteger("12345678901234567890"))
    )

    val inputStream = new ByteArrayInputStream(testDataWithBigInt.getBytes())
    val result = parseToMap(parser.option(Json.JSON_MODE, MODE_ROW), inputStream)
    inputStream.close()

    result shouldBe expected
  }

  test("parsing in row mode with empty lines") {
    val testData =
      """
        |
        |   {"value1": 1}
        |
        |{"value2": 2}
        |
        |
        |{"value3": 3}
        |
        |""".stripMargin

    val expected = List(
      Map("value1" -> Integer.valueOf(1)),
      Map("value2" -> Integer.valueOf(2)),
      Map("value3" -> Integer.valueOf(3))
    )

    val inputStream = new ByteArrayInputStream(testData.getBytes())

    val result = parseToMap(parser.option(Json.JSON_PATH, ""), inputStream)
    inputStream.close()

    result shouldBe expected
  }

  test("parsing fails in row mode with garbage lines") {
    val handlerMock = mock(classOf[BiConsumer[ParseError, Exception]])
    val testData =
      """
        |
        |   {"value1": 1}
        |garbage
        |{"value2": 2}
        |
        |<garbage>
        |{"value3": 3}
        |
        |""".stripMargin

    val expected = List(
      Map("value1" -> Integer.valueOf(1)),
      Map("value2" -> Integer.valueOf(2)),
      Map("value3" -> Integer.valueOf(3))
    )

    val inputStream = new ByteArrayInputStream(testData.getBytes())
    val p = parser.option(Json.JSON_PATH, "")
    p.exceptionHandler(handlerMock)

    val result = parseToMap(p, inputStream)
    inputStream.close()

    result shouldBe expected
  }

  test("parsing in entire file mode with empty lines") {
    val testData =
      """{
        |
        | "value1": 1,
        |
        | "value2": 2,
        |
        |
        | "value3": 3
        |
        |}""".stripMargin

    val expected = Array(
      Map("value1" -> Integer.valueOf(1),
        "value2" -> Integer.valueOf(2),
        "value3" -> Integer.valueOf(3)))

    val inputStream = new ByteArrayInputStream(testData.getBytes())

    val result = parseToMap(parser.option(Json.JSON_PATH, ""), inputStream)
    inputStream.close()

    result shouldBe expected
  }

  private def parseToMap(p: AdditionalPathsParser, inputStream: InputStream) =
    p.parseMessages(inputStream).iterator().asScala.toArray.map(_.get.getRawMessage.asScala)

  private def testData(p: AdditionalPathsParser, file: String = "testJsonPath.json") = {
    val inputStream = new Supplier[InputStream] {
      override def get(): InputStream = TarTest.getResourceAsStreaz(this.getClass(), file)
    }
    val result = p.parseMessages(inputStream, true).iterator().asScala.toArray.map(_.get.getRawMessage)
    result
  }

  private def map(tuple: (String, Any)*) = mutable.LinkedHashMap(tuple: _*)
}
