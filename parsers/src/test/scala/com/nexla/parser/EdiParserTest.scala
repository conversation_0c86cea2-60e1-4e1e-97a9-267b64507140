package com.nexla.parser

import java.io.{ByteArrayInputStream, File}
import java.nio.charset.StandardCharsets._
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import com.nexla.common.parse.ParserConfigs.Edi.{EDI_OPTIONS, EDI_SKIP_ADVANCE_TO_ISA}
import com.nexla.writer.EdiFileWriter
import org.apache.commons.io.{FileUtils, IOUtils}
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class EdiParserTest extends AnyFlatSpecLike with Matchers {

  private val mapper = new ObjectMapper().registerModule(DefaultScalaModule).writerWithDefaultPrettyPrinter()

  behavior of "EdiParser"

  val testData = Seq(
    "/edi/OOLU9801374.edi",
    "/edi/831.x12",
    "/edi/214.x12",
    "/edi/EDI-850-sample1.x12",
    "/edi/lenovo_ups_HKERX9RP.DAT",
    "/edi/PARSE_OPT_YES.DAT",
    "/edi/UPSEDITESTFILE.DAT"
  )

  testData.foreach { filePath =>

    it should s"write edi from $filePath" in {

      val inputStream = TarTest.getResourceAsStreaz(this.getClass(), filePath)
      val sourceData = IOUtils.toString(inputStream, UTF_8)
      inputStream.close()

      try {
        val parser = new EdiParser()
          .option(EDI_OPTIONS, "newline=crlf:typ=no:opt=yes")
          .withLoggerPrefix("[testing]", s"[$filePath]")

        val parsedMessagesOriginal = parser
          .parseMessages(new ByteArrayInputStream(sourceData.getBytes))
          .iterator().asScala.map(_.get().getRawMessage).toList

        val writer = new EdiFileWriter()
          .useTempFile()
          .option(EDI_OPTIONS, "typ=no:opt=yes")

        parsedMessagesOriginal.foreach(writer.append(_))

        writer.close()
        writer.finish()

        val writtenData = FileUtils.readFileToString(new File(writer.getOutputPath), UTF_8)

        val parsedMessagesAfterWriting = parser
          .parseMessages(new ByteArrayInputStream(writtenData.getBytes))
          .iterator().asScala.map(_.get().getRawMessage).toList

        val originallyParsed = parsedMessagesOriginal.map(removeBrokenFields)
        val parsedAfterWriting = parsedMessagesAfterWriting.map(removeBrokenFields)

        originallyParsed shouldBe parsedAfterWriting

        writer.delete()

//        println(s"Result for file: $filePath")
//        println(mapper.writeValueAsString(parsedMessagesOriginal))
      } finally {
        inputStream.close()
      }
    }
  }

  def removeBrokenFields(data: java.util.LinkedHashMap[String, AnyRef]): java.util.LinkedHashMap[String, AnyRef] = {
    data.remove("SE01")
    data.remove("GE01")
    data.remove("CTT01")
    data.remove("CTT02")

    data.values().asScala
      .filter(_.isInstanceOf[java.util.LinkedHashMap[_, _]])
      .map(_.asInstanceOf[java.util.LinkedHashMap[String, AnyRef]])
      .foreach(data => removeBrokenFields(data))

    data
  }

  it should "accept parameters" in {

    val parser = new EdiParser()
      .option(EDI_OPTIONS, "a=b")
      .option(EDI_SKIP_ADVANCE_TO_ISA, "false")

    parser.options.get() shouldBe "a=b"
    parser.advanceToDocStart shouldBe false
  }
}
