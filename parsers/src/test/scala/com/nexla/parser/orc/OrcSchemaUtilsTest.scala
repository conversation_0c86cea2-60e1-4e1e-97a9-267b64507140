package com.nexla.parser.orc

import com.bazaarvoice.jolt.JsonUtils
import com.nexla.parser.TarTest
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class OrcSchemaUtilsTest extends AnyFlatSpecLike with Matchers {

  it should "getSchema" in {
    val inputStream = TarTest.getResourceAsStreaz(this.getClass(), "orc/data.snappy.orc")
    val parser = new OrcParser()
    parser.parseMessages(inputStream)
    inputStream.close()

    JsonUtils.toPrettyJsonString(parser.getSchema) shouldBe
      """{
        |  "type" : "object",
        |  "properties" : {
        |    "arrayData" : {
        |      "items" : {
        |        "type" : "string"
        |      },
        |      "type" : "array"
        |    },
        |    "boolData" : {
        |      "type" : "boolean"
        |    },
        |    "key" : {
        |      "type" : "integer"
        |    },
        |    "mapOfLists" : {
        |      "items" : {
        |        "properties" : {
        |          "_key" : {
        |            "type" : "integer"
        |          },
        |          "_value" : {
        |            "items" : {
        |              "type" : "integer"
        |            },
        |            "type" : "array"
        |          }
        |        },
        |        "type" : "object"
        |      },
        |      "type" : "array"
        |    },
        |    "value" : {
        |      "type" : "number"
        |    }
        |  },
        |  "$schema" : "http://json-schema.org/draft-04/schema#",
        |  "$schema-id" : 1962746663
        |}""".stripMargin
  }

}
