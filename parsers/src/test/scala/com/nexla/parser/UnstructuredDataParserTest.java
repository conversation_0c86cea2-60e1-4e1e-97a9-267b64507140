package com.nexla.parser;

import com.nexla.common.NexlaMessage;
import com.nexla.test.UnitTests;
import one.util.streamex.StreamEx;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.mockito.Mockito;
import org.springframework.http.HttpEntity;
import org.springframework.web.client.RestTemplate;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.FileSystems;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.any;

@Category(UnitTests.class)
@Ignore
// FIXME: Inject mocked restTemplate into the parser
public class UnstructuredDataParserTest {

    private RestTemplate restTemplate;
    private UnstructuredDataParser parser;

    @Before
    public void setup() {
        restTemplate = Mockito.mock(RestTemplate.class);
        parser = new UnstructuredDataParser(null, restTemplate);
    }

    @Test
    public void parseMessages() throws IOException {
        String fileName = "unstructured" + FileSystems.getDefault().getSeparator() + "simple_table.pdf";
        try (InputStream inputStream = TarTest.getResourceAsStreaz(this.getClass(), fileName)) {
            String response = "[{\"type\":\"Table\",\"element_id\":\"778febfb07e07a7911ea8700cea1aeaf\",\"metadata\":{\"filename\":\"7ccdded5-d494-46e1-9f8e-39c98ff5040f17482659314668280188.docx\",\"text_as_html\":\"<table>\\n<thead>\\n<tr><th>A  </th><th>E  </th><th>  </th><th>  </th><th>  </th><th>3  </th></tr>\\n</thead>\\n<tbody>\\n<tr><td>B  </td><td>F  </td><td>2 </td><td>5 </td><td>4 </td><td>   </td></tr>\\n<tr><td>C  </td><td>G  </td><td>  </td><td>  </td><td>3 </td><td>   </td></tr>\\n<tr><td>D  </td><td>H  </td><td>  </td><td>  </td><td>  </td><td>1  </td></tr>\\n</tbody>\\n</table>\",\"languages\":[\"deu\"],\"filetype\":\"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"},\"text\":\"A  E           3\\nB  F  2  5  4\\nC  G        3\\nD  H           1\"} " +
                    ",{\"type\":\"Table\",\"element_id\":\"778febfb07e07a7911ea8700cea1aeaf\",\"metadata\":{\"filename\":\"7ccdded5-d494-46e1-9f8e-39c98ff5040f17482659314668280188.docx\",\"text_as_html\":\"<table>\\n<thead>\\n<tr><th>A  </th><th>E  </th><th>  </th><th>  </th><th>  </th><th>3  </th></tr>\\n</thead>\\n<tbody>\\n<tr><td>B  </td><td>F  </td><td>2 </td><td>5 </td><td>4 </td><td>   </td></tr>\\n<tr><td>C  </td><td>G  </td><td>  </td><td>  </td><td>3 </td><td>   </td></tr>\\n<tr><td>D  </td><td>H  </td><td>  </td><td>  </td><td>  </td><td>1  </td></tr>\\n</tbody>\\n</table>\",\"languages\":[\"deu\"],\"filetype\":\"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"},\"text\":\"A  E           3\\nB  F  2  5  4\\nC  G        3\\nD  H           1\"}]";
            Mockito.when(restTemplate.postForObject(any(String.class), any(HttpEntity.class), any()))
                    .thenReturn(response);

            StreamEx<Optional<NexlaMessage>> result = parser.parseMessages(inputStream);
            List<Optional<NexlaMessage>> actual = result.collect(Collectors.toList());
            Assert.assertEquals(actual.size(), 2);
        }
    }
}
