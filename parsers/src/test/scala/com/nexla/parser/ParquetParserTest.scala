package com.nexla.parser

import org.scalatest.{Ignore, TagAnnotation}

import java.util
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._
// TODO(bryan) something about the jvm version on <PERSON> is causing a very very weird set of errors in this test file,
// so i'm disabling it for now.
@Ignore
@TagAnnotation("com.nexla.test.ScalaUnitTests")
class ParquetParserTest extends AnyFunSuite with Matchers {

  val useDecimalAsDate = false

  test("readLines") {
    val inputStream = TarTest.getResourceAsStreaz(this.getClass(), "users.parquet")
    val lines = new ParquetParser(useDecimalAsDate).readLines(inputStream)
    val actual = lines.iterator().asScala.toArray

    lines.close()
    inputStream.close()

    actual shouldEqual
      Array(
        "{\"name\": \"Alyssa\", \"favorite_color\": null, \"favorite_numbers\": [3, 9, 15, 20]}",
        "{\"name\": \"Ben\", \"favorite_color\": \"red\", \"favorite_numbers\": []}")
  }

  test("readLines with int96 date") {
    val inputStream = TarTest.getResourceAsStreaz(this.getClass(), "temp.parquet")
    val lines = new ParquetParser(true).readLines(inputStream)
    val actual = lines.iterator().asScala.toArray

    lines.close()
    inputStream.close()
    val parsedDate = JsonUtils.convertToMap(actual(0)).get("date_of_birth")
    assert(parsedDate.toString == "2024-07-11 00:00:00.000")
  }

  test("readLines with int96 date and null inside") {
    val inputStream = TarTest.getResourceAsStreaz(this.getClass(), "nullts.parquet")
    val lines = new ParquetParser(true).readLines(inputStream)
    val actual = lines.iterator().asScala.toArray

    lines.close()
    inputStream.close()
    val parsedDate = JsonUtils.convertToMap(actual(0)).get("date_of_birth")
    assert(parsedDate == null)
  }

  test("readLinesFromZip") {
    val inputStream = TarTest.getResourceAsStreaz(this.getClass(), "users.zip")
    val zipParser = new ZipParserUtils()
    val lines = zipParser.readLines(inputStream)
    val actual = lines.iterator().asScala.toArray

    lines.close()
    inputStream.close()

    actual shouldEqual
      Array(
        "{\"name\": \"Alyssa\", \"favorite_color\": null, \"favorite_numbers\": [3, 9, 15, 20]}",
        "{\"name\": \"Ben\", \"favorite_color\": \"red\", \"favorite_numbers\": []}",
        "{\"name\": \"Alyssa\", \"favorite_color\": null, \"favorite_numbers\": [3, 9, 15, 20]}",
        "{\"name\": \"Ben\", \"favorite_color\": \"red\", \"favorite_numbers\": []}")

  }

  test("checkSchema") {
    val inputStream = TarTest.getResourceAsStreaz(this.getClass(), "users.parquet")
    val parquetParser = new ParquetParser(useDecimalAsDate)
    val lines = parquetParser.readLines(inputStream)
    val schema = parquetParser.getSchema

    lines.close()
    inputStream.close()

    val props = schema.getProperties.asInstanceOf[util.LinkedHashMap[String, util.LinkedHashMap[String, AnyRef]]]
    props.get("favorite_color").get("type") shouldEqual "string"
    props.get("favorite_numbers").get("type") shouldEqual "object"
    props.get("name").get("type") shouldEqual "string"
  }
}
