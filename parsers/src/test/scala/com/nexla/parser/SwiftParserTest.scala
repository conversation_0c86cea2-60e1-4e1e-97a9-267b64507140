package com.nexla.parser

import com.nexla.common.parse.ParserConfigs.Swift._
import org.scalatest.OneInstancePerTest
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers

import java.io.InputStream
import scala.collection.JavaConverters._

class SwiftParserTest extends AnyFunSuite with Matchers with OneInstancePerTest {

  test("parse messages in normal mode") {
    val inputStream = TarTest.getResourceAsStreaz(this.getClass(), "swift_sample_1.OUT")

    val parser = new SwiftParser()

    val expectedResult = List(
      Map("applicationId" -> "F", "serviceId" -> "01", "logicalTerminal" -> "CWANUS55AXXX", "sessionNumber" -> "3715", "sequenceNumber" -> "611087"),
      Map("senderInputTime" -> "1832", "MIRDate" -> "221130", "MIRLogicalTerminal" -> "SBOSUS3UBIMS", "MIRSessionNumber" -> "1397", "MIRSequenceNumber" -> "654810", "receiverOutputDate" -> "221130", "receiverOutputTime" -> "1633", "messagePriority" -> "N", "messageType" -> "940", "blockType" -> "O", "direction" -> "O"),
      Map("20" -> "2211300000036214"),
      Map("25" -> "AYFN 00000000 GBP"),
      Map("28C" -> "238/1"),
      Map("60F" -> "D221130GBP335053,"),
      Map("61" -> "2211291130CP33798,NMSCNONREF//TR-MT2233313316\n/ACOM/UNADVISED CASH POSTING"),
      Map("61" -> "2211301130CP58225,65NTRFARG420221130P//22KUKBBQ01T\n/US/000000000/SHS/0,"),
      Map("86" -> "/NEW MONEY CAPSTOCK"),
      Map("62F" -> "D221130GBP243029,35"),
      Map("64" -> "D221130GBP243029,35"),
      Map("CHK" -> "DED1D81AC0F0")
    )

    val actualResult = readMessages(inputStream, parser)
    
    val normalizedExpectedResult = expectedResult.map(map => map.map {
      case (key, value: String) => key -> normalizeNewlines(value)
      case other => other
    })

    val normalizedActualResult = actualResult.map(map => map.map {
      case (key, value: String) => key -> normalizeNewlines(value)
      case other => other
    })

    normalizedActualResult shouldBe normalizedExpectedResult
  }

  test("parse messages with disabled text block parsing") {
    val inputStream = TarTest.getResourceAsStreaz(this.getClass(), "swift_sample_1.OUT")

    val parser = new SwiftParser()
      .option(PARSE_TEXT_BLOCK, "false")

    val expectedResult = List(
      Map("applicationId" -> "F", "serviceId" -> "01", "logicalTerminal" -> "CWANUS55AXXX", "sessionNumber" -> "3715", "sequenceNumber" -> "611087"),
      Map("senderInputTime" -> "1832", "MIRDate" -> "221130", "MIRLogicalTerminal" -> "SBOSUS3UBIMS", "MIRSessionNumber" -> "1397", "MIRSequenceNumber" -> "654810", "receiverOutputDate" -> "221130", "receiverOutputTime" -> "1633", "messagePriority" -> "N", "messageType" -> "940", "blockType" -> "O", "direction" -> "O"),
      Map("CHK" -> "DED1D81AC0F0")
    )

    val actualResult = readMessages(inputStream, parser)

    val normalizedExpectedResult = expectedResult.map(map => map.map {
      case (key, value: String) => key -> normalizeNewlines(value)
      case other => other
    })

    val normalizedActualResult = actualResult.map(map => map.map {
      case (key, value: String) => key -> normalizeNewlines(value)
      case other => other
    })

    normalizedActualResult shouldBe normalizedExpectedResult
  }

  private def readMessages(inputStream: InputStream, parser: SwiftParser, charset: String = "UTF-8") = {
    parser.parseMessages(inputStream).iterator().asScala
      .filter(_.isPresent)
      .map(_.get)
      .toList
      .map(_.getRawMessage.asScala)
  }

  def normalizeNewlines(str: String): String = str.replace("\r\n", "\n")

}
