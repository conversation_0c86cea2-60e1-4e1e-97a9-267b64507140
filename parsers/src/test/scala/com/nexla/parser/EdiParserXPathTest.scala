package com.nexla.parser

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import org.scalatest.{OneInstancePerTest, TagAnnotation}
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class EdiParserXPathTest
  extends AnyFunSuite
    with Matchers
    with OneInstancePerTest {

  private val mapper = new ObjectMapper().registerModule(DefaultScalaModule).writerWithDefaultPrettyPrinter()

  val filePath = "/edi/214.x12"

  test(s"parse edi with options from file $filePath") {

    val inputStream = TarTest.getResourceAsStreaz(this.getClass(), filePath)
    try {
      val parsed = new EdiParser().option("edi.xpath", "/X12/TS_214").parseMessages(inputStream).iterator().asScala.toList
      mapper.writeValueAsString(parsed.map(_.get).map(_.getRawMessage)).split('\n').length shouldEqual 189
    } finally {
      inputStream.close()
    }
  }
}
