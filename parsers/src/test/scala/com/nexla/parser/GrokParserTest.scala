package com.nexla.parser

import java.io.ByteArrayInputStream
import java.util
import java.util.Optional
import com.nexla.common.NexlaMessage
import com.nexla.common.parse.ParserConfigs.Grok.GROK_PATTERN
import org.scalatest.{OneInstancePerTest, TagAnnotation}
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class GrokParserTest extends AnyFunSuite with Matchers with OneInstancePerTest {

  private def parser = new GrokParser

  test("parse jsonPath: map") {

    val p = parser.option(GROK_PATTERN, "%{TIME}")
    val input = "112.169.19.192 - - [06/Mar/2013 01:35:30 +0900] \"GET / HTTP/1.1\" 200 44346 \"-\" \"Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_8_2) AppleWebKit/537.22 (KHT<PERSON>, like Gecko) Chrome/25.0.1364.152 Safari/537.22\"\n" +
      "112.169.19.192 - - [06/Mar/2013 01:36:30 +0900] \"GET / HTTP/1.1\" 200 44346 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_2) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.152 Safari/537.22\"\n" +
      "112.169.19.192 - - [06/Mar/2013 01:37:30 +0900] \"GET / HTTP/1.1\" 200 44346 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_2) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.152 Safari/537.22\"";

    val stream = new ByteArrayInputStream(input.getBytes)
    val result = p.parseMessages(stream).toList

    val exp1 = new util.LinkedHashMap[String, AnyRef](Map("HOUR" -> "01", "TIME" -> "01:35:30", "MINUTE" -> "35", "SECOND" -> "30").asJava)
    val exp2 = new util.LinkedHashMap[String, AnyRef](Map("HOUR" -> "01", "TIME" -> "01:36:30", "MINUTE" -> "36", "SECOND" -> "30").asJava)
    val exp3 = new util.LinkedHashMap[String, AnyRef](Map("HOUR" -> "01", "TIME" -> "01:37:30", "MINUTE" -> "37", "SECOND" -> "30").asJava)

    val expected = List(
      Optional.of(new NexlaMessage(exp1)),
      Optional.of(new NexlaMessage(exp2)),
      Optional.of(new NexlaMessage(exp3))).asJava

    result shouldBe expected
  }

}
