package com.nexla.parser.xml

import org.scalatest.TagAnnotation

import java.io.StringReader
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class DomParserTest extends AnyFlatSpecLike with Matchers {

  behavior of "DomParserTest"

  it should "parse int as string" in {
    val data = "<result><key1>123</key1><key2>1586E2</key2></result>"
    val messageStream = DomParser.parse(new StringReader(data), "/result")
    val actual = messageStream.iterator().asScala.toList.map(_.asScala)

    actual shouldBe List(
      Map("result" ->
        Map(
          "key1" -> "123",
          "key2" -> "1586E2").asJava)
    )
  }
}
