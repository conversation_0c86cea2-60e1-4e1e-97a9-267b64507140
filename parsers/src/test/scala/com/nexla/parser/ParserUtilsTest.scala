package com.nexla.parser

import com.nexla.admin.client.DataCredentials.NonConnectionTypeCredentialType
import com.nexla.admin.client._
import com.nexla.common.NexlaConstants.{CLIENT_SSL_ENABLED, SSL_KAFKA_ENABLED}
import com.nexla.common.{ConnectionType, NexlaConstants}
import com.nexla.common.parse.ParserConfigs.Csv._
import com.nexla.connector.ConnectorService.UNIT_TEST
import com.nexla.connector.config.file.FileSourceConnectorConfig.{BATCH_ENABLED, BATCH_ROWS}
import com.nexla.connector.config.file.FtpConstants.{FTP_MODE, PASSWORD, PORT, USERNAME}
import com.nexla.parser.ParserUtils.getParserByFormat
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{mock, when}
import org.scalatest.{BeforeAndAfterEach, TagAnnotation}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import java.util
import java.util.{Base64, Collections, Optional}
import scala.collection.JavaConverters._
import scala.language.implicitConversions

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class ParserUtilsTest extends AnyFlatSpecLike with Matchers with BeforeAndAfterEach {

  implicit def toJavaMap[K, V](scalaMap: Map[K, V]): util.Map[K, V] = scalaMap.asJava
  private val mockAdminApiClient: AdminApiClient = mock(classOf[AdminApiClient])
  private val testEncryptionKey = "key-to-the-kingdom"

  private val config = Map(
    // help us keep the test logs quiet
    SSL_KAFKA_ENABLED -> "false",
    CLIENT_SSL_ENABLED -> "false"
  )

  override protected def beforeEach(): Unit = {
    val testDataSource = new DataSource()
    testDataSource.setId(1)
    testDataSource.setCodeContainerId(10)

    when(mockAdminApiClient.getDataSource(any())).thenReturn(Optional.ofNullable(testDataSource))
    when(mockAdminApiClient.getCodeContainer(any())).thenReturn(new CodeContainer(1, "python", null, "efficient and beautiful code", null, Optional.empty()))
  }

  behavior of "ParserUtils"

  it should "override defaults" in {

    // default
    val value1 = getParserByFormat(Optional.empty(), "text.csv", Map.empty[String, String], config)
    value1.get() should
      matchPattern { case p: DelimitedTextParser => }

    // overridden
    val config2 = Map(CSV_DELIMITER -> ";",
      SSL_KAFKA_ENABLED -> "false",
      CLIENT_SSL_ENABLED -> "false")

    getParserByFormat(Optional.empty(),"text.csv", Map.empty[String, String], config2).get() should
      matchPattern { case p: DelimitedTextParser => }
  }

  it should "override extensions" in {

    // default
    getParserByFormat(Optional.empty(),"text.dat", Map.empty[String, String], config).get() should
      matchPattern { case _: EdiParser => }

    // overridden
    getParserByFormat(Optional.empty(),"text.dat", Map("dat" -> "csv"), config).get() should
      matchPattern { case _: DelimitedTextParser => }

    getParserByFormat(Optional.empty(),"text.dat", Map("*" -> "csv"), config).get() should
      matchPattern { case _: DelimitedTextParser => }
  }

  it should "override extensions even when MIME type is present" in {
    getParserByFormat(Optional.of("text/csv"), "text.csv", Map("*" -> "fw"), config).get() should
      matchPattern { case _: FixedWidthParser => }
  }

  it should "override extensions for zip" in {
    getParserByFormat(Optional.of("application/zip"), "test.dat.zip", Map("dat" -> "csv"), config).get() should matchPattern {
      case zip: ZipParserUtils if zip.overriddenExtensions.asScala == Map("dat" -> "csv") =>
    }
    getParserByFormat(Optional.empty(), "test.dat.zip", Map("dat" -> "csv"), config).get() should matchPattern {
      case zip: ZipParserUtils if zip.overriddenExtensions.asScala == Map("dat" -> "csv") =>
    }
  }

  it should "ignore overrides when root is archive type" in {
    getParserByFormat(Optional.of("application/zip"), "content.zip", Map("*" -> "json"), config)
      .get() should matchPattern { case _: ZipParserUtils => }
    getParserByFormat(Optional.of("application/gzip"), "content.gz", Map("*" -> "json"), config)
      .get() should matchPattern { case _: GzipParser => }
    getParserByFormat(Optional.of("application/x-tar"), "content.tar", Map("*" -> "json"), config)
      .get() should matchPattern { case _: TarParser => }
  }

  it should "ignore overrides when file is pgp encrypted" in {
    getParserByFormat(Optional.empty(), "content.csv.pgp", Map("*" -> "json"), getPgpFileSourceConfigMap("content.csv.pgp", batchEnabled = false, 22))
      .get() should matchPattern { case _: AscParser => }
  }

  it should "ignore case when determining file extensions from filename" in {
    getParserByFormat(Optional.empty(), "CONTENT.ZIP", Map.empty[String, String], config)
      .get() should matchPattern { case _: ZipParserUtils => }
  }

  "createCustomParser" should "return empty parser if code container has no code" in {
    ParserUtils.createCustomParser(new CodeContainer(1, "python", "", "", null, Optional.empty()), Collections.emptyMap(), Collections.emptyMap()) shouldEqual Optional.empty()
    ParserUtils.createCustomParser(new CodeContainer(1, "python", "", null, null, Optional.empty()), Collections.emptyMap(), Collections.emptyMap()) shouldEqual Optional.empty()
  }

  it should "return a javascript parser" in {
    val metadata: util.Map[String, Object] = util.Map.of("key", "value")
    val parser =ParserUtils.createCustomParser(new CodeContainer(1, "javascript", null, "verygoodcode", null, Optional.empty()), metadata, Collections.emptyMap())
    parser.get() shouldBe a[JavaScriptCustomParser]
    val jsParser = parser.get().asInstanceOf[JavaScriptCustomParser]
    jsParser.getScript shouldEqual "verygoodcode"
    jsParser.getMetadata shouldEqual metadata
  }

  it should "return a python parser" in {
    val parser = ParserUtils.createCustomParser(new CodeContainer(1, "python", "", "verygoodcode", null, Optional.empty()), Collections.emptyMap(), Collections.emptyMap())
    parser.get() shouldBe a[PythonCustomParser]
    val pythonParser = parser.get().asInstanceOf[PythonCustomParser]
    pythonParser.getScript shouldEqual "verygoodcode"

    val parserWithEncodedScript = ParserUtils.createCustomParser(new CodeContainer(1, "python", "base64", new String(Base64.getEncoder.encode("somemoreverygoodcode".getBytes())), null, Optional.empty()), Collections.emptyMap(), Collections.emptyMap())
    val pythonParserWithEncodedScript = parserWithEncodedScript.get().asInstanceOf[PythonCustomParser]
    pythonParserWithEncodedScript.getScript shouldEqual "somemoreverygoodcode"
  }

  "createCustomParser with source" should "decrypt data credentials" in {
    val expectedSecretArgs = util.Map.of("key 1", "secret value 1", "key 2", "secret value 2")
    val testDataCredentials = new DataCredentials
    testDataCredentials.setId(100)
    testDataCredentials.setCredentialsType(NonConnectionTypeCredentialType.GENERIC)
    testDataCredentials.setCredentialsEnc("bFnnvQBFDhomRPUM3tsk2QL/B5F61E3ikG4flRg6M/LKPcwPA/DD22claaz+hVfQRnGqucbhAbKRHqau4taQaNvYdQ==")
    testDataCredentials.setCredentialsEncIv(new String(Base64.getEncoder.encode(testEncryptionKey.getBytes)))
    when(mockAdminApiClient.getCodeContainer(any())).thenReturn(new CodeContainer(1, "python", null, "efficient and beautiful code", new RuntimeDataCredentials(100, null, null, null, null), Optional.empty()))
    when(mockAdminApiClient.getDataCredentials(any())).thenReturn(Optional.of(testDataCredentials))

    val parser = ParserUtils.createCustomParser(mockAdminApiClient, 1, Collections.emptyMap(),
      new String(util.Arrays.copyOf(testEncryptionKey.getBytes, 16)))
    val pythonParser = parser.get().asInstanceOf[PythonCustomParser]
    pythonParser.getArgs shouldEqual expectedSecretArgs
  }

  it should "return empty container if not supported language" in {
    when(mockAdminApiClient.getCodeContainer(any())).thenReturn(new CodeContainer(1, "golang", null, "efficient and beautiful code", null, Optional.empty()))
    val parser = ParserUtils.createCustomParser(mockAdminApiClient, 1, Collections.emptyMap(),
      new String(util.Arrays.copyOf(testEncryptionKey.getBytes, 16)))
    parser shouldEqual Optional.empty()
  }

  it should "set other fields of parser" in {
    val parser = ParserUtils.createCustomParser(mockAdminApiClient, 1, Collections.emptyMap(),
      new String(util.Arrays.copyOf(testEncryptionKey.getBytes, 16)))
    val pythonParser = parser.get().asInstanceOf[PythonCustomParser]
    pythonParser.getMetadata shouldEqual Collections.emptyMap()
    pythonParser.getScript shouldEqual "efficient and beautiful code"

    val testMetadata: util.Map[String,Object] = util.Map.of("filepath", "abcedfg")
    val parserWithMetadata = ParserUtils.createCustomParser(mockAdminApiClient, 1, testMetadata,
      new String(util.Arrays.copyOf(testEncryptionKey.getBytes, 16)))
    val pythonParserWithMetadata = parserWithMetadata.get().asInstanceOf[PythonCustomParser]
    pythonParserWithMetadata.getMetadata shouldEqual testMetadata
    pythonParserWithMetadata.getScript shouldEqual "efficient and beautiful code"
  }

  it should "return empty parser if code container is not on data source" in {
    when(mockAdminApiClient.getDataSource(any())).thenReturn(Optional.ofNullable(new DataSource))
    val parser = ParserUtils.createCustomParser(mockAdminApiClient, 1, Collections.emptyMap(),
      new String(util.Arrays.copyOf(testEncryptionKey.getBytes, 16)))
    parser shouldEqual Optional.empty()
  }

  private def getPgpFileSourceConfigMap(path: String,
                           batchEnabled: Boolean,
                           port: Int) = Map(
    NexlaConstants.SOURCE_ID -> "1",
    NexlaConstants.CREDS_ENC -> "1",
    NexlaConstants.CREDS_ENC_IV -> "1",
    NexlaConstants.CREDENTIALS_DECRYPT_KEY -> "1",
    NexlaConstants.PATH -> path,
    NexlaConstants.SOURCE_TYPE -> ConnectionType.FTP.name(),
    NexlaConstants.MONITOR_POLL_MS -> "1",
    BATCH_ENABLED -> batchEnabled.toString,
    BATCH_ROWS -> "1",
    NexlaConstants.LISTING_ENABLED -> "false",
    FTP_MODE -> "passive.local",
    USERNAME -> "user",
    PASSWORD -> "password",
    PORT -> port.toString,
    UNIT_TEST -> "true",
    "encrypt.standard" -> "pgp",
    "encrypt.private.key" -> "privateKey",
    "external.public.key" -> "publicKey",
    "encrypt.private.password" -> "password",
    "external.user.id" -> "externalUser",
    "encrypt.user.id" -> "internalUser",
    SSL_KAFKA_ENABLED -> "false",
    CLIENT_SSL_ENABLED -> "false"
  )
}