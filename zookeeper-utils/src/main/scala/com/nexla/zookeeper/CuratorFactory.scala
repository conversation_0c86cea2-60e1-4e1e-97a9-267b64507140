package com.nexla.zookeeper

import com.typesafe.scalalogging.StrictLogging
import org.apache.curator.framework.{CuratorFramework, CuratorFrameworkFactory}
import org.apache.curator.retry.ExponentialBackoffRetry

class CuratorFactory(zookeeperConnect: String)
  extends StrictLogging {

  /**
    * Creates new [[org.apache.curator.framework.CuratorFramework]] instance
    * and register shutdown hook for it.
    * Don't try to close provided client directly.
    */
  def newClient(namespace: String): CuratorFramework = {
    logger.info(s"Creating curator client to $zookeeperConnect namespace=$namespace")

    val retries = new ExponentialBackoffRetry(500, 5)

    val curatorBase = CuratorFrameworkFactory.newClient(
      zookeeperConnect,
      retries)

    curatorBase.start()

    val terminator = new CuratorTerminator(curatorBase, namespace)

    Runtime.getRuntime
      .addShutdownHook(new Thread(terminator, "curator-terminator"))

    curatorBase.usingNamespace(namespace)
  }
}
