package com.nexla.zookeeper

import java.util.concurrent.TimeUnit

import org.apache.curator.framework.CuratorFramework
import org.apache.curator.framework.recipes.locks.InterProcessSemaphoreMutex

object Locks {

  def createLock(path: String)
                (implicit curator: CuratorFramework): InterProcessSemaphoreMutex = {
    new InterProcessSemaphoreMutex(curator, path)
  }

  def tryLock(lock: InterProcessSemaphoreMutex)
             (implicit curator: CuratorFramework): Boolean = lock.acquire(10, TimeUnit.MILLISECONDS)

  def lock(lock: InterProcessSemaphoreMutex)(implicit curator: CuratorFramework) = lock.acquire()

  def release(lock: InterProcessSemaphoreMutex)
             (implicit curator: CuratorFramework): Unit = lock.release()
}

class Locks {

  def createLock(path: String)(implicit curator: CuratorFramework): InterProcessSemaphoreMutex = Locks.createLock(path)

  def tryLock(lock: InterProcessSemaphoreMutex)(implicit curator: CuratorFramework): Boolean = Locks.tryLock(lock)

  def lock(lock: InterProcessSemaphoreMutex)(implicit curator: CuratorFramework) = Locks.lock(lock)

  def release(lock: InterProcessSemaphoreMutex)(implicit curator: CuratorFramework): Unit = Locks.release(lock)
}
