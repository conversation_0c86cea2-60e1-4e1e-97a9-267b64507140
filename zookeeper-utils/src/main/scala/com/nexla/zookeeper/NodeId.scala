package com.nexla.zookeeper

import java.util.UUID

import org.apache.curator.framework.CuratorFramework
import org.apache.zookeeper.CreateMode

import scala.collection.JavaConverters._

class NodeId(implicit curator: CuratorFramework) {

  val instanceId = UUID.randomUUID()

  def getActiveNodes =
    curator
      .getChildren
      .forPath("/nodes")
      .asScala
      .sorted
      .map(UUID.fromString)
      .toList

}

object NodeId {

  def newNode(implicit curator: CuratorFramework) = {
    val nodeId = new NodeId()

    curator
      .create()
      .creatingParentsIfNeeded()
      .withMode(CreateMode.EPHEMERAL)
      .forPath(s"/nodes/${nodeId.instanceId}")

    nodeId
  }

}