package com.nexla.zookeeper

import org.apache.curator.framework.CuratorFramework
import org.slf4j.LoggerFactory

/**
  * Terminates [[CuratorFramework]] by closing it.
  */
class CuratorTerminator(closableCurator: CuratorFramework,
                        namespace: String)
  extends Runnable {

  private val log = LoggerFactory.getLogger(getClass)

  def run(): Unit = {
    log.info(s"Close curator with namespace $namespace")
    try {
      closableCurator.close()
      log.info(
        s"Successfully close curator with namespace $namespace")
    }
    catch {
      case e: Exception =>
        log.error(
          s"Error while close curator with namespace $namespace", e)
    }
  }
}