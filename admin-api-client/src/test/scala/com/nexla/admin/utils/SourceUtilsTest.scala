package com.nexla.admin.utils

import com.nexla.admin.client.{DataSet, DataSink, DataSource, FlowTrigger, ResourceStatus, WithClassVersion}
import com.nexla.common.StreamUtils
import org.joda.time.LocalDateTime
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class SourceUtilsTest extends AnyFlatSpecLike with Matchers {
  it should "be equals to datasource after serialization/deserialization" in {
    val dataSource = new DataSource
    dataSource.setId(1)
    dataSource.setDataSinkId(2);
    dataSource.setSourceUrl("url")
    dataSource.setCreatedAt(LocalDateTime.now())
    dataSource.setCodeContainerId(3)
    dataSource.setVersion(4)

    val jsonMap = SourceUtils.toResourceDto(dataSource)

    val str = StreamUtils.jsonUtil().toJsonString(jsonMap)
    val deserializedDataSource = StreamUtils.jsonUtil().stringToType(str, classOf[DataSource])

    dataSource shouldEqual deserializedDataSource
  }

  it should "be equals to datasource after serialization/deserialization including FlowTriggers" in {
    val ft1 = new FlowTrigger
    ft1.setId(123)
    ft1.setStatus(ResourceStatus.ACTIVE)

    val ft2 = new FlowTrigger
    ft2.setId(456)
    ft2.setStatus(ResourceStatus.INIT)

    val dataSource = new DataSource
    dataSource.setId(1)
    dataSource.setDataSinkId(2);
    dataSource.setSourceUrl("url")
    dataSource.setCreatedAt(LocalDateTime.now())
    dataSource.setCodeContainerId(3)
    dataSource.setVersion(4)
    dataSource.setFlowTriggers(List(ft1, ft2).asJava)

    val jsonMap = SourceUtils.toResourceDto(dataSource)

    val str = StreamUtils.jsonUtil().toJsonString(jsonMap)
    val deserializedDataSource = StreamUtils.jsonUtil().stringToType(str, classOf[DataSource])

    dataSource shouldEqual deserializedDataSource
  }

  it should "serialize/deserialize class version" in {
    val dataSource = new DataSource
    dataSource.setId(1)
    dataSource.setObjectVersion(10)

    val jsonMap = SourceUtils.toResourceDto(dataSource)

    val str = StreamUtils.jsonUtil().toJsonString(jsonMap)
    val deserializedDataSource = StreamUtils.jsonUtil().stringToType(str, classOf[DataSource])

    dataSource shouldEqual deserializedDataSource
    deserializedDataSource.getObjectVersion shouldEqual 10
    jsonMap.get(WithClassVersion.OBJECT_VERSION_JSON_PROPERTY) shouldEqual 10
    jsonMap.size() shouldEqual 3
  }

  it should "deserialize -1 when there is no version" in {
    val dataSource = new DataSource
    dataSource.setId(1)

    val jsonMap = SourceUtils.toResourceDto(dataSource)

    val str = StreamUtils.jsonUtil().toJsonString(jsonMap)
    val deserializedDataSource = StreamUtils.jsonUtil().stringToType(str, classOf[DataSource])

    dataSource shouldEqual deserializedDataSource
    deserializedDataSource.getObjectVersion shouldEqual -1
  }

  it should "parse resourceJson ONLY when object version is bigger or equal to class version (datasource)" in {
    val ds1 = new DataSource()
    ds1.setId(1)
    ds1.setObjectVersion(DataSource.CLASS_VERSION)     // Same as current version
    val jsonMap1 = SourceUtils.toResourceDto(ds1)

    val ds2 = new DataSource()
    ds2.setId(2)
    ds2.setObjectVersion(DataSource.CLASS_VERSION + 1)   // Next version
    val jsonMap2 = SourceUtils.toResourceDto(ds2)

    val parsed1 = SourceUtils.dtoToResourceChecked(jsonMap1, classOf[DataSource])
    parsed1 shouldEqual ds1

    val parsed2 = SourceUtils.dtoToResourceChecked(jsonMap2, classOf[DataSource])
    parsed2 should be(null)
  }

  it should "parse resourceJson ONLY when object version is bigger or equal to class version (datasink)" in {
    val ds1 = new DataSink()
    ds1.setId(1)
    ds1.setObjectVersion(DataSink.CLASS_VERSION)     // Same as current version
    val jsonMap1 = SourceUtils.toResourceDto(ds1)

    val ds2 = new DataSink()
    ds2.setId(2)
    ds2.setObjectVersion(DataSink.CLASS_VERSION + 1)   // Next version
    val jsonMap2 = SourceUtils.toResourceDto(ds2)

    val parsed1 = SourceUtils.dtoToResourceChecked(jsonMap1, classOf[DataSink])
    parsed1 shouldEqual ds1

    val parsed2 = SourceUtils.dtoToResourceChecked(jsonMap2, classOf[DataSink])
    parsed2 should be(null)
  }

  it should "parse resourceJson ONLY when object version is bigger or equal to class version (dataset)" in {
    val ds1 = new DataSet()
    ds1.setId(1)
    ds1.setObjectVersion(DataSet.CLASS_VERSION)     // Same as current version
    val jsonMap1 = SourceUtils.toResourceDto(ds1)

    val ds2 = new DataSet()
    ds2.setId(2)
    ds2.setObjectVersion(DataSet.CLASS_VERSION + 1)   // Next version
    val jsonMap2 = SourceUtils.toResourceDto(ds2)

    val parsed1 = SourceUtils.dtoToResourceChecked(jsonMap1, classOf[DataSet])
    parsed1 shouldEqual ds1

    val parsed2 = SourceUtils.dtoToResourceChecked(jsonMap2, classOf[DataSet])
    parsed2 should be(null)
  }
}
