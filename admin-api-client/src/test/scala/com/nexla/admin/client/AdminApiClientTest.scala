package com.nexla.admin.client

import com.bazaarvoice.jolt.JsonUtils
import com.nexla.common.StreamUtils.lhm
import com.nexla.common.{NexlaMessage, NexlaMetaData, StreamUtils}
import com.nexla.telemetry.Telemetry
import edu.emory.mathcs.backport.java.util.concurrent.atomic.AtomicInteger
import okhttp3.mockwebserver.{MockResponse, MockWebServer, RecordedRequest}
import org.scalatest.{BeforeAndAfterAll, TagAnnotation}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.springframework.http.HttpStatus
import org.springframework.web.client.{HttpClientErrorException, RestTemplate}

import java.io.IOException
import java.util.{Optional, concurrent}
import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class AdminApiClientTest extends AnyFlatSpecLike with Matchers with BeforeAndAfterAll {
  var mockHttpServer: MockWebServer = _
  var webhookUrl: String = _

  override protected def beforeAll(): Unit = {
    super.beforeAll()
    mockHttpServer = new MockWebServer
    mockHttpServer.start()
    webhookUrl = mockHttpServer.url("/test/adminapi").toString
  }

  override protected def afterAll(): Unit = {
    super.afterAll()
    mockHttpServer.shutdown()
  }

  "putDataSetSamples" should "send null values" in {
    val adminApiClientBuilder = new AdminApiClientBuilder()
    adminApiClientBuilder.setAppName("unittest")
    adminApiClientBuilder.setEnrichmentUrl("")
    val client = adminApiClientBuilder.create(webhookUrl, "", new RestTemplate())
    val msg1 = new NexlaMessage(lhm("placeholder", null, "title", "iamlegend"), new NexlaMetaData())
    mockHttpServer.enqueue(new MockResponse().setHeader("Content-Type","application/json").setBody("{}"))
    mockHttpServer.enqueue(new MockResponse().setBody("ok"))

    client.putDataSetSamples(List(msg1).asJava, 1)
    mockHttpServer.takeRequest()
    val putDataSetSampleRequest: RecordedRequest = mockHttpServer.takeRequest()
    val body: String = putDataSetSampleRequest.getBody.readUtf8()
    val result = JsonUtils.jsonToList(body).get(0).asInstanceOf[java.util.Map[String, AnyRef]]
    val expectedRawMessage = Map("placeholder" -> null, "title" -> "iamlegend").asJava

    result.get("rawMessage") should equal(expectedRawMessage)
    result.get("nexlaMetaData") should not be null
    result.get("nexlaMetaData").isInstanceOf[java.util.Map[String, AnyRef]] shouldEqual true
    val deserializedMetadata = result.get("nexlaMetaData").asInstanceOf[java.util.Map[String, AnyRef]]
    deserializedMetadata.containsKey("sourceType") shouldEqual true
    deserializedMetadata.get("sourceType") shouldEqual null
  }

  it should "Retry and throw the original checked exception" in {
    val counter = new AtomicInteger(0)
    val callableException = new concurrent.Callable[Integer] {
      override def call(): Integer = {
        counter.incrementAndGet()
        throw new IOException("Artificial")
      }
    }
    the[IOException] thrownBy AdminApiClient.retryIfException(callableException, 5, 1) should have message "Artificial"
    counter.get shouldEqual 5
  }

  it should "Retry and throw the original unchecked exception" in {
    val counter = new AtomicInteger(0)
    val callableException = new concurrent.Callable[Integer] {
      override def call(): Integer = {
        counter.incrementAndGet()
        throw new IllegalStateException("Artificial")
      }
    }
    the[IllegalStateException] thrownBy AdminApiClient.retryIfException(callableException, 5, 1) should have message "Artificial"
    counter.get shouldEqual 5
  }

  it should "Don't retry and throw the original NotFound exception" in {
    val counter = new AtomicInteger(0)
    val callableException = new concurrent.Callable[Integer] {
      override def call(): Integer = {
        counter.incrementAndGet()
        throw HttpClientErrorException.create(HttpStatus.NOT_FOUND, null, null, null, null)
      }
    }
    the[HttpClientErrorException.NotFound] thrownBy AdminApiClient.retryIfException(callableException, 5, 1)
    counter.get shouldEqual 1
  }

  it should "Retry once and recover from the exception" in {
    val counter = new AtomicInteger(0)
    val callableException = new concurrent.Callable[Integer] {
      override def call(): Integer = {
        if (counter.getAndIncrement() == 0) {
          throw new IOException("Artificial")
        }
        counter.get()
      }
    }
    AdminApiClient.retryIfException(callableException, 5, 1) shouldEqual 2
  }

  it should "filter objectVersion" in {
    val adminApiClientBuilder = new AdminApiClientBuilder()
    adminApiClientBuilder.setAppName("unittest")
    adminApiClientBuilder.setEnrichmentUrl("")
    val client = adminApiClientBuilder.create(webhookUrl, "", new RestTemplate())

    val objStr = "{\"description\":\"DataSet #1 detected from 10\",\"name\":\"10/10 [Sheet1]\",\"source_schema\":{\"type\":\"object\",\"properties\":{\"f1\":{\"type\":\"string\"},\"f2\":{\"format\":\"integer\",\"type\":\"string\"},\"f3\":{\"format\":\"integer\",\"type\":\"string\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"$schema-id\":1563356745},\"data_source_id\":12730,\"objectVersion\":-1}"

    val dataSet = StreamUtils.jsonUtil().stringToType(objStr, classOf[DataSet])

    val objStrFiltered = client.writeDataSetAsStringForAdminApi(dataSet)

    objStrFiltered shouldBe "{\"description\":\"DataSet #1 detected from 10\",\"name\":\"10/10 [Sheet1]\",\"source_schema\":{\"type\":\"object\",\"properties\":{\"f1\":{\"type\":\"string\"},\"f2\":{\"format\":\"integer\",\"type\":\"string\"},\"f3\":{\"format\":\"integer\",\"type\":\"string\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"$schema-id\":1563356745},\"data_source_id\":12730}"
  }

}
