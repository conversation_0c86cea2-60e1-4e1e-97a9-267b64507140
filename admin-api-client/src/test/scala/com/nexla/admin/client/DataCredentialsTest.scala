package com.nexla.admin.client

import com.fasterxml.jackson.databind.{DeserializationFeature, ObjectMapper}
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module
import com.fasterxml.jackson.datatype.joda.JodaModule
import com.nexla.admin.client.DataCredentials.NonConnectionTypeCredentialType
import com.nexla.common.ConnectionType
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class DataCredentialsTest extends AnyFlatSpecLike with Matchers {

  val OBJECT_MAPPER = new ObjectMapper
  OBJECT_MAPPER.registerModule(new Jdk8Module)
  OBJECT_MAPPER.registerModule(new JodaModule)
  OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

  "DataCredentials" should "serialize from JSON for generic" in {
    val jsonString = buildDataCredJsonString("generic")
    val result = OBJECT_MAPPER.readValue(jsonString, classOf[DataCredentials])
    result.getCredentialsType shouldEqual NonConnectionTypeCredentialType.GENERIC
  }

  it should "serialize from JSON for connection type" in {
    val jsonString = buildDataCredJsonString("s3")
    val result = OBJECT_MAPPER.readValue(jsonString, classOf[DataCredentials])
    result.getCredentialsType shouldEqual ConnectionType.S3
  }

  it should "not throw for unknown credential type" in {
    val jsonString = buildDataCredJsonString("foobar")

    OBJECT_MAPPER.readValue(jsonString, classOf[DataCredentials])
  }

  def buildDataCredJsonString(credentialType: String) : String = {
    val jsonString =
    s"""
      |{
      |    "id": 6531,
      |    "name": "Custom parser input creds",
      |    "description": null,
      |    "credentials_non_secure_data": {"key1" : "value1", "key2": "value2"},
      |    "owner": {
      |        "id": 390,
      |        "full_name": "Rebecca Wong",
      |        "email": "<EMAIL>",
      |        "email_verified_at": "2021-07-07T22:41:16.000Z"
      |    },
      |    "org": {
      |        "id": 64,
      |        "name": "Nexla",
      |        "email_domain": "nexla.com",
      |        "email": null,
      |        "client_identifier": null
      |    },
      |    "access_roles": [
      |        "owner"
      |    ],
      |    "credentials_type": "$credentialType",
      |    "credentials_version": "1",
      |    "managed": false,
      |    "credentials_non_secure_data": {},
      |    "verified_status": null,
      |    "verified_at": null,
      |    "copied_from_id": null,
      |    "updated_at": "2021-08-27T21:53:17.000Z",
      |    "created_at": "2021-08-27T21:53:17.000Z",
      |    "tags": []
      |}
      |""".stripMargin
    jsonString
  }
}
