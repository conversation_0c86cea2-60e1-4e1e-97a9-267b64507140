package com.nexla.admin.client

import com.nexla.common.StreamUtils
import com.nexla.connector.config.{FlowType, IngestionMode}
import com.nexla.control.ControlMessageFlowAttachment
import org.mockito.Mockito._
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class AdminApiClientCondensedTest extends AnyFlatSpecLike with Matchers {
  private val m = java.util.Map.of(1, java.util.Map.of("2", "3"))
  private val adminApiClient = mock(classOf[AdminApiClient])

  private def mockAdminApiCondensed(useOldCondensedMethods: Boolean) = {
    val t = spy(new AdminApiClientCondensed(adminApiClient, useOldCondensedMethods))

    doReturn(m, m).when(t).dataSinkCondensedMapOld()
    doReturn(m, m).when(t).dataSinkCondensedMapNew()

    t
  }

  it should "call dataSinkCondensedMapNew if useOldCondensedMethods=false" in {
    val t = mockAdminApiCondensed(false)

    t.dataSinkCondensedMap()

    verify(t).dataSinkCondensedMapNew()
  }

  it should "not call dataSinkCondensedMapNew if useOldCondensedMethods=true" in {
    val t = mockAdminApiCondensed(true)

    t.dataSinkCondensedMap()

    verify(t, times(0)).dataSinkCondensedMapNew()
  }

  it should "pass deserialization" in {
    val flow = "    {\n        \"id\": 112774,\n        \"origin_node_id\": 112774,\n        \"parent_node_id\": null,\n        \"shared_origin_node_id\": null,\n        \"owner_id\": 440,\n        \"org_id\": 64,\n        \"cluster_id\": null,\n        \"status\": \"INIT\",\n        \"ingestion_mode\": \"sampling\",\n        \"flow_type\": \"streaming\",\n        \"data_source_id\": null,\n        \"data_set_id\": null,\n        \"data_sink_id\": 36638\n    }"

    val flowAttachment = StreamUtils.jsonUtil.stringToType(flow, classOf[ControlMessageFlowAttachment])

    flowAttachment.getFlowType shouldBe FlowType.STREAMING
    flowAttachment.getIngestionMode shouldBe IngestionMode.SAMPLING
  }
}
