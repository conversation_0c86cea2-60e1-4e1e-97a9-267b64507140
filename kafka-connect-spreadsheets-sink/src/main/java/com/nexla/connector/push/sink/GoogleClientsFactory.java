package com.nexla.connector.push.sink;

import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.googleapis.services.json.AbstractGoogleJsonClient;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.drive.Drive;
import com.google.api.services.sheets.v4.Sheets;
import com.google.auth.oauth2.GoogleCredentials;
import com.nexla.connector.config.file.FileSinkConnectorConfig;
import com.nexla.connector.config.gdrive.SpreadsheetSinkConnectorConfig;
import lombok.SneakyThrows;

public class GoogleClientsFactory {

    @SneakyThrows
    public static Sheets getSheetsService(FileSinkConnectorConfig config, GoogleCredential credential) {
        return new Sheets.Builder(
                GoogleNetHttpTransport.newTrustedTransport(),
                GsonFactory.getDefaultInstance(), credential
        ).setApplicationName("sink-" + config.sinkId).build();
    }

    @SneakyThrows
    public static Drive getDriveService(FileSinkConnectorConfig config, GoogleCredential credential) {
        return new Drive.Builder(
                GoogleNetHttpTransport.newTrustedTransport(),
                GsonFactory.getDefaultInstance(), credential
        ).setApplicationName("sink-" + config.sinkId).build();
    }
}
