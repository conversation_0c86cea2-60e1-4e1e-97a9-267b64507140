package com.nexla.connector.push.sink;

import com.nexla.common.sink.TopicPartition;
import lombok.AllArgsConstructor;
import one.util.streamex.StreamEx;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

import static java.util.Optional.empty;

@AllArgsConstructor
public class SheetContext {

	public StreamEx<List> toFlush;
	public boolean needToWriteHeader;
	public int flushCounter;
	public AtomicLong byteCounter;
	public AtomicLong rowCounter;
	public Map<TopicPartition, Long> offsets;

	public SheetContext(boolean needToWriteHeader) {
		this.needToWriteHeader = needToWriteHeader;
		this.toFlush = StreamEx.empty();
		this.flushCounter = 0;
		this.byteCounter = new AtomicLong();
		this.rowCounter = new AtomicLong();
		this.offsets = new ConcurrentHashMap<>();
	}

}