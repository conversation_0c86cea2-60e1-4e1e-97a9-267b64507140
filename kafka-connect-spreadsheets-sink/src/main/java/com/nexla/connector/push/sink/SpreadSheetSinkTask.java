package com.nexla.connector.push.sink;

import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.services.sheets.v4.Sheets;
import com.google.api.services.sheets.v4.model.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.admin.client.DataCredentials;
import com.nexla.common.NexlaDataCredentials;
import com.nexla.common.NotificationEventType;
import com.nexla.common.ResourceType;
import com.nexla.common.notify.NexlaNotificationEvent;
import com.nexla.common.notify.context.NexlaNotificationEventContext;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connect.common.ReadyToFlush;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.file.FileSinkConnectorConfig;
import com.nexla.connector.config.gdrive.SpreadsheetSinkConnectorConfig;
import com.nexla.connector.config.rest.RestAuthConfig;
import com.nexla.probe.gdrive.GDriveConnectorService;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.config.ConfigDef;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Stream;

import static com.google.api.client.util.Data.NULL_STRING;
import static com.nexla.common.MetricUtils.calcBytes;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.NotificationUtils.getStacktraceForDb;
import static com.nexla.common.ResourceType.SINK;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.time.VarUtils.replaceVars;
import static java.util.Optional.ofNullable;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

public class SpreadSheetSinkTask extends BaseSinkTask<FileSinkConnectorConfig> {

	public static final int FLUSH_EVERY_PUTS = 100;

	private GoogleClients cachedClient;
	private String range;
	private SpreadsheetSinkConnectorConfig sheetConfig;

	private final Map<String, SheetContext> fileContexts = Maps.newHashMap();
	private final Map<String, String> spreadsheetToId = Maps.newHashMap();
	private String globalId;
	private Boolean isFolderMode;
	private boolean isSheetNameRange = false;
	private Optional<String> customHeader;

	@Override
	public void doStart() {
		this.sinkTelemetryReporter =
				Optional.of(new ConnectorTelemetryReporter(config.sinkId, config.getConnectionType().name(), SINK, isDedicatedNode));

		this.sheetConfig = config.spreadsheetConfig.get();
		this.globalId = config.path.replace("/", "");
		this.isFolderMode = withClientRetriable(
				this::getGoogleServices,
				serv -> GDriveConnectorService.isFolder(globalId, serv.drive));
		this.customHeader = config.spreadsheetConfig
				.map(c -> c.headerTemplate);
	}

	@Override
	protected ConfigDef configDef() {
		return SpreadsheetSinkConnectorConfig.configDef();
	}

	private String getOrCreateSpreadsheetId(
			GoogleClients clients,
			Map<String, String> messageKey,
			String topicName,
			long offset
	) {
		if (isFolderMode) {
			DateTime now = getNowInUserTimeZone(nowUTC());
			String fileNamePrefix = config.fileNamePrefix
					.map(prefix -> replaceVars(prefix, messageKey, now, config.datetimePadding, config.dateTimeUnit, config.dateFormat))
					.orElse("");

			if (isNotEmpty(fileNamePrefix)) {
				fileNamePrefix += "-";
			}
			String outputDir = config.outputDirNamePattern
					.map(o -> replaceVars(o, messageKey, now, config.datetimePadding))
					.orElse("");

			if (!outputDir.replaceAll("[/]", "").isEmpty() &&
					outputDir.charAt(outputDir.length() - 1) != '/') {
				outputDir += '/';
			}

			String filePathNoOffset = String.format("%s%s%s", outputDir, fileNamePrefix, topicName);

			String filePath;
			if (sheetConfig.newFileOnRun) {
				filePath = String.format("%s-%012d", filePathNoOffset, offset);
			} else {
				filePath = filePathNoOffset;
			}

			return spreadsheetToId.computeIfAbsent(filePathNoOffset, s -> {
				List<String> filePathSplit = Lists.newArrayList(filePath.split("/"));
				String spreadsheetName = filePathSplit.remove(filePathSplit.size() - 1);
				String parentId = globalId;
				for (String f : filePathSplit) {
					parentId = GDriveConnectorService.getOrCreateFile(clients.drive, parentId, f, MIME_GDRIVE_FOLDER);
				}
				String sheetId = GDriveConnectorService.getOrCreateFile(clients.drive, parentId, spreadsheetName, MIME_GOOGLE_SPREADSHEET);
				setupSheet(clients, sheetId);
				return sheetId;
			});
		} else {
			return spreadsheetToId.computeIfAbsent(globalId, s -> {
				setupSheet(clients, globalId);
				return globalId;
			});
		}
	}

	private void setupSheet(GoogleClients clients, String sheetId) {
		logger.info("M=setupSheet, sheetConfig.range={}, sheetConfig.clearSheets={}", sheetConfig.range, sheetConfig.clearSheets);
		List<String> sheetNames = getSheetNames(sheetId, clients);
		if (StringUtils.isEmpty(sheetConfig.range)) {
			this.range = sheetNames.get(0);
			this.isSheetNameRange = !range.contains("!") && !range.contains(":");
			logger.info("M=setupSheet, range={}, isSheetNameRange={}", this.range, isSheetNameRange);
			if (sheetConfig.clearSheets) {
				clearRange(sheetId, clients);
			}
		} else {
			this.range = sheetConfig.range;
			this.isSheetNameRange = !sheetConfig.range.contains("!") && !sheetConfig.range.contains(":");
			boolean addSheet = isSheetNameRange && !sheetNames.contains(sheetConfig.range);
			if (addSheet) {
				addSheet(clients, sheetId, sheetConfig.range);
			}
		}
	}

	private DateTime getNowInUserTimeZone(DateTime nowUtc) {
		return nowUtc.withZone(DateTimeZone.forID(config.timezone));
	}

	@SneakyThrows
	private void clearRange(String sheetId, GoogleClients holder) {
		logger.info("M=clearRange, sheetId={}, range={}", sheetId, range);
		holder.sheets
				.spreadsheets()
				.values()
				.clear(sheetId, range, new ClearValuesRequest())
				.execute();
	}

	@SneakyThrows
	private List<String> getSheetNames(String sheetId, GoogleClients googleClients) {
		List<Sheet> sheetList = googleClients
				.sheets
				.spreadsheets()
				.get(sheetId)
				.execute()
				.getSheets();

		return StreamEx.of(sheetList)
				.map(x -> x.getProperties().getTitle())
				.toList();
	}

	@Override
	protected FileSinkConnectorConfig parseConfig(Map<String, String> props) {
		return new FileSinkConnectorConfig(props);
	}

	@SneakyThrows
	public GoogleClients getGoogleServices() {
		DataCredentials creds = adminApiClient.getDataCredentials(config.authConfig.getCredsId()).get();
		Map<String, String> credsMap = NexlaDataCredentials.getCreds(config.decryptKey, creds.getCredentialsEnc(), creds.getCredentialsEncIv());
		RestAuthConfig restConfig = new RestAuthConfig(credsMap, config.credsId);
		GoogleCredential credential = new GoogleCredential().setAccessToken(restConfig.vendorAccessToken);

		return new GoogleClients(
				GoogleClientsFactory.getSheetsService(config, credential),
				GoogleClientsFactory.getDriveService(config, credential)
		);
	}

	@Override
	@SneakyThrows
	protected void doPut(StreamEx<NexlaMessageContext> messagesStream, int streamSize) {
		Map<String, List<NexlaMessageContext>> messagesByFiles = messagesStream.groupingBy(message -> {

			Map<String, String> strMap = Maps.newHashMap();
			EntryStream.of(message.mapped.getRawMessage())
				.forKeyValue((k, v) ->
					strMap.put(k, ofNullable(v).map(Object::toString).orElse(null)));

			return withClientRetriable(this::getGoogleServices,
					clients -> getOrCreateSpreadsheetId(
							clients,
							strMap,
							message.topicPartition.topic,
							message.kafkaOffset));
		});

		messagesByFiles.forEach((fileId, messageContext) -> {
			SheetContext sheetCtx = fileContexts.computeIfAbsent(fileId, s -> withClientRetriable(this::getGoogleServices, googleClients -> createSheetContext(fileId, googleClients)));

			if (customHeader.isPresent()) {
				Stream<List<String>> values = Stream.of(Collections.singletonList(customHeader.get()));
				sheetCtx.toFlush = sheetCtx.toFlush.append(values);
				customHeader = Optional.empty();
			}

			if (sheetCtx.needToWriteHeader) {
				Stream<List> values = StreamEx.of(messageContext.get(0))
						.map(message -> EntryStream.of(message.mapped.getRawMessage()).keys().toList());

				sheetCtx.toFlush = sheetCtx.toFlush.append(values);
				sheetCtx.needToWriteHeader = false;
			}

			Stream<List<Object>> values = messageContext
					.stream()
					.map(x -> {
						sheetCtx.offsets.put(x.topicPartition, x.kafkaOffset);
						sheetCtx.rowCounter.incrementAndGet();
						sheetCtx.byteCounter.addAndGet(calcBytes(x.mapped.toJsonString()));
						return EntryStream.of(x.mapped.getRawMessage()).values()
								// sheets will skip nulls leading to mismatched column order, replace with null strings
								// see: com.google.api.services.sheets.v4.model.ValueRange#values
								.map(val -> val == null ? NULL_STRING : val)
								.toList();
					});

			sheetCtx.toFlush = sheetCtx.toFlush.append(values);

			withClientRetriable(this::getGoogleServices, sheets -> {
				boolean addAndGetEvery100 = ++sheetCtx.flushCounter % FLUSH_EVERY_PUTS == 0;
				if (addAndGetEvery100) {
					writeToSpreadSheet(fileId, sheets, sheetCtx);
				}
				return null;
			});
		});
	}

	@SneakyThrows
	private SheetContext createSheetContext(String fileId, GoogleClients googleClients) {
		Sheets sheetsService = googleClients.sheets;
		String queryRange = isSheetNameRange ? "!1:1" : "";
		ValueRange response = sheetsService
				.spreadsheets()
				.values()
				.get(fileId, range + queryRange)
				.execute();

		return new SheetContext(sheetConfig.writeHeader && isEmpty(response.getValues()));
	}

	@SneakyThrows
	private synchronized void writeToSpreadSheet(String fileId, GoogleClients serv, SheetContext sheetCtx) {
		List rowsToFlush = sheetCtx.toFlush.toList();
		ValueRange body = new ValueRange().setValues(rowsToFlush);
		String sheetName = GDriveConnectorService.getFileName(serv.drive, fileId);

		if (!rowsToFlush.isEmpty()) {
			try {
				clearSheetsIfNecessary(fileId, serv);
				serv.sheets
						.spreadsheets()
						.values()
						.append(fileId, this.range, body)
						.setValueInputOption("RAW")
						.setInsertDataOption(sheetConfig.insertMode)
						.execute();

				sendMetric(globalId, Optional.of(sheetName), sheetCtx.rowCounter.get(), sheetCtx.byteCounter.get(), 0L);
			} catch (Exception err) {
				if (isUnauthorizedError(err.getMessage())) {
					sheetCtx.toFlush = StreamEx.of(rowsToFlush);
					throw err;
				}

				int errorsNumber = rowsToFlush.size();
				if (errorsNumber > 0) {
					sendMetric(globalId, Optional.of(sheetName), 0L, 0L, errorsNumber);
				}
				sendErrorNotification(err);
			}
		}

		offsetsSender.ifPresent(os -> {
			os.updateSinkOffsets(config.sinkId, sheetCtx.offsets);
			logger.info("Updated offsets: {}", sheetCtx.offsets);
			sheetCtx.offsets.clear();
		});

		sheetCtx.byteCounter.set(0);
		sheetCtx.rowCounter.set(0);
		sheetCtx.toFlush = StreamEx.empty();

		if (sheetConfig.clearSheets) {
			sheetCtx.needToWriteHeader = true;
		}
	}

	private void clearSheetsIfNecessary(String fileId, GoogleClients serv) {
		if (sheetConfig.clearSheets) {
			if (StringUtils.isBlank(this.range)) {
				logger.info("M=clearSheetsIfNecessary, fetching sheet name, sheetId={}", fileId);
				this.range = getSheetNames(fileId, serv).get(0);
			}
			logger.info("M=clearSheetsIfNecessary, clearing sheet for sheetId={}", fileId);
			clearRange(fileId, serv);
		}
	}

	private void sendErrorNotification(Exception err) {
		NexlaNotificationEvent notification = new NexlaNotificationEvent();
		notification.setEventSource(SINK.name());
		notification.setEventType(NotificationEventType.ERROR);
		notification.setEventTimeMillis(nowUTC().getMillis());
		notification.setResourceId(this.config.sinkId);
		notification.setResourceType(SINK);
		NexlaNotificationEventContext notificationEventContext = new NexlaNotificationEventContext();
		notificationEventContext.setErrorMessage(err.getMessage());
		notificationEventContext.setErrorDetail(getStacktraceForDb(err));
		notificationEventContext.setRunId(runId);
		notification.setContext(notificationEventContext);
		nexlaMessageProducer.publishNotification(notification);

		publishMonitoringLog(getStacktraceForDb(err), NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
	}

	@SneakyThrows
	private <U> U withClientRetriable(Supplier<GoogleClients> supplier, Function<GoogleClients, U> consumer) {
		try {
			if (this.cachedClient == null) {
				this.cachedClient = supplier.get();
			}
			return consumer.apply(cachedClient);
		} catch (Exception e) {
			String message = e.getMessage();
			if (message.contains("authError") || isUnauthorizedError(message)) {
				logger.info("M=withClientRetriable, message={}, refreshing creds", message);
				listingClient.refreshRestToken(config.authConfig.getCredsId());
				// Invalidate cached credentials to get the latest config after refresh
				adminApiClient.invalidate(config.authConfig.getCredsId(), Optional.empty(), ResourceType.CREDENTIALS);
				GoogleClients client = supplier.get();
				this.cachedClient = client;
				return consumer.apply(client);
			}
			throw e;
		}
	}

	@SneakyThrows
	private static void addSheet(GoogleClients googleClients, String fileId, String sheetName) {
		AddSheetRequest addSheetRequest = new AddSheetRequest();
		SheetProperties sheetProperties = new SheetProperties();

		addSheetRequest.setProperties(sheetProperties);
		addSheetRequest.setProperties(sheetProperties.setTitle(sheetName));

		BatchUpdateSpreadsheetRequest batchUpdateSpreadsheetRequest = new BatchUpdateSpreadsheetRequest();

		List<Request> requestsList = new ArrayList<>();
		batchUpdateSpreadsheetRequest.setRequests(requestsList);

		Request request = new Request();
		request.setAddSheet(addSheetRequest);
		requestsList.add(request);

		batchUpdateSpreadsheetRequest.setRequests(requestsList);
		googleClients.sheets.spreadsheets().batchUpdate(fileId, batchUpdateSpreadsheetRequest).execute();
	}

	@Override
	public boolean doFlush(ReadyToFlush readyToFlush) {
		withClientRetriable(this::getGoogleServices, sheets -> {
					fileContexts.forEach((s, sheetContext) -> writeToSpreadSheet(s, sheets, sheetContext));
					return null;
				}
		);
		return false;
	}

	private static boolean isUnauthorizedError(String message) {
		return message.toLowerCase().contains("unauthorized");
	}
}
