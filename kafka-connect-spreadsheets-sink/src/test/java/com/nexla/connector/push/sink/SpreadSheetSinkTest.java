package com.nexla.connector.push.sink;

import com.google.api.services.drive.Drive;
import com.google.api.services.drive.model.File;
import com.google.api.services.sheets.v4.Sheets;
import com.google.api.services.sheets.v4.model.Sheet;
import com.google.api.services.sheets.v4.model.SheetProperties;
import com.google.api.services.sheets.v4.model.ValueRange;
import com.nexla.admin.client.*;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaMessage;
import com.nexla.common.StreamUtils;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connect.common.BaseKafkaTest;
import com.nexla.connect.common.PostponedFlush;
import com.nexla.connect.common.ReadyToFlush;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.properties.RestConfigAccessor;
import com.nexla.listing.client.ListingClient;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.kafka.connect.sink.SinkTaskContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.testcontainers.containers.KafkaContainer;

import java.io.IOException;
import java.util.*;

import static com.nexla.common.NexlaConstants.*;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.BaseConnectorConfig.FAST_MODE;
import static com.nexla.connector.properties.RestConfigAccessor.AUTH_TYPE;
import static com.nexla.probe.gdrive.GDriveConnectorService.FILE_FIELDS;

@ExtendWith(MockitoExtension.class)
public class SpreadSheetSinkTest extends BaseKafkaTest {

    public static final KafkaContainer kafka = new KafkaContainer("7.2.11");

    // encrypted value: {"credentials_type":"gdrive","vendor.access_token":"accesstoken"}
    private final String enc = "8JZAt21LgW22rRB17urTiFI5GgMpzM4VwImn3wYV/9g6Q0tADiSPXIa5gMmaGSWWl31hWsNxJ9pOw1msKwZuW15KlCYd2BWZsWUCpsgHhhM=";
    private final String iv = "+DhLxMA7txUIzcj1";
    private final String decryptKey = "eac05c0421b94f9acde7fb1b1d59b6c3";

    @Mock(answer=Answers.RETURNS_DEEP_STUBS)
    private Sheets sheets;
    @Mock(answer=Answers.RETURNS_DEEP_STUBS)
    private Drive drive;
    private AutoCloseable closeableMocks;

    @Mock
    private PostponedFlush postponedFlush;

    @BeforeAll
    public static void setup() {
        kafka.withReuse(true);
        kafka.start();
        init(kafka);
    }

    @SneakyThrows
    @BeforeEach
    public void setupMocks() {
        final HashMap<String, Object> BASE_PARAMS = new HashMap<>() {{
            put(UNIT_TEST, "true");
            put(AUTH_TYPE, RestConfigAccessor.AuthType.NONE.name());

            put(CREDENTIALS_DECRYPT_KEY, decryptKey);

            put(SINK_ID, "1");
            put(FAST_MODE, "true"); // disable listing calls
            put(PATH, "a_gdrive_fileid");
            put(DATA_FORMAT, "GOOGLE_SPREADSHEET");
        }};

        closeableMocks = MockitoAnnotations.openMocks(this);
        AdminApiClient adminApiClient = Mockito.mock(AdminApiClient.class);
        AdminApiClientBuilder.INSTANCE = adminApiClient;

        DataCredentials dataCredentials = new DataCredentials();
        dataCredentials.setCredentialsEnc(enc);
        dataCredentials.setCredentialsEncIv(iv);

        DataSink dataSink = new DataSink();
        dataSink.setId(1);
        dataSink.setConnectionType(ConnectionType.GDRIVE);
        dataSink.setSinkConfig(BASE_PARAMS);
        dataSink.setStatus(ResourceStatus.ACTIVE);

        Sheet sheet = new Sheet();
        SheetProperties sheetProps = new SheetProperties();
        sheetProps.setTitle("Blizzard Races");
        sheet.setProperties(sheetProps);

        MockedStatic<GoogleClientsFactory> mockClientFactory = Mockito.mockStatic(GoogleClientsFactory.class);
        mockClientFactory.when(() -> GoogleClientsFactory.getSheetsService(Mockito.any(), Mockito.any()))
                .thenReturn(sheets);
        mockClientFactory.when(() -> GoogleClientsFactory.getDriveService(Mockito.any(), Mockito.any()))
                .thenReturn(drive);
        Mockito.when(drive
                .files()
                .get(Mockito.anyString())
                .setFields(FILE_FIELDS)
                .setSupportsAllDrives(true)
                .setSupportsTeamDrives(true)
                .execute()).thenReturn(new File().setName("Blizzard Races").setMimeType("application/vnd.google-apps.spreadsheet"));
        Mockito.when(sheets
                .spreadsheets()
                .get(Mockito.anyString())
                .execute()
                .getSheets()).thenReturn(Collections.singletonList(sheet));
        Mockito.when(adminApiClient.getDataSink(Mockito.anyInt())).thenReturn(Optional.of(dataSink));
        Mockito.when(adminApiClient.getDataCredentials(Mockito.anyInt())).thenReturn(Optional.of(dataCredentials));

        SpreadSheetSinkTask task = new SpreadSheetSinkTask() {
            @Override
            protected ListingClient createListingClient() {
                return Mockito.mock(ListingClient.class);
            }
        };

        task.initialize(Mockito.mock(SinkTaskContext.class));
    }

    @SneakyThrows
    @AfterEach
    public void closeMocks() {
        closeableMocks.close();
    }

    @SneakyThrows


    @Test
    public void shouldConvertNullsToNullString() {
        withConsumer((metricsTopic, metricsConsumer) -> {
            SpreadSheetSinkTask task = new SpreadSheetSinkTask(){
                @Override
                protected PostponedFlush postponedFlush() {
                    return postponedFlush;
                }
            };
            Map<String, String> taskProps = new HashMap<>();
            taskProps.put("sink_id", "1");
            taskProps.put("bootstrap.servers", kafka.getBootstrapServers());
            task.start(taskProps);

            var nm1 = new NexlaMessage(StreamUtils.lhm("name", "Kerrigan", "race", "Zerg"));
            var nm2 = new NexlaMessage(StreamUtils.lhm("name", "Zeratul", "race", "Protoss"));
            var nm3 = new NexlaMessage(StreamUtils.lhm("name", null, "race", "Undead"));
            var nm4 = new NexlaMessage(StreamUtils.lhm("name", "Jim Raynor", "race", "Terran"));

            StreamEx<NexlaMessage> nexlaMessages = StreamEx.of(nm1, nm2, nm3, nm4);

            StreamEx<NexlaMessageContext> nmContexts = nexlaMessages.map(nm ->
                    new NexlaMessageContext(nm, nm, new TopicPartition("test-topic", 1), 0L));
            task.doPut(nmContexts, 4);
            task.doFlush(ReadyToFlush.FLUSH);

            try {
                Mockito.verify(sheets.spreadsheets().values()
                        .append(Mockito.anyString(), Mockito.anyString(), Mockito.eq(getValueRange()))
                        .setValueInputOption(Mockito.anyString())
                        .setInsertDataOption(Mockito.anyString()), Mockito.atLeastOnce()).execute();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    private static ValueRange getValueRange() {
        ValueRange valueRange = new ValueRange();
        List<List<Object>> values = new ArrayList<>();

        values.add(List.of("Kerrigan", "Zerg"));
        values.add(List.of("Zeratul", "Protoss"));
        values.add(List.of("", "Undead"));
        values.add(List.of("Jim Raynor", "Terran"));

        valueRange.setValues(values);
        return valueRange;
    }
}
