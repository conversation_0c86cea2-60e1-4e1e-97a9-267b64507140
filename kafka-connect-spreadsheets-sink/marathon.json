{"labels": {"DCOS_PACKAGE_IS_FRAMEWORK": "false", "DCOS_SERVICE_SCHEME": "http", "DCOS_SERVICE_NAME": "sink-spreadsheet-connector", "DCOS_SERVICE_PORT_INDEX": "0"}, "id": "/sink-spreadsheet-connector", "backoffFactor": 1.15, "backoffSeconds": 1, "container": {"portMappings": [{"containerPort": 8083, "hostPort": 0, "labels": {"VIP_0": "/sink-spreadsheet-connector:8083"}, "protocol": "tcp", "servicePort": 11009, "name": "default"}], "type": "DOCKER", "volumes": [], "docker": {"image": "nexla/kafka-connect-spreadsheets-sink:release-v.2.4.1-4090f257a583ed1db468ec710682e0cdb1561fc9", "forcePullImage": true, "privileged": false, "parameters": []}}, "cpus": 0.5, "disk": 0, "env": {"VAULT_HOST": "https://vault.marathon.l4lb.thisdcos.directory:8200", "CONNECT_EXTERNAL_KAFKA_STATSD_HOST": "datadog-agent.marathon.l4lb.thisdcos.directory", "CONNECT_KEY_CONVERTER_SCHEMA_REGISTRY_URL": "http://schema-registry.marathon.l4lb.thisdcos.directory:8081", "CONNECT_KEY_CONVERTER": "org.apache.kafka.connect.storage.StringConverter", "CONNECT_METRIC_REPORTERS": "com.airbnb.kafka.kafka09.StatsdMetricsReporter", "CONNECT_VALUE_CONVERTER_SCHEMA_REGISTRY_URL": "http://schema-registry.marathon.l4lb.thisdcos.directory:8081", "VAULT_TOKEN": "547Vw2WehgvMGqYvYy00Mog5", "CONNECT_REST_PORT": "8083", "CONNECT_BOOTSTRAP_SERVERS": "broker.kafka2.l4lb.thisdcos.directory:9092", "CONNECT_GROUP_ID": "dcos-connect-groupsink-sheet", "CONNECT_INTERNAL_KEY_CONVERTER": "org.apache.kafka.connect.json.JsonConverter", "CONNECT_STATUS_STORAGE_TOPIC": "dcos-connect-statussink-sheet", "CONNECT_VALUE_CONVERTER": "org.apache.kafka.connect.storage.StringConverter", "CREDENTIALS_SOURCE": "VAULT", "CONNECT_ZOOKEEPER_CONNECT": "zookeeper-0-server.kafka-zookeeper2.autoip.dcos.thisdcos.directory:1140,zookeeper-1-server.kafka-zookeeper2.autoip.dcos.thisdcos.directory:1140,zookeeper-2-server.kafka-zookeeper2.autoip.dcos.thisdcos.directory:1140", "KAFKA_HEAP_OPTS": "-Xmx5000M", "CONNECT_INTERNAL_VALUE_CONVERTER": "org.apache.kafka.connect.json.JsonConverter", "ENVIRONMENT_TYPE": "stage", "CONNECT_OFFSET_STORAGE_TOPIC": "dcos-connect-offsetssink-sheet", "CONNECT_CONFIG_STORAGE_TOPIC": "dcos-connect-configssink-sheet", "CONNECT_EXTERNAL_KAFKA_STATSD_REPORTER_ENABLED": "true", "CONNECT_EXTERNAL_KAFKA_STATSD_TAG_ENABLED": "true"}, "fetch": [{"uri": "https://s3.amazonaws.com/mesos-config3/docker.tar.gz", "extract": true, "executable": false, "cache": false}], "healthChecks": [{"gracePeriodSeconds": 300, "intervalSeconds": 60, "maxConsecutiveFailures": 3, "portIndex": 0, "timeoutSeconds": 20, "delaySeconds": 15, "protocol": "MESOS_HTTP", "path": "/", "ipProtocol": "IPv4"}], "instances": 1, "maxLaunchDelaySeconds": 3600, "mem": 4096, "gpus": 0, "networks": [{"mode": "container/bridge"}], "requirePorts": false, "taskKillGracePeriodSeconds": 20, "upgradeStrategy": {"maximumOverCapacity": 1, "minimumHealthCapacity": 1}, "killSelection": "YOUNGEST_FIRST", "unreachableStrategy": {"inactiveAfterSeconds": 300, "expungeAfterSeconds": 600}, "constraints": []}