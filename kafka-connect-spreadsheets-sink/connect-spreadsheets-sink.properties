name=spreadsheets-sink

# Match it up with the environment where the connector is defined
credentials.source=test

bootstrap.servers=localhost:9092
dataset.replication=1
sink_id=REPLACE_ME

# Many types possible, replace with appropriate type
sink_type=REPLACE_ME

# Find this in the connector ConfigDef, or otherwise in the connector repo. It should be the class that extends BaseSourceConnector or BaseSinkConnector
connector.class=com.nexla.connector.push.sink.SpreadSheetSinkConnector

# Retrieve these from admin-api
credsEnc=REPLACE_ME
credsEncIv=REPLACE_ME

# Only required for sinks
topics=REPLACE_ME