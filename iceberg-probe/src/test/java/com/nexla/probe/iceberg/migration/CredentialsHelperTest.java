package com.nexla.probe.iceberg.migration;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataCredentials;
import com.nexla.test.UnitTests;
import lombok.SneakyThrows;
import lombok.val;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.mockito.Mockito;

import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@Category(UnitTests.class)
public class CredentialsHelperTest {

  public static final String ENCRYPTED_CREDS = "Uii3JUoROZv9fyhm/788q+a4/jbVITgh7QxA6Tgqz/uODZ4+tOc9KtFVlZUlDmOg5ea8/eVyvDzTd8mKFm3fXSzIUIobFxgHG72rGT0EWspQNARFn/SkWKld6n27c1p7+UGuWnKs1KiUS3OozbCVwjclivfm+a78V7lZWVMH";
  public static final String ENCRYPTED_CREDS_IV = "TBhVuxoKLtrIWUFR";
  private CredentialsHelper helper;
  private final AdminApiClient adminApiClient = Mockito.mock(AdminApiClient.class);
  private final DataCredentials dataCredentials = Mockito.mock(DataCredentials.class);

  @Before
  public void setUp() {
    helper = new CredentialsHelper(adminApiClient);
    when(dataCredentials.getCredentialsEnc()).thenReturn(ENCRYPTED_CREDS);
    when(dataCredentials.getCredentialsEncIv()).thenReturn(ENCRYPTED_CREDS_IV);
    when(adminApiClient.getDataCredentials(anyInt())).thenReturn(Optional.of(dataCredentials));
  }

  @After
  public void tearDown() {
    helper = null;
  }

  @Test
  @SneakyThrows
  public void enrichWithDataCredentials() {
    val credentialsId = 123; // Example credentials ID
    val credentials = helper.enrichWithDataCredentials(credentialsId);

    assertEquals("s3", credentials.getCredentialsType());
    assertEquals("test-access-id", credentials.getAccessKeyId());
    assertEquals("test-secret-key", credentials.getSecretKey());
    assertEquals("us-west-2", credentials.getRegion());
  }
}