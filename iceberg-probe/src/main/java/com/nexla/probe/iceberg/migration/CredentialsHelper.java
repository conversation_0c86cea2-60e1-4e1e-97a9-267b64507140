package com.nexla.probe.iceberg.migration;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataCredentials;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Optional;

public class CredentialsHelper {
  private static final Logger logger = LoggerFactory.getLogger(CredentialsHelper.class);
  // GCM authentication tag length - 128 bits
  private static final int GCM_TAG_LENGTH = 128;
  private static final String CREDENTIALS_DECRYPT_KEY = "e2bdf6f807bff66316c38acb0cc01919";
  private static final ObjectMapper mapper = new ObjectMapper()
      .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

  private final AdminApiClient adminApiClient;

  public CredentialsHelper(final AdminApiClient adminApiClient) {
    this.adminApiClient = adminApiClient;
  }

  /**
   * Retrieves AWS credentials for the given credentials ID.
   *
   * @param credentialsId AWS credentials ID
   * @return AWS Credentials
   */
  public Credentials enrichWithDataCredentials(final int credentialsId) {
    try {
      logger.info("Fetching AWS credentials for ID: {}", credentialsId);
      final Optional<DataCredentials> opDataCredentials = adminApiClient.getDataCredentials(credentialsId);
      if (opDataCredentials.isEmpty()) {
        throw new RuntimeException("No credentials found for ID: " + credentialsId);
      }
      final DataCredentials dataCredentials = opDataCredentials.get();
      return decryptCredentials(CREDENTIALS_DECRYPT_KEY, dataCredentials.getCredentialsEnc(), dataCredentials.getCredentialsEncIv());
    } catch (final Exception e) {
      logger.error("Error retrieving AWS credentials for id: {}", credentialsId, e);
      throw new RuntimeException(e);
    }
  }

  private Credentials decryptCredentials(final String keyHex, final String credsEnc, final String credsEncIv) {
    try {
      // Decode base64 inputs
      final byte[] ciphertext = Base64.getDecoder().decode(credsEnc);
      final byte[] iv = Base64.getDecoder().decode(credsEncIv);

      // Use key as UTF-8 encoded string
      final byte[] key = keyHex.getBytes(StandardCharsets.UTF_8);

      // Create secret key
      final SecretKey secretKey = new SecretKeySpec(key, "AES");

      // Initialize cipher with GCM/NoPadding
      final Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
      final GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);

      cipher.init(Cipher.DECRYPT_MODE, secretKey, parameterSpec);
      final byte[] plaintext = cipher.doFinal(ciphertext);

      // Parse JSON
      return mapper.readValue(new String(plaintext, StandardCharsets.UTF_8), Credentials.class);

    } catch (final Exception e) {
      logger.error("Error decrypting credentials", e);
      throw new RuntimeException(e);
    }
  }
}