package com.nexla.probe.ftp;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataCredentials;
import com.nexla.admin.client.oauth2.NexlaTokenProvider;
import com.nexla.admin.client.oauth2.RefreshingTokenProvider;
import com.nexla.common.ConnectionType;
import com.nexla.common.connectiontype.LoadConnectorServiceClient;
import com.nexla.connect.box.BoxConnectorService;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.file.service.FileConnectorService;
import com.nexla.listing.client.FileVaultClient;
import com.nexla.listing.client.ListingClient;
import com.nexla.probe.azure.blob.AzureBlobStoreConnectorService;
import com.nexla.probe.azure.datalake.AzureDataLakeConnectorService;
import com.nexla.probe.deltalake.DeltaLakeConnectorService;
import com.nexla.probe.dropbox.DropboxConnectorService;
import com.nexla.probe.gcs.GCSConnectorService;
import com.nexla.probe.gdrive.GDriveConnectorService;
import com.nexla.probe.onedrive.OneDriveConnectorService;
import com.nexla.probe.s3.FileVaultConnectorService;
import com.nexla.probe.s3.S3ConnectorService;
import com.nexla.probe.sharepoint.SharepointConnectorService;
import com.nexla.probe.webdav.WebDAVConnectorService;
import com.nexla.spec.Configs;
import lombok.Data;
import lombok.SneakyThrows;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Constructor;
import java.util.Optional;

import static com.nexla.admin.client.oauth2.NexlaTokenProvider.sameToken;
import static com.nexla.common.ConnectionType.*;

@Data
public class FileConnectorServiceBuilder {

	public final AdminApiClient adminApiClient;
	public final ListingClient listingClient;
	public final String decryptKey;
	public boolean noTokenRefresh = false;
	public boolean noConnectionPooling = false;
	public DataCredentials credentials = null;
	public FileVaultClient fileVault = null;

	public FileConnectorService<? extends BaseAuthConfig> createFileConnectorService(ConnectionType connectionType) {
		return createFileConnectorService(connectionType, decryptKey);
	}

	@SneakyThrows
	public FileConnectorService<? extends BaseAuthConfig> createFileConnectorService(ConnectionType connectionType, String passedDecryptKey) {
		if (connectionType.equals(FTP)) {
			if (noConnectionPooling) {
				return new FtpConnectorService(adminApiClient, listingClient, passedDecryptKey, 0);
			} else {
				return new FtpConnectorService(adminApiClient, listingClient, passedDecryptKey);
			}
		} else if (connectionType.equals(S3) || connectionType.equals(MERCURY_S3) || connectionType.equals(NEPTUNE_S3) || connectionType.equals(MIN_IO_S3)) {
			return new S3ConnectorService(adminApiClient, listingClient, passedDecryptKey);
		} else if (connectionType.equals(GCS)) {
			return new GCSConnectorService(adminApiClient, listingClient, passedDecryptKey, Optional.ofNullable(credentials));
		} else if (connectionType.equals(AZURE_BLB)) {
			return new AzureBlobStoreConnectorService(adminApiClient, listingClient, passedDecryptKey);
		} else if (connectionType.equals(AZURE_DATA_LAKE)) {
			return new AzureDataLakeConnectorService(adminApiClient, listingClient, passedDecryptKey);
		} else if (connectionType.equals(DELTA_LAKE_S3) || connectionType.equals(DELTA_LAKE_AZURE_BLB) || connectionType.equals(DELTA_LAKE_AZURE_DATA_LAKE)) {
			return new DeltaLakeConnectorService(adminApiClient, listingClient, passedDecryptKey);
		} else if (connectionType.equals(DROPBOX)) {
			return new DropboxConnectorService(adminApiClient, listingClient, passedDecryptKey);
		} else if (connectionType.equals(BOX)) {
			NexlaTokenProvider tokenProvider = noTokenRefresh ? sameToken() : new RefreshingTokenProvider(adminApiClient, passedDecryptKey);
			return new BoxConnectorService(adminApiClient, listingClient, passedDecryptKey, tokenProvider);
		} else if (connectionType.equals(GDRIVE)) {
			return new GDriveConnectorService(adminApiClient, listingClient, passedDecryptKey);
		} else if (connectionType.equals(WEBDAV)) {
			return new WebDAVConnectorService(adminApiClient, listingClient, passedDecryptKey);
		} else if (connectionType.equals(FILE_UPLOAD)) {
			return new FileVaultConnectorService(adminApiClient, listingClient, passedDecryptKey, fileVault);
		} else if (connectionType.equals(SHAREPOINT)) {
			return new SharepointConnectorService(adminApiClient, listingClient, passedDecryptKey);
		} else if (connectionType.equals(ONEDRIVE)) {
			return new OneDriveConnectorService(adminApiClient, listingClient, passedDecryptKey);
		} else {
			String className = Configs.findSpec(connectionType).connectorServiceClass().get();
			Class<?> serviceClass = LoadConnectorServiceClient.remoteClassLoader().loadClass(className);
			Constructor<?> constructor = serviceClass.getDeclaredConstructor(AdminApiClient.class, ListingClient.class, passedDecryptKey.getClass());
			Object fileService = constructor.newInstance(adminApiClient, listingClient, passedDecryptKey);
			return (FileConnectorService<? extends BaseAuthConfig>) fileService;
		}
	}

}
