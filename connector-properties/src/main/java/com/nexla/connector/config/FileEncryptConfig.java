package com.nexla.connector.config;

import com.nexla.connector.config.file.encryption.EncryptionAlgorithm;
import org.apache.kafka.common.config.ConfigDef;

import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.connector.config.BaseConnectorConfig.require;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.common.config.ConfigDef.Importance.LOW;

public class FileEncryptConfig extends AbstractNoLoggingConfig {

	public static final String ENCRYPT_STANDARD = "encrypt.standard";
	public static final String EXTERNAL_PUBLIC_KEY = "external.public.key";
	public static final String ENCRYPT_PRIVATE_KEY = "encrypt.private.key";
	public static final String ENCRYPT_PRIVATE_PASSWORD = "encrypt.private.password";
	public static final String EXTERNAL_USER_ID = "external.user.id";
	public static final String ENCRYPT_USER_ID = "encrypt.user.id";

	public static final String ENCRYPT_DATA_HASH_ALGORITHM = "encrypt.data.hash.algorithm";
	public static final String ENCRYPT_DATA_ENCRYPTION_ALGORITHM = "encrypt.data.encryption.algorithm";
	public static final String ENCRYPT_DATA_COMPRESSION_ALGORITHM = "encrypt.data.compression.algorithm";

	public final Optional<EncryptionAlgorithm> encryptStandard;
	public final Optional<String> externalPublicKey;
	public final Optional<String> externalUserId;
	public final Optional<String> encryptUserId;
	public final Optional<String> privateKey;
	public final Optional<String> privatePassword;
	public final Optional<String> dataHashAlgorithm;
	public final Optional<String> dataEncryptionAlgorithm;
	public final Optional<String> dataCompressionAlgorithm;

	public FileEncryptConfig(Map<?, ?> originals) {
		super(configDef(new NexlaConfigDef()), originals);
		this.privateKey = opt(getString(ENCRYPT_PRIVATE_KEY));
		this.privatePassword = opt(getString(ENCRYPT_PRIVATE_PASSWORD));
		this.dataCompressionAlgorithm = opt(getString(ENCRYPT_DATA_COMPRESSION_ALGORITHM));
		this.dataEncryptionAlgorithm = opt(getString(ENCRYPT_DATA_ENCRYPTION_ALGORITHM));
		this.dataHashAlgorithm = opt(getString(ENCRYPT_DATA_HASH_ALGORITHM));
		this.encryptUserId = opt(getString(ENCRYPT_USER_ID));
		this.externalUserId = opt(getString(EXTERNAL_USER_ID));
		this.externalPublicKey = opt(getString(EXTERNAL_PUBLIC_KEY));
		this.encryptStandard = opt(getString(ENCRYPT_STANDARD))
			.map(EncryptionAlgorithm::fromProperty);

		encryptStandard.ifPresent(this::validateEncryptionProps);
	}

	private void validateEncryptionProps(EncryptionAlgorithm encryptStandard) {
		switch (encryptStandard) {
			case PGP:
				require(ENCRYPT_PRIVATE_KEY, privateKey);
				// require(EXTERNAL_PUBLIC_KEY, externalPublicKey); // Optional for decryption-only
				require(ENCRYPT_PRIVATE_PASSWORD, privatePassword);
				require(EXTERNAL_USER_ID, externalUserId);
				require(ENCRYPT_USER_ID, encryptUserId);
				break;
			default:
				throw new IllegalArgumentException(encryptStandard + " is not supported");
		}
	}

	public static NexlaConfigDef configDef(NexlaConfigDef parentConfig) {

		return new NexlaConfigDef(parentConfig)
			.withKey(nexlaKey(ENCRYPT_STANDARD, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Encryption standard")
				.displayName("Encryption standard"))

			.withKey(nexlaKey(ENCRYPT_PRIVATE_KEY, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Private key for encryption")
				.displayName("Private key for encryption"))

			.withKey(nexlaKey(EXTERNAL_PUBLIC_KEY, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Public key for encryption")
				.displayName("Public key for encryption"))

			.withKey(nexlaKey(ENCRYPT_PRIVATE_PASSWORD, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Password for private key")
				.displayName("Password for private key"))

			.withKey(nexlaKey(EXTERNAL_USER_ID, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("User Id to sign encrypted data")
				.displayName("User Id to sign encrypted data"))

			.withKey(nexlaKey(ENCRYPT_USER_ID, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Recipient Id for encrypted data")
				.displayName("Recipient Id for encrypted data"))

			.withKey(nexlaKey(ENCRYPT_DATA_HASH_ALGORITHM, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Hash algorithm for data encryption")
				.displayName("Hash algorithm for data encryption"))

			.withKey(nexlaKey(ENCRYPT_DATA_ENCRYPTION_ALGORITHM, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Encryption algorithm")
				.displayName("Encryption algorithm"))

			.withKey(nexlaKey(ENCRYPT_DATA_COMPRESSION_ALGORITHM, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Data compression algorithm")
				.displayName("Data compression algorithm"));


	}

}
