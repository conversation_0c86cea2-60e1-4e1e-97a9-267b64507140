# probe-http
HTTP interface to Probe service

Spring boot is used for implementation. The service relies on specific probe services to provide connectivity to the data sources.

Probe Services are added to pom.xml and initialized as part of AppConfig.

Any new probes should be added to pom.xml and AppConfig.

Most of the code is in the ProbeController. The controller takes the connectionType as a path variable and uses that as discriminator to call the specific service

Example for S3 the endpoints are 
```
/authenticate/s3
/listBuckets/s3
/listFiles/s3
/read/s3
/checkWrite/s3
```

Each endpoint takes ProbeInput object as input.

Credentials are to be encrypted in Nexla format. 
Example Input message for read is 
```
{
	"credsEnc":"credsEnc",
	"credsEncIv":"credEncIv",
	"params":{
		"bucket":"csv-test.nexla.com",
		"file":"test.csv"
	}
}
```

