{"id": "/probe", "instances": 1, "cpus": 0.25, "mem": 2048, "disk": 0, "gpus": 0, "fetch": [{"uri": "https://s3.amazonaws.com/mesos-config3/docker.tar.gz", "extract": true, "executable": false, "cache": false}], "backoffSeconds": 1, "backoffFactor": 1.15, "maxLaunchDelaySeconds": 3600, "container": {"type": "DOCKER", "docker": {"image": "nexla/probe-http", "network": "BRIDGE", "portMappings": [{"containerPort": 8080, "hostPort": 0, "servicePort": 10003, "protocol": "tcp", "name": "default", "labels": {"VIP_0": "/probe:8080"}}, {"containerPort": 0, "hostPort": 0, "servicePort": 10104, "protocol": "tcp"}, {"containerPort": 2000, "hostPort": 0, "protocol": "tcp"}], "privileged": false, "forcePullImage": true}}, "healthChecks": [{"gracePeriodSeconds": 20, "intervalSeconds": 60, "timeoutSeconds": 20, "maxConsecutiveFailures": 3, "portIndex": 0, "path": "/health", "protocol": "MESOS_HTTP", "delaySeconds": 15}], "upgradeStrategy": {"minimumHealthCapacity": 1, "maximumOverCapacity": 1}, "unreachableStrategy": {"inactiveAfterSeconds": 300, "expungeAfterSeconds": 600}, "killSelection": "YOUNGEST_FIRST", "requirePorts": true, "taskKillGracePeriodSeconds": 20, "labels": {"HAPROXY_0_BACKEND_HTTP_HEALTHCHECK_OPTIONS": "  http-send-name-header Host\n  timeout check {healthCheckTimeoutSeconds}s\n", "HAPROXY_GROUP": "external", "HAPROXY_0_VHOST": "test-be.nexla.com", "HAPROXY_0_PATH": "-i /probe", "HAPROXY_0_HTTP_BACKEND_PROXYPASS_PATH": "/probe"}, "env": {"API_CREDENTIALS_SERVER": "https://test.nexla.com/admin-api", "SAMPLE_REPLICATION_COUNT": "1", "ZOOKEEPER_CONNECT": "zookeeper-0-server.kafka-zookeeper.autoip.dcos.thisdcos.directory:1140,zookeeper-1-server.kafka-zookeeper.autoip.dcos.thisdcos.directory:1140,zookeeper-2-server.kafka-zookeeper.autoip.dcos.thisdcos.directory:1140", "BOOTSTRAP_SERVERS": "broker.kafka.l4lb.thisdcos.directory:9092", "SAMPLE_PARTITION_COUNT": "64"}}