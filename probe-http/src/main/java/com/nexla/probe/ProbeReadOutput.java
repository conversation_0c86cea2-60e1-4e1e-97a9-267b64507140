package com.nexla.probe;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.NexlaMessage;

import java.util.List;

public class ProbeReadOutput {

	public final String format;
	public final List<NexlaMessage> messages;

	@JsonCreator
	public ProbeReadOutput(
		@JsonProperty("format") String format,
		@JsonProperty("messages") List<NexlaMessage> messages
	) {
		this.format = format;
		this.messages = messages;
	}
}
