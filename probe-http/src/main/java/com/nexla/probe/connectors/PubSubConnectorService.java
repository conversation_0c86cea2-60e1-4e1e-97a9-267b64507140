package com.nexla.probe.connectors;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.api.gax.core.CredentialsProvider;
import com.google.api.resourcenames.ResourceName;
import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.pubsub.v1.SubscriptionAdminClient;
import com.google.cloud.pubsub.v1.SubscriptionAdminSettings;
import com.google.cloud.pubsub.v1.TopicAdminClient;
import com.google.cloud.pubsub.v1.TopicAdminSettings;
import com.google.cloud.pubsub.v1.stub.GrpcSubscriberStub;
import com.google.cloud.pubsub.v1.stub.SubscriberStub;
import com.google.cloud.pubsub.v1.stub.SubscriberStubSettings;
import com.google.pubsub.v1.*;
import com.nexla.common.*;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.parse.NexlaParser;
import com.nexla.common.probe.ProbeException;
import com.nexla.common.probe.ProbeSampleResult;
import com.nexla.common.probe.ProbeSampleResultEntry;
import com.nexla.common.probe.StringSampleResult;
import com.nexla.connector.ConnectorService;
import com.nexla.connector.MessageReader;
import com.nexla.connector.config.kafka.pubsub.PubSubAuthConfig;
import com.nexla.connector.config.kafka.pubsub.PubSubSinkConnectorConfig;
import com.nexla.connector.config.kafka.pubsub.PubSubSourceConnectorConfig;
import com.nexla.connector.config.rest.RestAuthConfig;
import com.nexla.parser.ParserUtils;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.AbstractConfig;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Stream;

import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;
import static com.nexla.connector.ConnectorService.AuthResponse.authError;

public class PubSubConnectorService extends ConnectorService<PubSubAuthConfig> implements MessageReader {

    public static final int DEFAULT_SAMPLE_ROWS = 10;
    private Logger logger = LoggerFactory.getLogger(PubSubConnectorService.class);

    public PubSubConnectorService() {
    }

    private static NexlaMessage nm(ReceivedMessage m) {
        return new NexlaMessage(new LinkedHashMap<>(m.getMessage().getAttributesMap()));
    }

    @Override
    public void initLogger(ResourceType resourceType, Integer resourceId, Optional<Integer> taskId) {
        this.logger = new NexlaLogger(logger, new NexlaLogKey(resourceType, resourceId, taskId));
    }

    @Override
    public AuthResponse authenticate(PubSubAuthConfig auth) {
        try {
            NexlaPubSubApi api = new NexlaPubSubApi(credentialsProvider(auth));

            api.listTopics(auth.projectId).toList();
            api.listSubscriptions(auth.projectId).toList();

            return SUCCESS;
        } catch (Throwable e) {
            logger.error("[creds-{}] Exception while authenticating", auth.getCredsId(), e);
            return authError(e);
        }
    }

    @Override
    public StreamEx<NexlaBucket> listBuckets(AbstractConfig config) {
        return StreamEx.empty();
    }

    @Override
    public boolean checkWriteAccess(AbstractConfig config) {
        return false;
    }

    @SneakyThrows
    public ProbeSampleResult readSample(AbstractConfig config, boolean raw) {
        PubSubSourceConnectorConfig cfg = (PubSubSourceConnectorConfig) config;

        String projectId = cfg.authConfig.projectId;
        String subscriptionId = cfg.subscriptionId;

        Optional<NexlaParser> optParser = Optional.ofNullable(cfg.parserType)
                .flatMap(parserType -> {
                    return ParserUtils.getParserByFormat(
                            Optional.empty(), parserType, Collections.emptyMap(), config.originalsStrings()
                    );
                });

        List<ReceivedMessage> pulled = new NexlaPubSubApi(credentialsProvider(cfg.authConfig))
                .pull(projectId, subscriptionId, DEFAULT_SAMPLE_ROWS);

        StreamEx<ProbeSampleResultEntry<String>> samples = StreamEx.of(pulled)
                .flatMap(m -> {
                    return optParser
                            .filter(p -> !m.getMessage().getData().isEmpty())
                            .map(parser -> {
                                try {
                                    return parser.parseMessages(new ByteArrayInputStream(m.getMessage().getData().toByteArray()))
                                            .filter(Optional::isPresent)
                                            .map(Optional::get)
                                            .map(NexlaMessage::getRawMessage);
                                } catch (Exception e) {
                                    logger.error("Error while parsing message with id {}, using {}", m.getMessage().getMessageId(), parser.getClass().getSimpleName());
                                    return null;
                                }
                            })
                            .orElseGet(() -> StreamEx.of(new LinkedHashMap<>(m.getMessage().getAttributesMap())));
                })
                .map(JsonUtils::toJsonString)
                .map(ProbeSampleResultEntry::new);

        return new StringSampleResult(samples.toList(), Optional.empty(), true);
    }

    @SneakyThrows
    @Override
    public StreamEx<NexlaFile> listBucketContents(AbstractConfig cfg) {
        PubSubSourceConnectorConfig config = (PubSubSourceConnectorConfig) cfg;
        NexlaPubSubApi api = new NexlaPubSubApi(credentialsProvider(config.authConfig));

        Stream<NexlaFile> subscriptions = api
                .listSubscriptions(config.authConfig.projectId)
                .map(subscription -> {
                    NexlaFile res = new NexlaFile();
                    res.setFullPath(subscription.getName());
                    res.setMetadata(Optional.of(Map.of(
                            NexlaConstants.PUBSUB_RESOURCE_TYPE, NexlaConstants.PUBSUB_RESOURCE_SUBSCRIPTION
                    )));
                    return res;
                });

        Stream<NexlaFile> topics = api
                .listTopics(config.authConfig.projectId)
                .map(subscription -> {
                    NexlaFile res = new NexlaFile();
                    res.setFullPath(subscription.getName());
                    res.setMetadata(Optional.of(Map.of(
                            NexlaConstants.PUBSUB_RESOURCE_TYPE, NexlaConstants.PUBSUB_RESOURCE_TOPIC
                    )));
                    return res;
                });

        return StreamEx.of(subscriptions).append(topics);
    }

    @SneakyThrows
    @Override
    public void createDestination(AbstractConfig cfg, Optional<Integer> sourceId) {
        PubSubSinkConnectorConfig config = (PubSubSinkConnectorConfig) cfg;

        String topic = TopicName.isParsableFrom(config.topicName)
                ? TopicName.parse(config.topicName).getTopic()
                : config.topicName;

        String subscription = ProjectSubscriptionName.isParsableFrom(config.subscriptionName)
                ? ProjectSubscriptionName.parse(config.topicName).getSubscription()
                : config.subscriptionName;

        new NexlaPubSubApi(credentialsProvider(config.authConfig))
                .createTopic(config.authConfig.projectId, topic, subscription);
    }

    @Override
    public StreamEx<NexlaFile> listTopLevelBuckets(AbstractConfig c) {
        return listBucketContents(c);
    }

    @Override
    public StreamEx<NexlaMessage> readStream(AbstractConfig config) {
        PubSubSourceConnectorConfig c = (PubSubSourceConnectorConfig) config;

        try {
            List<ReceivedMessage> messages = new NexlaPubSubApi(credentialsProvider(c.authConfig))
                    .pull(
                            c.authConfig.projectId,
                            c.subscriptionId,
                            DEFAULT_SAMPLE_ROWS
                    );

            return StreamEx.of(messages.iterator())
                    .map(PubSubConnectorService::nm);

        } catch (Exception e) {
            logger.error("Error reading topic", e);
            throw new RuntimeException(e);
        }
    }

    private CredentialsProvider credentialsProvider(PubSubAuthConfig config) {
        return () -> {
            return Optional.ofNullable(config.credsMap.get("json_creds"))
                    .map(v -> {
                        try {
                            return GoogleCredentials.fromStream(new ByteArrayInputStream(v.getBytes()));
                        } catch (IOException e) {
                            logger.error("Error during parsing service account key", e);
                            return null;
                        }
                    })
                    .map(credentials -> credentials.createScoped(List.of("https://www.googleapis.com/auth/pubsub")))
                    .orElseGet(() -> GoogleCredentials.create(new AccessToken(
                            config.credsMap.get(RestAuthConfig.VENDOR_ACCESS_TOKEN),
                            DateTime.now().plusDays(1).toDate()
                    )));
        };
    }

    private interface GPSMessageParser {
        StreamEx<Map<String, ?>> parse(ReceivedMessage m);
    }

    @AllArgsConstructor
    private static class MessageParser implements GPSMessageParser {
        private NexlaParser parser;

        @Override
        public StreamEx<Map<String, ?>> parse(ReceivedMessage m) {
            return parser.parseMessages(new ByteArrayInputStream(m.getMessage().getData().toByteArray()))
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .map(NexlaMessage::getRawMessage);
        }
    }

    private static class AttributesParser implements GPSMessageParser {
        @Override
        public StreamEx<Map<String, ?>> parse(ReceivedMessage m) {
            return StreamEx.of(m.getMessage().getAttributesMap());
        }
    }

    @Slf4j
    private static class NexlaPubSubApi {
        public static final int ACK_DEADLINE_SECONDS = 600; // max

        private final CredentialsProvider credentialsProvider;

        private NexlaPubSubApi(CredentialsProvider credentialsProvider) {
            this.credentialsProvider = credentialsProvider;
        }

        private static String resource(String id, Supplier<ResourceName> or) {
            return id.startsWith("projects/")
                    ? id
                    : or.get().toString();
        }

        @SneakyThrows
        void createSubscription(String projectId, String topic, String subscriptionId) {

            try (SubscriptionAdminClient subscriptionAdminClient = SubscriptionAdminClient.create(subscriptionSettings())) {
                subscriptionAdminClient.createSubscription(
                        ProjectSubscriptionName.of(projectId, subscriptionId),
                        TopicName.of(projectId, topic),
                        PushConfig.getDefaultInstance(),
                        ACK_DEADLINE_SECONDS
                );
            }
        }

        @SneakyThrows
        void createTopic(String projectId, String topicName, String subscriptionName) {
            try (TopicAdminClient topicAdminClient = TopicAdminClient.create(topicSettings())) {
                TopicName topicId = TopicName.of(projectId, topicName);

                Topic topic = topicAdminClient.createTopic(topicId);

                log.info("PubSub: Created topic: {}", topic.getName());
            } catch (Exception e) {
                throw new ProbeException("Could not create topic " + projectId + "/" + topicName, e);
            }

            try (SubscriptionAdminClient subscriptionAdminClient = SubscriptionAdminClient.create(subscriptionSettings())) {
                TopicName topicId = TopicName.of(projectId, topicName);
                ProjectSubscriptionName subscriptionId =
                        ProjectSubscriptionName.of(projectId, subscriptionName);

                Subscription subscription =
                        subscriptionAdminClient.createSubscription(subscriptionId, topicId, PushConfig.getDefaultInstance(), 10);
                log.info("PubSub: Created subscription: {}", subscription.getName());
            } catch (Exception e) {
                throw new ProbeException("Could not create subscription " + projectId + "/" + subscriptionName, e);
            }
        }

        private TopicAdminSettings topicSettings() throws IOException {
            return TopicAdminSettings
                    .newBuilder()
                    .setCredentialsProvider(credentialsProvider)
                    .build();
        }

        @SneakyThrows
        public StreamEx<Topic> listTopics(String projectId) {
            TopicAdminSettings settings = topicSettings();

            try (TopicAdminClient subscriptionAdminClient = TopicAdminClient.create(settings)) {
                ProjectName projectName = ProjectName.of(projectId);

                List<Topic> topics = new ArrayList<>();
                subscriptionAdminClient.listTopics(projectName).iterateAll().forEach(topics::add);
                return StreamEx.of(topics);
            }
        }

        @SneakyThrows
        public StreamEx<Subscription> listSubscriptions(String projectId) {

            try (SubscriptionAdminClient subscriptionAdminClient = SubscriptionAdminClient.create(subscriptionSettings())) {
                String projectName = resource(projectId, () -> ProjectName.of(projectId));

                List<Subscription> subscriptions = new ArrayList<>();
                subscriptionAdminClient.listSubscriptions(projectName).iterateAll().forEach(subscriptions::add);
                return StreamEx.of(subscriptions);
            }
        }

        private SubscriptionAdminSettings subscriptionSettings() throws IOException {
            return SubscriptionAdminSettings
                    .newBuilder()
                    .setCredentialsProvider(this.credentialsProvider)
                    .build();
        }

        public List<ReceivedMessage> pull(String projectId, String subscriptionId, Integer numOfMessages) throws IOException {
            SubscriberStubSettings subscriberStubSettings =
                    SubscriberStubSettings.newBuilder()
                            .setCredentialsProvider(credentialsProvider)
                            .setTransportChannelProvider(
                                    SubscriberStubSettings.defaultGrpcTransportProviderBuilder().build()
                            )
                            .build();

            try (SubscriberStub subscriber = GrpcSubscriberStub.create(subscriberStubSettings)) {
                String subscriptionName = resource(subscriptionId, () -> ProjectSubscriptionName.of(projectId, subscriptionId));
                PullRequest pullRequest =
                        PullRequest.newBuilder()
                                .setMaxMessages(numOfMessages)
                                .setSubscription(subscriptionName)
                                .build();

                return subscriber.pullCallable()
                        .call(pullRequest)
                        .getReceivedMessagesList();
            }
        }
    }
}
