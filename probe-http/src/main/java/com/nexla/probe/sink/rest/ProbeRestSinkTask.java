package com.nexla.probe.sink.rest;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.NexlaSslContext;
import com.nexla.common.metrics.MetricWithErrors;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.notify.transport.NexlaMessageProducer;
import com.nexla.common.notify.transport.NexlaMessageTransport;
import com.nexla.common.notify.transport.NoopNexlaMessageTransport;
import com.nexla.connector.rest.sink.RestSinkTask;
import com.nexla.kafka.KafkaMessageTransport;

public class ProbeRestSinkTask extends RestSinkTask {
    public ProbeRestSinkTask(AdminApiClient adminApiClient) {
        this.adminApiClient = adminApiClient;
    }

    @Override
    protected NexlaMessageProducer messageProducer(NexlaSslContext nexlaSslConfig) {
        NexlaMessageTransport nexlaMessageTransport = new NoopNexlaMessageTransport();
        return new NexlaMessageProducer(nexlaMessageTransport);
    }

    @Override
    protected void publishMonitoringLog(
            String log,
            NexlaMonitoringLogType type,
            NexlaMonitoringLogSeverity severity) {}


    @Override
    protected void publishMonitoringLogMetrics(MetricWithErrors metricWithErrors) {}
    
}
