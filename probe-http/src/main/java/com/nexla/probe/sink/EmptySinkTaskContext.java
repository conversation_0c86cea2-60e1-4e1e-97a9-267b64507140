package com.nexla.probe.sink;

import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.connect.sink.SinkTaskContext;

import java.util.Collections;
import java.util.Map;
import java.util.Set;

public class EmptySinkTaskContext implements SinkTaskContext {

    @Override
    public Map<String, String> configs() {
        return null;
    }

    @Override
    public void offset(Map<TopicPartition, Long> offsets) {

    }

    @Override
    public void offset(TopicPartition tp, long offset) {

    }

    @Override
    public void timeout(long timeoutMs) {

    }

    @Override
    public Set<TopicPartition> assignment() {
        return Collections.emptySet();
    }

    @Override
    public void pause(TopicPartition... partitions) {

    }

    @Override
    public void resume(TopicPartition... partitions) {

    }

    @Override
    public void requestCommit() {

    }

}
