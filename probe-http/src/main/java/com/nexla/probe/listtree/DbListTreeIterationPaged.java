package com.nexla.probe.listtree;

import com.nexla.common.probe.ColumnInfo;
import com.nexla.connector.config.jdbc.DbListTreeService;
import com.nexla.connector.config.jdbc.DbListTreeServicePaged;
import com.nexla.connector.config.jdbc.DbPage;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.TreeMap;

import static com.nexla.common.probe.ProbeControllerConstants.TYPE;
import static com.nexla.common.probe.ProbeControllerConstants.VALUE;

public class DbListTreeIterationPaged<T> {

    public static final String DEFAULT_DATABASE = "Database";

    private final T config;

    private final DbListTreeService<T> probeService;

    public DbListTreeIterationPaged(T config,
                                    DbListTreeService<T> probeService) {
        this.config = config;
        this.probeService = probeService;
    }

    public DbPage listTree(final String database,
                           final String schema,
                           final String table,
                           final int pageSize,
                           final String offset) {

        if (StringUtils.isEmpty(database)) {
            return listTreeDatabase(config, pageSize, offset);
        } else if (StringUtils.isEmpty(schema) && StringUtils.isEmpty(table)) {
            return listTreeSchema(config, database, pageSize, offset);
        } else if (StringUtils.isEmpty(table)) {
            return listTreeTables(config, database, schema, pageSize, offset);
        } else {
            return listTreeColumns(config, database, schema, table);
        }
    }

    private DbPage<TreeMap<String, Object>> listTreeDatabase(final T config,
                                                             final int pageSize,
                                                             final String offset) {
        final List<String> listDatabases = probeService.listDatabases(config).toList();

        if (listDatabases.size() > 1) {
            TreeMap<String, Object> resultMap = new TreeMap<>();
            listDatabases
                    .forEach(dsName -> {
                        TreeMap<String, Object> value = new TreeMap<>();
                        value.put(TYPE, "database");
                        value.put(VALUE, new TreeMap<>());
                        resultMap.put(dsName, value);
                    });
            return DbPage.of(resultMap);
        } else {
            String databaseName = listDatabases.isEmpty() ? DEFAULT_DATABASE : listDatabases.get(0);
            return listTreeSchema(config, databaseName, pageSize, offset);
        }
    }

    private DbPage<TreeMap<String, Object>> listTreeSchema(final T config,
                                                           final String database,
                                                           final int pageSize,
                                                           final String offset) {
        final List<String> listSchemas = probeService.listSchemas(database, config).toList();
        if (listSchemas.isEmpty() || listSchemas.size() == 1) {
            return listTreeTables(config, database, null, pageSize, offset);
        } else {
            TreeMap<String, Object> resultMap = new TreeMap<>();
            TreeMap<String, Object> databaseMap = new TreeMap<>();
            TreeMap<String, Object> schemaMap = new TreeMap<>();
            databaseMap.put(TYPE, "database");
            databaseMap.put(VALUE, schemaMap);
            listSchemas
                    .forEach(t -> {
                        TreeMap<Object, Object> val2 = new TreeMap<>();
                        val2.put(TYPE, "schema");
                        val2.put(VALUE, new TreeMap<>());
                        schemaMap.put(t, val2);
                    });
            resultMap.put(database, databaseMap);
            return DbPage.of(resultMap);
        }
    }

    private DbPage<TreeMap<String, Object>> listTreeTables(final T config,
                                                           final String database,
                                                           final String schema,
                                                           final int pageSize,
                                                           final String offset) {
        TreeMap<String, Object> resultMap = new TreeMap<>();

        TreeMap<String, Object> dsMap = new TreeMap<>();
        TreeMap<String, Object> tablesMap = new TreeMap<>();
        dsMap.put(TYPE, "database");
        dsMap.put(VALUE, tablesMap);

        DbListTreeServicePaged<T> pagedProbeService = (DbListTreeServicePaged) probeService;
        DbPage<Iterable<String>> dbPage = pagedProbeService.listTables(config, database, schema, pageSize, offset);
        dbPage
                .getResult()
                .forEach(t -> {
                    TreeMap<Object, Object> val2 = new TreeMap<>();
                    val2.put(TYPE, "table");
                    val2.put(VALUE, new TreeMap<>());
                    tablesMap.put(t, val2);
                });
        resultMap.put(database, dsMap);
        return new DbPage<>(resultMap, dbPage.getPageSize(), dbPage.getOffset(), dbPage.getHasNextPage());
    }

    private DbPage<TreeMap<String, Object>> listTreeColumns(T config,
                                                            String database,
                                                            String schema,
                                                            String table) {
        TreeMap<String, Object> resultMap = new TreeMap<>();

        TreeMap<String, Object> dsMap = new TreeMap<>();
        TreeMap<String, Object> tablesMap = new TreeMap<>();
        TreeMap<String, Object> tableMap = new TreeMap<>();
        dsMap.put(TYPE, "database");
        dsMap.put(VALUE, tablesMap);
        tableMap.put(TYPE, "table");
        List<ColumnInfo> columns = probeService.listColumnInfos(database, Optional.ofNullable(schema), table, config);
        List<ColumnInfo> columnsSorted = StreamEx.of(columns)
                .sorted(Comparator.comparing(o -> o.name))
                .toList();
        tableMap.put(VALUE, columnsSorted);
        tablesMap.put(table, tableMap);
        resultMap.put(database, dsMap);

        return DbPage.of(resultMap);
    }
}
