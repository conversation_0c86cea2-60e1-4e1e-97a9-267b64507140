package com.nexla.probe.listtree;

import com.nexla.common.probe.ColumnInfo;
import com.nexla.connector.config.jdbc.DbListTreeService;
import lombok.AllArgsConstructor;
import lombok.Data;
import one.util.streamex.StreamEx;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collectors;

import static com.nexla.common.probe.ProbeControllerConstants.TYPE;
import static com.nexla.common.probe.ProbeControllerConstants.VALUE;

@Data
public class DepthAwareDbListTreeIteration<T> {
	public static final String DEFAULT_DATABASE = "Database";

	private final T config;
	private final DbListTreeService<T> probeService;

	@Data
	@AllArgsConstructor
	private static class Tree {
		private List<Database> databases;

		public Tree(Database database) {
			this.databases = List.of(database);
		}

		public static Tree wrap(String name, List<Schema> schemas) {
			return  new Tree(new Database(name, schemas));
		}

		public static Tree wrap(String name, String schema, List<Table> tables) {
			return new Tree(new Database(name, List.of(new Schema(schema, tables))));
		}

		public static Tree wrap(String name, String schema, String table, List<ColumnInfo> columns) {
			return  new Tree(new Database(name, List.of(new Schema(schema, List.of(new Table(table, columns))))));
		}

		private TreeMap<String, Object> toMap() {
			TreeMap<String, Object> result = new TreeMap<>();
			for (Database db : databases) {
				result.put(db.getName(), db.toMap());
			}
			return result;
		}
	}

	@Data
	@AllArgsConstructor
	private static class Database {
		private String name;
		private List<Schema> schemas;

		private TreeMap<String, Object> toMap() {
			TreeMap<String, Object> value = new TreeMap<>();
			value.put(TYPE, "database");

			TreeMap<Object, Object> nestedStructs = new TreeMap<>();

			boolean supportSchemas = this.schemas
					.stream()
					.anyMatch(schema -> schema.getName() != null && !schema.getName().isBlank());

			if (!supportSchemas) {
				// if no schemas, put all tables directly under the database
				List<Table> allTables = this.schemas.stream()
						.flatMap(schema -> schema.getTables().stream())
						.collect(Collectors.toList());

				for (Table table : allTables) {
					nestedStructs.put(table.getName(), table.toMap());
				}

			} else {
				for (Schema schema : this.schemas) {
					nestedStructs.put(schema.getName(), schema.toMap());
				}
			}

			value.put(VALUE, nestedStructs);

			return value;
		}

	}

	@Data
	@AllArgsConstructor
	private static class Schema {
		private String name;
		private List<Table> tables;

		public TreeMap<Object, Object> toMap() {
			TreeMap<Object, Object> tables = new TreeMap<>();
			for (Table table : this.tables) {
				tables.put(table.getName(), table.toMap());
			}

			// This indicates that we are dealing with a database that doesn't support schemas.
			// In such cases, we return the tables directly without wrapping them in a schema.
			if ((name == null || name.isBlank())) {
				return tables;
			}

			TreeMap<Object, Object> schemaValue = new TreeMap<>();
			schemaValue.put(TYPE, "schema");
			schemaValue.put(VALUE, tables);
			return schemaValue;
		}
	}

	@Data
	@AllArgsConstructor
	private static class Table {
		private String name;
		private List<ColumnInfo> columns;

		public TreeMap<Object, Object> toMap() {
			TreeMap<Object, Object> tableValue = new TreeMap<>();
			tableValue.put(TYPE, "table");

			tableValue.put(VALUE,
					StreamEx.of(this.columns)
							.sorted(Comparator.comparing(o -> o.name))
							.toList()
			);
			return tableValue;
		}
	}

	public TreeMap<String, Object> listTree(Optional<String> database, Optional<String> schema, Optional<String> table, int depth) {
		if (depth <= 0) {
			return new TreeMap<>();
		}

		if (database.isEmpty()) {
			return new Tree(
					listTreeDatabase(config, depth)
			).toMap();
		} else if (schema.isEmpty() && table.isEmpty()) {
			return Tree.wrap(
					database.get(),
					listTreeSchema(config, database.get(), depth)
			).toMap();
		} else if (table.isEmpty()) {
			return Tree.wrap(
					database.get(),
					schema.orElse(null),
					listTreeTables(config, database.get(), schema, depth)
			).toMap();
		} else {
			return Tree.wrap(
					database.get(),
					schema.orElse(null),
					table.get(),
					probeService.listColumnInfos(database.get(), schema, table.get(), config)
			).toMap();
		}
	}

	private List<Database> listTreeDatabase(T config, int depth) {
		List<String> listDatabases = probeService.listDatabases(config).toList();

		if (listDatabases.size() > 1) {
			return listDatabases
					.stream()
					.filter(Objects::nonNull)
					.filter(db -> !db.isBlank())
					.map(x -> new Database(x, listTreeSchema(config, x, depth - 1)))
					.collect(Collectors.toList());
		} else {
			String databaseName = listDatabases.stream()
					.filter(Objects::nonNull)
					.filter(db -> !db.isBlank())
					.findFirst()
					.orElse(DEFAULT_DATABASE);

			return List.of(new Database(
					databaseName,
					listTreeSchema(config, databaseName, depth - 1)
			));
		}
	}

	private List<Schema> listTreeSchema(T config, String database, int depth) {
		if (depth <= 0) {
			return List.of();
		}

		List<String> listSchemas = probeService.listSchemas(database, config)
				.filter(Objects::nonNull)
				.toList();

		if (listSchemas.isEmpty()) {
			return List.of(new Schema(
					// Different database vendors implement schema concepts inconsistently
					// Decision here is to be as close to the db structure as possible.
					// Depth is already subtracted.
					null,
					listTreeTables(config, database, Optional.empty(), depth)
			));
		}

		return listSchemas.stream()
				.map(schemaName -> new Schema(
						schemaName,
						listTreeTables(config, database, Optional.ofNullable(schemaName), depth - 1)
				))
				.collect(Collectors.toList());
	}

	private List<Table> listTreeTables(T config, String database, Optional<String> schema, int depth) {
		if (depth <= 0) {
			return List.of();
		}

		return probeService.listTables(database, schema, config)
				.map(tableName -> new Table(
						tableName,
						probeService.listColumnInfos(database, schema, tableName, config)
				))
				.toList();
	}
}
