package com.nexla.probe.listtree;

import com.nexla.connector.config.documentdb.DocumentDbTreeService;
import lombok.Data;

import java.util.List;
import java.util.Optional;
import java.util.TreeMap;

import static com.nexla.common.probe.ProbeControllerConstants.TYPE;
import static com.nexla.common.probe.ProbeControllerConstants.VALUE;

@Data
public class DocumentDbListTreeIteration <T> {
    public static final String DEFAULT_DATABASE = "Database";
    private final T config;
    private final DocumentDbTreeService<T> probeService;

    public TreeMap<String, Object> listTree(Optional<String> database) {
        if (database.isEmpty()) {
            return listTreeDatabase(config);
        } else {
            return listTreeCollections(config, database);
        }
    }

    private TreeMap<String, Object> listTreeDatabase(T config) {
        List<String> listDatabases = probeService.listDatabases(config).toList();

        if (listDatabases.size() > 1) {
            TreeMap<String, Object> resultMap = new TreeMap<>();
            listDatabases
                    .forEach(dsName -> {
                        TreeMap<String, Object> value = new TreeMap<>();
                        value.put(TYPE, "database");
                        value.put(VALUE, new TreeMap<>());
                        resultMap.put(dsName, value);
                    });
            return resultMap;
        } else {
            String databaseName = listDatabases.isEmpty() ? DEFAULT_DATABASE : listDatabases.get(0);
            return listTreeCollections(config, Optional.of(databaseName));
        }
    }

    private TreeMap<String, Object> listTreeCollections(T config, Optional<String> database) {
        TreeMap<String, Object> resultMap = new TreeMap<>();

        TreeMap<String, Object> dsMap = new TreeMap<>();
        TreeMap<String, Object> collectionsMap = new TreeMap<>();
        probeService.listCollections(database.get(), config)
                .forEach(t -> {
                    TreeMap<Object, Object> val2 = new TreeMap<>();
                    val2.put(TYPE, "collection");
                    val2.put(VALUE, new TreeMap<>());
                    collectionsMap.put(t, val2);
                });
        dsMap.put(TYPE, "database");
        dsMap.put(VALUE, collectionsMap);
        resultMap.put(database.get(), dsMap);
        return resultMap;
    }

}
