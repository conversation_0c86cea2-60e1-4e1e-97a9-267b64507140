package com.nexla.probe.custom.ray

import com.nexla.probe.MezzanineAuthConfig
import com.nexla.probe.custom.ray.RayApiClient._
import spray.json.JsValue

import scala.concurrent.Future

object RayApiClient {
  final case class JobId(id: String)

  final case class SubmitDirectoryJobRequest(dryRunId: Long,
                                             customCode: String,
                                             driverFunction: String,
                                             packages: List[String],
                                             privatePackages: Option[PrivatePackages],
                                             sinkType: String,
                                             sourceType: String,
                                             fullSourceDir: String,
                                             fullDestinationDir: String,
                                             sourceS3Creds: MezzanineAuthConfig,
                                             destS3Creds: MezzanineAuthConfig,
                                             extraData: Option[JsValue],
                                             limit: Option[Int],
                                             pushStatus: Boolean
                                            )

  final case class PrivatePackages(username: String, token: String, urls: List[String])

  sealed trait JobStatus

  object JobStatus {
    case object Pending extends JobStatus

    case object Running extends JobStatus

    case object Stopped extends JobStatus

    case object Succeeded extends JobStatus

    case object Failed extends JobStatus
  }

  final case class JobDetails(status: JobStatus)

  final case class DirectoryJobDetails(status: JobStatus, childJobs: Set[JobId])

  final case class JobErrorDetails(reasons: List[String])

}
