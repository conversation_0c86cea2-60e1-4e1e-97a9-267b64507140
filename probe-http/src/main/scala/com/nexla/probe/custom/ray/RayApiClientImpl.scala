package com.nexla.probe.custom.ray

import akka.actor.ActorSystem
import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport._
import akka.http.scaladsl.marshalling.Marshal
import akka.http.scaladsl.model.{RequestEntity, Uri}
import com.nexla.probe.custom.ray.RayApiClient._
import com.nexla.sc.config.NexlaCredsConf
import com.nexla.sc.util.{BaseHttpClient, NexlaBasicAuth}
import spray.json.JsValue

import scala.concurrent.{ExecutionContext, Future}

class RayApiClientImpl(rayClusterUrl: String, val nexlaCreds: NexlaCredsConf)
                      (implicit ec: ExecutionContext, system: ActorSystem)
  extends BaseHttpClient with NexlaBasicAuth {

  private val rayApiCodecs = new RayApiCodecs()
  def submitJobV2(submitJobRequest: SubmitDirectoryJobRequest): Option[JobId] =  {
    val requestJson: JsValue = rayApiCodecs.submitDirectoryJobRequestEncoder.write(submitJobRequest)
    logger.info(s"Submitting job for ray V2 with body: ${requestJson.compactPrint}")
    for {
      entity <- Marshal[JsValue](requestJson).to[RequestEntity]
      url = s"$rayClusterUrl/v2/submit-job"
      httpResponse <- post(Uri(url), Seq(basicAuthHeader, acceptJson), entity)
      response <- handle[JsValue](url, Some(requestJson.compactPrint), httpResponse, "/v2/submit-job")
      jobId <- Future(rayApiCodecs.jobIdDecoder.read(response))
    } yield jobId
  }.value.flatMap { res =>
    res.fold(
      e => {
        logger.error(s"Failed to submit job for ray V2: ${e.getMessage}")
        throw e
        None
      },
      jobId => {
        logger.info(s"Successfully submitted job for ray V2 with jobId: ${jobId.id}")
        Some(jobId)
      }
    )
  }

}
