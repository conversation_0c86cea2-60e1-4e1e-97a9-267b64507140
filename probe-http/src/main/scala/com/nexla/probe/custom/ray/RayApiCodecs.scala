package com.nexla.probe.custom.ray

import com.nexla.connector.config.file.AWSAuthConfig
import com.nexla.probe.custom.ray.RayApiClient._
import spray.json.{DeserializationException, JsArray, JsBoolean, JsNull, JsNumber, JsObject, JsString, JsValue, RootJsonReader, RootJsonWriter}

class RayApiCodecs {

  val jobIdDecoder: RootJsonReader[JobId] = (json: JsValue) => {
    json.asJsObject.fields.get("job_id") match {
      case Some(JsString(jobId)) => JobId(jobId)
      case _ => throw DeserializationException(s"Failed to parse json: ${json.compactPrint}")
    }
  }

  val submitDirectoryJobRequestEncoder: RootJsonWriter[SubmitDirectoryJobRequest] = (obj: SubmitDirectoryJobRequest) => {
    val srcBucketPrefix = AWSAuthConfig.toBucketPrefix(obj.fullSourceDir, false)
    val dstBucketPrefix = AWSAuthConfig.toBucketPrefix(obj.fullDestinationDir, false)
    val pvtPackagesJson = obj.privatePackages.map(pp => JsObject(
      "auth" -> JsObject(
        "username" -> JsString(pp.username),
        "token" -> JsString(pp.token),
      ),
      "urls" -> JsArray(pp.urls.map(JsString.apply).toVector),
    ))
    val original = JsObject(
      "metadata" -> JsObject(
        "org_id" -> JsNull,
        "run_id" -> JsNumber(obj.dryRunId),
        "source_id" -> JsNull,
        "dataset_id" -> JsNull,
      ),
      "custom_code" -> JsObject(
        "base64_code" -> JsString(obj.customCode),
        "args" -> JsArray.empty,
        "kwargs" -> JsObject.empty,
        "driver_function" -> JsString(obj.driverFunction),
        "packages" -> JsArray(obj.packages.map(JsString.apply).toVector),
        "pvt_packages" -> pvtPackagesJson.getOrElse(JsNull),
      ),
      "job_config" -> JsObject(),
      "source" -> JsObject(
        "type" -> JsString(obj.sourceType),
        "credentials" -> JsObject(
          "bucket" -> JsString(srcBucketPrefix.bucket),
          "access_key" -> JsString(obj.sourceS3Creds.authConfig.accessKeyId),
          "secret_key" -> JsString(obj.sourceS3Creds.authConfig.secretKey),
        ),
        "input_dir" -> JsString(srcBucketPrefix.prefix),
      ),
      "destination" -> JsObject(
        Map(
          "push_status" -> JsBoolean(obj.pushStatus),
          "type" -> JsString(obj.sinkType),
          "credentials" -> JsObject(
            "bucket" -> JsString(dstBucketPrefix.bucket),
            "access_key" -> JsString(obj.destS3Creds.authConfig.accessKeyId),
            "secret_key" -> JsString(obj.destS3Creds.authConfig.secretKey)
          ),
          "output_dir" -> JsString(dstBucketPrefix.prefix)
        ) ++ obj.limit.map(l => "limit" -> JsNumber(l))
      ),
    )
    obj.extraData match {
      case Some(extraData) => deepMergeJsons(original, extraData)
      case None => original
    }
  }

  private val jobStatusDecoder: RootJsonReader[JobStatus] = {
    case JsString("PENDING") => JobStatus.Pending
    case JsString("RUNNING") => JobStatus.Running
    case JsString("STOPPED") => JobStatus.Stopped
    case JsString("SUCCEEDED") => JobStatus.Succeeded
    case JsString("FAILED") => JobStatus.Failed
    case json => throw DeserializationException(s"Failed to parse json: ${json.compactPrint}")
  }
  val jobDetailsDecoder: RootJsonReader[JobDetails] = (json: JsValue) => {
    val fields = json.asJsObject.fields
    val jobStatusRaw = fields.getOrElse("status", throw DeserializationException(s"Failed to extract status field: ${json.compactPrint}"))
    val jobStatus = jobStatusDecoder.read(jobStatusRaw)
    JobDetails(jobStatus)
  }

  val jobErrorDetailsDecoder: RootJsonReader[JobErrorDetails] = (json: JsValue) => {
    val reasons = json.asJsObject.fields.values.flatMap {
      case JsArray(elements) => elements.collect {
        case obj: JsObject => obj.fields.get("reason").collect {
          case JsString(reason) => reason
        }
      }.flatten
      case _ => Seq.empty
    }.toList
    JobErrorDetails(reasons)
  }

  val directoryJobDetailsDecoder: RootJsonReader[DirectoryJobDetails] = (json: JsValue) => {
    val fields = json.asJsObject.fields
    val jobStatusRaw = fields.getOrElse("status", throw DeserializationException(s"Failed to extract status field: ${json.compactPrint}"))
    val jobStatus = jobStatusDecoder.read(jobStatusRaw)
    val childJobs = fields.get("batches").map(_.asJsObject.fields.keySet.map(JobId)).getOrElse(Set.empty)
    DirectoryJobDetails(jobStatus, childJobs)

  }

  private def deepMergeJsons(j1: JsValue, j2: JsValue): JsValue = (j1, j2) match {
    case (JsObject(fields1), JsObject(fields2)) =>
      // Merge two JsObjects recursively
      JsObject(fields1 ++ fields2.map {
        case (key, j2Value) =>
          key -> (fields1.get(key) match {
            case Some(j1Value) => deepMergeJsons(j1Value, j2Value) // Recursively merge nested JsObjects
            case None => j2Value // J1 doesn't have the key, so take J2's value
          })
      })
    case (_, j2Value) =>
      // If they're not both JsObjects, override J1's value with J2's
      j2Value
  }

}
