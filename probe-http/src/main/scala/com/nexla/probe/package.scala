package com.nexla

import com.nexla.admin.client.DataCredentials
import com.nexla.connector.config.file.AWSAuthConfig

package object probe {

  case class MezzanineAuthConfig(authConfig: AWSAuthConfig,
                                 dataCredentials: DataCredentials)

  case class DryRunStartRequest(uuidHash: String,
                                dryRunId: Long,
                                   userId: Int,
                                mezzFullPath: String,
                                mezzBucket: String,
                                mezzBucketPrefix: String,
                                   sourceCredsId: Int,
                                   inputFilePath: String,
                                inputFilePathNoBucket: String,
                                   driverFunction: String,
                                   packages: String,
                                   dstFileExtension: String,
                                   codeBase64: Option[String],
                                   limited: Option[Boolean],
                                   extraData: Option[String],
                                sourceConfig: Option[String],
                                mezzanineS3Creds: MezzanineAuthConfig,
                                runRayProcessingJob: Boolean,
                                autoStatusFile: Boolean) {

    val dryRunInputPath =  s"$mezzFullPath/ray-input/$uuidHash/-1/$uuidHash-$dryRunId"
    val dryRunOutputPath =  s"$mezzFullPath/ray-output/0/0/$uuidHash-$dryRunId"
    val jobTrackFile = s"ray-output/dry-run-jobs/${uuidHash}_timestamp"
    val jobParserTrackFile = s"ray-output/dry-run-jobs/${uuidHash}_forced"

    def noBucketOutputPath(runId: String) = s"ray-output/0/0/$uuidHash-$runId"

    def fullOutputPath(runId: String) = s"$mezzFullPath/${noBucketOutputPath(runId)}"
  }
}
