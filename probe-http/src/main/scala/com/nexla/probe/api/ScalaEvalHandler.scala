package com.nexla.probe.api

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import akka.http.scaladsl.model.ContentTypes.`application/json`
import akka.http.scaladsl.model.HttpEntity
import akka.http.scaladsl.model.StatusCodes._
import akka.http.scaladsl.server.{Directives, Route}
import com.nexla.common.StreamUtils
import com.nexla.connector.config.rest.{BodyMakerEvalDto, HmacSignerEvalDto, HttpCallParameters}
import com.nexla.sc.util._
import com.nexla.sc.util.StrictNexlaLogging
import spray.json.DefaultJsonProtocol
import fr.davit.akka.http.metrics.core.scaladsl.server.HttpMetricsDirectives._
import org.apache.commons.codec.binary.Base64

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class ScalaEvalHandler(implicit ec: ExecutionContext)
  extends Directives
    with SprayJsonSupport
    with DefaultJsonProtocol
    with StrictNexlaLogging {

  private val hmacSigner =
    (post
      & pathPrefixLabeled("hmacSigner")
      & end) {
      entity(as[String]) {
        json =>
          onComplete {
            Future {
              val dto = StreamUtils.SCALA_OBJECT_MAPPER.readValue(json, classOf[HmacSignerEvalDto])
              ReflectToolbox.hmacSigner[HttpCallParameters](dto)
            }
          } {
            case Success(callParams) => complete(OK -> HttpEntity(`application/json`, StreamUtils.SCALA_OBJECT_MAPPER.writeValueAsString(callParams)))
            case Failure(e) => failWith(e)
          }
      }
    }

  private val bodyMaker64 =
    (post
      & pathPrefix("bodyMaker64")
      & end) {
      entity(as[String]) {
        json =>
          onComplete {
            Future {
              val dto = StreamUtils.SCALA_OBJECT_MAPPER.readValue(json, classOf[BodyMakerEvalDto])
              val bodyFn = new String(Base64.decodeBase64(dto.bodyFn))
              val dtoDecoded = new BodyMakerEvalDto(bodyFn, dto.messages)
              ReflectToolbox.bodyMaker(dtoDecoded)
            }
          } {
            case Success(callParams) => complete(OK -> HttpEntity(`application/json`, callParams))
            case Failure(e) => failWith(e)
          }
      }
    }

  val route: Route = concat(
    hmacSigner,
    bodyMaker64
  )

}