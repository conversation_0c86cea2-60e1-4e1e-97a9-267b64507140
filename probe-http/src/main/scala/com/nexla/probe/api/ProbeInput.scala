package com.nexla.probe.api

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import com.nexla.common.ConnectionType
import com.nexla.sc.format.json.AnyToStringJsonFormat
import spray.json.{BasicFormats, CollectionFormats, DefaultJsonProtocol, JsString, JsValue, JsonFormat}

case class ProbeInput(credsId: Int = 0,
                      credsEnc: String,
                      credsEncIv: String,
                      params: Option[Map[String, String]])

object ProbeInputFormat
  extends BasicFormats
    with CollectionFormats
    with SprayJsonSupport
    with <PERSON>faultJsonProtocol {

  private implicit val anyToString = AnyToStringJsonFormat

  private implicit val ConnectionTypeFormat = new JsonFormat[ConnectionType] {

    override def read(json: JsValue): ConnectionType = ConnectionType.fromString(json.toString())

    override def write(obj: ConnectionType): JsValue = JsString(obj.name())
  }

  val Format = jsonFormat4(ProbeInput)
}