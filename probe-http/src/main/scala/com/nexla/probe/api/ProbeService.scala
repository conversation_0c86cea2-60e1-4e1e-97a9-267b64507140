package com.nexla.probe.api

import com.nexla.common.{ConnectionType, probe}
import com.nexla.connector.ConnectorService.DEFAULT_SAMPLE_ROWS
import com.nexla.connector.config.redis.RedisConnectorConfig
import com.nexla.probe.redis.RedisConnectorService
import com.nexla.sc.config.{NexlaCredsEncodedConf, RedisCredsEncodedConf}
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import com.nexla.sc.util.StrictNexlaLogging

import scala.collection.JavaConverters._

class ProbeService(nexlaCredsEncoded: NexlaCredsEncodedConf,
                   decryptKey: String)
  extends StrictNexlaLogging
    with WithLogging {

  def readDataMapSample(connectionType: ConnectionType,
                        probeInput: probe.ProbeInput,
                        offset: Option[Int],
                        pageSizeOpt: Option[Int]): List[Map[String, String]] = {

    val redisConnectorService = new RedisConnectorService
    val config = RedisConnectorConfig.useNexlaRedisCluster(probeInput.getAllParams(decryptKey, connectionType, nexlaCredsEncoded.enc, nexlaCredsEncoded.encIv))
    val pageSize = pageSizeOpt.getOrElse(DEFAULT_SAMPLE_ROWS).intValue()

    val samples = redisConnectorService.readSample(config) // to get data in less number of requests

    offset
      .map(offsetVal => samples.skip(offsetVal.longValue()))
      .getOrElse(samples)
      .limit(pageSize)
      .toList
      .asScala
      .toList
      .map(_.asScala.toMap)
  }

}
