package com.nexla.probe.api

import com.nexla.common.{NexlaConstants, NexlaFile}

case class Bucket(name: String)

case class ColumnInfo(name: String,
                      primaryKey: Boolean,
                      `type`: String,
                      defaultValue: Option[String])

case class TableInfo(name: String,
                     columns: List[ColumnInfo])

case class DbInfo(database: Option[String],
                  tables: List[TableInfo])

case class TopicsInfo(topics: List[String])

case class TopicSubscriptionInfo(topics: List[String], subscriptions: List[String])

case class NexFile(id: Option[Long],
                   linkedFileId: Option[Long],
                   source: Option[String],
                   fullPath: String,
                   size: Option[Long],
                   nameSpace: String,
                   md5: Option[String],
                   lastModified: Option[Long],
                   metadata: Option[Map[String, String]],
                   deleted: Boolean,
                   `type`: String)

case class WsdlPort(name: String,
                    location: String)

case class WsdlParameter(path: String,
                         `type`: Option[String],
                         possibleValues: Option[List[String]],
                         pattern: Option[String],
                         maxLength: Option[Int],
                         maxDigits: Option[Int],
                         fractionDigits: Option[Int],
                         minLength: Option[Int],
                         minInclusive: Option[Int],
                         minExclisive: Option[Int],
                         maxInclusive: Option[Int],
                         maxExclusive: Option[Int],
                         minOccurs: Option[Int],
                         maxOccurs: Option[Int])

case class WsdlOperation(name: String,
                         parameters: List[WsdlParameter])

case class WsdlBinding(name: String,
                       portType: String,
                       operations: List[WsdlOperation])

case class WsdlService(name: String,
                       ports: List[WsdlPort],
                       bindings: List[WsdlBinding])

case class WsdlDefinition(services: List[WsdlService])

object Dto {

  import com.nexla.sc.util._

  import scala.collection.JavaConverters._
  import scala.compat.java8.OptionConverters._

  def toNexFile(f: NexlaFile) = NexFile(
    f.getId.opt.map(_.longValue),
    f.getLinkedFileId.opt.map(_.longValue),
    f.getSource.opt.map(_.toString),
    f.getFullPath,
    f.getSize.opt.map(_.longValue),
    f.getNameSpace,
    f.getMd5.opt,
    f.getLastModified.opt.map(_.longValue),
    f.getMetadata.asScala.map(_.asScala.mapValues(_.toString).toMap),
    f.isDeleted,
    f.getType.toString
  )

  def toDbInfo(info: com.nexla.common.probe.DbInfo): DbInfo = DbInfo(
    database = Option(info.getDatabase),
    tables = info.getTables.asScala
      .map(t => TableInfo(
        name = t.name,
        columns = t.columns.asScala.map(c =>
          ColumnInfo(c.name, c.primaryKey, c.`type`, c.defaultValue.opt)).toList)).toList)

  def toTopics(info: Seq[NexlaFile]): TopicsInfo = TopicsInfo(info.map(_.getFullPath).toList)

  def toTopicWithSubscriptions(info: Seq[NexlaFile]): TopicSubscriptionInfo = {
    val isPubSubTopic = (_: NexlaFile).getMetadata
      .filter(_.get(NexlaConstants.PUBSUB_RESOURCE_TYPE) == NexlaConstants.PUBSUB_RESOURCE_TOPIC)
      .isPresent

    val isPubSubSubscription = (_: NexlaFile).getMetadata
      .filter(_.get(NexlaConstants.PUBSUB_RESOURCE_TYPE) == NexlaConstants.PUBSUB_RESOURCE_SUBSCRIPTION)
      .isPresent

    TopicSubscriptionInfo(
      info.filter(isPubSubTopic).map(_.getFullPath).toList,
      info.filter(isPubSubSubscription).map(_.getFullPath).toList,
    )
  }


  implicit class RichWsdlDefinition(w: com.nexla.soap.wsdl.WsdlDefinition) {

    def toWsdlDefinition = WsdlDefinition(
      services = w.getServices.asScala
        .map(s => WsdlService(s.getName, s.getPorts.asScala
          .map(p => WsdlPort(p.getName, p.getLocation)).toList,
          s.getBindings.asScala.map(b => WsdlBinding(b.getName, b.getPortType, b.getOperations.asScala
            .map(o => WsdlOperation(
              o.getName, o.getParameters.asScala.map(p => WsdlParameter(
                p.getPath,
                Option(p.getType),
                p.getPossibleValues.asScala.map(_.asScala.toList),
                p.getPattern.asScala,
                p.getMaxLength.asScala.map(_.intValue()),
                p.getMaxDigits.asScala.map(_.intValue()),
                p.getFractionDigits.asScala.map(_.intValue()),
                p.getMinLength.asScala.map(_.intValue()),
                p.getMinInclusive.asScala.map(_.intValue()),
                p.getMinExclisive.asScala.map(_.intValue()),
                p.getMaxInclusive.asScala.map(_.intValue()),
                p.getMaxExclusive.asScala.map(_.intValue()),
                p.getMinOccurs.asScala.map(_.intValue()),
                p.getMaxOccurs.asScala.map(_.intValue())
              )).toList
            )).toList)).toList)).toList
    )
  }

}
