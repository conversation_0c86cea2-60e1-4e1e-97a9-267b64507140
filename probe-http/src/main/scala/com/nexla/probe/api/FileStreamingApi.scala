package com.nexla.probe.api

import akka.actor.ActorSystem
import akka.http.scaladsl.model.ContentTypes._
import akka.http.scaladsl.model.StatusCodes._
import akka.http.scaladsl.model.{HttpEntity, StatusCode, StatusCodes}
import akka.http.scaladsl.server.{Directives, Route}
import akka.stream.{IOResult, Materializer}
import akka.stream.scaladsl.{FileIO, Source, StreamConverters}
import akka.util.ByteString
import com.nexla.admin.client.AdminApiClient
import com.nexla.probe.ProbeController
import com.nexla.probe.api.FileStreamingApi.{BadRequestException, FileDownloadRequest}
import com.nexla.probe.api.Marshalling._
import com.nexla.probe.api.commons.TappingOps.futureOps
import com.nexla.sc.api.common.ApiResponse
import com.nexla.sc.util.{StrictNexlaLogging, _}
import fr.davit.akka.http.metrics.core.scaladsl.server.HttpMetricsDirectives._
import spray.json.{JsValue, RootJsonReader}

import java.io.{File, InputStream}
import scala.compat.java8.OptionConverters.RichOptionalGeneric
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class FileStreamingApi(probeController: ProbeController, adminApiClient: AdminApiClient)(implicit ec: ExecutionContext, mat: Materializer, system: ActorSystem) extends Directives with StrictNexlaLogging {
  private implicit val fileDownloadRequestReader: RootJsonReader[FileDownloadRequest] = (json: JsValue) => {
    FileDownloadRequest(
      filePath = json.asJsObject.fields("filePath").convertTo[String],
      sourceId = json.asJsObject.fields("sourceId").convertTo[Int]
    )
  }

  private val downloadFile =
    (post & pathPrefixLabeled("files" / "download", "files/download") & end & entity(as[FileDownloadRequest])) { body =>
      logger.info(s"Received download request for file: ${body.filePath}, sourceId: ${body.sourceId}")

      val f: Future[Source[ByteString, Future[IOResult]]] = for {
        dataSource <- Future(adminApiClient.getDataSource(body.sourceId).asScala.getOrElse(throw BadRequestException(s"Source with id ${body.sourceId} could not be found")))
        inputStream <- Future(probeController.readEntireFile(body.filePath, dataSource))(Async.ioExecutorContext)
        _ = logger.info(s"Successfully opened InputStream for file: ${body.filePath}, sourceId: ${body.sourceId}")
        scalaStream = StreamConverters.fromInputStream(() => inputStream)
      } yield scalaStream

      onComplete(f) {
        case Success(stream) =>
          logger.info(s"Streaming started for file: ${body.filePath}, sourceId: ${body.sourceId}")
          val entity = HttpEntity(`application/octet-stream`, stream)
          complete(entity)

        case Failure(ex) =>
          val errorMessage = s"Failed to stream file: ${body.filePath}, sourceId: ${body.sourceId}, reason: ${ex.getMessage}"
          logger.error(errorMessage, ex)
          complete(determineStatusCode(ex), ApiResponse(errorMessage, Some(errorMessage)))
      }
    }

  private val uploadFileRoute = withoutRequestTimeout {
    (post & path("files" / "upload") & parameter("sinkId".as[Int]) & withoutSizeLimit & fileUpload("file")) { case (sinkId, (fileInfo, byteSource)) =>
      logger.info(s"Received upload request for file: ${fileInfo.fileName}, sinkId: $sinkId")

      val tmpFile = File.createTempFile("upload-", ".tmp")
      logger.info(s"Created temporary file at ${tmpFile.getAbsolutePath} for upload: ${fileInfo.fileName}, sinkId: $sinkId")

      val f = for {
        dataSink <- Future(adminApiClient.getDataSink(sinkId).asScala.getOrElse(throw BadRequestException(s"Sink with id $sinkId could not be found")))
        _ <- saveToTmpFile(tmpFile, byteSource)
        fileDetails <- Future(probeController.uploadFile(fileInfo.fileName, dataSink, tmpFile))(Async.ioExecutorContext)
      } yield fileDetails

      val withFileDeleted = f.andThen { case _ =>
          if (tmpFile.delete()) logger.info(s"Successfully deleted temp file: ${tmpFile.getAbsolutePath}")
          else logger.warn(s"Failed to delete temp file: ${tmpFile.getAbsolutePath}")
      }

      onComplete(withFileDeleted) {
        case Success(fileDetails) =>
          val filePath = fileDetails.getDisplayPath.asScala.getOrElse(fileDetails.getPath)
          val msg = s"Upload completed: ${fileInfo.fileName} -> $filePath (sinkId: $sinkId)"
          logger.info(msg)
          complete(StatusCodes.OK, ApiResponse(msg))
        case Failure(ex) =>
          val errorMessage = s"Failed to upload file: ${fileInfo.fileName}, sinkId: $sinkId, reason: ${ex.getMessage}"
          logger.error(errorMessage, ex)
          complete(determineStatusCode(ex), ApiResponse(errorMessage, Some(errorMessage)))
      }
    }
  }

  private def saveToTmpFile(tmpFile: File, byteSource: Source[ByteString, _]): Future[Unit] = {
    val fileSink = FileIO.toPath(tmpFile.toPath)
    byteSource.runWith(fileSink).map(_ => ())
      .tap(_ => logger.info(s"File bytes written to temp file: ${tmpFile.getAbsolutePath}"))
      .tapError(ex => logger.error(s"Failed to consume upload stream to temp file: ${tmpFile.getAbsolutePath}", ex))
  }

  private def determineStatusCode(ex: Throwable): StatusCode = ex match {
    case _: BadRequestException => StatusCodes.BadRequest
    case _ => StatusCodes.InternalServerError
  }

  val route: Route = downloadFile ~ uploadFileRoute
}

object FileStreamingApi {
  case class FileDownloadRequest(filePath: String, sourceId: Int)

  private case class BadRequestException(message: String) extends RuntimeException(message)
}
