package com.nexla.probe.api.ray

import com.nexla.probe.api.ray.ExtraDataBuilder.{ExtraData, PrivatePackages}
import com.typesafe.scalalogging.StrictLogging
import spray.json.{JsObject, JsValue}

import scala.util.{Success, Try}

class ExtraDataBuilder extends StrictLogging {
  private def extractPrivatePackages(extraData: JsValue): Try[Option[PrivatePackages]] = {
    val maybePvtPackages: Try[Option[JsObject]] = Try(extraData.asJsObject.fields.get("custom_code").flatMap(_.asJsObject.fields.get("pvt_packages").map(_.asJsObject)))
    maybePvtPackages.flatMap {
      case Some(pvtPackages) => Try {
        import spray.json.DefaultJsonProtocol._
        val credentialId = pvtPackages.fields("credential_id").convertTo[Int]
        val urls = pvtPackages.fields("urls").convertTo[List[String]]
        Some(PrivatePackages(credentialId, urls))
      }
      case None => Success(None)
    }
  }

  private def dropPvtPackages(extraData: JsValue): JsValue = {
    extraData.asJsObject.fields.get("custom_code") match {
      case Some(customCode: JsObject) =>
        val modifiedCustomCode = JsObject(customCode.fields.filterNot { case (key, _) => key == "pvt_packages" })
        JsObject(extraData.asJsObject.fields.filterNot { case (key, _) => key == "custom_code" } ++ Map("custom_code" -> modifiedCustomCode))
      case Some(j) =>
        logger.warn(s"Unexpected value inside custom_code: $j. Can't drop it from extra_data")
        extraData
      case None =>
        extraData
    }
  }

  def parse(extraData: JsValue): Try[ExtraData] =
    extractPrivatePackages(extraData).map { maybePrivatePackages =>
      val extraDataWithoutPvtPackages = dropPvtPackages(extraData)
      ExtraData(maybePrivatePackages, extraDataWithoutPvtPackages)
    }
}

object ExtraDataBuilder {
  final case class PrivatePackages(credentialId: Int, urls: List[String])

  final case class ExtraData(privatePackage: Option[PrivatePackages], extraData: JsValue)
}
