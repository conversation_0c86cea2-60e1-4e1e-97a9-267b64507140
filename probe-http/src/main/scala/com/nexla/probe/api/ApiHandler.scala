package com.nexla.probe.api

import akka.actor.ActorSystem
import akka.http.scaladsl.server.Route._
import com.nexla.common.AppType
import com.nexla.sc.api.CommonApiHandler
import com.nexla.sc.config.NexlaCredsConf
import fr.davit.akka.http.metrics.core.HttpMetricsRegistry

class ApiHandler(nexla: NexlaCredsConf,
                 probeApiHandler: ProbeApiHandler,
                 fileStreamingApi: FileStreamingApi,
                 scalaEvalHandler: ScalaEvalHandler,
                 val registry: Option[HttpMetricsRegistry],
                 val envMap: java.util.Map[String, String])
                (implicit val system: ActorSystem)
  extends CommonApiHandler {

  private val basicAuthRoutes =
    seal {
      basicAuth(nexla, AppType.PROBE.appName) {
        _ => concat(
          scalaEvalHandler.route,
          probeApiHandler.route,
          fileStreamingApi.route,
        )
      }
    }

  val route = routesWithPrometheusMetrics(buildRoutes(routes = Some(basicAuthRoutes)))
}