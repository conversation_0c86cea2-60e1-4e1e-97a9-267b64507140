package com.nexla.probe.api.ray

import com.nexla.admin.client.AdminApiClient
import com.nexla.admin.client.oauth2.NexlaTokenProvider.sameToken
import com.nexla.common.ConnectionType
import com.nexla.connect.box.BoxConnectorService
import com.nexla.connector.ConnectorService
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.file.service.FileConnectorService
import com.nexla.listing.client.ListingClient
import com.nexla.probe.azure.blob.AzureBlobStoreConnectorService
import com.nexla.probe.azure.datalake.AzureDataLakeConnectorService
import com.nexla.probe.deltalake.DeltaLakeConnectorService
import com.nexla.probe.dropbox.DropboxConnectorService
import com.nexla.probe.ftp.FtpConnectorService
import com.nexla.probe.gcs.GCSConnectorService
import com.nexla.probe.gdrive.GDriveConnectorService
import com.nexla.probe.iceberg.IcebergConnectorService
import com.nexla.probe.onedrive.OneDriveConnectorService
import com.nexla.probe.s3.S3ConnectorService
import com.nexla.probe.sharepoint.SharepointConnectorService
import com.nexla.probe.webdav.WebDAVConnectorService

import java.util.Optional.empty
import scala.compat.java8.OptionConverters.RichOptionForJava8

object Utils {

  def getProbeService(connectionType: ConnectionType, adminApiClient: AdminApiClient, listingClient: ListingClient, decryptKey: String) = {
    if (connectionType == ConnectionType.FTP) {
      new FtpConnectorService(adminApiClient, listingClient, decryptKey)
    } else if (connectionType == ConnectionType.S3 || connectionType == ConnectionType.MERCURY_S3 || connectionType == ConnectionType.NEPTUNE_S3 || connectionType == ConnectionType.MIN_IO_S3) {
      new S3ConnectorService(adminApiClient, listingClient, decryptKey)
    } else if (connectionType == ConnectionType.GCS) {
      new GCSConnectorService(adminApiClient, listingClient, decryptKey, Option.empty.asJava)
    } else if (connectionType == ConnectionType.AZURE_BLB) {
      new AzureBlobStoreConnectorService(adminApiClient, listingClient, decryptKey)
    } else if (connectionType == ConnectionType.AZURE_DATA_LAKE) {
      new AzureDataLakeConnectorService(adminApiClient, listingClient, decryptKey)
    } else if (connectionType == ConnectionType.DELTA_LAKE_S3 || connectionType == ConnectionType.DELTA_LAKE_AZURE_BLB || connectionType == ConnectionType.DELTA_LAKE_AZURE_DATA_LAKE) {
      new DeltaLakeConnectorService(adminApiClient, listingClient, decryptKey)
    } else if (connectionType == ConnectionType.S3_ICEBERG) {
      new IcebergConnectorService(adminApiClient, listingClient, decryptKey)
    } else if (connectionType == ConnectionType.DROPBOX) {
      new DropboxConnectorService(adminApiClient, listingClient, decryptKey)
    } else if (connectionType == ConnectionType.BOX) {
      new BoxConnectorService(adminApiClient, listingClient, decryptKey, sameToken)
    } else if (connectionType == ConnectionType.ONEDRIVE) {
      new OneDriveConnectorService(adminApiClient, listingClient, decryptKey)
    } else if (connectionType == ConnectionType.GDRIVE) {
      new GDriveConnectorService(adminApiClient, listingClient, decryptKey)
    } else if (connectionType == ConnectionType.WEBDAV) {
      new WebDAVConnectorService
    } else if (connectionType == ConnectionType.SHAREPOINT) {
      new SharepointConnectorService(adminApiClient, listingClient, decryptKey)
    } else {
      throw new IllegalArgumentException(s"${connectionType.name} is not supported by RayDryRunnerUtils")
    }
  }
}
