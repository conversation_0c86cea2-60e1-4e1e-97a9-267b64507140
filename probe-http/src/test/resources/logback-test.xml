<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Set logging levels for testcontainers -->
    <logger name="org.testcontainers" level="INFO"/>
    <logger name="org.testcontainers.utility.ResourceReaper" level="DEBUG"/>
    <logger name="org.testcontainers.dockerclient" level="DEBUG"/>
    <logger name="com.github.dockerjava" level="INFO"/>
    
    <!-- Redis connection debugging -->
    <logger name="io.lettuce" level="DEBUG"/>
    <logger name="com.nexla.redis" level="DEBUG"/>
    
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
    
</configuration> 