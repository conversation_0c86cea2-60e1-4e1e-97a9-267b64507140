# Testcontainers configuration for Jenkins/CI environments
# Disable Ryuk (resource cleanup container) as it can cause issues in CI
testcontainers.reuse.enable=false
testcontainers.ryuk.disabled=true

# Configure for Docker-in-Docker scenarios
testcontainers.docker.client.strategy=org.testcontainers.dockerclient.EnvironmentAndSystemPropertyClientProviderStrategy

# Increase timeouts for CI environments
testcontainers.startup.timeout=120

# Log testcontainers activity for debugging
org.testcontainers=DEBUG 