package com.nexla.probe.listtree;

import com.fasterxml.jackson.core.JsonParser;
import com.nexla.common.probe.ColumnInfo;
import com.nexla.connector.config.jdbc.DbListTreeService;
import com.nexla.connector.config.jdbc.JdbcAuthConfig;
import com.nexla.test.UnitTests;
import one.util.streamex.StreamEx;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.HashMap;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;

/**
 * path 1: Multiple databases -> empty database structures
 * path 2: Single database -> multiple schemas -> schema structures
 * path 3: Single database -> single schema -> tables (skip schema layer)
 * path 4: Single database -> no schemas -> tables
 * path 5: Empty database list -> DEFAULT_DATABASE
 * path 6: Single null database -> DEFAULT_DATABASE
 * path 7: Direct schema query -> multiple schemas
 * path 8: Direct schema query -> no/single schema -> tables
 * path 9: Direct table query -> tables
 * path 10: Direct column query -> columns (with sorting verification)
 * path 11: Original null database test (preserved for compatibility)
 */
@Category(UnitTests.class)
public class DbListTreeIterationTest {
    private final ObjectMapper mapper = new ObjectMapper()
            .configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);

    private JdbcAuthConfig config;
    private TestDbListTreeService probeService;
    private DbListTreeIteration dbListTreeIteration;

    private static class TestDbListTreeService implements DbListTreeService<JdbcAuthConfig> {
        private final Map<String, StreamEx<String>> databasesMap = new HashMap<>();
        private final Map<String, StreamEx<String>> schemasMap = new HashMap<>();
        private final Map<String, StreamEx<String>> tablesMap = new HashMap<>();
        private final Map<String, List<ColumnInfo>> columnsMap = new HashMap<>();

        public void setDatabases(StreamEx<String> databases) {
            databasesMap.put("default", databases);
        }

        public void setSchemas(String database, StreamEx<String> schemas) {
            schemasMap.put(database, schemas);
        }

        public void setTables(String database, Optional<String> schema, StreamEx<String> tables) {
            String key = database + ":" + schema.orElse("empty");
            tablesMap.put(key, tables);
        }

        public void setColumns(String database, Optional<String> schema, String table, List<ColumnInfo> columns) {
            String key = database + ":" + schema.orElse("empty") + ":" + table;
            columnsMap.put(key, columns);
        }

        @Override
        public StreamEx<String> listDatabases(JdbcAuthConfig config) {
            return databasesMap.getOrDefault("default", StreamEx.empty());
        }

        @Override
        public StreamEx<String> listSchemas(String database, JdbcAuthConfig config) {
            return schemasMap.getOrDefault(database, StreamEx.empty());
        }

        @Override
        public StreamEx<String> listTables(String database, Optional<String> schema, JdbcAuthConfig config) {
            String key = database + ":" + schema.orElse("empty");
            return tablesMap.getOrDefault(key, StreamEx.empty());
        }

        @Override
        public List<ColumnInfo> listColumnInfos(String database, Optional<String> schema, String table, JdbcAuthConfig config) {
            String key = database + ":" + schema.orElse("empty") + ":" + table;
            return columnsMap.getOrDefault(key, Arrays.asList());
        }
    }

    private void assertSerializedEquals(String expectedJson, Map<String, Object> actualMap) {
        try {
            Map<String, Object> expectedMap = mapper
                    .readValue(expectedJson, new TypeReference<Map<String, Object>>(){});
            Assert.assertEquals(expectedMap, actualMap);
        } catch (Exception e) {
            throw new RuntimeException("JSON comparison failed", e);
        }
    }

    @Before
    public void setup() {
        config = mock(JdbcAuthConfig.class);
        probeService = new TestDbListTreeService();
        dbListTreeIteration = new DbListTreeIteration(config, probeService);
    }

    // path 1: listtreedatabase - multiple databases
    @Test
    public void testMultipleDatabases() {
        probeService.setDatabases(StreamEx.of("db1", "db2", "db3"));

        TreeMap<String, Object> result = dbListTreeIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty());

        String expectedJson = "{'db1': {'type': 'database', 'value': {}}, 'db2': {'type': 'database', 'value': {}}, 'db3': {'type': 'database', 'value': {}}}";
        assertSerializedEquals(expectedJson, result);
    }

    // path 2: listtreedatabase -> listtreeschema (multiple schemas)
    @Test
    public void testSingleDatabaseWithMultipleSchemas() {
        probeService.setDatabases(StreamEx.of("mssql_db"));
        probeService.setSchemas("mssql_db", StreamEx.of("dbo", "sales", "hr"));

        TreeMap<String, Object> result = dbListTreeIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty());

        String expectedJson = "{'mssql_db': {'type': 'database', 'value': {'dbo': {'type': 'schema', 'value': {}}, 'hr': {'type': 'schema', 'value': {}}, 'sales': {'type': 'schema', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    // path 3: listtreedatabase -> listtreeschema -> listtreetables (single schema)
    @Test
    public void testSingleDatabaseWithSingleSchema() {
        probeService.setDatabases(StreamEx.of("postgres_db"));
        probeService.setSchemas("postgres_db", StreamEx.of("public"));
        probeService.setTables("postgres_db", Optional.empty(), StreamEx.of("customers", "invoices"));

        TreeMap<String, Object> result = dbListTreeIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty());

        String expectedJson = "{'postgres_db': {'type': 'database', 'value': {'customers': {'type': 'table', 'value': {}}, 'invoices': {'type': 'table', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    // path 4: listtreedatabase -> listtreeschema -> listtreetables (no schemas)
    @Test
    public void testSingleDatabaseWithNoSchemas() {
        probeService.setDatabases(StreamEx.of("mysql_db"));
        probeService.setSchemas("mysql_db", StreamEx.empty());
        probeService.setTables("mysql_db", Optional.empty(), StreamEx.of("users", "orders"));

        TreeMap<String, Object> result = dbListTreeIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty());

        String expectedJson = "{'mysql_db': {'type': 'database', 'value': {'orders': {'type': 'table', 'value': {}}, 'users': {'type': 'table', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    // path 5: empty database list -> default_database
    @Test
    public void testEmptyDatabaseList() {
        probeService.setDatabases(StreamEx.empty());
        probeService.setSchemas(DbListTreeIteration.DEFAULT_DATABASE, StreamEx.of("schema1", "schema2"));

        TreeMap<String, Object> result = dbListTreeIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty());

        String expectedJson = "{'" + DbListTreeIteration.DEFAULT_DATABASE + "': {'type': 'database', 'value': {'schema1': {'type': 'schema', 'value': {}}, 'schema2': {'type': 'schema', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    // path 6: single null database -> default_database
    @Test
    public void testSingleNullDatabase() {
        probeService.setDatabases(StreamEx.of((String) null));
        probeService.setSchemas(DbListTreeIteration.DEFAULT_DATABASE, StreamEx.of("schema1", "schema2"));

        TreeMap<String, Object> result = dbListTreeIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty());

        String expectedJson = "{'" + DbListTreeIteration.DEFAULT_DATABASE + "': {'type': 'database', 'value': {'schema1': {'type': 'schema', 'value': {}}, 'schema2': {'type': 'schema', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    // path 7: direct schema query -> multiple schemas
    @Test
    public void testDirectSchemaQueryWithMultipleSchemas() {
        probeService.setSchemas("mssql_db", StreamEx.of("dbo", "sales", "hr"));

        TreeMap<String, Object> result = dbListTreeIteration
                .listTree(Optional.of("mssql_db"), Optional.empty(), Optional.empty());

        String expectedJson = "{'mssql_db': {'type': 'database', 'value': {'dbo': {'type': 'schema', 'value': {}}, 'hr': {'type': 'schema', 'value': {}}, 'sales': {'type': 'schema', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    // path 8: direct schema query -> single/empty schema -> tables
    @Test
    public void testDirectSchemaQueryWithNoSchemas() {
        probeService.setSchemas("db", StreamEx.empty());
        probeService.setTables("db", Optional.empty(), StreamEx.of("table1", "table2"));

        TreeMap<String, Object> result = dbListTreeIteration
                .listTree(Optional.of("db"), Optional.empty(), Optional.empty());

        String expectedJson = "{'db': {'type': 'database', 'value': {'table1': {'type': 'table', 'value': {}}, 'table2': {'type': 'table', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    // path 9: direct table query -> tables
    @Test
    public void testDirectTableQuery() {
        probeService.setTables("test_db", Optional.of("test_schema"), StreamEx.of("table1", "table2"));

        TreeMap<String, Object> result = dbListTreeIteration
                .listTree(Optional.of("test_db"), Optional.of("test_schema"), Optional.empty());

        String expectedJson = "{'test_db': {'type': 'database', 'value': {'table1': {'type': 'table', 'value': {}}, 'table2': {'type': 'table', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    // path 10: direct column query -> columns
    @Test
    public void testDirectColumnQuery() {
        ColumnInfo col1 = new ColumnInfo("id", true, "INTEGER", null);
        ColumnInfo col2 = new ColumnInfo("name", false, "VARCHAR(255)", null);
        ColumnInfo col3 = new ColumnInfo("created_at", false, "TIMESTAMP", "CURRENT_TIMESTAMP");

        probeService.setColumns("test_db", Optional.of("test_schema"), "users", Arrays.asList(col3, col1, col2)); // Intentionally unsorted

        TreeMap<String, Object> result = dbListTreeIteration
                .listTree(Optional.of("test_db"), Optional.of("test_schema"), Optional.of("users"));

        assertNotNull(result);
        Map<String, Object> dbMap = (Map<String, Object>) result.get("test_db");
        Map<String, Object> tables = (Map<String, Object>) dbMap.get("value");
        Map<String, Object> usersTable = (Map<String, Object>) tables.get("users");
        Assert.assertEquals("table", usersTable.get("type"));

        List<ColumnInfo> columns = (List<ColumnInfo>) usersTable.get("value");
        Assert.assertEquals(3, columns.size());
        // Verify columns are sorted by name
        Assert.assertEquals("created_at", columns.get(0).name);
        Assert.assertEquals("id", columns.get(1).name);
        Assert.assertEquals("name", columns.get(2).name);
    }

    // path 11: original test preserved
    @Test
    public void listTreeDatabaseWithDatabaseNameNull() {
        final String databaseName = null;
        probeService.setDatabases(StreamEx.of(databaseName));
        probeService.setSchemas(DbListTreeIteration.DEFAULT_DATABASE, StreamEx.of("my_schema_1", "my_schema_2"));

        TreeMap<String, Object> result = dbListTreeIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty());
        
        String expectedJson = "{'" + DbListTreeIteration.DEFAULT_DATABASE + "': {'type': 'database', 'value': {'my_schema_1': {'type': 'schema', 'value': {}}, 'my_schema_2': {'type': 'schema', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }
}