package com.nexla.probe.listtree;

import com.nexla.common.probe.ColumnInfo;
import com.nexla.connector.config.big_query.BigQuerySourceConnectorConfig;
import com.nexla.connector.config.jdbc.DbListTreeServicePaged;
import com.nexla.connector.config.jdbc.DbPage;
import com.nexla.test.UnitTests;
import one.util.streamex.StreamEx;
import org.junit.experimental.categories.Category;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@Category(UnitTests.class)
public class DbListTreeIterationPagedTest {

    private static final String DATABASE = "database";

    private static final String SCHEMA = "schema";

    private static final String TABLE = "table";

    @Mock
    private BigQuerySourceConnectorConfig bigQuerySourceConnectorConfig;

    @Mock
    private DbListTreeServicePaged<BigQuerySourceConnectorConfig> probeService;

    @InjectMocks
    private DbListTreeIterationPaged dbListTreeIterationPaged;

    @BeforeEach
    void setup() {
        bigQuerySourceConnectorConfig = mock(BigQuerySourceConnectorConfig.class);
        probeService = mock(DbListTreeServicePaged.class);

        dbListTreeIterationPaged = new DbListTreeIterationPaged(bigQuerySourceConnectorConfig, probeService);
    }

    @Test
    public void listDatabases() {
        when(probeService.listDatabases(bigQuerySourceConnectorConfig))
                .thenReturn(StreamEx.of(List.of("schema1", "schema2")));

        DbPage dbPage = dbListTreeIterationPaged.listTree(
                null,
                null,
                null,
                10,
                null);

        verifyNoMoreInteractions(probeService);
        Assertions.assertNotNull(dbPage);
    }

    @Test
    public void listSchemas() {
        when(probeService.listSchemas(DATABASE, bigQuerySourceConnectorConfig))
                .thenReturn(StreamEx.of(List.of("schema1", "schema2")));

        DbPage dbPage = dbListTreeIterationPaged.listTree(
                DATABASE,
                null,
                null,
                10,
                null);

        verifyNoMoreInteractions(probeService);
        Assertions.assertNotNull(dbPage);
    }

    @Test
    public void listSchemaAndListTables() {
        when(probeService.listSchemas(DATABASE, bigQuerySourceConnectorConfig))
                .thenReturn(StreamEx.of(List.of(SCHEMA)));

        when(probeService.listTables(bigQuerySourceConnectorConfig, DATABASE, null, 10, null))
                .thenReturn(DbPage.of(List.of("a")));

        DbPage dbPage = dbListTreeIterationPaged.listTree(
                DATABASE,
                null,
                null,
                10,
                null);

        verifyNoMoreInteractions(probeService);
        Assertions.assertNotNull(dbPage);
    }

    @Test
    public void listTablesPaged() {
        when(probeService.listTables(bigQuerySourceConnectorConfig, DATABASE, SCHEMA, 10, null))
                .thenReturn(DbPage.of(List.of("a")));

        DbPage dbPage = dbListTreeIterationPaged.listTree(
                DATABASE,
                SCHEMA,
                null,
                10,
                null);

        verifyNoMoreInteractions(probeService);
        Assertions.assertNotNull(dbPage);
    }

    @Test
    public void listColumns() {
        ColumnInfo columnInfo = new ColumnInfo("id", true, "integer", "0");
        when(probeService.listColumnInfos(DATABASE, Optional.of(SCHEMA), TABLE, bigQuerySourceConnectorConfig))
                .thenReturn(List.of(columnInfo));

        DbPage dbPage = dbListTreeIterationPaged.listTree(
                DATABASE,
                SCHEMA,
                TABLE,
                10,
                null);

        verifyNoMoreInteractions(probeService);
        Assertions.assertNotNull(dbPage);
    }

}
