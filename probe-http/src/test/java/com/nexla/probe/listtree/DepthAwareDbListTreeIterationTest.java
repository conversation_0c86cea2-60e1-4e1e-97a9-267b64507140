package com.nexla.probe.listtree;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.nexla.common.probe.ColumnInfo;
import com.nexla.connector.config.jdbc.DbListTreeService;
import com.nexla.connector.config.jdbc.JdbcAuthConfig;
import com.nexla.test.UnitTests;
import one.util.streamex.StreamEx;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.HashMap;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import static org.mockito.Mockito.mock;

@Category(UnitTests.class)
public class DepthAwareDbListTreeIterationTest {
    private final ObjectMapper mapper = new ObjectMapper()
            .configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);

    private JdbcAuthConfig config;

    private static class TestDbListTreeService implements DbListTreeService<JdbcAuthConfig> {
        private final Map<String, StreamEx<String>> databasesMap = new HashMap<>();
        private final Map<String, StreamEx<String>> schemasMap = new HashMap<>();
        private final Map<String, StreamEx<String>> tablesMap = new HashMap<>();
        private final Map<String, List<ColumnInfo>> columnsMap = new HashMap<>();

        public void setDatabases(StreamEx<String> databases) {
            databasesMap.put("default", databases);
        }

        public void setSchemas(String database, StreamEx<String> schemas) {
            schemasMap.put(database, schemas);
        }

        public void setTables(String database, Optional<String> schema, StreamEx<String> tables) {
            String key = database + ":" + schema.orElse("empty");
            tablesMap.put(key, tables);
        }

        public void setColumns(String database, Optional<String> schema, String table, List<ColumnInfo> columns) {
            String key = database + ":" + schema.orElse("empty") + ":" + table;
            columnsMap.put(key, columns);
        }

        @Override
        public StreamEx<String> listDatabases(JdbcAuthConfig config) {
            return databasesMap.getOrDefault("default", StreamEx.empty());
        }

        @Override
        public StreamEx<String> listSchemas(String database, JdbcAuthConfig config) {
            return schemasMap.getOrDefault(database, StreamEx.empty());
        }

        @Override
        public StreamEx<String> listTables(String database, Optional<String> schema, JdbcAuthConfig config) {
            String key = database + ":" + schema.orElse("empty");
            return tablesMap.getOrDefault(key, StreamEx.empty());
        }

        @Override
        public List<ColumnInfo> listColumnInfos(String database, Optional<String> schema, String table, JdbcAuthConfig config) {
            String key = database + ":" + schema.orElse("empty") + ":" + table;
            return columnsMap.getOrDefault(key, List.of());
        }
    }

    private void assertSerializedEquals(String expectedJson, Map<String, Object> actualMap) {
        TypeReference<Map<String, Object>> ref = new TypeReference<Map<String, Object>>() {};
        try {
            Map<String, Object> expectedMap = mapper.readValue(expectedJson, ref);
            Assert.assertEquals(
                    expectedJson,
                    expectedMap,
                    mapper.readValue(mapper.writeValueAsString(actualMap), ref)
            );
        } catch (Exception e) {
            throw new RuntimeException("JSON comparison failed", e);
        } catch (AssertionError e) {
            // Print the actual map for debugging
            try {
                System.err.println("Actual: " + mapper.writeValueAsString(actualMap));
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
            throw e; // rethrow the assertion error
        }
    }

    @Before
    public void setup() {
        config = mock(JdbcAuthConfig.class);
    }

    // =========================
    // DEPTH BEHAVIOR TESTS
    // =========================

    /**
     * Tests that depth 0 returns empty result regardless of available data.
     * Setup: Full hierarchy (databases, schemas, tables, columns) available.
     * Expected: Empty TreeMap because depth 0 should return nothing.
     */
    @Test
    public void testDepth0ReturnsEmpty() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.of("db1", "db2"));
        probeService.setSchemas("db1", StreamEx.of("schema1"));
        probeService.setSchemas("db2", StreamEx.of("schema2"));
        probeService.setTables("db1", Optional.of("schema1"), StreamEx.of("table1"));
        probeService.setTables("db2", Optional.of("schema2"), StreamEx.of("table2"));
        probeService.setColumns("db1", Optional.of("schema1"), "table1", List.of(
                new ColumnInfo("col1", false, "VARCHAR", null)
        ));
        probeService.setColumns("db2", Optional.of("schema2"), "table2", List.of(
                new ColumnInfo("col2", true, "INTEGER", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 0);

        String expectedJson = "{}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests depth 1 shows only databases with empty schema values.
     * Setup: Multiple databases with schemas, tables, and columns available.
     * Expected: Databases shown with empty values (schemas at depth 0).
     */
    @Test
    public void testDepth1ShowsDatabasesWithEmptySchemas() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.of("db1", "db2"));
        probeService.setSchemas("db1", StreamEx.of("schema1", "schema2"));
        probeService.setSchemas("db2", StreamEx.of("schema3"));
        // Set tables and columns that should NOT appear at depth 1
        probeService.setTables("db1", Optional.of("schema1"), StreamEx.of("table1", "table2"));
        probeService.setTables("db2", Optional.of("schema3"), StreamEx.of("table3"));
        probeService.setColumns("db1", Optional.of("schema1"), "table1", List.of(
                new ColumnInfo("col1", false, "VARCHAR", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 1);

        // At depth 1: databases shown, but schemas are at depth 0 (empty)
        String expectedJson = "{'db1': {'type': 'database', 'value': {}}, 'db2': {'type': 'database', 'value': {}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests depth 2 shows databases and schemas with empty table values.
     * Setup: Database with schemas, tables, and columns available.
     * Expected: Databases and schemas shown, but tables at depth 0 (empty).
     */
    @Test
    public void testDepth2ShowsDatabasesAndSchemasWithEmptyTables() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.of("db1"));
        probeService.setSchemas("db1", StreamEx.of("schema1", "schema2"));
        // Set tables and columns that should NOT appear at depth 2
        probeService.setTables("db1", Optional.of("schema1"), StreamEx.of("table1", "table2"));
        probeService.setTables("db1", Optional.of("schema2"), StreamEx.of("table3"));
        probeService.setColumns("db1", Optional.of("schema1"), "table1", Arrays.asList(
            new ColumnInfo("id", true, "INTEGER", null),
            new ColumnInfo("name", false, "VARCHAR", null)
        ));
        probeService.setColumns("db1", Optional.of("schema2"), "table3", List.of(
                new ColumnInfo("data", false, "TEXT", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 2);

        // At depth 2: databases and schemas shown, but tables are at depth 0 (empty)
        String expectedJson = "{'db1': {'type': 'database', 'value': {'schema1': {'type': 'schema', 'value': {}}, 'schema2': {'type': 'schema', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests depth 3 shows complete hierarchy including columns.
     * Setup: Database with schema, table, and columns.
     * Expected: Full hierarchy with tables containing their columns.
     */
    @Test
    public void testDepth3ShowsFullHierarchy() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.of("db1"));
        probeService.setSchemas("db1", StreamEx.of("schema1"));
        probeService.setTables("db1", Optional.of("schema1"), StreamEx.of("users"));
        probeService.setColumns("db1", Optional.of("schema1"), "users", Arrays.asList(
            new ColumnInfo("id", true, "INTEGER", null),
            new ColumnInfo("name", false, "VARCHAR(255)", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 3);

        // At depth 3: full hierarchy with columns
        String expectedJson = "{'db1': {'type': 'database', 'value': {'schema1': {'type': 'schema', 'value': {'users': {'type': 'table', 'value': [{'name': 'id', 'primaryKey': true, 'type': 'INTEGER', 'defaultValue': null}, {'name': 'name', 'primaryKey': false, 'type': 'VARCHAR(255)', 'defaultValue': null}]}}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests very large depth returns all available data.
     * Setup: Complete hierarchy with limited depth.
     * Expected: Same result as appropriate depth (no deeper data available).
     */
    @Test
    public void testVeryLargeDepthBeyondData() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.of("db1"));
        probeService.setSchemas("db1", StreamEx.of("schema1"));
        probeService.setTables("db1", Optional.of("schema1"), StreamEx.of("table1"));
        probeService.setColumns("db1", Optional.of("schema1"), "table1", List.of(
                new ColumnInfo("col1", false, "VARCHAR", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 1000);

        // Should return everything available (same as depth 3)
        String expectedJson = "{'db1': {'type': 'database', 'value': {'schema1': {'type': 'schema', 'value': {'table1': {'type': 'table', 'value': [{'name': 'col1', 'primaryKey': false, 'type': 'VARCHAR', 'defaultValue': null}]}}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests negative depth returns empty result.
     * Setup: Full hierarchy available.
     * Expected: Empty TreeMap (same as depth 0).
     */
    @Test
    public void testNegativeDepth() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.of("db1"));
        probeService.setSchemas("db1", StreamEx.of("schema1"));
        probeService.setTables("db1", Optional.of("schema1"), StreamEx.of("table1"));
        probeService.setColumns("db1", Optional.of("schema1"), "table1", List.of(
                new ColumnInfo("col1", false, "VARCHAR", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), -1);

        String expectedJson = "{}";
        assertSerializedEquals(expectedJson, result);
    }

    // =========================
    // DATABASE HANDLING TESTS
    // =========================

    /**
     * Tests single database behavior maintains actual database name.
     * Setup: Single database with schema, tables, and columns available.
     * Expected: Database shown with actual name (not DEFAULT_DATABASE).
     */
    @Test
    public void testSingleDatabaseUsesActualName() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.of("mydb"));
        probeService.setSchemas("mydb", StreamEx.of("schema1"));
        // Set tables and columns that should NOT appear at depth 2
        probeService.setTables("mydb", Optional.of("schema1"), StreamEx.of("table1"));
        probeService.setColumns("mydb", Optional.of("schema1"), "table1", List.of(
                new ColumnInfo("col1", false, "VARCHAR", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 2);

        String expectedJson = "{'mydb': {'type': 'database', 'value': {'schema1': {'type': 'schema', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests empty database list defaults to DEFAULT_DATABASE.
     * Setup: Empty database list with schemas and tables for DEFAULT_DATABASE.
     * Expected: Single database entry using DEFAULT_DATABASE name.
     */
    @Test
    public void testEmptyDatabaseListUsesDefault() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.empty());
        probeService.setSchemas(DepthAwareDbListTreeIteration.DEFAULT_DATABASE, StreamEx.of("schema1"));
        // Set tables and columns that should NOT appear at depth 2
        probeService.setTables(DepthAwareDbListTreeIteration.DEFAULT_DATABASE, Optional.of("schema1"), StreamEx.of("table1"));
        probeService.setColumns(DepthAwareDbListTreeIteration.DEFAULT_DATABASE, Optional.of("schema1"), "table1", List.of(
                new ColumnInfo("col1", false, "VARCHAR", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 2);

        String expectedJson = "{'" + DepthAwareDbListTreeIteration.DEFAULT_DATABASE + "': {'type': 'database', 'value': {'schema1': {'type': 'schema', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    // =========================
    // SCHEMA HANDLING TESTS
    // =========================

    /**
     * Tests schema flattening when no schemas exist.
     * Setup: Database with empty schema list, tables directly under database.
     * Expected: Tables appear directly under database (flattened structure).
     */
    @Test
    public void testNoSchemasCreatesNullSchema() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.of("mysql_db"));
        probeService.setSchemas("mysql_db", StreamEx.empty());
        probeService.setTables("mysql_db", Optional.empty(), StreamEx.of("users"));
        probeService.setColumns("mysql_db", Optional.empty(), "users", List.of(
                new ColumnInfo("id", true, "INTEGER", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 3);

        // No schemas creates a schema with null name
        String expectedJson = "{'mysql_db': {'type': 'database', 'value': {'users': {'type': 'table', 'value': [{'name': 'id', 'primaryKey': true, 'type': 'INTEGER', 'defaultValue': null}]}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests schema flattening when no schemas exist.
     * Setup: Database with empty schema list, tables directly under database.
     * Expected: Tables appear directly under database (flattened structure).
     */
    @Test
    public void testNoSchemasGoesToTables() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);

        probeService.setDatabases(StreamEx.of("mysql_db"));
        probeService.setSchemas("mysql_db", StreamEx.empty());
        probeService.setTables("mysql_db", Optional.empty(), StreamEx.of("users"));
        probeService.setColumns("mysql_db", Optional.empty(), "users", List.of(
                new ColumnInfo("id", true, "INTEGER", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 2);

        // No schemas creates a schema with null name
        String expectedJson = "{'mysql_db': {'type': 'database', 'value': {'users': {'type': 'table', 'value': [{'name': 'id', 'primaryKey': true, 'type': 'INTEGER', 'defaultValue': null}]}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    // =========================
    // DIRECT QUERY TESTS
    // =========================

    /**
     * Tests direct database query with depth 2 shows schemas and tables.
     * Setup: Specific database with schemas, tables, and columns.
     * Expected: Schemas and tables with columns shown (depth relative to database).
     */
    @Test
    public void testDirectDatabaseQuery() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setSchemas("specific_db", StreamEx.of("schema1", "schema2"));
        // Set tables and columns that should NOT appear at depth 2
        probeService.setTables("specific_db", Optional.of("schema1"), StreamEx.of("table1"));
        probeService.setTables("specific_db", Optional.of("schema2"), StreamEx.of("table2", "table3"));
        probeService.setColumns("specific_db", Optional.of("schema1"), "table1", List.of(
                new ColumnInfo("col1", false, "VARCHAR", null)
        ));
        probeService.setColumns("specific_db", Optional.of("schema2"), "table2", List.of(
                new ColumnInfo("col2", true, "INTEGER", null)
        ));
        probeService.setColumns("specific_db", Optional.of("schema2"), "table3", List.of());

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.of("specific_db"), Optional.empty(), Optional.empty(), 2);

        // Direct database query with depth 2 - schemas at depth 1, tables at depth 1, so tables with columns are shown
        String expectedJson = "{'specific_db': {'type': 'database', 'value': {'schema1': {'type': 'schema', 'value': {'table1': {'type': 'table', 'value': [{'name': 'col1', 'primaryKey': false, 'type': 'VARCHAR', 'defaultValue': null}]}}}, 'schema2': {'type': 'schema', 'value': {'table2': {'type': 'table', 'value': [{'name': 'col2', 'primaryKey': true, 'type': 'INTEGER', 'defaultValue': null}]}, 'table3': {'type': 'table', 'value': []}}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests direct database query with depth 1 shows only schemas.
     * Setup: Specific database with schemas, tables, and columns available.
     * Expected: Only schemas shown with empty values (tables at depth 0).
     */
    @Test
    public void testDirectDatabaseQueryDepth1() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setSchemas("specific_db", StreamEx.of("schema1", "schema2"));
        // Set tables and columns that should NOT appear at depth 1
        probeService.setTables("specific_db", Optional.of("schema1"), StreamEx.of("table1"));
        probeService.setColumns("specific_db", Optional.of("schema1"), "table1", List.of(
                new ColumnInfo("col1", false, "VARCHAR", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.of("specific_db"), Optional.empty(), Optional.empty(), 1);

        // Direct database query with depth 1 - shows schemas with empty tables
        String expectedJson = "{'specific_db': {'type': 'database', 'value': {'schema1': {'type': 'schema', 'value': {}}, 'schema2': {'type': 'schema', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests direct schema query with depth 1 shows tables with columns.
     * Setup: Schema with tables and columns.
     * Expected: Tables with columns shown (depth relative to schema level).
     */
    @Test
    public void testDirectSchemaQuery() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setTables("db1", Optional.of("schema1"), StreamEx.of("table1", "table2"));
        probeService.setColumns("db1", Optional.of("schema1"), "table1", List.of(
                new ColumnInfo("col1", false, "VARCHAR", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.of("db1"), Optional.of("schema1"), Optional.empty(), 1);

        // Direct schema query with depth 1 - table depth = 1, so columns are included
        String expectedJson = "{'db1':{'type':'database','value':{'schema1':{'type':'schema','value':{'table1':{'type':'table','value':[{'name':'col1','primaryKey':false,'type':'VARCHAR','defaultValue':null}]},'table2':{'type':'table','value':[]}}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests direct table query always shows columns regardless of depth.
     * Setup: Table with columns (sorted by name).
     * Expected: Table with sorted columns shown regardless of depth value.
     */
    @Test
    public void testDirectTableQuery() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setColumns("db1", Optional.of("schema1"), "users", Arrays.asList(
            new ColumnInfo("email", false, "VARCHAR(255)", null),
            new ColumnInfo("id", true, "INTEGER", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.of("db1"), Optional.of("schema1"), Optional.of("users"), 1);

        String expectedJson = "{'db1':{'type':'database','value':{'schema1':{'type':'schema','value':{'users':{'type':'table','value':[{'name':'email','primaryKey':false,'type':'VARCHAR(255)','defaultValue':null},{'name':'id','primaryKey':true,'type':'INTEGER','defaultValue':null}]}}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests multiple databases with nulls filters out null/empty values.
     * Setup: Database list with mix of valid, null, and empty database names.
     * Expected: Only valid databases shown, nulls and empties filtered out.
     */
    @Test
    public void testMultipleDatabasesWithNulls() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.of("db1", null, "db2", "", "db3"));
        probeService.setSchemas("db1", StreamEx.of("schema1"));
        probeService.setSchemas("db2", StreamEx.of("schema2"));
        probeService.setSchemas("db3", StreamEx.of("schema3"));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 2);

        // Null and empty databases should be filtered out, multiple non-null databases remain
        String expectedJson = "{'db1': {'type': 'database', 'value': {'schema1': {'type': 'schema', 'value': {}}}}, 'db2': {'type': 'database', 'value': {'schema2': {'type': 'schema', 'value': {}}}}, 'db3': {'type': 'database', 'value': {'schema3': {'type': 'schema', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests null and empty string databases get filtered out and use DEFAULT_DATABASE.
     * Setup: Database list with null and empty values, schemas for DEFAULT_DATABASE.
     * Expected: Invalid databases filtered out, DEFAULT_DATABASE used instead.
     */
    @Test
    public void testInvalidDatabasesFiltered() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.of((String) null));
        probeService.setSchemas(DepthAwareDbListTreeIteration.DEFAULT_DATABASE, StreamEx.of("schema1"));
        // Set tables and columns that should NOT appear at depth 2
        probeService.setTables(DepthAwareDbListTreeIteration.DEFAULT_DATABASE, Optional.of("schema1"), StreamEx.of("table1"));
        probeService.setColumns(DepthAwareDbListTreeIteration.DEFAULT_DATABASE, Optional.of("schema1"), "table1", List.of(
                new ColumnInfo("col1", false, "VARCHAR", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 2);

        // Null database gets filtered out, defaults to DEFAULT_DATABASE
        String expectedJson = "{'" + DepthAwareDbListTreeIteration.DEFAULT_DATABASE + "': {'type': 'database', 'value': {'schema1': {'type': 'schema', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    // =========================
    // EDGE CASES AND VALIDATION
    // =========================

    /**
     * Tests mix of null/blank and real schema names uses normal schema structure.
     * Setup: Database with mix of null, empty, and real schema names.
     * Expected: Normal schema structure maintained (not flattened) because real schema exists.
     */
    @Test
    public void testMixOfNullAndRealSchemas() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.of("db1"));
        probeService.setSchemas("db1", StreamEx.of(null, "real_schema", ""));
        probeService.setTables("db1", Optional.empty(), StreamEx.of("table_from_null"));
        probeService.setTables("db1", Optional.of("real_schema"), StreamEx.of("table_from_real"));
        probeService.setTables("db1", Optional.of(""), StreamEx.of("table_from_empty"));
        probeService.setColumns("db1", Optional.empty(), "table_from_null", List.of(
                new ColumnInfo("col1", false, "VARCHAR", null)
        ));
        probeService.setColumns("db1", Optional.of("real_schema"), "table_from_real", List.of(
                new ColumnInfo("col2", false, "VARCHAR", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 3);

        // Since there's at least one real schema, all non-null schemas should be shown normally
        String expectedJson = "{'db1':{'type':'database','value':{'':{'table_from_empty':{'type':'table','value':[]}},'real_schema':{'type':'schema','value':{'table_from_real':{'type':'table','value':[{'name':'col2','primaryKey':false,'type':'VARCHAR','defaultValue':null}]}}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests only blank/null schema names causes table flattening.
     * Setup: Database with only null/empty/blank schema names.
     * Expected: Tables flattened directly under database (no schema structure).
     */
    @Test
    public void testOnlyBlankSchemas() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.of("db1"));
        probeService.setSchemas("db1", StreamEx.of("   ", "", null));  // All blank/null
        probeService.setTables("db1", Optional.of("   "), StreamEx.of("table1"));
        probeService.setTables("db1", Optional.of(""), StreamEx.of("table2"));
        probeService.setTables("db1", Optional.empty(), StreamEx.of("table3"));
        probeService.setColumns("db1", Optional.of("   "), "table1", List.of(
                new ColumnInfo("col1", false, "VARCHAR", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 3);

        // No real schemas, so tables should be flattened directly under database
        String expectedJson = "{'db1':{'type':'database','value':{'table1':{'type':'table','value':[{'name':'col1','primaryKey':false,'type':'VARCHAR','defaultValue':null}]},'table2':{'type':'table','value':[]}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests empty lists at all hierarchy levels.
     * Setup: Empty databases, schemas, and tables.
     * Expected: DEFAULT_DATABASE with empty structure.
     */
    @Test
    public void testEmptyListsAtAllLevels() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        // Empty databases -> should use DEFAULT_DATABASE
        probeService.setDatabases(StreamEx.empty());
        probeService.setSchemas(DepthAwareDbListTreeIteration.DEFAULT_DATABASE, StreamEx.empty());
        probeService.setTables(DepthAwareDbListTreeIteration.DEFAULT_DATABASE, Optional.empty(), StreamEx.empty());

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 3);

        // Empty schemas -> null schema with empty tables
        String expectedJson = "{'" + DepthAwareDbListTreeIteration.DEFAULT_DATABASE + "': {'type': 'database', 'value': {}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests querying non-existent database returns empty structure.
     * Setup: No schemas defined for queried database.
     * Expected: Database structure with empty values.
     */
    @Test
    public void testQueryNonExistentDatabase() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        // Don't set any schemas for "nonexistent_db"

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.of("nonexistent_db"), Optional.empty(), Optional.empty(), 2);

        // No schemas for this database -> null schema with empty tables
        String expectedJson = "{'nonexistent_db': {'type': 'database', 'value': {}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests tables with no columns show empty column lists.
     * Setup: Tables without any column definitions.
     * Expected: Tables shown with empty column arrays.
     */
    @Test
    public void testTablesWithNoColumns() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.of("db1"));
        probeService.setSchemas("db1", StreamEx.of("schema1"));
        probeService.setTables("db1", Optional.of("schema1"), StreamEx.of("empty_table", "another_empty"));
        // Don't set any columns for these tables

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 3);

        String expectedJson = "{'db1': {'type': 'database', 'value': {'schema1': {'type': 'schema', 'value': {'another_empty': {'type': 'table', 'value': []}, 'empty_table': {'type': 'table', 'value': []}}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests columns are sorted alphabetically by name.
     * Setup: Table with columns in non-alphabetical order.
     * Expected: Columns returned sorted by name (a_column, m_column, z_column).
     */
    @Test
    public void testColumnSorting() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setDatabases(StreamEx.of("db1"));
        probeService.setSchemas("db1", StreamEx.of("schema1"));
        probeService.setTables("db1", Optional.of("schema1"), StreamEx.of("table1"));
        // Add columns in non-alphabetical order
        probeService.setColumns("db1", Optional.of("schema1"), "table1", Arrays.asList(
            new ColumnInfo("z_column", false, "VARCHAR", null),
            new ColumnInfo("a_column", true, "INTEGER", null),
            new ColumnInfo("m_column", false, "TEXT", "default")
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.empty(), Optional.empty(), Optional.empty(), 3);

        // Columns should be sorted by name
        String expectedJson = "{'db1': {'type': 'database', 'value': {'schema1': {'type': 'schema', 'value': {'table1': {'type': 'table', 'value': [{'name': 'a_column', 'primaryKey': true, 'type': 'INTEGER', 'defaultValue': null}, {'name': 'm_column', 'primaryKey': false, 'type': 'TEXT', 'defaultValue': 'default'}, {'name': 'z_column', 'primaryKey': false, 'type': 'VARCHAR', 'defaultValue': null}]}}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

    /**
     * Tests direct schema query maintains structure even for non-existent schema.
     * Setup: Database with no schemas, query specific schema name.
     * Expected: Schema structure maintained with empty table list.
     */
    @Test
    public void testDirectSchemaQueryOnFlattenedDatabase() {
        TestDbListTreeService probeService = new TestDbListTreeService();
        DepthAwareDbListTreeIteration<JdbcAuthConfig> depthAwareIteration = new DepthAwareDbListTreeIteration<>(config, probeService);
        
        probeService.setSchemas("mysql_db", StreamEx.empty()); // No schemas
        probeService.setTables("mysql_db", Optional.empty(), StreamEx.of("table1"));
        probeService.setTables("mysql_db", Optional.of("some_schema"), StreamEx.empty()); // No tables for this specific schema
        probeService.setColumns("mysql_db", Optional.empty(), "table1", List.of(
                new ColumnInfo("col1", false, "VARCHAR", null)
        ));

        TreeMap<String, Object> result = depthAwareIteration
                .listTree(Optional.of("mysql_db"), Optional.of("some_schema"), Optional.empty(), 1);

        // Structure is maintained even when querying non-existent schema, but tables list is empty
        String expectedJson = "{'mysql_db': {'type': 'database', 'value': {'some_schema': {'type': 'schema', 'value': {}}}}}";
        assertSerializedEquals(expectedJson, result);
    }

}