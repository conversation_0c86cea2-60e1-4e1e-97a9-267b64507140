package com.nexla.probe

import com.bazaarvoice.jolt.JsonUtils
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import com.google.common.collect.ImmutableMap
import com.nexla.admin.client._
import com.nexla.common.ConnectionType._
import com.nexla.common.ListingResourceType.{FILE, FOLDER}
import com.nexla.common.StreamUtils.map
import com.nexla.common.exception.ProbeRetriableException
import com.nexla.common.probe._
import com.nexla.common.{ConnectionType, NexlaDataCredentials, NexlaFile}
import com.nexla.connector.ConnectorService
import com.nexla.connector.ConnectorService.UNIT_TEST
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.connector.config.vault.NexlaAppConfig
import com.nexla.probe.ProbeControllerTest.mapper
import com.nexla.sc.config.NexlaCredsEncodedConf
import com.nexla.transform.schema.FormatDetector
import one.util.streamex.StreamEx
import org.apache.kafka.common.config.AbstractConfig
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.mockito.{ArgumentMatchers, Mockito}
import org.mockserver.integration.ClientAndServer
import org.mockserver.integration.ClientAndServer.startClientAndServer
import org.mockserver.matchers.MatchType
import org.mockserver.model.Header
import org.mockserver.model.HttpRequest.request
import org.mockserver.model.HttpResponse.response
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfterEach, OneInstancePerTest, TagAnnotation}
import org.springframework.http.HttpStatus
import org.springframework.web.client.HttpClientErrorException

import java.util
import java.util.function.Supplier
import java.util.{Base64, Optional}
import scala.collection.JavaConverters._
import scala.collection.immutable.TreeMap
import scala.collection.mutable.Buffer

@TagAnnotation("com.nexla.test.ScalaUnitTests")
object ProbeControllerTest {
  val mapper = new ObjectMapper().registerModule(DefaultScalaModule)
}

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class ProbeControllerTest
  extends AnyFunSuite
    with Matchers
    with OneInstancePerTest
    with BeforeAndAfterEach {

  FormatDetector.initDefault()

  val probeServiceMock = mock(classOf[ConnectorService[_]])

  val props: AppProps = mock(classOf[AppProps])
  Mockito.when(props.nexlaCredsEncoded).thenReturn(NexlaCredsEncodedConf("", ""))
  Mockito.when(props.consumerMaxPartitionFetchBytes).thenReturn(None)
  Mockito.when(props.consumerFetchMaxBytes).thenReturn(None)

  val appConfig: NexlaAppConfig = mock(classOf[NexlaAppConfig])

  val probeController = new ProbeController(null, null, props, appConfig, null, null, null) {
    override def getProbeService(connectionType: ConnectionType): ConnectorService[_] = {
      probeServiceMock
    }

    override def parseAuthConfig(decryptKey: String,
                                 probeInput: ProbeInput,
                                 connectionType: ConnectionType): BaseAuthConfig = null
  }

  test("treeify") {

    val files = Buffer(
      "/yandex/logs/",
      "/home/<USER>",
      "/documents/test/json",
      "/documents/test/xml")
      .map(fullPath => {
        val file = new NexlaFile()
        file.setFullPath(fullPath)
        file.setType(FOLDER)
        file
      })

    val file = new NexlaFile()
    file.setFullPath("/documents/test/json/test.json")
    file.setType(FILE)
    files += file

    val props = mock(classOf[AppProps])
    Mockito.when(props.nexlaCredsEncoded).thenReturn(NexlaCredsEncodedConf("", ""))
    Mockito.when(props.consumerMaxPartitionFetchBytes).thenReturn(None)
    Mockito.when(props.consumerFetchMaxBytes).thenReturn(None)

    val appConfig = mock(classOf[NexlaAppConfig])

    val result = new ProbeController(null, null, props, appConfig, null, null, null)
      .treeify(files.asJava)

    val expected = TreeMap(
      "/" -> TreeMap(
        "type" -> "folder",
        "value" -> TreeMap(
          "yandex" -> TreeMap(
            "type" -> "folder",
            "value" -> TreeMap(
              "logs" -> TreeMap(
                "type" -> "folder",
                "value" -> TreeMap
              )
            )
          ),
          "home" -> TreeMap(
            "type" -> "folder",
            "value" -> TreeMap(
              "ubuntu" -> TreeMap(
                "type" -> "folder",
                "value" -> TreeMap
              )
            )
          ),
          "documents" -> TreeMap(
            "type" -> "folder",
            "value" -> TreeMap(
              "test" -> TreeMap(
                "type" -> "folder",
                "value" -> TreeMap(
                  "json" -> TreeMap(
                    "type" -> "folder",
                    "value" -> TreeMap(
                      "test.json" -> TreeMap(
                        "created_at" -> null,
                        "size" -> null,
                        "type" -> "file",
                        "updated_at" -> null,
                        "value" -> TreeMap
                      )
                    )
                  ),
                  "xml" -> TreeMap(
                    "type" -> "folder",
                    "value" -> TreeMap
                  )
                )
              )
            )
          )
        )
      ),
      "meta" -> TreeMap(
        "type" -> "meta",
        "value" -> TreeMap
      )
    )

    mapper.writeValueAsString(expected) shouldEqual mapper.writeValueAsString(result)
  }

  test("read source sample no resource") {

    doReturn(ProbeSampleResult.EMPTY_SAMPLE, ProbeSampleResult.EMPTY_SAMPLE).when(probeServiceMock).readSample(any[AbstractConfig](), ArgumentMatchers.eq(false))

    val responseEntity = probeController.readSample(
      REST, new ProbeInput(-1, "", "", baseConfig), Optional.empty(), Optional.empty(), Optional.empty())

    responseEntity.get("statusCode") shouldEqual 404
    responseEntity.get("errorMessage") shouldEqual "Not Found"
  }

  test("read source sample client error") {
    doThrow(new ProbeRetriableException(new HttpClientErrorException(HttpStatus.UNAUTHORIZED))).when(probeServiceMock).readSample(any[AbstractConfig](), ArgumentMatchers.eq(false))

    val responseEntity = probeController.readSample(
      REST, new ProbeInput(-1, "", "", baseConfig), Optional.empty(), Optional.empty(), Optional.empty())

    responseEntity.get("statusCode") shouldEqual 401
    responseEntity.get("errorMessage") shouldEqual "401 UNAUTHORIZED"
    responseEntity.get("response") shouldEqual ""
  }

  test("read source sample error") {
    doThrow(new ProbeRetriableException(new IllegalArgumentException("URI is not absolute"))).when(probeServiceMock).readSample(any[AbstractConfig](), ArgumentMatchers.eq(false))

    val responseEntity = probeController.readSample(
      REST, new ProbeInput(-1, "", "", baseConfig), Optional.empty(), Optional.empty(), Optional.empty())

    responseEntity.get("errorMessage") shouldEqual "URI is not absolute"
    responseEntity.get("statusCode") shouldEqual 500
  }

  test("read source sample success") {
    val strings: StreamEx[ProbeSampleResultEntry[String]] = StreamEx.of(util.Arrays.asList("sample")).map(t => new ProbeSampleResultEntry[String](t))
    val response = new StringSampleResult(strings.toList, Optional.empty(), false)
    doReturn(response, response).when(probeServiceMock).readSample(any[AbstractConfig](), ArgumentMatchers.eq(false))

    val responseEntity = probeController.readSample(
      REST, new ProbeInput(-1, "", "", baseConfig), Optional.empty(), Optional.empty(), Optional.empty())

    val body = ImmutableMap.of("response", "sample",
      "statusCode", 200,
      "contentType", "text/plain")

    responseEntity shouldEqual body
  }

  test("read source SQL sample success") {
    val lhm1 = new util.LinkedHashMap[String, AnyRef](Map("col1" -> "val1", "col2" -> "val2").asJava)
    val lhm2 = new util.LinkedHashMap[String, AnyRef](Map("col1" -> "_val1", "col2" -> "_val2").asJava)
    val columns = Set("col1", "col2").asJava

    val list = new util.ArrayList[ProbeSampleResultEntry[util.LinkedHashMap[String, Object]]]()
    list.add(new ProbeSampleResultEntry(lhm1))
    list.add(new ProbeSampleResultEntry(lhm2))

    val response = new SqlSampleResult(list, columns);
    doReturn(response, response).when(probeServiceMock).readSample(any[AbstractConfig](), ArgumentMatchers.eq(false))

    val responseEntity = probeController.readSample(
      POSTGRES, new ProbeInput(-1, "", "", baseConfig), Optional.empty(), Optional.empty(), Optional.empty())

    val expectedString = "{\"response\":{\"data\":[{\"col1\":\"val1\",\"col2\":\"val2\"},{\"col1\":\"_val1\",\"col2\":\"_val2\"}],\"columns\":[\"col1\",\"col2\"]},\"statusCode\":200,\"contentType\":\"application/json\"}"

    JsonUtils.toJsonString(responseEntity) shouldEqual expectedString
  }

  test("do not allow executing DDL queries in the database source sampling") {
    val lhm1 = new util.LinkedHashMap[String, AnyRef](Map("col1" -> "val1", "col2" -> "val2").asJava)
    val lhm2 = new util.LinkedHashMap[String, AnyRef](Map("col1" -> "_val1", "col2" -> "_val2").asJava)
    val columns = Set("col1", "col2").asJava

    val list = new util.ArrayList[ProbeSampleResultEntry[util.LinkedHashMap[String, Object]]]()
    list.add(new ProbeSampleResultEntry(lhm1))
    list.add(new ProbeSampleResultEntry(lhm2))

    val response = new SqlSampleResult(list, columns);
    doReturn(response, response).when(probeServiceMock).readSample(any[AbstractConfig](), ArgumentMatchers.eq(false))

    val req =
      """
        |{"db_query_mode":"Query","start.cron":"0 5 22 1/1 * ? *","query":"CREATE TABLE my_table (\n    id INT PRIMARY KEY,\n    name VARCHAR(100),\n    age INT\n);","referenced_resource_ids":{"data_sets":[],"data_maps":[],"data_credentials":[],"code_containers":[]}}
        |""".stripMargin
        val updCfg = baseConfig
          updCfg.asInstanceOf[util.Map[String, AnyRef]].putAll(JsonUtils.jsonToMap(req))
    val responseEntity = probeController.readSample(
      REDSHIFT, new ProbeInput(-1, "", "", updCfg), Optional.empty(), Optional.empty(), Optional.empty())

    val expectedString = "{\"statusCode\":403,\"errorMessage\":\"DDL/DML queries containing [DELETE , INSERT , REVOKE , DROP , UPDATE , TRUNCATE , RENAME , ALTER , COMMENT , GRANT , CREATE , MERGE ] are prohibited in the source sampling\"}"
    JsonUtils.toJsonString(responseEntity) shouldEqual expectedString
  }

  test("read source sample file success") {
    val response = new Supplier[StringSampleResult] {
      override def get(): StringSampleResult = {
        val value: StreamEx[ProbeSampleResultEntry[String]] = StreamEx.of("zoo", "asb", "sample").map(t => new ProbeSampleResultEntry(t))
        new StringSampleResult(value.toList, Optional.empty(), false)
      }
    }

    doReturn(response.get(), response.get()).when(probeServiceMock).readSample(any[AbstractConfig](), ArgumentMatchers.eq(false))

    val responseEntity = probeController.readSample(
      S3, new ProbeInput(-1, "", "", baseConfig), Optional.empty(), Optional.of(1), Optional.of(1))

    val body = ImmutableMap.of("response", "asb",
      "statusCode", 200,
      "contentType", "text/plain")

    responseEntity shouldEqual body

    val responseEntity2 = probeController.readSample(
      S3, new ProbeInput(-1, "", "", baseConfig), Optional.empty(), Optional.of(1), Optional.empty())

    val body2 = ImmutableMap.of("response", "asb\nsample",
      "statusCode", 200,
      "contentType", "text/plain")

    responseEntity2 shouldEqual body2
  }

  val mockServerPort = 3141
  var mockServer: ClientAndServer = _
  val mockAdminApi = Mockito.mock(classOf[AdminApiClient])
  Mockito.when(mockAdminApi.getDataSink(1)).thenReturn(Optional.of(new DataSink()))

  val sinkConfig =
    s"""
       |{
       |  "body.template": "{message.json}",
       |  "content.type": "application/json",
       |  "create.datasource": "false",
       |  "mapping": {
       |    "mode": "auto"
       |  },
       |  "method": "POST",
       |  "sink_type": "rest",
       |  "url.template": "http://localhost:$mockServerPort/test"
       |}
       |""".stripMargin
  val input =
    """
      |[
      |  {
      |    "rawMessage": {
      |      "foo": 1,
      |      "bar": "baz"
      |    }
      |  }
      |]
      |""".stripMargin

  val params = baseConfig
  params.put("input", input)
  params.put("sink_config", sinkConfig)
  val probeInput = new ProbeInput(
    -1,
    null,
    null,
    params
  )

  override def beforeEach(): Unit = {
    mockServer = startClientAndServer(mockServerPort)
  }

  override def afterEach(): Unit = mockServer.stop()

  val successMessage = "all good here :)"
  val errorMessage = "oh no :("

  test("pass input through when testing REST sink") {
    val probeController = new ProbeController(mockAdminApi, null, props, appConfig, null, null, null)

    mockServer.when(request withPath "/test" withBody "{message.json}")
      .respond(response withStatusCode HttpStatus.NOT_ACCEPTABLE.value() withBody errorMessage)
    mockServer.when(request withPath "/test" withBody "{\"foo\":1,\"bar\":\"baz\"}")
      .respond(response withStatusCode HttpStatus.OK.value() withBody successMessage)

    val res = probeController.readSample(ConnectionType.REST, probeInput, Optional.empty(), Optional.empty(), Optional.empty())
    res.get("statusCode") shouldEqual HttpStatus.OK.value()
    res.get("response") shouldEqual successMessage
  }

  test("pass status code through REST sink tests") {
    val probeController = new ProbeController(mockAdminApi, null, props, appConfig, null, null, null)

    mockServer.when(request withPath "/test")
      .respond(response withStatusCode HttpStatus.I_AM_A_TEAPOT.value() withBody errorMessage)

    val res = probeController.readSample(ConnectionType.REST, probeInput, Optional.empty(), Optional.empty(), Optional.empty())
    res.get("statusCode") shouldEqual HttpStatus.I_AM_A_TEAPOT.value()
    res.get("response") shouldEqual errorMessage
  }

  test("batched REST sink tests (array of records)") {
    val sinkConfig =
      s"""
         |{
         |  "batch.mode": "true",
         |  "batch.size": "100",
         |  "body.template": "{message.json}",
         |  "body.transform.function": "messages",
         |  "content.type": "application/json",
         |  "create.datasource": "false",
         |  "mapping": {
         |    "mode": "auto"
         |  },
         |  "method": "POST",
         |  "sink_type": "rest",
         |  "ui.batch_algo": "ALGO_ARRAY",
         |  "ui.batch_fieldname": "data",
         |  "url.template": "http://localhost:$mockServerPort/test"
         |}
         |""".stripMargin

    val params = baseConfig
    params.put("input", input)
    params.put("sink_config", sinkConfig)
    val probeInput = new ProbeInput(
      -1,
      null,
      null,
      params
    )

    val probeController = new ProbeController(mockAdminApi, null, props, appConfig, null, null, null)

    mockServer.when(request withPath "/test" withBody "[{\"foo\":1,\"bar\":\"baz\"}]")
      .respond(response withStatusCode HttpStatus.OK.value() withBody successMessage)

    val res = probeController.readSample(ConnectionType.REST, probeInput, Optional.empty(), Optional.empty(), Optional.empty())
    res.get("statusCode") shouldEqual HttpStatus.OK.value()
    res.get("response") shouldEqual successMessage
  }

  test("batched REST sink tests (property inside JSON)") {
    val sinkConfig =
      s"""
         |{
         |  "batch.mode": "true",
         |  "batch.size": "100",
         |  "body.template": "{message.json}",
         |  "body.transform.function": "Map(\\\"data\\\" -> messages)",
         |  "content.type": "application/json",
         |  "create.datasource": "false",
         |  "mapping": {
         |    "mode": "auto"
         |  },
         |  "method": "POST",
         |  "sink_type": "rest",
         |  "ui.batch_algo": "ALGO_JSON",
         |  "ui.batch_fieldname": "data",
         |  "ui.body.transform.function": "{}",
         |  "url.template": "http://localhost:$mockServerPort/test"
         |}
         |""".stripMargin

    val params = baseConfig
    params.put("input", input)
    params.put("sink_config", sinkConfig)
    val probeInput = new ProbeInput(
      -1,
      null,
      null,
      params
    )

    val probeController = new ProbeController(mockAdminApi, null, props, appConfig, null, null, null)

    mockServer.when(request withPath "/test" withBody "{\"data\":[{\"foo\":1,\"bar\":\"baz\"}]}")
      .respond(response withStatusCode HttpStatus.OK.value() withBody successMessage)

    val res = probeController.readSample(ConnectionType.REST, probeInput, Optional.empty(), Optional.empty(), Optional.empty())
    res.get("statusCode") shouldEqual HttpStatus.OK.value()
    res.get("response") shouldEqual successMessage
  }

  test("batched REST sink tests (code)") {
    val sinkConfig =
      s"""
         |{
         |  "batch.mode": "true",
         |  "batch.size": "100",
         |  "body.template": "{message.json}",
         |  "body.transform.function": "messages.map(m => m)",
         |  "content.type": "application/json",
         |  "create.datasource": "false",
         |  "mapping": {
         |    "mode": "auto"
         |  },
         |  "method": "POST",
         |  "sink_type": "rest",
         |  "ui.batch_algo": "ALGO_CODE",
         |  "ui.batch_fieldname": "data",
         |  "url.template": "http://localhost:$mockServerPort/test"
         |}
         |""".stripMargin

    val params = baseConfig
    params.put("input", input)
    params.put("sink_config", sinkConfig)
    val probeInput = new ProbeInput(
      -1,
      null,
      null,
      params
    )

    val probeController = new ProbeController(mockAdminApi, null, props, appConfig, null, null, null)

    mockServer.when(request withPath "/test" withBody "[{\"foo\":1,\"bar\":\"baz\"}]")
      .respond(response withStatusCode HttpStatus.OK.value() withBody successMessage)

    val res = probeController.readSample(ConnectionType.REST, probeInput, Optional.empty(), Optional.empty(), Optional.empty())
    res.get("statusCode") shouldEqual HttpStatus.OK.value()
    res.get("response") shouldEqual successMessage
  }

  private val codeContainerId = 1
  private val transformCode =
    """def transform(input, metadata, args):
      |    input['baz'] = input['foo'] + input['bar']
      |    return input""".stripMargin
  private val encodedTransformCode = new String(Base64.getEncoder.encode(transformCode.getBytes))

  private val transform = new Transform(
    codeContainerId,
    "test transform",
    true,
    true,
    null,
    null,
    "test",
    "jolt_custom",
    new util.ArrayList[TransformCode]() {{
      add(new TransformCode("nexla.custom", new TransformCodeSpec(null, "python", "base64", encodedTransformCode)))
    }},
    "record"
  )

  test("run code container in source probe (single step)") {
    val iterations =
      s"""
        |[
        |  {
        |    "iteration.type": "code.container",
        |    "method": "GET",
        |    "key": "step1",
        |    "url.template": "http://localhost:$mockServerPort/test",
        |    "code.container.id": $codeContainerId
        |  }
        |]
        |""".stripMargin

    val params = baseConfig
    params.put("rest.iterations", iterations)
    val probeInput = new ProbeInput(-1, null, null, params)

    Mockito.when(mockAdminApi.getTransform(codeContainerId)).thenReturn(transform)

    val probeController = new ProbeController(mockAdminApi, null, props, appConfig, null, null, null)

    mockServer.when(request withPath "/test")
      .respond(response withStatusCode HttpStatus.OK.value() withBody "{\"foo\": 1, \"bar\": 2}")

    val res = probeController.readSample(ConnectionType.REST, probeInput, Optional.empty(), Optional.empty(), Optional.empty())
    val expected = Map("foo" -> 1, "bar" -> 2, "baz" -> 3)
    val actual = mapper.readValue(res.get("response").asInstanceOf[String], classOf[Map[String,Int]])
    actual shouldEqual expected
  }

  test("run code container in source probe (multi step)") {
    val codeContainerId = 1
    val iterations =
      s"""
         |[
         |  {
         |    "iteration.type": "static.url",
         |    "method": "GET",
         |    "key": "step1",
         |    "url.template": "http://localhost:$mockServerPort/test"
         |  },
         |  {
         |    "iteration.type": "code.container",
         |    "key": "step2",
         |    "code.container.id": $codeContainerId
         |  }
         |]
         |""".stripMargin

    val params = baseConfig
    params.put("rest.iterations", iterations)
    val probeInput = new ProbeInput(-1, null, null, params)

    Mockito.when(mockAdminApi.getTransform(codeContainerId)).thenReturn(transform)

    val probeController = new ProbeController(mockAdminApi, null, props, appConfig, null, null, null)

    mockServer.when(request withPath "/test")
      .respond(response withStatusCode HttpStatus.OK .value() withBody "{\"foo\": 1, \"bar\": 2}")

    val res = probeController.readSample(ConnectionType.REST, probeInput, Optional.empty(), Optional.empty(), Optional.empty())
    val expected = Map("foo" -> 1, "bar" -> 2, "baz" -> 3)
    val actual = mapper.readValue(res.get("response").asInstanceOf[String], classOf[Map[String,Int]])
    actual shouldEqual expected
  }

  test("processes substitutions with multi-step REST source probes") {
    val iterations =
      s"""
         |[
         |  {
         |    "iteration.type": "static.url",
         |    "method": "GET",
         |    "key": "step1",
         |    "url.template": "http://localhost:$mockServerPort/test"
         |  },
         |  {
         |    "iteration.type": "static.url",
         |    "method": "GET",
         |    "key": "step2",
         |    "url.template": "http://localhost:$mockServerPort/{step1.foo}"
         |  }
         |]
         |""".stripMargin

    val params = baseConfig
    params.put("rest.iterations", iterations)
    val probeInput = new ProbeInput(-1, null, null, params)

    val probeController = new ProbeController(mockAdminApi, null, props, appConfig, null, null, null)

    mockServer.when(request withPath "/test")
      .respond(response withStatusCode HttpStatus.OK .value() withBody "{\"foo\": 1, \"bar\": 2}")
    mockServer.when(request withPath "/1")
      .respond(response withStatusCode HttpStatus.OK .value() withBody "{\"baz\": 3}")

    val res = probeController.readSample(ConnectionType.REST, probeInput, Optional.empty(), Optional.empty(), Optional.empty())
    val expected = Map("baz" -> 3)
    val actual = mapper.readValue(res.get("response").asInstanceOf[String], classOf[Map[String,Int]])
    actual shouldEqual expected
  }

  test("REST oauth2 sink") {
    import org.mockserver.model
    import org.mockserver.model.{JsonBody, MediaType}

    // it is batched to be able to mock static NexlaDataCredentials#getCreds method, so the code is executed in the test's thread.
    val accessToken = "12"

    val sinkConfig =
      s"""
         |{
         |  "mapping": {
         |      "mode": "auto",
         |      "tracker_mode": "NONE"
         |  },
         |  "body.template": "{message.json}",
         |  "method": "PATCH",
         |  "content.type": "application/json",
         |  "create.datasource": false,
         |  "sink_type": "rest",
         |
         |  "ui.batch_algo": "ALGO_CODE",
         |  "url.template": "http://localhost:${mockServer.getPort}/test",
         |  "advanced_templatized_api": true,
         |  "batch.mode": true,
         |  "max.poll.records": 200,
         |  "body.transform.function": "messages.map(m => m)",
         |
         |  "auth.type" : "OAUTH2",
         |  "test.content.type" : "application/json",
         |  "vendor.auth_scheme" : "header",
         |  "vendor.refresh_token" : "1",
         |  "vendor.last_refreshed_at" : "2024-01-01T00:00:00Z",
         |  "vendor.token_type" : "Bearer",
         |  "test.method" : "GET",
         |  "oauth2.access.token.method" : "POST",
         |  "oauth2.token.type.override" : "Bearer",
         |  "credentials_type" : "rest",
         |
         |  "credentials.decrypt.key": "decrypt.key"
         |}
         |""".stripMargin

    val input =
      """
        |[
        |  {
        |    "rawMessage": {
        |      "Review #": "2580",
        |      "Brand": "New Touch",
        |      "Variety": "Ts Restaurant Tantanmen",
        |      "Style": "Cup",
        |      "Country": "Japan",
        |      "Stars": "3.75",
        |      "Top Ten": ""
        |    }
        |  }
        |]
        |""".stripMargin

    val params = baseConfig
    params.put("input", input)
    params.put("sink_config", sinkConfig)

    val probeController = new ProbeController(mockAdminApi, null, props, appConfig, null, null, null)

    val request = model.HttpRequest.request()
      .withPath("/test")
      .withMethod("PATCH")
      .withHeaders(new Header("Authorization", "Bearer " + accessToken))
      .withBody(new JsonBody(
        """
          |[{
          |  "Review #" : "2580",
          |  "Brand" : "New Touch",
          |  "Variety" : "Ts Restaurant Tantanmen",
          |  "Style" : "Cup",
          |  "Country" : "Japan",
          |  "Stars" : "3.75",
          |  "Top Ten" : ""
          |}]
          |""".stripMargin,
        MatchType.ONLY_MATCHING_FIELDS
      ))

    mockServer.when(request)
      .respond(
        model.HttpResponse.response()
          .withStatusCode(HttpStatus.OK.value())
          .withContentType(MediaType.APPLICATION_JSON)
          .withBody(successMessage)
      )

    val credId = -1;

    val dataCredentials = new DataCredentials

    dataCredentials.setCredentialsEnc("")
    dataCredentials.setCredentialsEncIv("")
    when(mockAdminApi.getDataCredentials(credId)).thenReturn(Optional.of(dataCredentials))


     val nexlaDataCredentialsMock = mockStatic(classOf[NexlaDataCredentials]);

    when(NexlaDataCredentials.getCreds("decrypt.key", "", ""))
          .thenReturn(util.Map.of("vendor.access_token", accessToken));


    val res = probeController.readSample(ConnectionType.REST, new ProbeInput(
      credId,
      null,
      null,
      params
    ), Optional.empty(), Optional.empty(), Optional.empty())
    res.get("statusCode") shouldEqual HttpStatus.OK.value()
    res.get("response") shouldEqual successMessage

    println(res.get("response"))
    nexlaDataCredentialsMock.close()
  }

  private def baseConfig: util.Map[String, String] = map(UNIT_TEST, "true")
}