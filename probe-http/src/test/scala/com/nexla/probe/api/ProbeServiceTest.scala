package com.nexla.probe.api

import com.dimafeng.testcontainers.{Contain<PERSON>, ForAllTest<PERSON><PERSON>r, <PERSON>ricContainer, MultipleContainers}
import com.nexla.admin.client.DataMap
import com.nexla.common.ConnectionType._
import com.nexla.common.NexlaConstants
import com.nexla.common.NexlaConstants._
import com.nexla.common.io.RedisConnect
import com.nexla.connector.ConnectorService.UNIT_TEST
import com.nexla.connector.config.redis.RedisAuthConfig.ID_FIELD_NAME
import com.nexla.redis.LookupUtils.withLookup
import com.nexla.redis.{Lookup, RedisCreds}
import com.nexla.sc.config.NexlaCredsEncodedConf
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.BeforeAndAfter

import java.util.function.Consumer
import scala.collection.JavaConverters._

@com.nexla.test.ScalaIntegrationTests
class ProbeServiceTest extends AnyFlatSpecLike with Matchers with BeforeAndAfter with ForAllTestContainer {

  private val redis = GenericContainer(
    dockerImage = "redis:7.0.12",
    exposedPorts = Seq(6379)
  )

  override val container: Container = MultipleContainers(
    redis
  )

  def toHostPort(redis: GenericContainer): RedisConnect = {
    new RedisConnect(redis.host, redis.mappedPort(6379), 0);
  }

  val MAP_ID = 1234

  it should "readDataMapSample page by page" in {
    val entries = List(
      Map("ID" -> "1", "key" -> "1"),
      Map("ID" -> "2", "key" -> "2"),
      Map("ID" -> "3", "key" -> "3"),
      Map("ID" -> "4", "key" -> "4")
    )

    fillDataMap(MAP_ID, entries)

    val originals = new java.util.HashMap[String, String]
    originals.put(UNIT_TEST, "true")
    originals.put(ID_FIELD_NAME, "*")
    originals.put(NexlaConstants.MAP_ID, MAP_ID + "")
    originals.put(SINK_ID, "1")
    originals.put(CREDS_ENC, "1")
    originals.put(CREDS_ENC_IV, "1")
    originals.put(CREDENTIALS_DECRYPT_KEY, "1")
    originals.put(LISTING_ENABLED, "false")
    originals.put(REDIS_HOSTS, toHostPort(redis).toUrl)
    originals.put(REDIS_CLUSTER_ENABLED, "false")
    originals.put(REDIS_TLS_ENABLED, "false")
    originals.put(REDIS_HOSTS, toHostPort(redis).toUrl)
    originals.put(LISTING_APP_SERVER_URL, "123");

    val service = new ProbeService(NexlaCredsEncodedConf("", ""), "")

    (0 to 3)
      .map { offset =>
        service.readDataMapSample(
          DATA_MAP,
          probeInput = new com.nexla.common.probe.ProbeInput(1, "", "", originals),
          offset = Some(offset),
          pageSizeOpt = Some(1))
      }
      .toSet shouldBe entries.map(List(_)).toSet
  }

  def fillDataMap(mapId: Int, entries: List[Map[String, String]]): Unit = {
    val dm = new DataMap
    dm.setEmitDataDefault(true)
    dm.setUseVersioning(true)
    dm.setMapPrimaryKey("ID")
    dm.setDataSinkId(5001)
    dm.setId(mapId)
    dm.setDataMap(entries.map(_.asJava).asJava)

    val creds = new RedisCreds(Set(toHostPort(redis)).asJava, false, java.util.Optional.empty(), java.util.Optional.empty())
    val consumer: Consumer[Lookup] = (lookup: Lookup) => {
      lookup.save(dm)
    }
    withLookup(creds, dm.getId, consumer)
  }

}
