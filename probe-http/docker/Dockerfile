FROM nexla/base-jdk11:release-v2.16.1-206-b410f057d6746bbe86bf4ac1986b2917d0d70b0e
MAINTAINER Avinash "<EMAIL>"

ARG GIT_HASH
ENV GIT_HASH=$GIT_HASH

ENV NEXLA_APP_DIR "/app"
ADD target/*.jar $NEXLA_APP_DIR/app.jar

RUN sh -c 'touch $NEXLA_APP_DIR/app.jar'

USER nexla

ENTRYPOINT exec java $JAVA_OPTS -Dcom.sun.management.jmxremote=true -Dcom.sun.management.jmxremote.port=2000 -Dcom.sun.management.jmxremote.ssl=false \
                     -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.local.only=false -jar /app/app.jar