package com.nexla.connector.file.source;

import com.nexla.connect.common.connector.BaseSourceConnector;
import com.nexla.connector.config.big_query.BigQuerySourceConnectorConfig;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.connector.Task;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.Collections.singletonList;

public class BigQuerySourceConnector extends BaseSourceConnector<BigQuerySourceConnectorConfig> {

	@Override
	protected String telemetryAppName() {
		return "bigquery-source";
	}

	@Override
	protected BigQuerySourceConnectorConfig parseConfig(Map<String, String> props) {
		return new BigQuerySourceConnectorConfig(props);
	}

	@Override
	public Class<? extends Task> taskClass() {
		return BigQuerySourceTask.class;
	}

	@Override
	public ConfigDef config() {
		return BigQuerySourceConnectorConfig.configDef();
	}

	@Override
	public List<Map<String, String>> taskConfigs(int maxTasks) {
		return singletonList(new HashMap<>(config.originalsStrings()));
	}
}
