package com.nexla.connector.file.source;

import com.google.api.gax.paging.Page;
import com.google.cloud.RetryHelper;
import com.google.cloud.RetryOption;
import com.google.cloud.bigquery.*;
import com.google.cloud.storage.Blob;
import com.google.cloud.storage.Bucket;
import com.google.cloud.storage.BucketInfo;
import com.google.cloud.storage.Storage;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ibm.icu.impl.Pair;
import com.nexla.admin.client.DataCredentials;
import com.nexla.admin.client.DataSource;
import com.nexla.admin.client.config.EnrichedConfig;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaFile;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.datetime.DateTimeUtils;
import com.nexla.common.notify.transport.NexlaMessageProducer;
import com.nexla.common.notify.transport.NoopNexlaMessageTransport;
import com.nexla.common.tracker.SourceItem;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.BaseSourceTask;
import com.nexla.connect.common.CollectRecordsResult;
import com.nexla.connect.common.SourceRecordCreator;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.big_query.BigQuerySourceConnectorConfig;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.probe.bigquery.BigQueryClientService;
import com.nexla.probe.bigquery.BigQueryConnectorService;
import com.nexla.probe.gcs.GCSConnectorService;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.errors.ConnectException;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.storage.OffsetStorageReader;
import org.threeten.bp.Duration;
import scala.Function2;

import java.net.URI;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.admin.client.config.SourceConfigUtils.createSourceConfig;
import static com.nexla.admin.config.ConfigUtils.enrichWithCredentialsStore;
import static com.nexla.admin.config.ConfigUtils.enrichWithDataCredentials;
import static com.nexla.common.ConnectionType.BIGQUERY;
import static com.nexla.common.ListingResourceType.FILE;
import static com.nexla.common.MetricUtils.calcBytes;
import static com.nexla.common.NexlaConstants.PATH;
import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.parse.ParserConfigs.SchemaDetection.SCHEMA_DETECTION_ONCE;
import static com.nexla.connector.file.source.OffsetUtils.createOffsetMap;
import static com.nexla.connector.file.source.OffsetUtils.createPartitionMap;
import static com.nexla.connector.properties.FileConfigAccessor.OFFSET_POSITION_KEY;
import static java.util.Optional.*;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.kafka.connect.data.Schema.STRING_SCHEMA;

public class BigQuerySourceTask extends BaseSourceTask<BigQuerySourceConnectorConfig> {

	private TransportFileReader fileReader;
	private BaseKafkaFileMessageReader consumer;
	private FileSourceConnectorConfig fileConfig;
	private BigQueryConnectorService bigQueryConnectorService;
	private GCSConnectorService gcsConnectorService;
	private Storage gcsClient;
	private String taskPartition;
	private Optional<BucketInfo> bucketInfo = empty();

	@Override
	public ConfigDef configDef() {
		return BigQuerySourceConnectorConfig.configDef();
	}

	@Override
	@SneakyThrows
	public void doStart(Map<String, String> props) throws ConnectException {
		try {
			doStart();
		} catch (Exception e) {
			clearTempBucket();
			throw e;
		}
	}

	@SneakyThrows
	public void doStart() {
		this.sourceTelemetryReporter = Optional.of(new ConnectorTelemetryReporter(config.sourceId, BIGQUERY.name(), SOURCE, true));
		this.bigQueryConnectorService = new BigQueryConnectorService(adminApiClient, listingClient, new BigQueryClientService(adminApiClient, config.decryptKey));
		this.gcsConnectorService = new GCSConnectorService(adminApiClient, listingClient, config.decryptKey, empty());

		this.gcsClient = gcsConnectorService.getService(config.authConfig);

		this.taskPartition = config.table.orElseGet(() -> config.query.get());

		Pair<TableId, Dataset> tableDataset = withClientRetriable(this::getBigQueryClient, bq -> {
			TableId table = saveQueryToTableOpt(bq);
			Dataset dataset = bq.getDataset(table.getDataset());
			return Pair.of(table, dataset);
		});

		TableId tableId = tableDataset.first;
		Dataset bqDataSet = tableDataset.second;

		String bucketName = config
			.intermediateGcsBucket
			.orElseGet(() -> createTempBucket(bqDataSet));

		String gcsDestination = withClientRetriable(this::getBigQueryClient, bigQuery -> writeToGcs(bigQuery, tableId, bucketName));
		withClientRetriable(this::getBigQueryClient, bigQuery -> {
			if (config.query.isPresent()) {
				bigQuery.delete(tableId);
			}
			return null;
		});

		EnrichedConfig.EnrichSourceParams enrichSourceParams = new EnrichedConfig.EnrichSourceParams(
			config.zookeeperConnect,
			config.bootstrapServers,
			config.vaultHost,
			config.vaultToken,
			1,
			1,
			config.credentialsSource,
			config.secretManagerRegion,
			config.secretManagerAccessKey,
			config.secretManagerSecretKey,
			config.secretManagerRoleArn,
			config.secretManagerIdentityTokenFile,
			config.secretNames
		);

		NexlaMessageProducer nexlaMessageProducer = new NexlaMessageProducer(new NoopNexlaMessageTransport());
		URI fileDestinationUri = new URI(gcsDestination);

		String bucketPath = StringUtils.removeStart(fileDestinationUri.getPath(), "/");
		String sourcePath = bucketName + "/" + bucketPath;

		Map<String, Object> cfg = Maps.newHashMap();
		cfg.put(SCHEMA_DETECTION_ONCE, Boolean.TRUE.toString());
		cfg.put(PATH, sourcePath);

		DataSource dataSource = new DataSource();
		dataSource.setId(config.sourceId);
		dataSource.setSourceConfig(cfg);
		dataSource.setConnectionType(ConnectionType.GCS);
		DataCredentials dataCredentials = new DataCredentials();
		dataCredentials.setCredentialsEnc(config.credsEnc);
		dataCredentials.setCredentialsEncIv(config.credsEncIv);
		dataCredentials.setId(config.credsId);
		dataSource.setDataCredentials(dataCredentials);

		Map<String, String> enrichedConfig = createSourceConfig(dataSource, enrichSourceParams);
		Map<String, String> resultConfig = enrichWithCredentialsStore(enrichedConfig, FileSourceConnectorConfig.configDef());
		enrichWithDataCredentials(adminApiClient, resultConfig);

		this.fileConfig = new FileSourceConnectorConfig(resultConfig);
		NoopOffsetWriter offsetWriter = new NoopOffsetWriter();
		gcsConnectorService.initLogger(SOURCE, fileConfig.sourceId, Optional.empty());

		FileSourceContext fileSourceContext = new FileSourceContext(fileConfig, empty(), runId);
		MessageGrouper messageGrouper = new NoopMessageGrouper();
		FileSourceNotificationSender notificationSender = new FileSourceNotificationSender(
			nexlaMessageProducer, FlowType.STREAMING, fileSourceContext, logger);

		this.consumer = new BaseKafkaFileMessageReader(
			offsetWriter, messageGrouper, fileSourceContext, logger, gcsConnectorService, notificationSender);

		OffsetStorageReader offsetStorageReader = context.offsetStorageReader();

		Consumer<TransportFile> tfConsumer = tf -> gcsConnectorService.deleteByName(fileConfig, Lists.newArrayList(tf.nexlaFile.getFullPath()));

		this.fileReader = new TransportFileReader(
			notificationSender, gcsConnectorService, schemaDetection, offsetWriter, messageGrouper,
			logger, Optional.of(offsetStorageReader),Optional.empty(), Optional.of(tfConsumer), fileSourceContext,
			empty(), false);

		this.fileReader.setCustomParserEnabled(true);

		int starIndex = bucketPath.indexOf("*");
		String prefix = starIndex > 0 ? bucketPath.substring(0, starIndex) : bucketPath;
		Page<Blob> listResult = gcsClient.list(bucketName, Storage.BlobListOption.prefix(prefix));

		List<TransportFile> transportFile = StreamEx
			.of(listResult.iterateAll().iterator())
			.map(blob -> {
				NexlaFile file = new NexlaFile(blob.getName(), 0L, null, null, null, null, FILE);
				Map<String, Object> offset = offsetStorageReader.offset(createPartitionMap(false, file));
				String offsetAsStr = ofNullable(offset)
					.map(x -> x.get(OFFSET_POSITION_KEY))
					.orElse("0")
					.toString();
				return new TransportFile(file, Long.parseLong(offsetAsStr), false);
			})
			.toList();

		fileReader.addFiles(transportFile);
	}

	private String createTempBucket(Dataset ds) {
		String name = "nexla" + UUID.randomUUID().toString().replaceAll("-", "_");

		BucketInfo bucket = BucketInfo
			.newBuilder(name)
			.setLocation(ds.getLocation())
			.build();

		logger.info("[gcs-bucket] creating temp bucket " + name);
		gcsClient.create(bucket);
		this.bucketInfo = of(bucket);
		return name;
	}

	public BigQuery getBigQueryClient() {
		return bigQueryConnectorService.getBigQueryService().getService(config.authConfig);
	}

	public TableId saveQueryToTableOpt(BigQuery bigQuery) {
		return config.query.map(query -> {
			DatasetInfo intermDataSet = DatasetInfo.of(config.tempDestinationDataset);
			boolean dsExists = bigQuery.getDataset(intermDataSet.getDatasetId()) != null;
			if (!dsExists) {
				bigQuery.create(intermDataSet);
			}
			String tableName = "nexla" + UUID.randomUUID().toString().replaceAll("-", "_");
			TableId tableId = TableId.of(config.tempDestinationDataset, tableName);
			Table table = bigQuery.getTable(tableId);
			if (table != null && table.exists()) {
				bigQuery.delete(tableId);
			}
			QueryJobConfiguration queryConfig =
				// Note that setUseLegacySql is set to false by default
				QueryJobConfiguration.newBuilder(query)
					.setAllowLargeResults(true)
					.setCreateDisposition(JobInfo.CreateDisposition.CREATE_IF_NEEDED)
					.setDestinationTable(tableId)
					.build();
			execQuery(bigQuery, queryConfig);
			return tableId;
		}).orElseGet(() -> TableId.of(config.dataset, config.table.get()));
	}

	public String writeToGcs(BigQuery bigQuery, TableId tableId, String bucketName) {
		Table table = bigQuery.getTable(tableId);
		String gcsDestination = "gs://" + bucketName + "/run-" + runId + "-*." + config.intermediateGcsFormat;
		String formatParam = config.intermediateGcsFormat.equalsIgnoreCase("csv") ? "CSV" : "NEWLINE_DELIMITED_JSON";
		Job job = table.extract(formatParam, gcsDestination);
		try {
			Job completedJob =
				job.waitFor(
					RetryOption.initialRetryDelay(Duration.ofSeconds(1)),
					RetryOption.totalTimeout(Duration.ofMinutes(3)));
			if (completedJob != null) {
				if (completedJob.getStatus().getError() == null) {
					boolean queryMode = config.query.isPresent();
					if (queryMode) {
						bigQuery.delete(tableId);
					}
				} else {
					String message = "Failed to save " + tableId + " to " + gcsDestination + "\n"
									 + completedJob.getStatus().getError().toString();
					logger.error(message);
				}
			} else {
				logger.error("Job for saving " + tableId + " to " + gcsDestination + "does not exist anymore");
			}
		} catch (InterruptedException e) {
			String message = "Failed to save " + tableId + " to " + gcsDestination;
			logger.error(message, e);
		}
		return gcsDestination;
	}

	@SneakyThrows
	public void execQuery(BigQuery bigQuery, QueryJobConfiguration queryConfig) {
		bigQuery.query(queryConfig);
	}

	@Override
	protected BigQuerySourceConnectorConfig parseConfig(Map<String, String> props) {
		return new BigQuerySourceConnectorConfig(props);
	}

	@SneakyThrows
	@Override
	public CollectRecordsResult collectRecords() {

		List<NexlaMessageFile> messages = ofNullable(
			fileReader
				.readNextBatch(consumer, adminApiClient)
				.messages
		).orElse(Collections.emptyList());

		List<SourceRecord> sourceRecords = detectSchemaIfNecessary(false, getSourceRecordCreators(messages), Optional.empty());

		if (isNotEmpty(sourceRecords)) {
			sendNexlaMetric(messages);
		}
		if (fileReader.getFileObjects().isEmpty()) {
			clearTempBucket();
		}

		return new CollectRecordsResult(sourceRecords);

	}

	public List<SourceRecordCreator> getSourceRecordCreators(List<NexlaMessageFile> messages) {
		long now = nowUTC().getMillis();

		return messages
			.stream()
			.map(r -> {
				NexlaMetaData meta = r.message.getNexlaMetaData();
				String sourceKey = config.dataset + "." + config.table.orElse("");
				Long sourceOffset = meta.getSourceOffset();

				Function2<Integer, String, NexlaMessage> nexlaMessageCreator = (dataSetId, dataSetTopic) -> {
					NexlaMetaData metaData = new NexlaMetaData(
						BIGQUERY,
						now,
						sourceOffset,
						sourceKey,
						dataSetTopic,
						SOURCE,
						config.sourceId,
						false,
						new Tracker(Tracker.TrackerMode.FULL, SourceItem.fullTracker(config.sourceId, dataSetId, sourceKey, sourceOffset + 1, config.version, now)),
						runId);

					return new NexlaMessage(r.message.getRawMessage(), metaData);
				};

				Function2<Integer, String, SourceRecord> sourceRecordCreator = (dataSetId, dataSetTopic) -> {
					NexlaMessage message = nexlaMessageCreator.apply(dataSetId, dataSetTopic);
					NexlaMetaData messageMeta = message.getNexlaMetaData();
					return new SourceRecord(createPartitionMap(false, r.file), createOffsetMap(r.file, messageMeta.isEof(), messageMeta.getSourceOffset(), messageMeta.isSkip(), messageMeta.isError()), messageMeta.getTopic(), null, null, null, STRING_SCHEMA, toJsonString(message));
				};

				return new SourceRecordCreator(r.message.getRawMessage(), sourceRecordCreator, nexlaMessageCreator);
			})
			.collect(toList());
	}

	private void sendNexlaMetric(List<NexlaMessageFile> messages) {
		long sizeBytes = StreamEx.of(messages)
			.mapToInt(r -> calcBytes(r.message.toJsonString()))
			.sum();
		sendMetrics(taskPartition, messages.size(), sizeBytes, 0L, DateTimeUtils.nowUTC().getMillis());
	}

	public static <T, U> U withClientRetriable(Supplier<T> supplier, Function<T, U> consumer) {
		try {
			T client = supplier.get();
			return consumer.apply(client);
		} catch (RetryHelper.RetryHelperException e) {
			T client = supplier.get();
			return consumer.apply(client);
		}
	}

	@Override
	public void stop() throws ConnectException {
		super.stop();
		clearTempBucket();
		fileReader.stop();
	}

	public void clearTempBucket() {
		logger.info("[gcs-bucket] start clearTempBucket for " + bucketInfo.map(BucketInfo::getName).orElse("[no bucket]"));
		bucketInfo.ifPresent(bi -> {
			String bucketName = bi.getName();
			if (bucketExists(bucketName)) {
				try {
					StreamEx
						.of(gcsClient.list(bucketName).iterateAll().iterator())
						.forEach(x -> gcsClient.delete(x.getBlobId()));
				} catch (Exception e) {
					logger.error("[gcs-bucket] Failed to delete bucket contents " + bucketName, e);
				}
				try {
					int i = 0;
					while (gcsClient.list(bucketName).iterateAll().iterator().hasNext()) {
						Thread.sleep(500);
						i++;
						if (i > 10) {
							logger.error("[gcs-bucket] Bucket " + bucketName + " is not empty after 5 seconds");
						}
					}
					logger.info("[gcs-bucket] deleting " + bucketName);
					gcsClient.delete(bucketName);
				} catch (Exception e) {
					logger.error("[gcs-bucket] Failed to delete bucket " + bucketName, e);
				}
			}
		});
	}

	private boolean bucketExists(String bucketName) {
		Bucket bucket = gcsClient.get(bucketName);
		return bucket != null && bucket.exists();
	}

}
