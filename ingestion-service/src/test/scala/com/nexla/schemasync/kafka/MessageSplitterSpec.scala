package com.nexla.schemasync.kafka

import com.nexla.common.tracker.Tracker
import com.nexla.common.{ConnectionType, NexlaMessage, NexlaMetaData, ResourceType}
import com.nexla.schemasync.kafka.codecs.NexlaMessageCodec
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

import java.nio.charset.StandardCharsets
import java.util
import scala.util.Random

class MessageSplitterSpec extends AnyFlatSpec with Matchers {
  private def randomStringOfSize(byteSize: Int): String = {
    val sb = new StringBuilder
    val rnd = new Random
    while (sb.toString.getBytes(StandardCharsets.UTF_8).length < byteSize) {
      sb.append(rnd.alphanumeric.head)
    }
    while (sb.toString.getBytes(StandardCharsets.UTF_8).length > byteSize) {
      sb.setLength(sb.length - 1)
    }
    sb.toString()
  }

  private def generateNexlaMessage(byteSize: Int): NexlaMessage = {
    val sizeWithoutData = NexlaMessageCodec.encode(
      new NexlaMessage(
        new util.LinkedHashMap[String, AnyRef](),
        new NexlaMetaData(ConnectionType.FTP, 1234L, 1L, "sourceKey", "topicName", 1, ResourceType.SOURCE, 11, true, new Tracker(), 12345L)
      )).compactPrint.getBytes.length

    val rawMessage = new util.LinkedHashMap[String, AnyRef]
    rawMessage.put("field", randomStringOfSize(byteSize - sizeWithoutData))

    new NexlaMessage(
      rawMessage,
      new NexlaMetaData(ConnectionType.FTP, 1234L, 1L, "sourceKey", "topicName", 1, ResourceType.SOURCE, 11, true, new Tracker(), 12345L)
    )
  }

  private val sourceId = 1
  private val datasetId = 1

  "MessageSplitter" should "split messages into separate batches when total size exceeds max limit" in {
    val maxSize = 10000
    val splitter = new MessageSplitter(maxSize)
    val samples = List(generateNexlaMessage(7000), generateNexlaMessage(7000), generateNexlaMessage(7000))

    val result = splitter.splitBySizeConstraint(sourceId, datasetId, samples)

    result should have size 3
  }

  it should "return a single batch when total message size is within the max limit" in {
    val maxSize = 10000
    val splitter = new MessageSplitter(maxSize)
    val samples = List(generateNexlaMessage(1000), generateNexlaMessage(1000), generateNexlaMessage(1000))

    val result = splitter.splitBySizeConstraint(sourceId, datasetId, samples)

    result should have size 1
  }

  it should "split messages correctly when only some exceed the max size" in {
    val maxSize = 10000
    val splitter = new MessageSplitter(maxSize)
    val samples = List(generateNexlaMessage(7000), generateNexlaMessage(1000), generateNexlaMessage(7000))

    val result = splitter.splitBySizeConstraint(sourceId, datasetId, samples)

    result should have size 2
  }
}