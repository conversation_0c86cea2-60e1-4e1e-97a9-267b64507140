package com.nexla.schemasync.kafka.codecs

import com.nexla.common.tracker.Tracker
import com.nexla.common.{ConnectionType, NexlaMessage, NexlaMetaData, ResourceType}
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

import java.util

class NexlaMessageCodecSpec extends AnyWordSpec with Matchers {
  "NexlaMessageCodec" should {
    "encode and decode nexla message" in {
      val rawMessage = new util.LinkedHashMap[String, AnyRef]
      rawMessage.put("id", Integer.valueOf(123))
      rawMessage.put("file_name", "filename.txt")
      rawMessage.put("file_path", "filepath/abc")
      rawMessage.put("size", Integer.valueOf(1234))
      val nexlaMessage: NexlaMessage = new NexlaMessage(
        rawMessage,
        new NexlaMetaData(ConnectionType.FTP, 1234L, 1L, "sourceKey", "topicName", 1, ResourceType.SOURCE, 11, true, new Tracker(), 12345L)
      )

      val encoded = NexlaMessageCodec.encode(nexlaMessage)
      val decoded = NexlaMessageCodec.decode(encoded).get

      nexlaMessage shouldEqual decoded
    }
  }
}
