package com.nexla.schemasync.kafka.codecs

import com.nexla.admin.client.NexlaSchema
import com.nexla.common.tracker.Tracker
import com.nexla.common.{ConnectionType, NexlaMessage, NexlaMetaData, ResourceType}
import com.nexla.schemasync.model.SchemaSyncEvent
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

import java.util

class SchemaSyncEventCodecSpec extends AnyWordSpec with Matchers {
  "SchemaSyncEventCodec" should {
    "encode and decode schema update event" in {
      val schema: NexlaSchema = new NexlaSchema
      schema.setSchema("http://example.com/schema")
      schema.setType("object")
      schema.setProperties(
        new util.LinkedHashMap[String, AnyRef] {
          {
            put("field18", new util.LinkedHashMap[String, AnyRef] {
              {
                put("type", "string")
              }
            })
            put("field19", new util.LinkedHashMap[String, AnyRef] {
              {
                put("type", "string")
              }
            })
          }
        })
      schema.setRequired(java.util.List.of("field18", "field19"))

      val event = SchemaSyncEvent.SchemaUpdate(1, 123, schema)

      val encoded = SchemaSyncEventCodec.encode(event)
      val decoded = SchemaSyncEventCodec.decode(encoded).get.get

      event shouldEqual decoded
    }

    "encode and decode samples update event" in {
      val rawMessage1 = new util.LinkedHashMap[String, AnyRef]
      rawMessage1.put("id", Integer.valueOf(123))
      rawMessage1.put("file_name", "filename.txt")
      rawMessage1.put("file_path", "filepath/abc")
      rawMessage1.put("size", Integer.valueOf(1234))
      val nexlaMessage1: NexlaMessage = new NexlaMessage(
        rawMessage1,
        new NexlaMetaData(ConnectionType.FTP, 1234L, 1L, "sourceKey", "topicName", 1, ResourceType.SOURCE, 11, true, new Tracker(), 12345L)
      )
      val rawMessage2 = new util.LinkedHashMap[String, AnyRef]
      rawMessage2.put("id2", Integer.valueOf(223))
      rawMessage2.put("file_name2", "filename2.txt")
      rawMessage2.put("file_path2", "filepath2/abc")
      rawMessage2.put("size2", Integer.valueOf(2234))
      val nexlaMessage2: NexlaMessage = new NexlaMessage(
        rawMessage2,
        new NexlaMetaData(ConnectionType.FTP, 1234L, 1L, "sourceKey2", "topicName2", 2, ResourceType.SOURCE, 11, false, new Tracker(), 12345L)
      )

      val event = SchemaSyncEvent.SamplesUpdate(1, 123, List(nexlaMessage1, nexlaMessage2))

      val encoded = SchemaSyncEventCodec.encode(event)
      val decoded = SchemaSyncEventCodec.decode(encoded).get.get

      event shouldEqual decoded
    }
  }
}
