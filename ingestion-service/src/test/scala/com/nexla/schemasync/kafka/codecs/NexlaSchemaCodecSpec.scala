package com.nexla.schemasync.kafka.codecs

import com.nexla.admin.client.NexlaSchema
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

import java.util

class NexlaSchemaCodecSpec extends AnyWordSpec with Matchers {
  "NexlaSchemaCodec" should {
    "encode and decode schema object" in {
      val schema: NexlaSchema = new NexlaSchema
      schema.setSchema("http://example.com/schema")
      schema.setType("object")
      schema.setProperties(
        new util.LinkedHashMap[String, AnyRef] {
          {
            put("field18", new util.LinkedHashMap[String, AnyRef] {
              {
                put("type", "string")
              }
            })
            put("field19", new util.LinkedHashMap[String, AnyRef] {
              {
                put("type", "string")
              }
            })
          }
        })
      schema.setRequired(java.util.List.of("field18", "field19"))

      val encoded = NexlaSchemaCodec.encode(schema)
      val decoded = NexlaSchemaCodec.decode(encoded).get

      schema shouldEqual decoded
    }
  }
}