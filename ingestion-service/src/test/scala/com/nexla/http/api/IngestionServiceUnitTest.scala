package com.nexla.http.api

import com.nexla.admin.client.{AdminApiClient, DataSource, ResourceStatus}
import com.nexla.common.NexlaSslContext
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.http.AppProps
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfterEach
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import java.util.Optional

@com.nexla.test.ScalaUnitTests
class IngestionServiceUnitTest extends AnyFlatSpecLike with Matchers with BeforeAndAfterEach {
  "IngestionService" should "throw SourceNotActive exception if the source in question is not ACTIVE" in {
    val mockAdminApi = mock(classOf[AdminApiClient])
    val stubDs = new DataSource()
    stubDs.setId(123)
    stubDs.setStatus(ResourceStatus.PAUSED)
    when(mockAdminApi.getDataSource(123)).thenReturn(Optional.of(stubDs))
    val mockKafka = mock(classOf[NexlaMessageProducer])
    val mockDetectionCache = mock(classOf[SchemaDetectionCache])
    val mockProps = mock(classOf[AppProps])
    when(mockProps.bootstrapServers).thenReturn("127.0.0.1:8080")
    when(mockProps.metricsWindowMs).thenReturn(Integer.MAX_VALUE)
    val mockSslContext = mock(classOf[NexlaSslContext])
    val instance = new IngestionService(mockAdminApi, mockKafka, mockDetectionCache, mockProps, mockSslContext)

    assertThrows[SourceNotActiveException] {
      instance.ingest(java.util.Map.of(), java.util.Map.of(), java.util.List.of(), 123, false)
    }
  }
}
