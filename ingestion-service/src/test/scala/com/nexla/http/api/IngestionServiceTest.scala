package com.nexla.http.api

import com.nexla.admin.client._
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.common.parse.ParserConfigs.SchemaDetection
import com.nexla.common.{NexlaMessage, NexlaSslContext, StreamUtils}
import com.nexla.http.AppProps
import com.nexla.transform.schema.FormatDetector
import io.github.embeddedkafka.{EmbeddedKafka, EmbeddedKafkaConfig}
import org.mockito.ArgumentMatchers.{any, anyInt}
import org.mockito.Mockito._
import org.mockito.{ArgumentCaptor, ArgumentMatchers, Mockito}
import org.scalatest.{BeforeAndAfterAll, Ignore}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import java.util
import java.util.Optional
import scala.collection.JavaConverters._
import scala.compat.java8.OptionConverters.RichOptionalGeneric

@Ignore // todo:bszmit - test is failing after moving from old backend release/2.16.3 to backend-connector release/3.2.0 repo
@com.nexla.test.ScalaIntegrationTests
class IngestionServiceTest extends AnyFlatSpecLike
  with Matchers
  with BeforeAndAfterAll with EmbeddedKafka {

  private var mockAdminApi: AdminApiClient = _
  private var schemaDetectionCache: SchemaDetectionCache = _
  private var mockAppProps: AppProps = _
  private var testDataSource: DataSource = _
  private var testDataSet: DataSet = _
  private var nexlaMessageProducer: NexlaMessageProducer = _
  private val kafkaConfig = EmbeddedKafka.start()(EmbeddedKafkaConfig(0, 0))

  private def initKafka(): Unit = {
    val bootstrapServers = s"localhost:${kafkaConfig.config.kafkaPort}"

    mockAdminApi = mock(classOf[AdminApiClient])
    mockAppProps = mock(classOf[AppProps])
    schemaDetectionCache = new SchemaDetectionCache(mockAdminApi, bootstrapServers, NexlaSslContext.NOSSL_CONTEXT, 1, 1, 1000)
    Mockito.when(mockAppProps.bootstrapServers).thenReturn(bootstrapServers)
    Mockito.when(mockAppProps.metricsWindowMs).thenReturn(1)
    Mockito.when(mockAppProps.flushPeriod).thenReturn(1)
    Mockito.when(mockAppProps.batchSize).thenReturn(1)
    testDataSource = createMockDataSource()
    testDataSet = createMockDataSet()
    Mockito.when(mockAdminApi.getDataSource(anyInt())).thenReturn(Optional.of(testDataSource))
    val result = new FindOrCreateDataSetResult(FindOrCreateDataSetResult.Result.CREATED, testDataSet.getId)
    Mockito.when(mockAdminApi.findOrCreateDataSet(any())).thenReturn(result)
    FormatDetector.initDefault()
  }
  initKafka()

  "ingestArbitrary" should "create new data set with correct schema and samples" in {
    val msg1: util.LinkedHashMap[String, AnyRef] = StreamUtils.lhm("a", Int.box(1), "b", Int.box(2))
    val utils = schemaDetectionCache.getSchemaDetection(testDataSource.getId)
    val datasetCaptor = ArgumentCaptor.forClass(classOf[DataSet])

    val ingestionService = new IngestionService(mockAdminApi, nexlaMessageProducer, schemaDetectionCache, mockAppProps, NexlaSslContext.NOSSL_CONTEXT)
    ingestionService.ingestArbitrary(util.Collections.emptyMap(), util.Collections.emptyMap(), List(msg1).asJava, 1, true)

    verify(mockAdminApi, times(1)).findOrCreateDataSet(datasetCaptor.capture())
    val resultDs: DataSet = datasetCaptor.getValue
    resultDs.getSourceSchema.getProperties shouldBe new util.LinkedHashMap[String, util.LinkedHashMap[String, String]](){{
      put("a", new util.LinkedHashMap[String, String](){{put("type", "number")}})
      put("b", new util.LinkedHashMap[String, String](){{put("type", "number")}})
    }}
    utils.getSamples(resultDs.getId)
      .asScala.map(_.asScala.map(_.getRawMessage)) shouldBe Some(List(msg1))
  }

  it should "append to schema and data samples on new superset schema" in {
    val msg1 : util.LinkedHashMap[String, AnyRef] = StreamUtils.lhm("a", Int.box(1), "b", Int.box(2))
    val msg2 : util.LinkedHashMap[String, AnyRef] = StreamUtils.lhm("a", Int.box(10), "b", Int.box(20), "c", "foo", "d", "bar")
    val utils = schemaDetectionCache.getSchemaDetection(testDataSource.getId)
    val dataSetCaptor = ArgumentCaptor.forClass(classOf[DataSet])
    val samplesCaptor: ArgumentCaptor[util.List[NexlaMessage]] = ArgumentCaptor.forClass(classOf[util.List[NexlaMessage]])

    Mockito.when(mockAdminApi.getDataSet(any())).thenReturn(Optional.of(testDataSet))
    val ingestionService = new IngestionService(mockAdminApi, nexlaMessageProducer, schemaDetectionCache, mockAppProps, NexlaSslContext.NOSSL_CONTEXT)
    ingestionService.ingestArbitrary(util.Collections.emptyMap(), util.Collections.emptyMap(), List(msg1).asJava, 1, true)
    val oldSchemaId = utils.getSchemaId(testDataSet.getId).get()
    waitOnThread("forkjoinpool")
    Mockito.clearInvocations(mockAdminApi)

    ingestionService.ingestArbitrary(util.Collections.emptyMap(), util.Collections.emptyMap(), List(msg2).asJava, 1, true)
    waitOnThread("forkjoinpool")
    verify(mockAdminApi, times(1)).updateDataSetSchema(dataSetCaptor.capture())
    verify(mockAdminApi, times(1)).putDataSetSamples(samplesCaptor.capture(), ArgumentMatchers.eq(testDataSet.getId))

    val resultDs: DataSet = dataSetCaptor.getValue
    val samplesToAdd = samplesCaptor.getValue
    resultDs.getSourceSchema.getProperties shouldBe new util.LinkedHashMap[String, util.LinkedHashMap[String, String]](){{
      put("a", new util.LinkedHashMap[String, String](){{put("type", "number")}})
      put("b", new util.LinkedHashMap[String, String](){{put("type", "number")}})
      put("c", new util.LinkedHashMap[String, String](){{put("type", "string")}})
      put("d", new util.LinkedHashMap[String, String](){{put("type", "string")}})
    }}
    samplesToAdd.asScala.map(s => s.getRawMessage) shouldBe List(msg2)
    utils.getSamples(testDataSet.getId).get.asScala.map(s => s.getRawMessage) should contain theSameElementsInOrderAs  List(msg1, msg2)
    utils.getSchemaId(testDataSet.getId).get() should not be oldSchemaId
  }

  it should "not change schema but add data samples on new subset schema" in {
    val msg1 : util.LinkedHashMap[String, AnyRef] = StreamUtils.lhm("a", Int.box(1), "b", Int.box(2))
    val msg2 : util.LinkedHashMap[String, AnyRef] = StreamUtils.lhm("a", Int.box(10))
    val utils = schemaDetectionCache.getSchemaDetection(testDataSource.getId)
    val samplesCaptor: ArgumentCaptor[util.List[NexlaMessage]] = ArgumentCaptor.forClass(classOf[util.List[NexlaMessage]])
    Mockito.when(mockAdminApi.getDataSet(any())).thenReturn(Optional.of(testDataSet))

    val ingestionService = new IngestionService(mockAdminApi, nexlaMessageProducer, schemaDetectionCache, mockAppProps, NexlaSslContext.NOSSL_CONTEXT)
    ingestionService.ingestArbitrary(util.Collections.emptyMap(), util.Collections.emptyMap(), List(msg1).asJava, 1, true)
    val oldSchemaId = utils.getSchemaId(testDataSet.getId).get()
    waitOnThread("forkjoinpool")
    Mockito.clearInvocations(mockAdminApi)

    ingestionService.ingestArbitrary(util.Collections.emptyMap(), util.Collections.emptyMap(), List(msg2).asJava, 1, true)
    waitOnThread("forkjoinpool")
    verify(mockAdminApi, never()).updateDataSetSchema(any())
    verify(mockAdminApi, times(1)).putDataSetSamples(samplesCaptor.capture(), ArgumentMatchers.eq(testDataSet.getId))

    samplesCaptor.getValue.asScala.map(s => s.getRawMessage) shouldBe List(msg2)
    utils.getSamples(testDataSet.getId).get.asScala.map(s => s.getRawMessage) shouldBe List(msg1, msg2)
    utils.getSchemaId(testDataSet.getId).get() shouldBe oldSchemaId
  }

  it should "not change schema but add data samples on same schema" in {
    val msg1 : util.LinkedHashMap[String, AnyRef] = StreamUtils.lhm("a", Int.box(1), "b", Int.box(2))
    val msg2 : util.LinkedHashMap[String, AnyRef] = StreamUtils.lhm("a", Int.box(3), "b", Int.box(4))
    val utils = schemaDetectionCache.getSchemaDetection(testDataSource.getId)
    val samplesCaptor: ArgumentCaptor[util.List[NexlaMessage]] = ArgumentCaptor.forClass(classOf[util.List[NexlaMessage]])
    Mockito.when(mockAdminApi.getDataSet(any())).thenReturn(Optional.of(testDataSet))

    val ingestionService = new IngestionService(mockAdminApi, nexlaMessageProducer, schemaDetectionCache, mockAppProps, NexlaSslContext.NOSSL_CONTEXT)
    ingestionService.ingestArbitrary(util.Collections.emptyMap(), util.Collections.emptyMap(), List(msg1).asJava, 1, true)
    val oldSchemaId = utils.getSchemaId(testDataSet.getId).get()
    waitOnThread("forkjoinpool")
    Mockito.clearInvocations(mockAdminApi)

    ingestionService.ingestArbitrary(util.Collections.emptyMap(), util.Collections.emptyMap(), List(msg2).asJava, 1, true)
    waitOnThread("forkjoinpool")
    verify(mockAdminApi, never()).updateDataSetSchema(any())
    verify(mockAdminApi, times(1)).putDataSetSamples(samplesCaptor.capture(), ArgumentMatchers.eq(testDataSet.getId))

    utils.getSamples(testDataSet.getId).get.asScala.map(m => m.getRawMessage) shouldBe List(msg1, msg2)
    utils.getSchemaId(testDataSet.getId).get() shouldBe oldSchemaId
  }

  it should "append to schema and data samples on new disjoint schema" in {
    val msg1 : util.LinkedHashMap[String, AnyRef] = StreamUtils.lhm("a", Int.box(1), "b", Int.box(2))
    val msg2 : util.LinkedHashMap[String, AnyRef] = StreamUtils.lhm("c", "foo", "d", "bar")
    val utils = schemaDetectionCache.getSchemaDetection(testDataSource.getId)
    val dataSetCaptor = ArgumentCaptor.forClass(classOf[DataSet])
    val samplesCaptor: ArgumentCaptor[util.List[NexlaMessage]] = ArgumentCaptor.forClass(classOf[util.List[NexlaMessage]])

    Mockito.when(mockAdminApi.getDataSet(any())).thenReturn(Optional.of(testDataSet))
    val ingestionService = new IngestionService(mockAdminApi, nexlaMessageProducer, schemaDetectionCache, mockAppProps, NexlaSslContext.NOSSL_CONTEXT)
    ingestionService.ingestArbitrary(util.Collections.emptyMap(), util.Collections.emptyMap(), List(msg1).asJava, 1, true)
    waitOnThread("forkjoinpool")
    Mockito.clearInvocations(mockAdminApi)

    ingestionService.ingestArbitrary(util.Collections.emptyMap(), util.Collections.emptyMap(), List(msg2).asJava, 1, true)
    waitOnThread("forkjoinpool")
    verify(mockAdminApi, times(1)).updateDataSetSchema(dataSetCaptor.capture())
    verify(mockAdminApi, times(1)).putDataSetSamples(samplesCaptor.capture(), ArgumentMatchers.eq(testDataSet.getId))

    val resultDs: DataSet = dataSetCaptor.getValue
    val samplesToAdd = samplesCaptor.getValue
    resultDs.getSourceSchema.getProperties shouldBe new util.LinkedHashMap[String, util.LinkedHashMap[String, String]](){{
      put("a", new util.LinkedHashMap[String, String](){{put("type", "number")}})
      put("b", new util.LinkedHashMap[String, String](){{put("type", "number")}})
      put("c", new util.LinkedHashMap[String, String](){{put("type", "string")}})
      put("d", new util.LinkedHashMap[String, String](){{put("type", "string")}})
    }}
    samplesToAdd.asScala.map(m => m.getRawMessage) shouldBe List(msg2)

    utils.getSamples(testDataSet.getId).get.asScala.map(m => m.getRawMessage) shouldBe List(msg1, msg2)
  }

  private def createMockDataSource() = {
    val dataSource = new DataSource
    dataSource.setSourceConfig(util.Map.of(SchemaDetection.SCHEMA_DETECTION_ONCE, Boolean.box(true)))
    dataSource.setStatus(ResourceStatus.ACTIVE)
    dataSource.setDatasets(util.Collections.emptyList())
    val mockOrg = mock(classOf[Org])
    when(mockOrg.getId).thenReturn(999)
    val mockOwner = mock(classOf[Owner])
    when(mockOwner.getId).thenReturn(999)
    dataSource.setOrg(mockOrg)
    dataSource.setOwner(mockOwner)
    dataSource.setId(1)
    dataSource
  }

  private def createMockDataSet() = {
    val dataSet = new DataSet
    dataSet.setId(2)
    val mockOrg = mock(classOf[Org])
    when(mockOrg.getId).thenReturn(999)
    dataSet.setOrg(mockOrg)
    dataSet
  }

  private def waitOnThread(namePrefix: String) = {
    // hacky but can't get to the ExecutorService used to execute parts of things the end-to-end tests want to verify
    val threadSet = Thread.getAllStackTraces.keySet
    threadSet.forEach( t => {
      if (t.getName.toLowerCase().startsWith(namePrefix.toLowerCase())){
        t.run()
        t.join(10)
      }
    })
  }
}
