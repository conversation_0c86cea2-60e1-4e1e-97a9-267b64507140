package com.nexla.service

import com.nexla.common.{NexlaMessage, NexlaMetaData, ResourceType}
import com.nexla.http.api.IngestionMessage
import com.nexla.service.IngestMessageParser.{WrappedNexlaMessage, WrappedTraceMessage}
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

import java.util
import java.util.Optional
import scala.jdk.CollectionConverters.mapAsJavaMapConverter
import scala.util.{Failure, Success}

class IngestMessageParserSpec extends AnyWordSpec with Matchers {

  "IngestMessageParser" should {
    "parse force schema detection correctly" in {
      val input1 =
        """{
          "force_schema_detection": true,
          "data": [
          ]
        }""".stripMargin

      val input2 =
        """{
          "force_schema_detection": false,
          "data": [
          ]
        }""".stripMargin

      val input3 =
        """{
          "data": [
          ]
        }""".stripMargin

      val triedResult1 = IngestMessageParser.parseForceSchemaDetection(input1)
      triedResult1.get shouldEqual true

      val triedResult2 = IngestMessageParser.parseForceSchemaDetection(input2)
      triedResult2.get shouldEqual false

      val triedResult3 = IngestMessageParser.parseForceSchemaDetection(input3)
      triedResult3.get shouldEqual false
    }

    "parse valid input correctly" in {
      val input =
        """{
          "force_schema_detection": true,
          "data": [
            {
              "rawMessage": { "type": "text", "text": "Some text" },
              "metadata": { "file_name": "file1.pdf", "file_id": 1234, "eof": false }
            },
            {
              "rawMessage": { "someInternal": { "nestedField": 1 }, "field1": "val1", "field2": true },
              "metadata": { "file_name": "file2.pdf", "file_id": 1235, "eof": true }
            },
            {
              "runId": 1742902666835,
              "lastBatch": true,
              "type": "NX_RUN_TRACE"
            }
          ]
        }""".stripMargin

      val triedResult = IngestMessageParser.parseInput(1, "t", 2, 1742902666836L, input)
      triedResult shouldBe a[Success[_]]
      val result = triedResult.get
      result.messages should have length 3
      result.parsingFailures should have length 0

      result.messages(0) shouldBe a [WrappedNexlaMessage]
      result.messages(1) shouldBe a [WrappedNexlaMessage]
      result.messages(2) shouldBe a [WrappedTraceMessage]

      val msg1 = result.messages(0).asInstanceOf[WrappedNexlaMessage].nexlaMessage
      msg1.getRawMessage shouldEqual Map("type"-> "text", "text"-> "Some text").asJava
      msg1.getNexlaMetaData.getSourceKey shouldEqual "file1.pdf"
      msg1.getNexlaMetaData.getSourceOffset shouldEqual 0
      msg1.getNexlaMetaData.isEof shouldEqual false
      msg1.getNexlaMetaData.getRunId shouldEqual 1742902666836L
      msg1.getNexlaMetaData.getResourceType shouldEqual ResourceType.SOURCE
      msg1.getNexlaMetaData.getResourceId shouldEqual 1
      msg1.getNexlaMetaData.getTags shouldEqual Map("file_id" -> 1234L).asJava

      val msg2 = result.messages(1).asInstanceOf[WrappedNexlaMessage].nexlaMessage
      msg2.getRawMessage shouldEqual Map("field1"-> "val1", "field2"-> true, "someInternal" -> Map("nestedField"-> 1).asJava).asJava
      msg2.getNexlaMetaData.getSourceKey shouldEqual "file2.pdf"
      msg2.getNexlaMetaData.getSourceOffset shouldEqual 0
      msg2.getNexlaMetaData.isEof shouldEqual true
      msg2.getNexlaMetaData.getRunId shouldEqual 1742902666836L
      msg2.getNexlaMetaData.getResourceType shouldEqual ResourceType.SOURCE
      msg2.getNexlaMetaData.getResourceId shouldEqual 1
      msg2.getNexlaMetaData.getTags shouldEqual Map("file_id" -> 1235L).asJava


      val msg3 = result.messages(2).asInstanceOf[WrappedTraceMessage].traceMessage
      msg3.getRunId shouldEqual 1742902666835L
      msg3.getLastBatch shouldEqual true
    }

    "parse valid input with tags correctly" in {
      val input =
        """{
          "force_schema_detection": true,
          "data": [
            {
              "rawMessage": { "type": "text", "text": "Some text" },
              "nexlaMetaData": {
                "file_name": "file1.pdf",
                "sourceKey": "Sites/saketnexla.sharepoint.com,d9970043-6985-43d8-81ca-2a709652d331,ef886d32-a6bb-46df-a7f5-648411fc30de/b!QwCX2YVp2EOByipwllLTMTJtiO-7pt9Gp_VkhBH8MN7lqPlVO9AWRood1tbCkWRA/01AJEM62ISBMIHRPDDFNEJ6UDZHSMKG2ZW/01AJEM62N2CN7DKOUESJDJD36IZKZZWL77",
                "sourceOffset": 32,
                "datasetId": -1,
                "file_id": 1234,
                "eof": false,
                "tags": { "tag1": "value1", "tag2": "value2" }
              }
            }
          ]
        }""".stripMargin

      val triedResult = IngestMessageParser.parseInput(1, "t", 2, 1742902666836L, input)
      triedResult shouldBe a[Success[_]]
      val result = triedResult.get
      result.messages should have length 1
      result.parsingFailures should have length 0

      result.messages.head shouldBe a [WrappedNexlaMessage]

      val msg1 = result.messages.head.asInstanceOf[WrappedNexlaMessage].nexlaMessage
      msg1.getRawMessage shouldEqual Map("type"-> "text", "text"-> "Some text").asJava

      val meta = msg1.getNexlaMetaData

      meta.getSourceKey shouldEqual "Sites/saketnexla.sharepoint.com,d9970043-6985-43d8-81ca-2a709652d331,ef886d32-a6bb-46df-a7f5-648411fc30de/b!QwCX2YVp2EOByipwllLTMTJtiO-7pt9Gp_VkhBH8MN7lqPlVO9AWRood1tbCkWRA/01AJEM62ISBMIHRPDDFNEJ6UDZHSMKG2ZW/01AJEM62N2CN7DKOUESJDJD36IZKZZWL77"
      meta.getSourceOffset shouldEqual 32
      meta.getDatasetId shouldEqual 2
      meta.isEof shouldEqual false
      meta.getRunId shouldEqual 1742902666836L
      meta.getResourceType shouldEqual ResourceType.SOURCE
      meta.getResourceId shouldEqual 1
      meta.getTags shouldEqual Map("file_id" -> 0L, "tag1" -> "value1", "tag2" -> "value2").asJava
    }

    "return failure when 'data' field is missing" in {
      val input = "{}"

      val result = IngestMessageParser.parseInput(1, "t", 2, 3, input)
      result shouldBe a[Failure[_]]
    }

    "return failure when 'rawMessage' field is missing" in {
      val input =
        """{
          "data": [
            { "metadata": { "file_name": "file1.pdf", "file_id": 1234, "eof": false } }
          ]
        }""".stripMargin

      val result = IngestMessageParser.parseInput(1, "t", 2, 3, input)
      result shouldBe a[Success[_]]
      result.get.messages should have length 0
      result.get.parsingFailures should have length 1
      result.get.parsingFailures.head.err.getMessage should include("'rawMessage' field was not found")
    }

    "handle optional metadata fields correctly" in {
      val input =
        """{
          "data": [
            {
              "rawMessage": { "type": "text", "text": "Some text" },
              "metadata": {}
            }
          ]
        }""".stripMargin

      val result = IngestMessageParser.parseInput(1, "t", 2, 3, input)
      result shouldBe a[Success[_]]
      result.get.messages should have length 1
      val nm = result.get.messages.head.asInstanceOf[WrappedNexlaMessage].nexlaMessage
      nm.getNexlaMetaData.getSourceKey shouldBe "unknown"
      nm.getNexlaMetaData.isEof shouldBe false
      nm.getNexlaMetaData.getTags shouldEqual Map("file_id" -> 0L).asJava
    }
  }
}
