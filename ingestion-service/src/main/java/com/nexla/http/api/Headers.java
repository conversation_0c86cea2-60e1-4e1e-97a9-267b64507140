package com.nexla.http.api;

import lombok.experimental.UtilityClass;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.google.common.net.HttpHeaders.*;

@UtilityClass
public final class Headers {
    private static final List<String> HEADERS = Stream.of(
            ACCEPT,
            ACCEPT_CHARSET,
            ACCEPT_ENCODING,
            ACCEPT_LANGUAGE,
            ACCEPT_RANGES,
            ACCESS_CONTROL_ALLOW_CREDENTIALS,
            ACCESS_CONTROL_ALLOW_HEADERS,
            ACCESS_CONTROL_ALLOW_METHODS,
            ACCESS_CONTROL_ALLOW_ORIGIN,
            ACCESS_CONTROL_EXPOSE_HEADERS,
            ACCESS_CONTROL_MAX_AGE,
            ACCESS_CONTROL_REQUEST_HEADERS,
            ACCESS_CONTROL_REQUEST_METHOD,
            AGE,
            ALLOW,
            AUTHORIZATION,
            CACHE_CONTROL,
            CONNECTION,
            CONTENT_DISPOSITION,
            CONTENT_ENCODING,
            CONTENT_LANGUAGE,
            CONTENT_LENGTH,
            CONTENT_LOCATION,
            CONTENT_MD5,
            CONTENT_RANGE,
            CONTENT_SECURITY_POLICY,
            CONTENT_SECURITY_POLICY_REPORT_ONLY,
            CONTENT_TYPE,
            COOKIE,
            DATE,
            DNT,
            ETAG,
            EXPECT,
            EXPIRES,
            FORWARDED,
            FROM,
            HOST,
            IF_MATCH,
            IF_MODIFIED_SINCE,
            IF_NONE_MATCH,
            IF_RANGE,
            IF_UNMODIFIED_SINCE,
            LAST_EVENT_ID,
            LAST_MODIFIED,
            LINK,
            LOCATION,
            MAX_FORWARDS,
            ORIGIN,
            P3P,
            PING_FROM,
            PING_TO,
            PRAGMA,
            PROXY_AUTHENTICATE,
            PROXY_AUTHORIZATION,
            RANGE,
            REFERER,
            REFRESH,
            RETRY_AFTER,
            SERVER,
            SERVICE_WORKER,
            SERVICE_WORKER_ALLOWED,
            SET_COOKIE,
            SET_COOKIE2,
            STRICT_TRANSPORT_SECURITY,
            TE,
            TIMING_ALLOW_ORIGIN,
            TRAILER,
            TRANSFER_ENCODING,
            UPGRADE,
            USER_AGENT,
            VARY,
            VIA,
            WARNING,
            WWW_AUTHENTICATE,
            X_CONTENT_SECURITY_POLICY,
            X_CONTENT_SECURITY_POLICY_REPORT_ONLY,
            X_CONTENT_TYPE_OPTIONS,
            X_DO_NOT_TRACK,
            X_FORWARDED_FOR,
            X_FORWARDED_HOST,
            X_FORWARDED_PORT,
            X_FORWARDED_PROTO,
            X_FRAME_OPTIONS,
            X_POWERED_BY,
            X_REQUESTED_WITH,
            X_USER_IP,
            X_WEBKIT_CSP,
            X_WEBKIT_CSP_REPORT_ONLY,
            X_XSS_PROTECTION
    ).map(String::toLowerCase).collect(Collectors.toList());

    public static boolean isStandard(String headerName) {
        return HEADERS.contains(headerName.toLowerCase());
    }
}
