package com.nexla.http.api;

import com.beust.jcommander.internal.Sets;
import com.nexla.admin.client.DataSource;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

public class QueuedProducer<T extends Supplier> implements AutoCloseable {

	private static final Logger logger = LoggerFactory.getLogger(QueuedProducer.class);

	private static final ScheduledExecutorService SCHEDULED_EXECUTOR_SERVICE = Executors.newScheduledThreadPool(0);
	public static final int ALL_ELEMENTS = -1;

	private final LinkedBlockingQueue<Pair<T, Integer>> queue;
	private final ScheduledFuture<?> flushFuture;
	private final DataSource dataSource;

	public QueuedProducer(DataSource dataSource, Duration flushPeriod, int batchSize) {
		this.queue = new LinkedBlockingQueue<>();

		this.flushFuture = SCHEDULED_EXECUTOR_SERVICE.scheduleWithFixedDelay(
			() -> shrinkQueue(batchSize),
			flushPeriod.toMillis(), flushPeriod.toMillis(), TimeUnit.MILLISECONDS);
		this.dataSource = dataSource;
	}

	public void add(T value, Integer dataSetId) {
		queue.add(Pair.of(value, dataSetId));
	}

	private void shrinkQueue(int maxElementsToFlush) {
		int actualRecordsFlushed = 0;

		int maxNumber = maxElementsToFlush == ALL_ELEMENTS ? Integer.MAX_VALUE : maxElementsToFlush;

		Set<Integer> dataSetIds = Sets.newHashSet();
		for (int i = 1; i <= maxNumber; i++) {
			Pair<T, Integer> poll = queue.poll();
			if (poll == null) {
				break;
			} else {
				T element = poll.getKey();
				dataSetIds.add(poll.getValue());
				try {
					element.get();
					actualRecordsFlushed++;
				} catch (Exception e) {
					logger.error("Error while flushing", e);
				}
			}
		}

		logger.info("Flushed {} records for dataSource {}, datasets {}",
			actualRecordsFlushed,
			dataSource.getId(),
			StreamEx.of(dataSetIds).joining(","));
	}

	@Override
	public synchronized void close() {
		if (!flushFuture.isCancelled()) {
			flushFuture.cancel(false);
			shrinkQueue(ALL_ELEMENTS);
		}
	}
}
