package com.nexla.http.api;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.ConfigUtils;
import com.nexla.common.NexlaSslContext;
import com.nexla.common.notify.transport.NexlaMessageProducer;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.kafka.KafkaMessageTransport;
import com.nexla.kafka.service.TopicMetaService;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import static com.nexla.common.NexlaConstants.TOPIC_METRICS;
import static com.nexla.common.NexlaConstants.TOPIC_NOTIFY;
import static com.nexla.common.parse.ParserConfigs.SchemaDetection.SCHEMA_DETECTION_ONCE;

public class SchemaDetectionCache {

	private final AdminApiClient adminApiClient;
	private final int partitions;
	private final int replication;
	private final long retentionTime;
	private final NexlaMessageProducer nexlaMessageProducer;
	private final TopicMetaService topicMetaService;

	private final ConcurrentHashMap<Integer, SchemaDetectionUtils> schemaDetectionBySource = new ConcurrentHashMap<>();

	public SchemaDetectionCache(
		AdminApiClient adminApiClient,
		String bootstrapServers,
		NexlaSslContext sslContext,
		int partitions,
		int replication,
		long retentionTime // 259200000 = 3 * 24 * 60 * 60 * 1000
	) {
		this.adminApiClient = adminApiClient;
		this.partitions = partitions;
		this.replication = replication;
		this.retentionTime = retentionTime;
		this.nexlaMessageProducer = new NexlaMessageProducer(new KafkaMessageTransport(bootstrapServers, sslContext, TOPIC_METRICS, TOPIC_NOTIFY));
		this.topicMetaService = new TopicMetaService(bootstrapServers, sslContext);
	}

	@SneakyThrows
	public SchemaDetectionUtils getSchemaDetection(int sourceId) {
		return schemaDetectionBySource.computeIfAbsent(sourceId, i ->
		{
			SchemaDetectionUtils schemaDetectionUtils = new SchemaDetectionUtils(sourceId, adminApiClient, nexlaMessageProducer,
				Optional.ofNullable(topicMetaService), partitions, replication, retentionTime, true);

			var dataSource = adminApiClient.getDataSource(sourceId);
			boolean isSchemaConfiguredToDetectOnce = dataSource
				.map(ds -> ds.getSourceConfig())
				.map(x -> x.get(SCHEMA_DETECTION_ONCE))
				.flatMap(ConfigUtils::optBoolean)
				.orElse(false);

			if (isSchemaConfiguredToDetectOnce) {
				schemaDetectionUtils.setTryCombineSingleSchema();
			}
			return schemaDetectionUtils;
		});
	}

	public void invalidate(int sourceId) {
		schemaDetectionBySource.remove(sourceId);
	}

	@SneakyThrows
	public void deleteDataSet(int dataSetId) {
		EntryStream.of(schemaDetectionBySource)
			.filterValues(v -> v.hasDataSet(dataSetId))
			.keys()
			.forEach(schemaDetectionBySource::remove);
	}
}
