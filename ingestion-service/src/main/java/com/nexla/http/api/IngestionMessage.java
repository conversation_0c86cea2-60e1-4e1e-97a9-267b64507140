package com.nexla.http.api;

import com.nexla.common.ConnectionType;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Optional;

@Data
public class IngestionMessage {
	private final LinkedHashMap<String, Object> message;
	private final Meta meta;

	public IngestionMessage(LinkedHashMap<String, Object> message, Meta meta) {
		this.message = message;
		this.meta = meta;
	}

	public LinkedHashMap<String, Object> getMessage() { // for scala code
		return message;
	}

	@Data
	public static class Meta {
		private final String name;
		private final Integer offset;
		private final Optional<ConnectionType> sourceType;
		private final Optional<Boolean> eof;
		private final Optional<Long> fileId;

		public Meta(String name, Integer offset, Optional<ConnectionType> sourceType, Optional<Boolean> eof, Optional<Long> fileId) {
			this.name = name;
			this.offset = offset;
			this.sourceType = sourceType;
			this.eof = eof;
			this.fileId = fileId;
		}
	}
}

