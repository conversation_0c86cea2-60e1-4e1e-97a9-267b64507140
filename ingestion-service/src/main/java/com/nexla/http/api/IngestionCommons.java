package com.nexla.http.api;

import com.nexla.admin.client.DataSource;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.NexlaSslContext;
import com.nexla.common.ResourceType;
import com.nexla.common.tracker.SourceItem;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.http.AppProps;
import com.nexla.kafka.control.listener.conf.SslContextApplier;
import one.util.streamex.EntryStream;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ConverterUtils.toLong;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static org.apache.commons.lang3.ObjectUtils.firstNonNull;

public class IngestionCommons {

    private static final String WEBHOOK = "webhook";
    private static final String HEADER_PREFIX = "header_";
    private static final String PARAM_PREFIX = "url_param_";

    public static KafkaProducer<String, String> kafkaProducer(AppProps appProps, NexlaSslContext sslContext) {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, appProps.bootstrapServers());
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put(CommonClientConfigs.METRIC_REPORTER_CLASSES_CONFIG, "com.airbnb.kafka.kafka09.StatsdMetricsReporter");
        props.put("external.kafka.statsd.reporter.enabled", String.valueOf(appProps.statsdEnabled()));
        props.put("external.kafka.statsd.host", appProps.statsdHost());
        props.put("external.kafka.statsd.port", String.valueOf(appProps.statsdPort()));
        props.put("external.kafka.statsd.metrics.prefix", appProps.statsdPrefix());
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "snappy");
        return new KafkaProducer<>(
                new SslContextApplier(sslContext).apply(props)
        );
    }

    public static boolean isTooFewSamples(DataSource source, SchemaDetectionUtils schemaDetection) {
        if (CollectionUtils.isEmpty(source.getDatasets())) {
            return true;
        }
        Integer dataSetId = source.getDatasets().get(0).getId();
        return schemaDetection.getSamples(dataSetId)
                .map(x -> x.size() < SchemaDetectionUtils.MAX_SAMPLES)
                .orElse(true);
    }

    public static NexlaMessage buildNexlaMessage(Map<String, Object> headers, Map<String, Object> params, IngestionMessage input, int sourceId, String topic, int offset, int dataSetId, long runId) {
        return buildNexlaMessage(headers, params, input, sourceId, topic, offset, dataSetId, runId, new NexlaMetaData());
    }

    public static NexlaMessage buildNexlaMessage(Map<String, Object> headers, Map<String, Object> params, IngestionMessage input, int sourceId, String topic, int offset, int dataSetId, long runId, NexlaMetaData metadata) {
        long now = nowUTC().getMillis();
        metadata.setResourceType(ResourceType.SOURCE);
        metadata.setResourceId(sourceId);
        metadata.setIngestTime(now);
        metadata.setDatasetId(dataSetId);
        metadata.setTopic(topic);
        metadata.setRunId(runId);
        if (input.getMeta() != null) {
            IngestionMessage.Meta meta = input.getMeta();
            offset = Optional.ofNullable(meta.getOffset()).orElse(offset);

            metadata.setTrackerId(firstNonNull(
                    metadata.getTrackerId(),
                    new Tracker(Tracker.TrackerMode.FULL, SourceItem.fullTracker(sourceId, dataSetId, input.getMeta().getName(), toLong(offset + 1), 1, now))
            ));

            metadata.setSourceType(firstNonNull(metadata.getSourceType(), meta.getSourceType().orElse(null)));
            metadata.setSourceOffset(firstNonNull(metadata.getSourceOffset(), toLong(offset)));
            metadata.setSourceKey(firstNonNull(metadata.getSourceKey(), meta.getName()));
            metadata.setEof(meta.getEof().orElse(false));

            if (metadata.getTags() == null) {
                metadata.setTags(new LinkedHashMap<>());
            }
            metadata.getTags().put("file_id", meta.getFileId().orElse(null));
        }

        return new NexlaMessage(merge(headers, params, input.getMessage()), metadata);
    }

    public static NexlaMessage buildNexlaMessageFromRaw(Map<String, Object> headers, Map<String, Object> params, Map<String, Object> rawMessage, int sourceId, String topic, int offset, int dataSetId, long runId) {
        long now = nowUTC().getMillis();
        NexlaMetaData metadata = new NexlaMetaData();
        metadata.setResourceType(ResourceType.SOURCE);
        metadata.setResourceId(sourceId);
        metadata.setIngestTime(now);
        metadata.setTopic(topic);
        metadata.setRunId(runId);
        metadata.setTrackerId(new Tracker(Tracker.TrackerMode.FULL, SourceItem.fullTracker(sourceId, dataSetId, WEBHOOK, toLong(offset + 1), 1, now)));

        return new NexlaMessage(merge(headers, params, rawMessage), metadata);
    }

    private static LinkedHashMap<String, Object> merge(Map<String, Object> headers, Map<String, Object> params, Map<String, Object> message) {
        LinkedHashMap<String, Object> rawMessage = new LinkedHashMap<>(message);

        rawMessage.putAll(EntryStream.of(headers).mapKeys(n -> HEADER_PREFIX + n).toMap());
        rawMessage.putAll(EntryStream.of(params).mapKeys(n -> PARAM_PREFIX + n).toMap());

        return rawMessage;
    }
}
