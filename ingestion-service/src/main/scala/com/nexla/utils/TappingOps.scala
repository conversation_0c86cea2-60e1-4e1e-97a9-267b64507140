package com.nexla.utils

import akka.actor.Scheduler

import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ExecutionContext, Future, Promise, TimeoutException}
import scala.util.Try

object TappingOps {
  implicit class futureOps[T](f: Future[T]) {
    def tapError(fn: Throwable => Any)(implicit ec: ExecutionContext): Future[T] = f.recover { case ex =>
      fn(ex)
      throw ex
    }

    def tap(fn: T => Any)(implicit ec: ExecutionContext): Future[T] = f.map { e =>
      fn(e)
      e
    }

    def timeout(duration: FiniteDuration)(implicit ec: ExecutionContext, scheduler: Scheduler): Future[T] = {
      val promise = Promise[T]()

      scheduler.scheduleOnce(duration) {
        promise.tryFailure(new TimeoutException(s"Future timed out after $duration"))
        ()
      }

      Future.firstCompletedOf(Seq(f, promise.future))
    }
  }

  implicit class tryOps[T](f: Try[T]) {
    def tapError(fn: Throwable => Any): Try[T] = f.recover { case ex =>
      fn(ex)
      throw ex
    }

    def tap(fn: T => Any): Try[T] = f.map { e =>
      fn(e)
      e
    }
  }
}

