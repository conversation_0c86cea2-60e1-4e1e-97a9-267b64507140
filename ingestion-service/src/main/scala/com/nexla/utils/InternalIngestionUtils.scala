package com.nexla.utils

import com.nexla.admin.client.NexlaSchema
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils
import com.nexla.transform.JsonCompareResult

object InternalIngestionUtils {

  def isUpdateNeeded(schemaDetectionUtils: SchemaDetectionUtils, newSchema: NexlaSchema, maybeOldSchema: Option[NexlaSchema]): Boolean = {
    maybeOldSchema match {
      case None => true
      case Some(oldSchema) =>
        val result = schemaDetectionUtils.compareSchemas(newSchema, oldSchema)
        result != JsonCompareResult.SUBSET && result != JsonCompareResult.EQUAL
    }
  }

}
