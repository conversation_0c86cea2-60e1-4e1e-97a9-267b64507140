package com.nexla.service

import com.bazaarvoice.jolt.JsonUtils
import com.nexla.admin.client.DataSource
import com.nexla.common.logging.{<PERSON><PERSON>la<PERSON><PERSON><PERSON><PERSON>, NexlaLogger}
import com.nexla.common.{NexlaMessage, ResourceType}
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils
import com.nexla.http.api.{DelayedKafkaMetricsProducer, IngestionCommons, IngestionResponse, SchemaDetectionCache}
import com.nexla.schemasync.SchemaSyncProducerService
import com.nexla.service.IngestMessageParser.{WrappedMessage, WrappedNexlaMessage}
import com.nexla.service.InternalIngestionService.{SchemaUpdateResult, SchemaWasNotUpdated, SchemaWasUpdated}
import com.nexla.service.InternalIngestionServiceFactory.{Context, SchemasCache}
import com.nexla.utils.InternalIngestionUtils
import org.apache.kafka.clients.producer.{<PERSON><PERSON><PERSON><PERSON>roduce<PERSON>, Producer<PERSON><PERSON>ord, RecordMetadata}
import org.slf4j.LoggerFactory

import java.util.Optional
import scala.compat.java8.OptionConverters.RichOptionForJava8
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters.seqAsJavaListConverter


class InternalIngestionService(
                                dataSource: DataSource,
                                ctx: Context,
                                schemaCache: SchemasCache,
                                schemaDetectionCache: SchemaDetectionCache,
                                kafkaProducer: KafkaProducer[String, String],
                                metricsSender: DelayedKafkaMetricsProducer,
                                schemaSyncService: SchemaSyncProducerService,
                              )(implicit ec: ExecutionContext) {
  private val logger = new NexlaLogger(LoggerFactory.getLogger(classOf[InternalIngestionService]), new NexlaLogKey(ResourceType.SOURCE, dataSource.getId, Optional.empty()));

  private val schemaDetection = schemaDetectionCache.getSchemaDetection(dataSource.getId)
  schemaDetection.setTryCombineSingleSchema()
  private val tooFewSamples = IngestionCommons.isTooFewSamples(dataSource, schemaDetection)

  def ingestInternal(messages: List[WrappedMessage]): Future[IngestionResponse] = {
    val nexlaMessages = messages.collect { case WrappedNexlaMessage(nm) => nm }
    val schemaUpdateAndSamplesEff = maybeUpdateSchema(nexlaMessages).flatMap {
      case InternalIngestionService.SchemaWasUpdated => updateSamples(nexlaMessages)
      case InternalIngestionService.SchemaWasNotUpdated if tooFewSamples => updateSamples(nexlaMessages)
      case InternalIngestionService.SchemaWasNotUpdated => Future.unit
    }
    schemaUpdateAndSamplesEff.map(_ => ingestMessagesInternal(messages))
  }

  private def maybeUpdateSchema(messages: List[NexlaMessage]): Future[SchemaUpdateResult] = {
    if (ctx.forceSchemaDetection || tooFewSamples) {
      val maybeOldSchema = schemaCache.getForDataset(ctx.datasetId)
      val mergedSchema = schemaDetection.computeMergedSchema(messages.map(_.getRawMessage).asJava, maybeOldSchema.asJava)
      if (InternalIngestionUtils.isUpdateNeeded(schemaDetection, mergedSchema, maybeOldSchema)) {
        logger.info(s"Schema update required. Updating dataset for source ${dataSource.getId}. New schemaId: ${mergedSchema.getSchemaId}")
        schemaSyncService.updateSchema(dataSource.getId, ctx.datasetId, mergedSchema).map { _ =>
          schemaCache.putForDataset(ctx.datasetId, mergedSchema)
          SchemaWasUpdated
        }
      } else {
        logger.info(s"Schema update not required.")
        Future.successful(SchemaWasNotUpdated)
      }
    } else Future.successful(SchemaWasNotUpdated)
  }

  private def updateSamples(messages: List[NexlaMessage]): Future[Unit] = {
    val newSamples = messages.take(SchemaDetectionUtils.MAX_SAMPLES)
    schemaDetection.addLocalSamples(newSamples.asJava, ctx.datasetId)
    schemaSyncService.sendSample(dataSource.getId, ctx.datasetId, newSamples)
  }

  private def ingestMessagesInternal(nexlaMessages: List[WrappedMessage]) = {
    val orgId = dataSource.getOrg.getId
    val ownerId = dataSource.getOwner.getId

    nexlaMessages.foreach {
      case IngestMessageParser.WrappedTraceMessage(traceMessage) =>
        val json = JsonUtils.toJsonString(traceMessage)
        val record = new ProducerRecord[String, String](ctx.topicName, json)
        kafkaProducer.send(record, (_: RecordMetadata, e: Exception) => {
          if (e != null) {
            logger.error(s"[$ctx.topicName] Failed to send=$json", e)
          } else { }
        })
      case IngestMessageParser.WrappedNexlaMessage(nm) =>
          val json = JsonUtils.toJsonString(nm)
          val record = new ProducerRecord[String, String](ctx.topicName, json)
          val name = nm.getNexlaMetaData.getSourceKey
          val eof = nm.getNexlaMetaData.isEof
          kafkaProducer.send(record, (_: RecordMetadata, e: Exception) => {
            if (e != null) {
              logger.error(s"[$ctx.topicName] Failed to send=$json", e)
              metricsSender.sendErrorDatasetMetricsDelayed(ctx.datasetId, name, Optional.of(eof), ctx.runId, json.length, 1, orgId, ownerId)
            } else {
              metricsSender.sendDatasetMetricsDelayed(ctx.datasetId, name, Optional.of(eof), ctx.runId, json.length, 1, orgId, ownerId)
            }
          })
    }

    IngestionResponse(ctx.datasetId, nexlaMessages.size)
  }
}

object InternalIngestionService {
  private sealed trait SchemaUpdateResult
  private case object SchemaWasUpdated extends SchemaUpdateResult
  private case object SchemaWasNotUpdated extends SchemaUpdateResult
}
