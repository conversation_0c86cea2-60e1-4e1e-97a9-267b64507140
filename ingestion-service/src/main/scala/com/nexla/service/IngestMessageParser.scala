package com.nexla.service

import com.bazaarvoice.jolt.JsonUtils
import com.nexla.common.{ConnectionType, NexlaMessage, NexlaMetaData, TraceMessage}
import com.nexla.http.api.{IngestionCommons, IngestionMessage}
import spray.json.DefaultJsonProtocol._
import spray.json.{JsArray, JsBoolean, JsNumber, JsObject, JsString, JsValue, JsonParser}

import java.util
import java.util.{Collections, Optional}
import scala.compat.java8.OptionConverters.RichOptionForJava8
import scala.util.{Failure, Success, Try}

object IngestMessageParser {

  def parseForceSchemaDetection(input: String): Try[Boolean]  = Try {
    val json = JsonParser(input).convertTo[JsObject]

    json.fields.get("force_schema_detection") match {
      case Some(JsBoolean(value)) => value
      case _ => false
    }
  }

  def parseInput(sourceId: Int, topicName: String, datasetId: Int, runId: Long, input: String): Try[IngestMessageData] = Try {
    val json = JsonParser(input).convertTo[JsObject]

    val jsonList: List[JsObject] = json.fields.get("data") match {
      case Some(data: JsArray) => data.convertTo[List[JsObject]]
      case None => throw new Exception("'data' field was not found")
    }

    val data = jsonList.map { data =>
      parseNexlaMessage(sourceId, topicName, datasetId, runId, data).recoverWith {
        case firstEx => parseTraceMessage(data).recoverWith { case _ => Failure(firstEx) }
      }
    }.zipWithIndex.map {
      case (Failure(exception), idx) => ParsingFailure(idx, exception)
      case (Success(v), _) => ParsingSuccess(v)
    }
    val parsingFailures = data.collect { case f: ParsingFailure => f }
    val ims = data.collect { case ParsingSuccess(im) => im }
    IngestMessageData(ims, parsingFailures)
  }


  private def parseNexlaMessage(sourceId: Int, topicName: String, datasetId: Int, runId: Long, data: JsObject): Try[WrappedNexlaMessage] = Try {
    val metadata = data.fields.get("metadata") match {
      case Some(JsObject(fields)) => fields
      case _ => Map.empty[String, JsValue]
    }

    def getAsString(key: String): Option[String] = metadata.get(key).flatMap {
      case JsString(value) => Some(value)
      case _ => None
    }

    def getAsBoolean(key: String): Option[Boolean] = metadata.get(key).flatMap {
      case JsBoolean(value) => Some(value)
      case _ => None
    }

    def getAsLong(key: String): Option[Long] = metadata.get(key).flatMap {
      case JsNumber(value) => Some(value.toLong)
      case _ => None
    }

    val fileName = getAsString("file_name").getOrElse("unknown")
    val offset = getAsLong("offset").map(_.toInt).getOrElse(0)
    val sourceType = getAsString("source_type").flatMap(v => Try(ConnectionType.fromString(v)).toOption)
    val eof = getAsBoolean("eof").getOrElse(false)
    val fileId = getAsLong("file_id").getOrElse(0L)

    val rawMessage = data.fields.get("rawMessage") match {
      case Some(j) => JsonUtils.jsonToObject(j.compactPrint).asInstanceOf[java.util.LinkedHashMap[String, AnyRef]]
      case None => throw new Exception("'rawMessage' field was not found")
    }

    val rawMetadata = data.fields.get("nexlaMetaData")
      .collect { case meta: JsObject => JsonUtils.stringToType(meta.compactPrint, classOf[NexlaMetaData])}

    val m = new IngestionMessage(
      rawMessage,
      new IngestionMessage.Meta(
        fileName,
        offset,
        sourceType.asJava,
        Optional.of(eof),
        Optional.of(fileId))
    )

    WrappedNexlaMessage(
      IngestionCommons.buildNexlaMessage(Collections.emptyMap(), Collections.emptyMap(), m, sourceId, topicName, offset, datasetId, runId, rawMetadata.getOrElse(new NexlaMetaData()))
    )
  }

  private def parseTraceMessage(data: JsObject): Try[WrappedTraceMessage] = Try {
    data.fields.get("type") match {
      case Some(JsString(value)) if value == TraceMessage.NX_RUN_TRACE => WrappedTraceMessage(JsonUtils.stringToType(data.compactPrint, classOf[TraceMessage]))
      case _ => throw new Exception("Not a trace message")
    }
  }

  case class IngestMessageData(messages: List[WrappedMessage], parsingFailures: List[ParsingFailure])

  sealed trait WrappedMessage
  final case class WrappedTraceMessage(traceMessage: TraceMessage) extends WrappedMessage
  final case class WrappedNexlaMessage(nexlaMessage: NexlaMessage) extends WrappedMessage

  sealed trait ParsingResult
  case class ParsingSuccess(im: WrappedMessage) extends ParsingResult
  case class ParsingFailure(index: Int, err: Throwable) extends ParsingResult

}
