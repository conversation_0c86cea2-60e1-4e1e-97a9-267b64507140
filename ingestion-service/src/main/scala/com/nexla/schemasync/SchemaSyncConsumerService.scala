package com.nexla.schemasync

import akka.actor.ActorSystem
import com.nexla.admin.client.AdminApiClient
import com.nexla.common.NexlaSslContext
import com.nexla.http.AppProps
import com.nexla.http.api.SchemaDetectionCache
import com.nexla.schemasync.kafka.SchemaSyncKafkaConsumer
import com.nexla.schemasync.model.SchemaSyncEvent
import com.nexla.utils.InternalIngestionUtils
import com.nexla.utils.TappingOps.futureOps
import com.typesafe.scalalogging.StrictLogging

import scala.compat.java8.OptionConverters.{RichOptionForJava8, RichOptionalGeneric}
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters.seqAsJavaListConverter
import scala.util.Try

class SchemaSyncConsumerService(adminApi: AdminApiClient, props: AppProps, sslContext: NexlaSslContext)
                               (implicit system: ActorSystem, ec: ExecutionContext) extends StrictLogging {

  private val kafkaConsumer = new SchemaSyncKafkaConsumer(props, sslContext)
  private val schemaDetectionCache = new SchemaDetectionCache(adminApi, props.bootstrapServers, sslContext, props.dataset.partitions, props.dataset.replication, props.retentionMs)

  def run(): Future[Unit] = {
    kafkaConsumer.readSchemaSync(handleSchemaSyncEvents).map(_ => ()).tap(_ => logger.error("Should never end"))
  }

  private def handleSchemaSyncEvents(event: SchemaSyncEvent): Future[Unit] = event match {
    case e: SchemaSyncEvent.SchemaUpdate => handleSchemaUpdateEvent(e)
    case e: SchemaSyncEvent.SamplesUpdate => handleSamplesUpdateEvent(e)
  }

  private def handleSchemaUpdateEvent(schemaUpdate: SchemaSyncEvent.SchemaUpdate): Future[Unit] = {
    Future.fromTry(Try {
      val schemaUtils = schemaDetectionCache.getSchemaDetection(schemaUpdate.sourceId)
      val maybeOldSchema = schemaUtils.getSchemaForDataset(schemaUpdate.datasetId)
      val mergedSchema = schemaUtils.computeMergedSchema(schemaUpdate.schema, maybeOldSchema)
      (schemaUtils, maybeOldSchema, mergedSchema)
    }).flatMap { case (schemaUtils, maybeOldSchema, mergedSchema) =>
      if (InternalIngestionUtils.isUpdateNeeded(schemaUtils, mergedSchema, maybeOldSchema.asScala)) {
        logger.info(s"Schema update required. Updating dataset for source ${schemaUpdate.sourceId}. New schemaId: ${mergedSchema.getSchemaId}")
        Future(schemaUtils.updateOrCreateDataSet(List.empty.asJava, Some(mergedSchema).asJava)).map(_ => ())
          .tap(_ => logger.info(s"Schema for source ${schemaUpdate.sourceId} has been updated")).tapError(logger.error(s"Schema update for source ${schemaUpdate.sourceId} failed", _))
      } else {
        logger.info(s"Schema update not required")
        Future.unit
      }
    }
  }

  private def handleSamplesUpdateEvent(event: SchemaSyncEvent.SamplesUpdate): Future[Unit] = {
    val schemaUtils = schemaDetectionCache.getSchemaDetection(event.sourceId)
    logger.info(s"Adding samples for source ${event.sourceId} and dataset ${event.datasetId}")
    Future(schemaUtils.addSamplesToExisting(event.samples.asJava, event.datasetId)).map(_ => ())
      .tap(_ => logger.info(s"Samples for source ${event.sourceId} and dataset ${event.datasetId} have been updated"))
      .tapError(logger.error(s"Samples update for source ${event.sourceId} and dataset ${event.datasetId} failed", _))
  }
}
