package com.nexla.schemasync.kafka.codecs

import com.nexla.common.{NexlaMessage, StreamUtils}
import com.nexla.utils.TappingOps.tryOps
import com.typesafe.scalalogging.StrictLogging
import spray.json.{Js<PERSON><PERSON>, JsValue, JsonParser}

import scala.util.Try

object NexlaMessageCodec extends StrictLogging {

  def encode(nexlaMessage: NexlaMessage): JsObject = {
    JsonParser(StreamUtils.OBJECT_MAPPER.writeValueAsString(nexlaMessage)).asJsObject
  }

  def decode(jsValue: JsValue): Try[NexlaMessage] = {
    Try(StreamUtils.OBJECT_MAPPER.readValue(jsValue.compactPrint, classOf[NexlaMessage])).tapError(logger.error(s"Unable to parse nexlaMessage: $jsValue", _))
  }

}

