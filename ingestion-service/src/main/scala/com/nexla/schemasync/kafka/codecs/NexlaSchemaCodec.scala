package com.nexla.schemasync.kafka.codecs

import com.nexla.admin.client.NexlaSchema
import com.nexla.common.StreamUtils
import com.nexla.utils.TappingOps.tryOps
import com.typesafe.scalalogging.StrictLogging
import spray.json.{JsObject, JsValue, JsonParser}

import scala.util.Try

object NexlaSchemaCodec extends StrictLogging {

  def encode(nexlaSchema: NexlaSchema): JsObject = {
    JsonParser(StreamUtils.OBJECT_MAPPER.writeValueAsString(nexlaSchema)).asJsObject
  }

  def decode(jsValue: JsValue): Try[NexlaSchema] = {
    Try(StreamUtils.OBJECT_MAPPER.readValue(jsValue.compactPrint, classOf[NexlaSchema])).tapError(logger.error(s"Unable to parse schema: $jsValue", _))
  }

}
