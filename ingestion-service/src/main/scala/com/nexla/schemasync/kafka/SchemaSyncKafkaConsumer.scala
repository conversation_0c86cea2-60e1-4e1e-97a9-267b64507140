package com.nexla.schemasync.kafka


import akka.Done
import akka.actor.ActorSystem
import akka.kafka.scaladsl.{Committer, Consumer}
import akka.kafka.{CommitterSettings, ConsumerSettings, Subscriptions}
import akka.stream.scaladsl.{Keep, RestartSource}
import akka.stream.{Attributes, RestartSettings}
import com.nexla.common.NexlaSslContext
import com.nexla.common.logging.NexlaLogger
import com.nexla.http.AppProps
import com.nexla.kafka.control.listener.conf.SslContextApplier
import com.nexla.schemasync.kafka.codecs.SchemaSyncEventCodec
import com.nexla.schemasync.model.SchemaSyncEvent
import org.apache.kafka.common.serialization.StringDeserializer
import org.slf4j.LoggerFactory
import spray.json.JsonParser

import java.util.Collections
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters.mapAsScalaMapConverter
import scala.util.{Failure, Success, Try}

class SchemaSyncKafkaConsumer(props: AppProps, sslContext: NexlaSslContext)(implicit system: ActorSystem, ec: ExecutionContext) {
  private val logger = new NexlaLogger(LoggerFactory.getLogger(classOf[SchemaSyncKafkaConsumer]))
  private val consumerRestartSettings = RestartSettings(minBackoff = 1.second, maxBackoff = 5.seconds, randomFactor = 0.2)
  private val consumerGroupId = props.internalIngestionGroupId
  private val topic = props.internalIngestionTopic
  private val consumerSettings: ConsumerSettings[String, String] =
    ConsumerSettings(props.kafkaConsumerConfig, new StringDeserializer, new StringDeserializer)
      .withBootstrapServers(props.bootstrapServers)
      .withGroupId(consumerGroupId)
      .withProperties(
        new SslContextApplier(sslContext)
          .apply(Collections.emptyMap())
          .asScala
          .map { case (k, v) => (k, v.toString) }
          .toMap
      )

  private def retry[A](future: () => Future[A], remainingAttempts: Int): Future[A] =
    future().recoverWith {
      case e if remainingAttempts > 0 =>
        logger.warn(s"Failed to process event. Retries left $remainingAttempts", e)
        retry(future, remainingAttempts - 1)
      case e =>
        logger.warn(s"Failed to process event. Not retries left", e)
        Future.failed(e)

    }

  def readSchemaSync(handler: SchemaSyncEvent => Future[Unit]): Future[Done] = {
    RestartSource.withBackoff(consumerRestartSettings) { () =>
        logger.info(s"Starting task listener for consumer group $consumerGroupId and topic: $topic")
        Consumer
          .committableSource(consumerSettings, Subscriptions.topics(topic))
          .mapAsync(1) { cm =>
            val eff = Try(JsonParser(cm.record.value())).flatMap(SchemaSyncEventCodec.decode) match {
              case Success(Some(v)) => retry(() => handler(v), 5)
              case Success(None) => Future.unit
              case Failure(e) => Future.failed(e)
            }
            eff.recoverWith { case e =>
              logger.error(s"Failed to process event. Skipping. ${cm.record.value()}", e)
              Future.unit
            }.map(_ => cm.committableOffset)

          }
          .viaMat(Committer.flow(CommitterSettings(system)))(Keep.right)
          .log(s"Consumer [group: $consumerGroupId, topic: $topic]").addAttributes(
            Attributes.logLevels(onElement = Attributes.LogLevels.Off, onFinish = Attributes.LogLevels.Error, onFailure = Attributes.LogLevels.Error)
          )
      }
      .run()

  }

}

