package com.nexla.schemasync.kafka

import com.nexla.common.NexlaMessage
import com.nexla.schemasync.kafka.codecs.SchemaSyncEventCodec
import com.nexla.schemasync.model.SchemaSyncEvent
import com.nexla.schemasync.model.SchemaSyncEvent.SamplesUpdate
import com.typesafe.scalalogging.StrictLogging

import java.nio.charset.StandardCharsets

class MessageSplitter(maxSizeBytes: Int) extends StrictLogging {
  private val margin = math.max(1000, (maxSizeBytes * 0.05).toInt) // 5% margin size
  private val maxSizeBytesWithMargin = maxSizeBytes - margin
  private def estimateSize(event: SchemaSyncEvent.SamplesUpdate): Int =
    SchemaSyncEventCodec.encode(event).compactPrint.getBytes(StandardCharsets.UTF_8).length

  private def splitSamples(sourceId: Int, datasetId: Int, samples: List[NexlaMessage]): List[List[NexlaMessage]] = {
    val batches = scala.collection.mutable.ListBuffer[List[NexlaMessage]]()
    var currentBatch = List.empty[NexlaMessage]

    for (sample <- samples) {
      val testBatch = currentBatch :+ sample
      val testEvent = SchemaSyncEvent.SamplesUpdate(sourceId, datasetId, testBatch)
      if (estimateSize(testEvent) > maxSizeBytesWithMargin && currentBatch.nonEmpty) {
        batches.append(currentBatch)
        currentBatch = List(sample)
      } else {
        currentBatch = testBatch
      }
    }
    if (currentBatch.nonEmpty) batches.append(currentBatch)

    batches.toList


  }

  def splitBySizeConstraint(sourceId: Int, datasetId: Int, samples: List[NexlaMessage]): List[SamplesUpdate] = {
    val event = SamplesUpdate(sourceId, datasetId, samples)
    val estimatedSize = estimateSize(event)
    if (estimatedSize <= maxSizeBytesWithMargin) {
      List(event)
    } else {
      logger.info(s"Estimated size $estimatedSize is greater than configured max size $maxSizeBytesWithMargin. Splitting the message.")
      splitSamples(sourceId, datasetId, samples).map(batch => SamplesUpdate(sourceId, datasetId, batch))
    }
  }
}
