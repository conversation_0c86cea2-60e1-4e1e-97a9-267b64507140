package com.nexla.schemasync.kafka



import com.nexla.http.AppProps
import com.nexla.schemasync.kafka.codecs.SchemaSyncEventCodec
import com.nexla.schemasync.model.SchemaSyncEvent
import org.apache.kafka.clients.producer.{KafkaProducer, ProducerRecord, RecordMetadata}

import scala.concurrent.{Future, Promise}

class SchemaSyncKafkaProducer(props: AppProps, producer: KafkaProducer[String, String]) {
  private val topic = props.internalIngestionTopic

  def sendSchemaUpdateToKafka(event: SchemaSyncEvent): Future[Unit] = {
    val key = event match {
      case e: SchemaSyncEvent.SchemaUpdate => s"${e.sourceId}-${e.datasetId}"
      case e: SchemaSyncEvent.SamplesUpdate => s"${e.sourceId}-${e.datasetId}"
    }
    val record = new ProducerRecord[String, String](topic, key, SchemaSyncEventCodec.encode(event).compactPrint)
    val p: Promise[Unit] = Promise[Unit]()
    producer.send(record, (_: RecordMetadata, e: Exception) => {
      if (e != null) {
        p.failure(e)
        ()
      } else {
        p.success(())
        ()
      }
    })
    p.future
  }


}

