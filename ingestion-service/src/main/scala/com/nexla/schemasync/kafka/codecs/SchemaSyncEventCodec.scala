package com.nexla.schemasync.kafka.codecs

import cats.implicits.toTraverseOps
import com.nexla.schemasync.model.SchemaSyncEvent
import com.nexla.schemasync.model.SchemaSyncEvent.{SamplesUpdate, SchemaUpdate}
import com.nexla.utils.TappingOps.tryOps
import com.typesafe.scalalogging.StrictLogging
import spray.json.DefaultJsonProtocol._
import spray.json.{JsArray, JsNumber, JsObject, JsString, JsValue}

import scala.util.{Success, Try}


object SchemaSyncEventCodec extends StrictLogging {

  def encode(event: SchemaSyncEvent): JsObject = event match {
    case SchemaUpdate(sourceId, datasetId, schema) => JsObject(
      "type" -> JsString("schemaUpdate"),
      "sourceId" -> Js<PERSON><PERSON>ber(sourceId),
      "datasetId" -> <PERSON>s<PERSON><PERSON><PERSON>(datasetId),
      "schema" -> NexlaSchemaCodec.encode(schema),
    )
    case SamplesUpdate(sourceId, datasetId, samples) => JsObject(
      "type" -> JsString("samplesUpdate"),
      "sourceId" -> JsNumber(sourceId),
      "datasetId" -> JsNumber(datasetId),
      "samples" -> JsArray(samples.map(NexlaMessageCodec.encode).toVector),
    )

  }

  def decode(jsValue: JsValue): Try[Option[SchemaSyncEvent]] = {
    val fields = jsValue.asJsObject.fields
    fields.get("type") match {
      case Some(JsString("schemaUpdate")) =>
        for {
          datasetId <- Try(fields("datasetId").convertTo[Int]).tapError(logger.error(s"Unable to extract dataset id from message ${jsValue.compactPrint}", _))
          sourceId <- Try(fields("sourceId").convertTo[Int]).tapError(logger.error(s"Unable to extract source id from message ${jsValue.compactPrint}", _))
          schema <- Try(fields("schema")).flatMap(NexlaSchemaCodec.decode).tapError(logger.error(s"Unable to extract schema from error message ${jsValue.compactPrint}", _))
        } yield Some(SchemaUpdate(sourceId, datasetId, schema))
      case Some(JsString("samplesUpdate")) =>
        for {
          datasetId <- Try(fields("datasetId").convertTo[Int]).tapError(logger.error(s"Unable to extract dataset id from message ${jsValue.compactPrint}", _))
          sourceId <- Try(fields("sourceId").convertTo[Int]).tapError(logger.error(s"Unable to extract source id from message ${jsValue.compactPrint}", _))
          samples <- Try(fields("samples").convertTo[JsArray].elements.toList).flatMap(_.traverse(NexlaMessageCodec.decode)).tapError(logger.error(s"Unable to extract samples from message ${jsValue.compactPrint}", _))
        } yield Some(SamplesUpdate(sourceId, datasetId, samples))
      case _ => Success(None)
    }
  }
}