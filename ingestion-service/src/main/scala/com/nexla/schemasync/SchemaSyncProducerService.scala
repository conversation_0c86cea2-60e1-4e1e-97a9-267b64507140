package com.nexla.schemasync

import cats.implicits.toTraverseOps
import com.nexla.admin.client.NexlaSchema
import com.nexla.common.{NexlaMessage, NexlaSslContext}
import com.nexla.http.AppProps
import com.nexla.http.api.IngestionCommons
import com.nexla.schemasync.kafka.{MessageSplitter, SchemaSyncKafkaProducer}
import com.nexla.schemasync.model.SchemaSyncEvent

import scala.concurrent.{ExecutionContext, Future}

class SchemaSyncProducerService(props: AppProps, sslContext: NexlaSslContext)(implicit ec: ExecutionContext) {
  private val kafkaProducer = IngestionCommons.kafkaProducer(props, sslContext)
  private val schemaSyncKafkaProducer = new SchemaSyncKafkaProducer(props, kafkaProducer)
  private val samplesSplitter = new MessageSplitter(props.internalIngestionMaxKafkaEventSizeBytes)

  def updateSchema(sourceId: Int, dataSetId: Int, schema: NexlaSchema): Future[Unit] = {
    val event = SchemaSyncEvent.SchemaUpdate(sourceId, dataSetId, schema)
    schemaSyncKafkaProducer.sendSchemaUpdateToKafka(event)
  }

  def sendSample(sourceId: Int, datasetId: Int, samples: List[NexlaMessage]): Future[Unit] = {
    samplesSplitter.splitBySizeConstraint(sourceId, datasetId, samples)
      .traverse(schemaSyncKafkaProducer.sendSchemaUpdateToKafka)
      .map(_ => ())
  }
}
