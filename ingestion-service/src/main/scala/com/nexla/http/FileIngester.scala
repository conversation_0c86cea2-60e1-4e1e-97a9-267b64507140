package com.nexla.http

import akka.actor.ActorSystem
import akka.kafka.ProducerSettings
import akka.kafka.scaladsl.Producer
import akka.stream.scaladsl.Source
import com.bazaarvoice.jolt.JsonUtils
import com.google.common.collect.Maps
import com.nexla.admin.client.config.EnrichedConfig.EnrichSourceParams
import com.nexla.admin.client.{AdminApiClient, DataSource}
import com.nexla.admin.config.ConfigUtils.{enrichWithCredentialsStore, enrichWithDataCredentials}
import com.nexla.common.ListingResourceType.FILE
import com.nexla.common.NexlaConstants._
import com.nexla.common.ResourceType.SOURCE
import com.nexla.common._
import com.nexla.common.datetime.DateTimeUtils.nowUTC
import com.nexla.common.logging.{NexlaLog<PERSON><PERSON>, NexlaLogger}
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils
import com.nexla.connector.config.FlowType
import com.nexla.connector.config.file.FileSourceConnectorConfig
import com.nexla.connector.file.source._
import com.nexla.control.crontask.messages.listing.ListSourceTask
import com.nexla.file.service.LocalConnectorService
import com.nexla.http.api.SchemaDetectionCache
import com.nexla.kafka.control.listener.conf.SslContextApplier
import com.nexla.listing.client.FileVaultClient
import com.nexla.sc.config.ConfigEnricher
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import one.util.streamex.StreamEx
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.io.FileUtils
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.StringSerializer

import java.io.File
import java.nio.file.Files
import java.util
import java.util.Optional.empty
import java.util.{Collections, Optional, UUID}
import scala.collection.JavaConverters._
import scala.compat.java8.OptionConverters._
import scala.concurrent.Future

class FileIngester(adminApiClient: AdminApiClient,
                   schemaDetectionCache: SchemaDetectionCache,
                   fileVaultClient: FileVaultClient,
                   nexlaMessageProducer: NexlaMessageProducer,
                   props: AppProps)
                  (implicit ioBoundSystem: ActorSystem,
                   ec: scala.concurrent.ExecutionContext,
                    sslContext: NexlaSslContext)
  extends StrictNexlaLogging
    with WithLogging
    with ConfigEnricher {

  val lcs = new LocalConnectorService()
  val producerConfig = props.config.getConfig("akka.kafka.producer")
  val producerSettings: akka.kafka.ProducerSettings[String, String] =
    ProducerSettings(producerConfig, new StringSerializer(), new StringSerializer())
      .withBootstrapServers(props.bootstrapServers)
      .withProperties(props.producerProps)
      .withProperties(
        new SslContextApplier(sslContext)
          .apply(Collections.emptyMap())
          .asScala
          .map { case (k, v) => (k, v.toString)}
          .toMap
      )

  val messageGrouper = new NoopMessageGrouper
  val offsetWriter = new NoopOffsetWriter

  val enrichSourceParams = new EnrichSourceParams(
    props.zookeeperConnect,
    props.bootstrapServers,
    props.vault.map(_.host).asJava,
    props.vault.map(_.token).asJava,
    props.dataset.partitions,
    props.dataset.replication,
    props.config.getStore.getType,
    props.credentialsAwsRegion.asJava,
    props.credentialsAwsAccessKey.asJava,
    props.credentialsAwsSecretKey.asJava,
    props.credentialsAwsRoleArn.asJava,
    props.credentialsAwsIdentityTokenFile.asJava,
    props.secretNames.asJava
  )

  def ingest(fileTmp: File, fileName: String, sourceId: Int, runId: Long): Future[Unit] = {

    val tmpDir = Files.createTempDirectory("ingest")
    val file = new File(tmpDir.toFile, fileName)

    FileUtils.copyFile(fileTmp, file)

    val fileSourceConfig = new util.HashMap[String, AnyRef](adminApiClient.getDataSource(sourceId).get().getSourceConfig)
    fileSourceConfig.put(PATH, file.getParent)

    val fileDataSource = new DataSource()
    fileDataSource.setId(sourceId);
    fileDataSource.setSourceConfig(fileSourceConfig);
    fileDataSource.setConnectionType(ConnectionType.FILE_UPLOAD);

    val enrichedConfigConfig = fullDataSourceConfig(props.config, fileDataSource, enrichSourceParams, FileSourceConnectorConfig.configDef())
    val resultConfig: util.Map[String, String] = enrichWithCredentialsStore(enrichedConfigConfig, FileSourceConnectorConfig.configDef)
    enrichWithDataCredentials(adminApiClient, resultConfig)

    val fileConfig = new FileSourceConnectorConfig(resultConfig)
    val fileSourceContext = new FileSourceContext(fileConfig, empty(), runId)
    val notificationSender = new FileSourceNotificationSender(nexlaMessageProducer, FlowType.STREAMING, fileSourceContext, logger.underlying)

    val nexlaFile = new NexlaFile(fileName, 0L, null, null, null, null, FILE)
    val transportFile = new TransportFile(nexlaFile, 0L, false);

    if (props.fileUploadVault) {
      triggerSchemaDetectionAndListing(fileName, sourceId, runId, file, fileSourceContext, notificationSender, transportFile)
      Future.successful(FileUtils.deleteDirectory(tmpDir.toFile))
    } else {
      for {
        _ <- readDirectly(sourceId, runId, file, fileSourceContext, notificationSender, transportFile)
      } yield {
        FileUtils.deleteDirectory(tmpDir.toFile)
      }
    }
  }

  private def triggerSchemaDetectionAndListing(fileName: String,
                                               sourceId: Int,
                                               runId: Long,
                                               file: File,
                                               fileSourceContext: FileSourceContext,
                                               notificationSender: FileSourceNotificationSender,
                                               transportFile: TransportFile) = {

    val nexlaLogger = new NexlaLogger(logger.underlying, new NexlaLogKey(SOURCE, sourceId, Optional.empty()));

    val utils = new SchemaDetectionUtils(
      sourceId,
      adminApiClient,
      nexlaMessageProducer,
      empty(),
      0,
      0,
      0,
      false)

    val fileReader = new TransportFileReader(
      notificationSender,
      lcs,
      utils,
      offsetWriter,
      messageGrouper,
      nexlaLogger,
      empty(),
      empty(),
      empty(),
      fileSourceContext,
      empty(),
      false
    )
    fileReader.addFiles(List(transportFile).asJava)
    logger.info("Before read next Batch")
    fileReader.readNextBatch(new NoopFileReadResult[NexlaMessageFile], adminApiClient)
    logger.info("After read next Batch, calling file Vault")
    fileVaultClient.putFile(sourceId, file);

    val task = new ListSourceTask(UUID.randomUUID.toString, nowUTC.getMillis, sourceId, true,
      true, true, Optional.of(runId), Maps.newHashMap())

    nexlaMessageProducer.sendTask(task)
  }

  private def readDirectly(sourceId: Int,
                           runId: Long,
                           file: File,
                           fileSourceContext: FileSourceContext,
                           notificationSender: FileSourceNotificationSender,
                           transportFile: TransportFile) = {

    val nexlaLogger = new NexlaLogger(logger.underlying, new NexlaLogKey(SOURCE, sourceId, Optional.empty()));

    val kafkaFileMessageReader = new BaseKafkaFileMessageReader(
      offsetWriter,
      messageGrouper,
      fileSourceContext,
      nexlaLogger,
      lcs,
      notificationSender
    )

    val fileReader = new TransportFileReader(
      notificationSender,
      lcs,
      schemaDetectionCache.getSchemaDetection(sourceId),
      offsetWriter,
      messageGrouper,
      nexlaLogger,
      empty(),
      empty(),
      empty(),
      fileSourceContext,
      empty(),
      false
    )
    fileReader.addFiles(List(transportFile).asJava)

    Source.fromIterator(
      () => StreamEx.iterate(
        fileReader.readNextBatch(kafkaFileMessageReader, adminApiClient),
        (t: ReadBatchResult[NexlaMessageFile]) => !CollectionUtils.isEmpty(t.messages),
        (_: ReadBatchResult[NexlaMessageFile]) => fileReader.readNextBatch(kafkaFileMessageReader, adminApiClient)
      ).iterator().asScala
    )
      .mapConcat(x => x.messages.asScala.toList)
      .map(x => new ProducerRecord[String, String](
        NexlaNamingUtils.nameDataSetTopic(x.dataSetId),
        null,
        JsonUtils.toJsonString(x.message))
      )
      .runWith(Producer.plainSink(producerSettings))
      .map(_ => ())
  }
}
