package com.nexla.http

import com.nexla.connector.config.vault.NexlaAppConfig
import com.nexla.sc.config._

class AppProps(val config: NexlaAppConfig)
  extends NexlaCreds
    with KafkaProperties
    with Zookeeper
    with Vault
    with SecretNames
    with AwsCredentials
    with NexlaClusterApplication
    with NexlaSslConfig
    with NexlaAdminApi
    with NexlaEndpoints
    with Dataset
    with StatsD
    with MetricsTopic
    with TelemetryConfig
    with DataDog
    with Prometheus
    with LicenseProperties {

  val flushPeriod = config.getInt("ingestion.flush.period.seconds")
  val batchSize = config.getInt("ingestion.batch.size")
  val retentionMs = config.getInt("retention.ms")

  val fileUploadVault = config.getBoolean("file.upload.vault")

  val maxRecordSize = config.getLong("max.record.size")
  val internalMode = config.getOptBoolean("internal.mode").getOrElse(false)

  val internalIngestionGroupId = config.getOptString("kafka.consumer.group").getOrElse("internal-ingestion")
  val internalIngestionTopic = config.getOptString("kafka.consumer.internal-ingestion.topic").getOrElse("internal-ingestion-schema-updates")
  val internalIngestionMaxKafkaEventSizeBytes = config.getOptInt("internal.ingestion.max.kafka.event.size.bytes").getOrElse(1024 * 1024)
}
