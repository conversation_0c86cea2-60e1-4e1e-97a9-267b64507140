package com.nexla.http

import akka.stream.Materializer
import com.nexla.admin.client.AdminApiClient
import com.nexla.common._
import com.nexla.common.metrics.HealthMetricAggregator
import com.nexla.control.message.ControlMessage
import com.nexla.control.message.ControlResourceType._
import com.nexla.http.api.IngestionService
import com.nexla.kafka.{CtrlMessageListener, CtrlTopics}
import com.nexla.sc.api.GroupId
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}

import scala.concurrent.{ExecutionContext, Future}

class IngestionCtrlListener(ingestionService: IngestionService,
                            ctrlCommon: CtrlTopics,
                            adminApi: AdminApiClient,
                            metricAggregator: HealthMetricAggregator)
                           (implicit ec: ExecutionContext,
                            mat: Materializer,
                            sslContext: NexlaSslContext)
  extends CtrlMessageListener(ctrlCommon, AppType.INGESTION_SERVICE, 1, metricAggregator)
    with StrictNexlaLogging
    with WithLogging {

  // Do not provide the third parameter in order not to change group ID of existing consumer in production
  override def getGroupId: GroupId = ctrlCommon.groupIdName(singleGroupId = false, AppType.INGESTION_SERVICE, None)

  def doProcess(m: ControlMessage, origin: AppType): Future[Unit] = Future {

    m.getResourceType match {
      case SOURCE if origin == AppType.CTRL_HTTP => ingestionService.notifyDataSourceChanged(m.getResourceId)
      case DATASET => ingestionService.notifyDataSetChanged(m.getResourceId)
      case _ =>
    }
  }

}