package com.nexla.http.api

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import akka.http.scaladsl.model.StatusCodes.{BadRequest, InternalServerError, OK}
import akka.http.scaladsl.model.{HttpRequest, StatusCode, StatusCodes, Uri}
import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.server.directives.FileInfo
import akka.http.scaladsl.server.{Directive1, Directives, Route}

import scala.jdk.CollectionConverters._
import scala.util.{Failure, Success, Try}
import com.bazaarvoice.jolt.JsonUtils
import com.google.common.cache.{Cache, CacheBuilder}
import com.google.common.collect.Lists
import com.nexla.admin.client.{AdminApiClient, AuthResource}
import com.nexla.common.datetime.DateTimeUtils.nowUTC
import com.nexla.common.license.NexlaLicenseManager
import com.nexla.common.notify.NexlaNotificationEvent
import com.nexla.common.notify.context.NexlaNotificationEventContext
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.common.{NexlaHashUtils, NotificationEventType, ResourceType}
import com.nexla.http.FileIngester
import com.nexla.service.IngestMessageParser.{IngestMessageData, ParsingFailure, ParsingSuccess}
import com.nexla.json.XML
import com.nexla.sc.api.common.ApiResponse
import com.nexla.sc.util._
import com.nexla.service.{IngestMessageParser, InternalIngestionServiceFactory}
import fr.davit.akka.http.metrics.core.scaladsl.server.HttpMetricsDirectives.pathPrefixLabeled
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.web.client.HttpClientErrorException
import spray.json.{DefaultJsonProtocol, JsArray, JsNumber, JsObject, JsString}

import java.io.{File, StringReader}
import java.nio.file.{Files, Path}
import java.util
import java.util.Collections
import java.util.concurrent.{ConcurrentHashMap, TimeUnit}
import scala.compat.java8.OptionConverters.RichOptionalGeneric
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal

class IngestionApiHandler(adminApiClient: AdminApiClient,
                          ingestionService: IngestionService,
                          internalIngestionServiceFactory: InternalIngestionServiceFactory,
                          nexlaMessageProducer: NexlaMessageProducer,
                          fileIngester: FileIngester,
                          licenseManager: NexlaLicenseManager)
                         (implicit ec: ExecutionContext)
  extends Directives
    with SprayJsonSupport
    with DefaultJsonProtocol
    with StrictNexlaLogging
    with WithLogging {

  val dataFormatted =
    ((post | get)
      & pathPrefixLabeled("data" / "formatted" / IntNumber / IntNumber.?, "data/formatted/:sourceId")
      & end
      & entity(as[String])
      & extractRequest
      & parameters("api_key".?)
      & optionalHeaderValueByName("Authorization")
      & parameters("force_schema_detection".as[Boolean] ? false)
      & paramsToIngest
      & headersToIngest) {
      case (sourceId, _, input, request, optApiKey, optAuthHead, forceSchemaDetection, params, headers) =>
        val authRes = authResourceAuth(request.uri, optApiKey, optAuthHead)
        if (authRes || authSignature(request, input)) {
          licenseManager.getValidationResult().asScala match {
            case Some(msg) => complete(StatusCodes.Forbidden -> msg)
            case _ =>
              onComplete {
                Future {
                  val messagesAsString = Some(request.entity.contentType.toString())
                    .filter(_.toLowerCase.contains("xml"))
                    .map(_ => XML.toJSONObject(new StringReader(input), true).toString)
                    .getOrElse(input)

                  val messages = Some(messagesAsString)
                    .filter(isRecordSizeValid(_, ingestionService.maxRecordSize))
                    .map(JsonUtils.stringToType(_, classOf[util.List[IngestionMessage]]))
                    .get

                  ingestionService.ingest(
                    headers.asJava, params.asJava, messages, sourceId, forceSchemaDetection
                  )
                }
              } {
                case Success(response) => complete(OK -> Map("datasetId" -> response.datasetId, "processed" -> response.processed))
                case Failure(e) => {
                  val errorType = processException(sourceId, e, input)
                  complete(errorType -> Map("error" -> e.getMessage))
                }
              }
          }
        } else {
          complete(StatusCodes.Forbidden -> "This resource requires authentication")
        }
    }

  val file = withoutSizeLimit {
    (post
      & pathPrefixLabeled("file" / IntNumber, "file/:sourceId")
      & storeUploadedFile("data", tempDestination)) {
      case (sourceId, meta, file) => {
        licenseManager.getValidationResult().asScala match {
          case Some(msg) => complete(StatusCodes.Forbidden -> msg)
          case _ =>
            Future {
              fileIngester
                .ingest(file, meta.fileName, sourceId, ingestionService.generateRunId())
            } recoverWith { case NonFatal(e) =>
              logger.error("File ingestion failed", e)
              Future.successful(())
            } onComplete { _ =>
              FileUtils.deleteDirectory(file.getParentFile)
            }
            complete(OK -> ApiResponse("Success"))
        }
      }
    }
  }

  val data =
    ((post | get)
      & pathPrefixLabeled("data" / IntNumber / IntNumber.?, "data/:sourceId/")
      & end
      & entity(as[String])
      & extractRequest
      & parameters("api_key".?)
      & optionalHeaderValueByName("Authorization")
      & parameters("force_schema_detection".as[Boolean] ? false)
      & paramsToIngest
      & headersToIngest) {
      case (sourceId, _, input, request, optApiKey, optAuthHead, forceSchemaDetection, params, headers) =>

        val authRes = authResourceAuth(request.uri, optApiKey, optAuthHead)
        if (authRes || authSignature(request, input)) {
          licenseManager.getValidationResult().asScala match {
            case Some(msg) => complete(StatusCodes.Forbidden -> msg)
            case _ =>
              onComplete {
                Future {

                  val messagesAsString = Some(request.entity.contentType.toString())
                    .filter(_.toLowerCase.contains("xml"))
                    .map(_ => XML.toJSONObject(new StringReader(input), true).toString)
                    .getOrElse(input)

                  val messages = Some(messagesAsString)
                    .filter(isRecordSizeValid(_, ingestionService.maxRecordSize))
                    .map(JsonUtils.jsonToObject(_))
                    .get

                  messages match {
                    case map: util.LinkedHashMap[String, AnyRef] =>
                      val maps = Lists.newArrayList(map)
                      ingestionService.ingestArbitrary(headers.asJava, params.asJava, maps, sourceId, forceSchemaDetection);
                    case _: util.List[_] =>
                      val maps = messages.asInstanceOf[util.List[util.LinkedHashMap[String, AnyRef]]]
                      ingestionService.ingestArbitrary(headers.asJava, params.asJava, maps, sourceId, forceSchemaDetection);
                    case _ => throw new IllegalArgumentException()
                  }
                }
              } {
                case Success(response) => complete(OK -> Map("datasetId" -> response.datasetId, "processed" -> response.processed))
                case Failure(e) => {
                  val errorType = processException(sourceId, e, input)
                  complete(errorType -> Map("error" -> e.getMessage))
                }
              }
          }
        } else {
          complete(StatusCodes.Forbidden -> "This resource requires authentication")
        }
    }

  val internalData: Route =
    (post
      & pathPrefixLabeled("internal" / "data" / IntNumber, "internal/data/:sourceId")
      & end
      & entity(as[String])
      & extractRequest
      & parameter("run_id")
      ) {
      case (sourceId, input, _, runIdStr) =>
        val runId = runIdStr.toLong
        val signature = s"$sourceId-$runIdStr-${trimInput(input)}"

        onComplete {
          logger.info(s"Received internal data ingestion request for source {} and runId {}", sourceId, runId)
          for {
            forceSchemaDetection <- Future.fromTry(IngestMessageParser.parseForceSchemaDetection(input))
            context <- internalIngestionServiceFactory.fetchOrCreateDatasetWithRetry(sourceId, runId, forceSchemaDetection)
            ingestMessageData <- parseRequestInput(context, input)
            internalIngestionService <- internalIngestionServiceFactory.forSource(context)
            result <- internalIngestionService.ingestInternal(ingestMessageData.messages)
            _ = logger.info("Ingested {} messages to dataset {} for source {} with run id {}", result.processed, result.datasetId, sourceId, runId)
            _ = if (ingestMessageData.parsingFailures.nonEmpty) logger.info("{} records failed to be parsed for source {} and runId {}", ingestMessageData.parsingFailures.size, sourceId, runId)
          } yield (result, ingestMessageData.parsingFailures)
        } {
          case Success((response, failures)) =>
            val failureSummary = JsArray(failures.map(f => JsObject("index" -> JsNumber(f.index), "err" -> JsString(f.err.getMessage))).toVector)
            val jsonResponse = JsObject(
              "datasetId" -> JsNumber(response.datasetId),
              "processed" -> JsNumber(response.processed),
              "failedToParse" -> failureSummary
            )

            logger.info(s"Response success for $signature: $jsonResponse")
            complete(OK -> jsonResponse)

          case Failure(e) =>
            val errorType = processException(sourceId, e, input)
            val errorResponse = Map("error" -> e.getMessage)

            logger.error(s"Response failure for $signature: errorType=$errorType, error=$errorResponse")
            complete(errorType -> errorResponse)
        }
    }

  private def parseRequestInput(ctx: InternalIngestionServiceFactory.Context, input: String): Future[IngestMessageData] =
    Future.fromTry(IngestMessageParser.parseInput(ctx.sourceId, ctx.topicName, ctx.datasetId, ctx.runId, input)).flatMap { ingestMessageData =>
      if (ingestMessageData.messages.nonEmpty) {
        Future.successful(ingestMessageData)
      } else if (ingestMessageData.parsingFailures.nonEmpty) {
        Future.failed(new IllegalArgumentException("Data could not be parsed"))
      } else {
        Future.failed(new IllegalArgumentException("Data is empty"))
      }
    }

  private def isRecordSizeValid(record: String, maxRecordSize: Long): Boolean = {
    val size = record.getBytes().length
    val isValidSize = size <= maxRecordSize

    if (!isValidSize) {
      val errorMsg = "Filtering out message of size " + size + " Max size = " + maxRecordSize
      logger.error(errorMsg)
      throw new IllegalArgumentException(errorMsg)
    }
    isValidSize
  }

  private def trimInput(input: String, maxChars: Int = 150): String = {
    val chunk = maxChars / 2
    if (input.length > maxChars) s"${input.take(chunk)} ... ${input.takeRight(chunk)}"
    else input
  }

  private def processException(sourceId: Int, e: Throwable, input: String) = {
    val errorMessage = s"Error while ingesting: '${e.getMessage}' in payload '${trimInput(input)}'"
    logger.error(errorMessage, e)
    val errorType: StatusCode = if (e.isInstanceOf[IllegalArgumentException] || e.isInstanceOf[SourceNotActiveException]) BadRequest else InternalServerError
    val event = createErrorNotificationEvent(sourceId, errorType.intValue, errorMessage)
    nexlaMessageProducer.publishNotification(event)
    errorType
  }

  def createErrorNotificationEvent(sourceId: Int, code: Int, message: String): NexlaNotificationEvent = {
    val notificationEvent = new NexlaNotificationEvent()
    notificationEvent.setEventSource("source")
    notificationEvent.setEventTimeMillis(nowUTC().getMillis)
    notificationEvent.setResourceId(sourceId)
    notificationEvent.setResourceType(ResourceType.SOURCE)
    notificationEvent.setEventType(NotificationEventType.ERROR)
    val nexlaNotificationEventContext = new NexlaNotificationEventContext()
    nexlaNotificationEventContext.setMessage(message)
    notificationEvent.setContext(nexlaNotificationEventContext)
    notificationEvent
  }

  private def paramsToIngest(): Directive1[Map[String, AnyRef]] = {
    val excluding = Set("force_schema_detection", "include_headers", "include_url_params", "api_key")
    parameter("include_url_params".as[Boolean] ? false)
      .flatMap(include =>
        parameterMap.flatMap(params =>
          provide(params.filter(_ => include).filterKeys(!excluding.contains(_)).toMap)
        )
      )
  }

  private def headersToIngest(): Directive1[Map[String, AnyRef]] =
    parameter("include_headers".as[Boolean] ? false)
      .flatMap(include =>
        extract(
          _.request.headers
            .filter(_ => include)
            .filter(_.isInstanceOf[RawHeader])
            .filter(h => !Headers.isStandard(h.name()))
            .map(h => h.name() -> h.value())
            .toMap
        )
      )

  val HASH_REQUEST_PARAM = "hash_request_param"
  val HASH_SECRET_KEY = "hash_secret_key"
  val HASH_ALGORITHM = "hash_algorithm"
  val API_KEY = "api_key"
  val ACCESS_MODE = "read"

  private val authKeyCache: ConcurrentHashMap[String, Cache[AuthResource, AuthResource]] =
    new ConcurrentHashMap[String, Cache[AuthResource, AuthResource]]

  def authIngestionNoBody(applicationRoutesFunc: => Route): Route = {
    val apiKeyParam = parameters("api_key".?)
    val authHeader = optionalHeaderValueByName("Authorization")
    (extractRequest & apiKeyParam & authHeader) { case (request, optApiKey, optAuthHead) =>
      val authRes = authResourceAuth(request.uri, optApiKey, optAuthHead)
      if (authRes) {
        applicationRoutesFunc
      } else {
        complete(StatusCodes.Forbidden -> "This resource requires authentication")
      }
    }
  }

  def tempDestination(fileInfo: FileInfo): File = {
    val tempDir: Path = Files.createTempDirectory("ingestion-app")
    new File(tempDir.toFile, fileInfo.fileName);
  }

  def getCache(header: String) =
    authKeyCache
      .computeIfAbsent(
        header,
        _ => CacheBuilder
          .newBuilder
          .expireAfterWrite(30, TimeUnit.MINUTES).maximumSize(10000).build
          .asInstanceOf[Cache[AuthResource, AuthResource]])

  def authResourceAuth(uri: Uri, optApiKey: Option[String], optAuthHead: Option[String]) = {
    val authResource = {
      val split = uri.path.toString().split("/")
      val id = Integer.valueOf(split(split.length - 1))
      val dataSetAndSource = split.length >= 2 && StringUtils.isNumeric(split(split.length - 2))
      val resourceType = if (dataSetAndSource) ResourceType.DATASET else ResourceType.SOURCE
      new AuthResource(resourceType, id, ACCESS_MODE)
    }

    val authHeader = optAuthHead
      .map(x => {
        if (x.contains("Bearer")) {
          x
        } else {
          s"Basic ${x.split(" ").last}"
        }
      })

    val headerOpt = optApiKey
      .map(x => s"Basic $x")
      .orElse(authHeader)

    headerOpt.exists { header =>
      val cacheForKey = getCache(header)
      val alreadyAuthenticated = cacheForKey.getIfPresent(authResource) != null
      if (alreadyAuthenticated) {
        true
      } else {
        Try(adminApiClient.resourceAuth(header, authResource))
          .map { res =>
            if (res.is2xxSuccessful()) cacheForKey.put(authResource, authResource)
            res.is2xxSuccessful()
          }
          .recoverWith {
            case _: HttpClientErrorException => Success(false)
            case e => {
              logger.error("AdminApi auth error", e)
              Success(false)
            }
          }
          .get
      }
    }
  }

  def authSignature(request: HttpRequest, body: String): Boolean = {
    val uri: Uri = request.uri
    val headers: Map[String, String] = request.headers.map(x => x.name() -> x.value()).toMap
    val params: Map[String, String] = request.uri.query().toList.toMap

    val segments = uri.path.toString().split("/")
    val datasourceId = if (segments.length > 2) {
      Try(Integer.parseInt(segments(2))).toOption
    } else {
      None
    }

    datasourceId
      .flatMap(dsId => adminApiClient.getDataSource(dsId).asScala)
      .exists(dataSource => {
        val hashRequestParam = dataSource.getStringConfigParam(HASH_REQUEST_PARAM)
        val hashSecretKey = dataSource.getStringConfigParam(HASH_SECRET_KEY)
        val hashAlgorithm = dataSource.getStringConfigParam(HASH_ALGORITHM)
        if (hashRequestParam != null && hashSecretKey != null) {
          val signature = (params ++ headers).get(hashRequestParam)
          signature.exists(requestSignature => {
            val calculatedHash: String = NexlaHashUtils.hashAndBase64Encode(body, hashSecretKey, "utf-8", hashAlgorithm)
            logger.info("data=" + body)
            logger.info("requestSignature=" + requestSignature)
            logger.info("calculatedHash=" + calculatedHash)
            calculatedHash == requestSignature
          })
        } else {
          false
        }
      })
  }

}
