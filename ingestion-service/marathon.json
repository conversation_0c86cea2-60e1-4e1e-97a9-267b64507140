{"id": "/ingestion", "instances": 1, "cpus": 0.25, "mem": 2048, "disk": 0, "gpus": 0, "fetch": [{"uri": "https://s3.amazonaws.com/mesos-config3/docker.tar.gz", "extract": true, "executable": false, "cache": false}], "backoffSeconds": 1, "backoffFactor": 1.15, "maxLaunchDelaySeconds": 3600, "container": {"type": "DOCKER", "docker": {"image": "nexla/ingestion-service", "network": "BRIDGE", "portMappings": [{"containerPort": 8080, "hostPort": 0, "servicePort": 10003, "protocol": "tcp", "name": "default", "labels": {"VIP_0": "/ingestion:8080"}}, {"containerPort": 0, "hostPort": 0, "servicePort": 10104, "protocol": "tcp"}], "privileged": false, "forcePullImage": true}}, "healthChecks": [{"gracePeriodSeconds": 300, "intervalSeconds": 60, "timeoutSeconds": 20, "maxConsecutiveFailures": 3, "portIndex": 0, "path": "/actuator/info", "protocol": "MESOS_HTTP", "delaySeconds": 15}], "upgradeStrategy": {"minimumHealthCapacity": 1, "maximumOverCapacity": 1}, "unreachableStrategy": {"inactiveAfterSeconds": 300, "expungeAfterSeconds": 600}, "killSelection": "YOUNGEST_FIRST", "requirePorts": true, "taskKillGracePeriodSeconds": 20, "labels": {"HAPROXY_GROUP": "external", "HAPROXY_0_VHOST": "hooks-test.nexla.com", "HAPROXY_0_BACKEND_HTTP_OPTIONS": "acl is_proxy_https hdr(X-Forwarded-Proto) https\n  redirect scheme https unless { ssl_fc } or is_proxy_https\n"}, "env": {"API_CREDENTIALS_SERVER": "https://test.nexla.com/admin-api", "SAMPLE_REPLICATION_COUNT": "3", "ZOOKEEPER_CONNECT": "zookeeper-0-server.kafka-zookeeper.autoip.dcos.thisdcos.directory:1140,zookeeper-1-server.kafka-zookeeper.autoip.dcos.thisdcos.directory:1140,zookeeper-2-server.kafka-zookeeper.autoip.dcos.thisdcos.directory:1140", "BOOTSTRAP_SERVERS": "broker.kafka.l4lb.thisdcos.directory:9092", "SAMPLE_PARTITION_COUNT": "8", "CONNECT_BOOTSTRAP_SERVERS": "broker.kafka.l4lb.thisdcos.directory:9092"}}