package com.nexla.probe.bigquery;

import com.google.api.gax.paging.Page;
import com.google.cloud.bigquery.*;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.connector.config.big_query.BigQuerySourceConnectorConfig;
import com.nexla.connector.config.file.BigQueryAuthConfig;
import com.nexla.connector.config.jdbc.DbPage;
import com.nexla.listing.client.ListingClient;
import com.nexla.test.UnitTests;
import one.util.streamex.StreamEx;
import org.junit.experimental.categories.Category;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@Category(UnitTests.class)
public class BigQueryConnectorServiceTest {

    private AdminApiClient adminApiClient;

    private ListingClient listingClient;

    private BigQueryClientService bigQueryClientService;

    private BigQueryConnectorService bigQueryConnectorService;

    @BeforeEach
    public void setup() {
        adminApiClient = mock(AdminApiClient.class);
        listingClient = mock(ListingClient.class);
        bigQueryClientService = mock(BigQueryClientService.class);

        bigQueryConnectorService = new BigQueryConnectorService(adminApiClient,
                listingClient,
                bigQueryClientService);
    }

    @Test
    public void listTablesWithPagination() {
        BigQuery bigQuery = mock(BigQuery.class);
        when(bigQueryClientService.getService(any()))
                .thenReturn(bigQuery);

        Dataset dataset = mock(Dataset.class);
        when(bigQuery.getDataset(any(DatasetId.class)))
                .thenReturn(dataset);

        Page<Table> page = mock(Page.class);
        when(dataset.list(BigQuery.TableListOption.pageSize(10), BigQuery.TableListOption.pageToken(null)))
                .thenReturn(page);

        Table table = mock(Table.class);
        when(page.getValues())
                .thenReturn(List.of(table));
        when(table.getTableId())
                .thenReturn(TableId.of("dataset", "my_table"));

        DbPage dbPage = bigQueryConnectorService.listTables(buildConnectorConfig(),
                "my_database",
                null,
                10,
                null);

        Assertions.assertEquals("my_table", ((StreamEx<String>)dbPage.getResult()).findFirst().get());
        Assertions.assertEquals(10, dbPage.getPageSize());
        Assertions.assertEquals(false, dbPage.getHasNextPage());
        Assertions.assertNull(dbPage.getOffset());

        Assertions.assertNotNull(dbPage);
    }

    private BigQuerySourceConnectorConfig buildConnectorConfig() {
        final BigQuerySourceConnectorConfig connectorConfig = mock(BigQuerySourceConnectorConfig.class);

        final BigQueryAuthConfig bigQueryAuthConfig = mock(BigQueryAuthConfig.class);
        when(connectorConfig.getAuthConfig()).thenReturn(bigQueryAuthConfig);


        return connectorConfig;
    }
}
