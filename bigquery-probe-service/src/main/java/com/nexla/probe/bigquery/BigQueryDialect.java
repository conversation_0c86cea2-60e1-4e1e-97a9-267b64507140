package com.nexla.probe.bigquery;

import com.google.cloud.bigquery.LegacySQLTypeName;
import connect.data.Date;
import connect.data.Decimal;
import connect.data.Schema;
import connect.data.Time;
import connect.data.Timestamp;
import connect.jdbc.sink.dialect.DbDialect;
import one.util.streamex.StreamEx;

import java.util.Map;

import static java.util.Optional.empty;

public class BigQueryDialect extends DbDialect {

	public BigQueryDialect() {
		super("", "", empty(), empty(), empty(), false);
	}

	@Override
	public String getDbTypeByKafka(String schemaName, Map<String, String> parameters) {
		switch (schemaName) {
			case Decimal.LOGICAL_NAME:
				return LegacySQLTypeName.NUMERIC.name();
			case Date.LOGICAL_NAME:
				return LegacySQLTypeName.DATE.name();
			case Time.LOGICAL_NAME:
				return LegacySQLTypeName.TIME.name();
			case Timestamp.LOGICAL_NAME:
				return LegacySQLTypeName.TIMESTAMP.name();
			default:
				return null;
		}
	}

	@Override
	public String getDbType(Schema.Type schemaName) {
		switch (schemaName) {
			case INT8:
			case INT16:
			case INT32:
			case INT64:
				return LegacySQLTypeName.INTEGER.name();
			case FLOAT32:
			case FLOAT64:
				return LegacySQLTypeName.FLOAT.name();
			case BOOLEAN:
				return LegacySQLTypeName.BOOLEAN.name();
			case STRING:
				return LegacySQLTypeName.STRING.name();
			case BYTES:
				return LegacySQLTypeName.BYTES.name();
			default:
				return null;
		}
	}

	@Override
	public StreamEx<String> getSqlTypes() {
		return StreamEx.of(
			LegacySQLTypeName.BOOLEAN.name(),
			LegacySQLTypeName.DATE.name(),
			LegacySQLTypeName.DATETIME.name(),
			LegacySQLTypeName.TIME.name(),
			LegacySQLTypeName.TIMESTAMP.name(),
			LegacySQLTypeName.FLOAT.name(),
			LegacySQLTypeName.INTEGER.name(),
			LegacySQLTypeName.STRING.name(),
			LegacySQLTypeName.NUMERIC.name());
	}
}

