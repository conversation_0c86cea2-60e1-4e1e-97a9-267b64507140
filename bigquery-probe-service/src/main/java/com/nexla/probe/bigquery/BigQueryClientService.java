package com.nexla.probe.bigquery;

import com.google.api.gax.rpc.FixedHeaderProvider;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.BigQueryOptions;
import com.google.cloud.bigquery.storage.v1.BigQueryWriteClient;
import com.google.cloud.bigquery.storage.v1.BigQueryWriteSettings;
import com.google.cloud.http.HttpTransportOptions;
import com.google.common.collect.Lists;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataCredentials;
import com.nexla.common.NexlaDataCredentials;
import com.nexla.connector.config.file.BigQueryAuthConfig;
import com.nexla.connector.config.rest.RestAuthConfig;
import lombok.SneakyThrows;
import org.jetbrains.annotations.NotNull;

import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;

public class BigQueryClientService {

    private static final String BIG_QUERY_SCOPE = "https://www.googleapis.com/auth/bigquery";

    private static final String STORAGE_SCOPE = "https://www.googleapis.com/auth/devstorage.read_write";

    private static final ArrayList<String> SCOPES = Lists.newArrayList(BIG_QUERY_SCOPE, STORAGE_SCOPE);

    private final AdminApiClient adminApiClient;

    private final String credentialsDecryptKey;

    public BigQueryClientService(AdminApiClient adminApiClient, String credentialsDecryptKey) {
        this.adminApiClient = adminApiClient;
        this.credentialsDecryptKey = credentialsDecryptKey;
    }

    @SneakyThrows
    public BigQuery getService(BigQueryAuthConfig authConfig) {
        DataCredentials creds = adminApiClient.getDataCredentials(authConfig.getCredsId()).get();
        Map<String, String> credsMap = NexlaDataCredentials.getCreds(credentialsDecryptKey, creds.getCredentialsEnc(), creds.getCredentialsEncIv());

        Boolean isServiceAccount = Optional.ofNullable(credsMap.get("is_service_account")).map(Boolean::valueOf).orElse(false);

        // BigQueryOptions uses HttpTransportOptions to set connect and read timeouts, and the default values are 20 seconds.
        // So, this should not change the default behavior
        int connectTimeout = Optional.ofNullable(credsMap.get("connect_timeout")).map(Integer::parseInt).orElse(20000);
        int readTimeout = Optional.ofNullable(credsMap.get("read_timeout")).map(Integer::parseInt).orElse(20000);

        if (isServiceAccount) {
            String jsonCreds = credsMap.get("json_creds");
            GoogleCredentials credentials = GoogleCredentials
                    .fromStream(new ByteArrayInputStream(jsonCreds.getBytes()))
                    .createScoped(SCOPES);

            return BigQueryOptions
                    .newBuilder()
                    .setCredentials(credentials)
                    .setHeaderProvider(getGpnUserAgent())
                    .setTransportOptions(HttpTransportOptions.newBuilder()
                            .setConnectTimeout(connectTimeout)
                            .setReadTimeout(readTimeout)
                            .build())
                    .build()
                    .getService();
        } else {
            RestAuthConfig restConfig = new RestAuthConfig(credsMap, authConfig.getCredsId());
            return BigQueryOptions
                    .newBuilder()
                    .setProjectId(authConfig.getProjectId())
                    .setCredentials(authConfig.toGoogleCredentials(restConfig))
                    .setHeaderProvider(getGpnUserAgent())
                    .setTransportOptions(HttpTransportOptions.newBuilder()
                            .setConnectTimeout(connectTimeout)
                            .setReadTimeout(readTimeout)
                            .build())
                    .build()
                    .getService();
        }
    }

    @SneakyThrows
    public BigQueryWriteClient getStreamingService(BigQueryAuthConfig authConfig) {
        DataCredentials creds = adminApiClient.getDataCredentials(authConfig.getCredsId()).get();
        Map<String, String> credsMap = NexlaDataCredentials.getCreds(credentialsDecryptKey, creds.getCredentialsEnc(), creds.getCredentialsEncIv());

        BigQueryWriteSettings settings = BigQueryWriteSettings.newBuilder()
                .setCredentialsProvider(() -> {
                    Boolean isServiceAccount = Optional.ofNullable(credsMap.get("is_service_account")).map(Boolean::valueOf).orElse(false);
                    if (isServiceAccount) {
                        return GoogleCredentials
                                .fromStream(new ByteArrayInputStream(credsMap.get("json_creds").getBytes()))
                                .createScoped(SCOPES);
                    } else {
                        return authConfig.toGoogleCredentials(
                                new RestAuthConfig(credsMap, authConfig.getCredsId())
                        );
                    }
                })
                .setHeaderProvider(getGpnUserAgent())
                .build();

        return BigQueryWriteClient.create(settings);
    }

    private static FixedHeaderProvider getGpnUserAgent() {
        return FixedHeaderProvider.create("user-agent", "Nexla/1.0 (GPN:Nexla;)");
    }
}
