package com.nexla.probe.bigquery;

import com.google.api.gax.paging.Page;
import com.google.cloud.bigquery.*;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.*;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.probe.ColumnInfo;
import com.nexla.common.probe.ProbeSampleResultEntry;
import com.nexla.common.probe.SqlSampleResult;
import com.nexla.connector.ConnectorService;
import com.nexla.connector.config.big_query.BigQuerySourceConnectorConfig;
import com.nexla.connector.config.file.BigQueryAuthConfig;
import com.nexla.connector.config.jdbc.DbListTreeServicePaged;
import com.nexla.connector.config.jdbc.DbPage;
import com.nexla.listing.client.ListingClient;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.AbstractConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;

import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;
import static com.nexla.connector.ConnectorService.AuthResponse.authError;
import static java.time.temporal.ChronoUnit.MICROS;
import static java.util.stream.Collectors.toList;

public class BigQueryConnectorService
	extends ConnectorService<BigQueryAuthConfig>
	implements DbListTreeServicePaged<BigQuerySourceConnectorConfig> {

	public static final int DEFAULT_SAMPLE_ROWS = 10;

	private static final DateTimeFormatter TIMESTAMP_FORMATTER =
			DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss z")
			.withZone(ZoneId.of("UTC"));

	private static Logger logger = LoggerFactory.getLogger(BigQueryConnectorService.class);

	private final AdminApiClient adminApiClient;

	private final ListingClient listingClient;

	private final BigQueryClientService bigQueryClientService;

	public BigQueryConnectorService(AdminApiClient adminApiClient,
									ListingClient listingClient,
									BigQueryClientService bigQueryClientService) {
		this.adminApiClient = adminApiClient;
		this.listingClient = listingClient;
		this.bigQueryClientService = bigQueryClientService;
	}

	@Override
	public void createDestination(AbstractConfig c, Optional<Integer> sourceId) {
		// does nothing, cause table BigQuery creation is part of SinkTask
		// this is cause for MAPPING = AUTO we like BigQuery to autocreate a table
		// inferring correct schema automatically
	}

	@Override
	public void initLogger(ResourceType resourceType, Integer resourceId, Optional<Integer> taskId) {
		logger = new NexlaLogger(logger, new NexlaLogKey(resourceType, resourceId, taskId));
	}

	@Override
	public AuthResponse authenticate(BigQueryAuthConfig auth) {
		try {
			return withClientRetriable(auth, bigQuery -> {
				StreamEx.of(
						bigQueryClientService.getService(auth)
						.listDatasets(auth.projectId)
						.iterateAll()
				).toList();
				return SUCCESS;
			});
		} catch (Exception e) {
			logger.error("[creds-{}] Exception while authenticating", auth.getCredsId(), e);
			return authError(e);
		}
	}

	public StreamEx<String> listSchemas(String database, BigQuerySourceConnectorConfig bConf) {
		return StreamEx.empty();
	}

	@Override
	public StreamEx<String> listTables(String database, Optional<String> schema, BigQuerySourceConnectorConfig config) {
		return withClientRetriable(config.authConfig, bigQuery -> {
			Dataset dataset = bigQuery
					.getDataset(DatasetId.of(config.authConfig.projectId, database));

			if (dataset == null) {
				return StreamEx.empty();
			}

			return StreamEx.of(
				dataset
					.list()
					.iterateAll()
					.iterator()
			).map(x -> x.getTableId().getTable());
		});
	}

	@Override
	public List<ColumnInfo> listColumnInfos(String datasetName, Optional<String> schema, String tableId, BigQuerySourceConnectorConfig config) {
		return withClientRetriable(config.authConfig, bigQuery -> {
			Table table = bigQuery.getTable(TableId.of(config.authConfig.projectId, datasetName, tableId));
			TableDefinition def = table.getDefinition();
			if (def.getSchema() == null || def.getSchema().getFields().isEmpty()) {
				return Collections.emptyList();
			}
			Iterator<Field> fieldIterator = def.getSchema().getFields().iterator();
			return StreamEx.of(fieldIterator)
				.map(col -> {
					StandardSQLTypeName standardType = col.getType().getStandardType();
					StandardSQLTypeName typeName = Optional.ofNullable(standardType).orElse(StandardSQLTypeName.STRING);
					return new ColumnInfo(col.getName(), false, typeName.name(), null);
				})
				.collect(toList());
		});
	}

	public StreamEx<String> listDatabases(BigQuerySourceConnectorConfig bConf) {
		return withClientRetriable(bConf.authConfig, bigQuery ->
			StreamEx.of(
				bigQuery
					.listDatasets(bConf.authConfig.projectId)
					.iterateAll()
					.iterator()
			))
			.map(x -> x.getDatasetId().getDataset());
	}

	@SneakyThrows
	@Override
	public StreamEx<NexlaBucket> listBuckets(AbstractConfig config) {
		return StreamEx.empty();
	}

	@Override
	public StreamEx<NexlaFile> listBucketContents(AbstractConfig c) {
		return StreamEx.empty();
	}

	@Override
	public boolean checkWriteAccess(AbstractConfig config) {
		BigQuerySourceConnectorConfig bConf = (BigQuerySourceConnectorConfig) config;
		String tableName = "nexla_temp_" + System.currentTimeMillis();

		Field field = Field.newBuilder("field", StandardSQLTypeName.STRING).build();
		Schema schema = Schema.of(field);
		TableDefinition tableDefinition = StandardTableDefinition.of(schema);

		com.google.cloud.bigquery.TableInfo tableInfo = com.google.cloud.bigquery.TableInfo
			.newBuilder(TableId.of(bConf.authConfig.projectId, bConf.dataset, tableName), tableDefinition)
			.build();

		withClientRetriable(bConf.authConfig, bigQuery -> bigQuery.create(tableInfo));
		withClientRetriable(bConf.authConfig, bigQuery -> bigQuery.delete(tableInfo.getTableId()));

		return true;
	}

	@Override
	@SneakyThrows
	public SqlSampleResult readSample(AbstractConfig config, boolean rawNotUsed) {
		BigQuerySourceConnectorConfig bConf = (BigQuerySourceConnectorConfig) config;

		return withClientRetriable(bConf.authConfig, bigQuery -> bConf.query
			.map(queryString -> {
				QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(queryString).build();
				TableResult query = executeQuery(bigQuery, queryConfig);

				return sampleFromFields(query.iterateAll().iterator(), query.getSchema());
			})
			.orElseGet(() -> {
				TableId tableId = TableId.of(bConf.authConfig.projectId, bConf.dataset, bConf.table.get());
				Table table = bigQuery.getTable(tableId);
				TableResult page = table.list(BigQuery.TableDataListOption.pageSize(DEFAULT_SAMPLE_ROWS));
				return sampleFromFields(page.iterateAll().iterator(), table.getDefinition().getSchema());
			}));
	}

	@SneakyThrows
	private static TableResult executeQuery(BigQuery bigQuery, QueryJobConfiguration queryConfig) {
		return bigQuery.query(queryConfig);
	}

	public StreamEx<NexlaMessage> readStream(AbstractConfig config) {
		BigQuerySourceConnectorConfig bConf = (BigQuerySourceConnectorConfig) config;

		return withClientRetriable(bConf.authConfig,
			bigQuery -> bConf.query
				.map(queryString -> queryMessages(bigQuery, queryString))
				.or(() -> bConf.table.map(tableName -> streamFromTable(bConf, bigQuery, tableName)))
				.get());
	}

	private StreamEx<NexlaMessage> streamFromTable(BigQuerySourceConnectorConfig bConf, BigQuery bigQuery, String tableName) {
		TableId tableId = TableId.of(bConf.authConfig.projectId, bConf.dataset, tableName);
		Table table = bigQuery.getTable(tableId);
		return streamFromFields(table.list().iterateAll().iterator(), table.getDefinition().getSchema());
	}

	private StreamEx<NexlaMessage> queryMessages(BigQuery bigQuery, String queryString) {
		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(queryString).build();
		TableResult query = executeQuery(bigQuery, queryConfig);

		return StreamEx.of(query.iterateAll().iterator())
			.map(x -> {
				LinkedHashMap<String, Object> res = Maps.newLinkedHashMap();
				for (int i = 0; i < x.size(); i++) {
					Field field = query.getSchema().getFields().get(i);
					String name = field.getName();
					FieldValue value = x.get(i);
					res.put(name, getFieldValue(field, value));
				}
				return res;
			})
			.map(NexlaMessage::new);
	}

	private static StreamEx<NexlaMessage> streamFromFields(Iterator<FieldValueList> fieldValues, Schema schema) {
		return StreamEx.of(fieldValues)
			.map(x -> {
				LinkedHashMap<String, Object> res = Maps.newLinkedHashMap();
				for (int i = 0; i < x.size(); i++) {
					Field field = schema.getFields().get(i);
					String name = field.getName();
					FieldValue value = x.get(i);
					res.put(name, getFieldValue(field, value));
				}
				return res;
			})
			.map(NexlaMessage::new);
	}


	private static SqlSampleResult sampleFromFields(Iterator<FieldValueList> fieldValues, Schema schema) {
		Set<String> fields = Sets.newHashSet();
		StreamEx<ProbeSampleResultEntry<LinkedHashMap<String, Object>>> values = StreamEx.of(fieldValues)
			.limit(DEFAULT_SAMPLE_ROWS)
			.map(x -> {
				LinkedHashMap<String, Object> res = Maps.newLinkedHashMap();
				for (int i = 0; i < x.size(); i++) {
					Field field = schema.getFields().get(i);
					String name = field.getName();
					FieldValue value = x.get(i);
					res.put(name, getFieldValue(field, value));
					fields.add(name);
				}
				return new ProbeSampleResultEntry<>(res);
			});
		return new SqlSampleResult(values.toList(), fields);
	}

	public <T, U> U withClientRetriable(BigQueryAuthConfig authConfig, Function<BigQuery, U> consumer) {
		try {
			BigQuery client = bigQueryClientService.getService(authConfig);
			return consumer.apply(client);
		} catch (Exception e) {
			logger.debug("client retryable error", e);
			adminApiClient.invalidate(authConfig.getCredsId(), Optional.empty(), ResourceType.CREDENTIALS);
			if (listingClient.refreshRestToken(authConfig.getCredsId())) {
				BigQuery client = bigQueryClientService.getService(authConfig);
				return consumer.apply(client);
			}
			throw e;
		}
	}

	private static Object getFieldValue(Field field, FieldValue value) {
		StandardSQLTypeName type = field.getType().getStandardType();
		if (value.isNull()) {
			return null;
		}
		switch (value.getAttribute()) {
			case PRIMITIVE:
				return getPrimitive(value, type);
			case REPEATED:
				return StreamEx.of(value.getRepeatedValue())
					.map(v -> getFieldValue(field, v))
					.toList();
			case RECORD:
				LinkedHashMap<String, Object> res = new LinkedHashMap<>();
				FieldList subFields = field.getSubFields();
				FieldValueList recordValue = value.getRecordValue();
				for (int i = 0; i < subFields.size(); i++) {
					Field subField = subFields.get(i);
					res.put(subField.getName(), getFieldValue(subField, recordValue.get(i)));
				}
				return res;
			default:
				throw new UnsupportedOperationException();
		}
	}

	private static Object getPrimitive(FieldValue value, StandardSQLTypeName type) {
		switch (type) {
			case BOOL:
				return value.getBooleanValue();
			case FLOAT64:
				return value.getDoubleValue();
			case TIMESTAMP:
				return TIMESTAMP_FORMATTER.format(Instant.EPOCH.plus(value.getTimestampValue(), MICROS));
			case NUMERIC:
				return value.getNumericValue();
			case INT64:
				return value.getLongValue();
			case STRING:
			case DATE:
			case TIME:
			case DATETIME:
			default:
				return value.getStringValue();
		}
	}

	@Override
	public DbPage listTables(
			BigQuerySourceConnectorConfig config,
			String database,
			String schema,
			Integer pageSize,
			String offset) {

		return withClientRetriable(config.getAuthConfig(), bigQuery -> {
			Dataset dataset = bigQuery
					.getDataset(DatasetId.of(config.getAuthConfig().projectId, database));

			if (dataset == null) {
				return DbPage.empty();
			}

			Page<Table> tablePage = dataset
					.list(BigQuery.TableListOption.pageSize(pageSize), BigQuery.TableListOption.pageToken(offset));

			StreamEx<String> tables = StreamEx.of(tablePage
							.getValues()
							.iterator())
					.map(x -> x.getTableId().getTable());

			return new DbPage<>(tables, pageSize, tablePage.getNextPageToken(), tablePage.hasNextPage());
        });
	}

	public BigQueryClientService getBigQueryService() {
		return bigQueryClientService;
	}
}
