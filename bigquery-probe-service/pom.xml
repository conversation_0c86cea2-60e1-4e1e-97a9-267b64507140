<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.nexla</groupId>
		<artifactId>backend-connectors</artifactId>
		<version>3.2.1-SNAPSHOT</version>
	</parent>

	<groupId>com.nexla.probe</groupId>
	<artifactId>bigquery-probe</artifactId>

	<dependencies>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>

		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>sql-probe</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jersey</groupId>
					<artifactId>jersey-server</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jersey</groupId>
					<artifactId>jersey-servlet</artifactId>
				</exclusion>
					<exclusion>
						<groupId>Athena</groupId>
						<artifactId>AthenaJDBC42</artifactId>
					</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common</artifactId>
			<version>${nexla-backend-common.version}</version>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-library</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-to-slf4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.google.oauth-client</groupId>
			<artifactId>google-oauth-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.google.cloud</groupId>
			<artifactId>google-cloud-bigquery</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.google.protobuf</groupId>
					<artifactId>protobuf-java</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.oauth-client</groupId>
					<artifactId>google-oauth-client</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
            <groupId>com.nexla</groupId>
            <artifactId>connector-properties</artifactId>
			<version>${project.version}</version>
        </dependency>
		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>admin-api-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common-connector</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.google.http-client</groupId>
			<artifactId>google-http-client-jackson2</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.cloud</groupId>
			<artifactId>google-cloud-bigquerystorage</artifactId>
		</dependency>

	</dependencies>

</project>
