testing pipeline: 23723
====================================== source ====================================== 

```
{
    "source_config": {
        "pipeline.type": "in.memory",
        "ray.creds.enc": "vGn08ARJntGSXXuxxiVzvKiJ2xdvKMkT4sigc4tMacBmWdQzbDNZ818Ee7NUFJjXj8hmIbE2PSYJICmq0vhbEh/JvM8yxVHX3SFjwrfSbxqNt+k9BA4t6JcnY3JirwYnAamHoEXDjLWumw/3sgMVDQ==",
        "ray.creds.enciv": "6EICMZnPSb/V2oKJ",
        "ray.upload.path": "nexla-shared-s3/bszmit/ray",
        "ray.mode": "replication",
        "ray.directoryMode": "false",
        "advanced_settings": "Auto Detect",
        "path_exclusions": false,
        "schema.detection.once": false,
        "allowGrouping": false,
        "start.cron": "0 20 11 23 7 ? 2024",
        "path": "qa-us-west-2.nexla.com/bszmit/uploaded/file_tx_input",
        "ui.display_path": "qa-us-west-2.nexla.com/bszmit/uploaded/file_tx_input"
    }
}
```

====================================== tx1 ====================================== 

```
    "runtime_config": {
        "customCode": "ZnJvbSBiczQgaW1wb3J0IEJlYXV0aWZ1bFNvdXAKaW1wb3J0IG9zCmltcG9ydCBwYW5kYXMgYXMgcGQKCgpkZWYgc3BsaXRfaHRtbF9ieV9oMihpbnB1dF9kYXRhLCAqYXJncywgKiprd2FyZ3MpOgogICAgaWYgb3MucGF0aC5leGlzdHMoaW5wdXRfZGF0YSk6CiAgICAgICAgZGYgPSBwZC5yZWFkX2V4Y2VsKGlucHV0X2RhdGEpCiAgICAgICAgcHJpbnQoZGYpCiAgICAgICAgcmV0dXJuIGRmLnRvX2RpY3Qob3JpZW50PSJyZWNvcmRzIikKICAgIGVsc2U6CiAgICAgICAgc291cCA9IEJlYXV0aWZ1bFNvdXAoaW5wdXRfZGF0YSwgImx4bWwiKQogICAgICAgIGNodW5rcyA9IFtdCiAgICAgICAgY3VycmVudF9jaHVuayA9IFtdCgogICAgICAgICMgQ2hlY2sgaWYgdGhlcmUncyBjb250ZW50IGJlZm9yZSB0aGUgZmlyc3QgPGgyPgogICAgICAgIGZpcnN0X2gyID0gc291cC5maW5kKCJoMiIpCiAgICAgICAgaWYgZmlyc3RfaDI6CiAgICAgICAgICAgIGJlZm9yZV9maXJzdF9oMiA9IHNvdXAubmV3X3RhZygiZGl2IikKICAgICAgICAgICAgZm9yIHNpYmxpbmcgaW4gZmlyc3RfaDIuZmluZF9wcmV2aW91c19zaWJsaW5ncygpOgogICAgICAgICAgICAgICAgYmVmb3JlX2ZpcnN0X2gyLmluc2VydCgwLCBzaWJsaW5nKQogICAgICAgICAgICBjaHVua3MuYXBwZW5kKHN0cihiZWZvcmVfZmlyc3RfaDIpKQoKICAgICAgICBmb3IgZWxlbWVudCBpbiBzb3VwLmJvZHkuY2hpbGRyZW46CiAgICAgICAgICAgIGlmIGVsZW1lbnQubmFtZSA9PSAiaDIiOgogICAgICAgICAgICAgICAgaWYgY3VycmVudF9jaHVuazoKICAgICAgICAgICAgICAgICAgICBjaHVua3MuYXBwZW5kKCIiLmpvaW4oc3RyKGUpIGZvciBlIGluIGN1cnJlbnRfY2h1bmspKQogICAgICAgICAgICAgICAgICAgIGN1cnJlbnRfY2h1bmsgPSBbXQogICAgICAgICAgICBjdXJyZW50X2NodW5rLmFwcGVuZChlbGVtZW50KQoKICAgICAgICAjIEFkZCB0aGUgbGFzdCBjaHVuayBpZiBpdCBleGlzdHMKICAgICAgICBpZiBjdXJyZW50X2NodW5rOgogICAgICAgICAgICBjaHVua3MuYXBwZW5kKCIiLmpvaW4oc3RyKGUpIGZvciBlIGluIGN1cnJlbnRfY2h1bmspKQoKICAgICAgICByZXR1cm4gY2h1bmtzCg==",
        "driverFunction": "split_html_by_h2",
        "packages": "beautifulsoup4,lxml,openpyxl,pandas",
        "sink": "s3_file",
        "source": "s3_file",
        "destinationFileExt": "csv"
    }
````


====================================== tx2 ====================================== 

````
    "runtime_config": {
        "customCode": "aW1wb3J0IHBhbmRhcyBhcyBwZAoKCmRlZiBsb3dlcmNhc2VfY3N2KGlucHV0X2RhdGEsICphcmdzLCAqKmt3YXJncyk6CiAgICBwcmludChpbnB1dF9kYXRhKQogICAgZGYgPSBwZC5yZWFkX2NzdihpbnB1dF9kYXRhKQogICAgcHJpbnQodHlwZShkZikpCiAgICBkZiA9IGRmLmFwcGx5bWFwKGxhbWJkYSB4OiB4Lmxvd2VyKCkgaWYgaXNpbnN0YW5jZSh4LCBzdHIpIGVsc2UgeCkKICAgIHByaW50KGRmKQogICAgcmV0dXJuIGRmLnRvX2RpY3Qob3JpZW50PSJyZWNvcmRzIikKCg==",
        "driverFunction": "lowercase_csv",
        "packages": "pandas",
        "sink": "s3_file",
        "source": "s3_file",
        "sourceFileExt": "csv",
        "destinationFileExt": "csv"
    }
````


====================================== ray tx ===================================

````
{
    "args": [],
    "custom_code": "aW1wb3J0IG9zCmltcG9ydCBwYW5kYXMgYXMgcGQKZnJvbSB0aW1lIGltcG9ydCBzbGVlcAoKCmRlZiBteV9mdW5jdGlvbihpbnB1dF9kYXRhLCAqYXJncywgKiprd2FyZ3MpOgogICAgaWYgb3MucGF0aC5leGlzdHMoaW5wdXRfZGF0YSk6CiAgICAgICAgZGYgPSBwZC5yZWFkX2V4Y2VsKGlucHV0X2RhdGEpCiAgICAgICAgcHJpbnQoZGYpCiAgICAgICAgc2xlZXAoNTAwKQogICAgICAgIHJldHVybiBkZi50b19kaWN0KG9yaWVudD0icmVjb3JkcyIpICAgIAogICAgcmV0dXJuIFtdCg==",
    "dest_s3_bucket": {
        "access_key": "********************",
        "bucket": "nexla-shared-s3",
        "secret_key": "+lpoITQhoQo+4IKSFzKaMZ/tmSAhxl5eziIcUlnF"
    },
    "destination_dir": "ray-output/18876/bszmit/ray/12671/a4f2facb-7f19-4ad1-a021-72137a65be78/bszmit/ray_demo/input_compressed/input_files.tar.gz;to_compress/",
    "destination_file_ext": "csv",
    "driver_function": "my_function",
    "files_per_job": 5,
    "folder_by_folder": false,
    "is_gaitup_file": false,
    "kwargs": {},
    "packages": [
        "beautifulsoup4",
        "lxml",
        "openpyxl",
        "boto3"
    ],
    "sink": "s3_file",
    "source": "s3_file",
    "source_dir": "bszmit/ray/12671/a4f2facb-7f19-4ad1-a021-72137a65be78/bszmit/ray_demo/input_compressed/input_files.tar.gz;to_compress/",
    "source_file_ext": "xlsx",
    "source_s3_bucket": {
        "access_key": "********************",
        "bucket": "nexla-shared-s3",
        "secret_key": "+lpoITQhoQo+4IKSFzKaMZ/tmSAhxl5eziIcUlnF"
    }
}
````
