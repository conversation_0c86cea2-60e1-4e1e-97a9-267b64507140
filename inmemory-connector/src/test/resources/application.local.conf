include "common.local.conf"

nexla.app.port = 8080
dedicated.node = false

flush.offsets.cron = "0/5 * * ? * *"

file-read {
  retry {
    attempts = 2
    min-backoff-ms = 100
    max-backoff-ms = 500
  }
}


ray.api.server.url = "http://nexla-ray-svc.nexla.svc.cluster.local:8080"

akka {
  http {
    host-connection-pool.max-open-requests = 256
    host-connection-pool.max-open-requests = ${?AKKA_HTTP_HOST_CONNECTION_POOL_MAX_OPEN_REQUESTS}
  }
}
