<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="APP_LOG" value="inmemory-connector"/>
    <include resource="logback/base.xml"/>

    <appender name="app"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>/tmp/${APP_LOG}.json</File>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder" >
            <customFields>{"app":"${APP_LOG}"}</customFields>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>/tmp/${APP_LOG}.%d{yyyy-MM-dd}.json
            </fileNamePattern>

            <!-- keep 30 days' worth of history capped at 3GB total size -->
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>

        </rollingPolicy>
    </appender>

    <!-- reset all previous level configurations of all j.u.l. loggers -->
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <logger name="com.joestelmach.natty">
        <level value="WARN"/>
    </logger>

    <logger name="org.apache.http">
        <level value="WARN"/>
    </logger>

    <logger name="org.apache.kafka">
        <level value="WARN"/>
    </logger>

    <logger name="org.apache.zookeeper">
        <level value="WARN"/>
    </logger>

    <logger name="com.airbnb.metrics">
        <level value="ERROR"/>
    </logger>

    <logger name="io.netty">
        <level value="WARN"/>
    </logger>

    <root level="INFO" additivity="false">
        <appender-ref ref="app"/>
    </root>

    <appender name="HTTP_ACCESS_LOG" class="ch.qos.logback.core.FileAppender">
        <file>/tmp/${APP_LOG}-http-access.log</file>
        <encoder>
            <pattern>[%date{"yyyy-MM-dd HH:mm:ss,SSS"}] %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="ASYNC_HTTP_ACCESS_LOG" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="HTTP_ACCESS_LOG"/>
    </appender>

    <logger name="http.access.logger" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_HTTP_ACCESS_LOG"/>
    </logger>

</configuration>	