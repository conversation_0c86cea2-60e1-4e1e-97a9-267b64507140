package com.nexla.inmemory_connector

import com.nexla.inmemory_connector.pipeline.flow.newimpl.GlobalFileNaming
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers

class GlobalFileNamingTest extends AnyFunSpec with Matchers {

  describe("GlobalFileNamingTest") {
    it("should generate file names") {
      GlobalFileNaming.setReceivedListingFileId(12123123400L)

      // Call the function multiple times
      val suffix1 = GlobalFileNaming.getNextSuffix()
      val suffix2 = GlobalFileNaming.getNextSuffix()
      val suffix3 = GlobalFileNaming.getNextSuffix()

      // Verify that the output matches the expected values
      suffix1 shouldBe "123400000001"
      suffix2 shouldBe "123400000002"
      suffix3 shouldBe "123400000003"
    }
  }

}
