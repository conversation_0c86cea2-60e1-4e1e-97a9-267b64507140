package com.nexla.inmemory_connector.monitoring

import com.bazaarvoice.jolt.JsonUtils
import com.nexla.common.ResourceType
import com.nexla.common.metrics.NexlaRawMetric
import com.nexla.common.notify.transport.{NexlaMessageProducer, NexlaMessageTransport}
import com.nexla.connector.config.FlowType
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryMessageProducer
import com.nexla.listing.client.AdaptiveFlowTask
import org.mockito.ArgumentMatchers.argThat
import org.mockito.Mockito.{mock, spy, verify, when}
import org.scalatest.TagAnnotation
import org.scalatest.funsuite.AnyFunSuite

import scala.concurrent.ExecutionContext

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class MetricsSenderImplTest extends AnyFunSuite {

  test("should append display path, name and id to the metric event") {
    val mockMsgProducer = mock(classOf[NexlaMessageProducer])
    val mockTransport = mock(classOf[NexlaMessageTransport])
    when(mockMsgProducer.getTransport).thenReturn(mockTransport)
    val spyProducer = spy(new InMemoryMessageProducer(mockMsgProducer))

    val metricsSender = new MetricsSenderImpl(spyProducer, FlowType.IN_MEMORY, 123L) {
      override protected val ioEx: ExecutionContext = DirectExecutionContext
    }
    val adaptiveFlowTask: AdaptiveFlowTask = JsonUtils.streamToType(getClass.getClassLoader.getResourceAsStream("./adaptiveTaskDef.json"), classOf[AdaptiveFlowTask])

    metricsSender.publishMetric(
      123,
      ResourceType.SOURCE,
      None,
      100L, // 100 records
      200L, // 200 bytes
      "filePath",
      Some(true), // eof
      Option(adaptiveFlowTask)
    )

    verify(spyProducer).publishMetrics(argThat[NexlaRawMetric](x => {
      x.getTags.get("name") == "99" &&
      x.getTags.get("display_path") == "#99 myTask"
    }))
  }
}

// hack: here we are creating a custom ExecutionContext that runs tasks directly in the current thread. otherwise futures get a bit crazy
object DirectExecutionContext extends ExecutionContext {
  override def execute(runnable: Runnable): Unit = runnable.run()
  override def reportFailure(cause: Throwable): Unit = throw cause
}