package com.nexla.inmemory_connector.state

import com.nexla.inmemory_connector.state.FilesState.FileInfo
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers

class FilesStateSpec extends AnyFunSpec with Matchers {
  describe("FilesState") {

    it("should add a file and retrieve it correctly") {
      val state = new FilesState
      state.addFile(1, "file1", 2, FilesState.DuringProcessing)

      val inFlightFile = state.getFiles().head
      inFlightFile.fileId shouldEqual 1
      inFlightFile.sourceId shouldEqual 2
      inFlightFile.status shouldEqual FilesState.DuringProcessing
    }

    it("should add multiple files and retrieve them correctly") {
      val state = new FilesState
      state.addFile(1, "file1", 1, FilesState.DuringProcessing)
      state.addFile(2, "file2", 1, FilesState.Failed("abc"))
      state.addFile(3, "file3", 2, FilesState.DuringProcessing)

      state.getFiles() should contain theSameElementsAs List(FileInfo(1, "file1", 1, FilesState.DuringProcessing), FileInfo(2, "file2", 1, FilesState.Failed("abc")), FileInfo(3, "file3", 2, FilesState.DuringProcessing))
    }

    it("should overwrite a file") {
      val state = new FilesState
      state.addFile(1, "file1", 1, FilesState.DuringProcessing)

      state.getFiles().size shouldEqual 1
      state.getFiles() should contain theSameElementsAs List(FileInfo(1, "file1", 1, FilesState.DuringProcessing))

      state.addFile(1, "file1", 1, FilesState.Failed("abc"))
      state.getFiles().size shouldEqual 1
      state.getFiles() should contain theSameElementsAs List(FileInfo(1, "file1", 1, FilesState.Failed("abc")))

    }

    it("should remove a file and retrieve the remaining files correctly") {
      val state = new FilesState
      state.addFile(1, "file1", 1, FilesState.DuringProcessing)
      state.addFile(2, "file2", 1, FilesState.Failed("abc"))
      state.addFile(3, "file3", 2, FilesState.DuringProcessing)

      state.removeFile(2)

      state.getFiles() should contain theSameElementsAs List(FileInfo(1, "file1", 1, FilesState.DuringProcessing), FileInfo(3, "file3", 2, FilesState.DuringProcessing))
    }
  }

}
