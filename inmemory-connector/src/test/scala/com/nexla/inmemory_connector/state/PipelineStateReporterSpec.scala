package com.nexla.inmemory_connector.state

import akka.actor.ActorSystem
import com.bazaarvoice.jolt.JsonUtils
import com.nexla.admin.client.flownode.{AdminApiFlow, FlowNodeElement}
import com.nexla.admin.client.{DataSource, ResourceStatus}
import com.nexla.common.{Resource, ResourceType}
import com.nexla.connector.config.{FlowType, IngestionMode}
import com.nexla.control.ListingFileStatus
import com.nexla.inmemory_connector.compat.PipelineConf
import com.nexla.inmemory_connector.context.Context.PipelineContext
import com.nexla.inmemory_connector.monitoring.MetricsSender
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryMessageProducer
import com.nexla.inmemory_connector.pipeline.sink.offsets.FileOffsetCommitter
import com.nexla.inmemory_connector.state.FilesState.Failed
import com.nexla.listing.client.AdaptiveFlowTask
import com.nexla.sc.client.listing.CoordinationAppClient
import org.mockito.Mockito.{mock, spy, when}
import org.mockito.{ArgumentMatchers, Mockito}
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers

import java.util.Collections
import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext, Future}

class PipelineStateReporterSpec extends AnyFunSpec with Matchers {
  implicit val system: ActorSystem = ActorSystem()
  implicit val ec: ExecutionContext = system.dispatcher

  describe("PipelineStateReporter") {
    it("should add send metrics with the task execution context if it was provided") {
      val pipelineContext = mock(classOf[PipelineContext])
      val inFlightFilesState = new FilesState
      inFlightFilesState.addFile(1L, "path", 1, Failed("error"))
      val metricsSender = spy(new MetricsSender {
        override def publishMetric(resourceId: Int, resourceType: ResourceType, dataSetId: Option[Int], records: Long, fileSize: Long, filePath: String, eof: Option[Boolean], maybeAdaptiveFlowTask: Option[AdaptiveFlowTask]): Future[Unit] = Future.unit
      })
      val fileOffsetCommitter = new FileOffsetCommitter {
        override def commit(sourceId: Int, fileId: Long, fileStatus: ListingFileStatus, maybeOffset: Option[Long], message: Option[String]): Future[Unit] = Future.unit
      }

      val pipConf = mock(classOf[PipelineConf])
      when(pipConf.dockerInstances).thenReturn(2)
      when(pipelineContext.pipelineConf).thenReturn(pipConf)
      val taskDef: AdaptiveFlowTask = JsonUtils.streamToType(getClass.getClassLoader.getResourceAsStream("./adaptiveTaskDef.json"), classOf[AdaptiveFlowTask])
      when(pipelineContext.maybeAdaptiveFlowTask).thenReturn(Option(taskDef))
      val stubFlow = new AdminApiFlow(1, 1, 1, 1, ResourceStatus.ACTIVE, FlowType.IN_MEMORY, IngestionMode.FULL_INGESTION, Collections.emptyList[FlowNodeElement])
      when(pipelineContext.flow).thenReturn(stubFlow)
      when(pipelineContext.flowType).thenReturn(FlowType.IN_MEMORY)
      val stubSource = new DataSource()
      stubSource.setId(1)
      when(pipelineContext.dataSource).thenReturn(stubSource)

      val pipelineStateReporter = new PipelineStateReporter(pipelineContext, inFlightFilesState)(
        coordinationClient = mock(classOf[CoordinationAppClient]),
        fileOffsetCommitter,
        metricsSender,
        nexlaMessageProducer = mock(classOf[InMemoryMessageProducer]),
        initialDelay = 1.second,
        heartbeatPeriod = 1.second
      )

      val resources = List(new ResourceState(new Resource(1, ResourceType.SOURCE)))
      pipelineStateReporter.start(resources)
      val completion = pipelineStateReporter.finalReportAndFinish()
      Await.result(completion, 5.seconds)

      Mockito.verify(metricsSender, Mockito.times(1)).publishMetric(
        ArgumentMatchers.eq(1),
        ArgumentMatchers.eq(ResourceType.SOURCE),
        ArgumentMatchers.eq(None),
        ArgumentMatchers.eq(0L),
        ArgumentMatchers.eq(0L),
        ArgumentMatchers.eq("path"),
        ArgumentMatchers.eq(Some(true)),
        ArgumentMatchers.any[Option[AdaptiveFlowTask]])
    }
  }
}
