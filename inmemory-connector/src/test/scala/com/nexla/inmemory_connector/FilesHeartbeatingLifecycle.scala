package com.nexla.inmemory_connector

import akka.actor.ActorSystem
import akka.stream.scaladsl.{Keep, Sink, Source}
import com.nexla.common.{NexlaMessage, Resource, ResourceType}
import com.nexla.connect.common.postponedFlush.InMemoryProgressTracker
import com.nexla.connector.config.FlowType
import com.nexla.control.ListingFileStatus
import com.nexla.inmemory_connector.compat.{BasicMessage, FileOffset, RecordWithOffset}
import com.nexla.inmemory_connector.monitoring.telemetry.InMemoryTelemetry
import com.nexla.inmemory_connector.monitoring.{MetricsOffsetReporterImpl, MetricsSender}
import com.nexla.inmemory_connector.pipeline.sink.flusher.SinkFlusher.FlushingResults
import com.nexla.inmemory_connector.pipeline.sink.flusher.{SinkFlusher, SinkFlusherFactory}
import com.nexla.inmemory_connector.pipeline.sink.offsets.{FileOffsetCommitter, MapOffsetCommitter}
import com.nexla.inmemory_connector.pipeline.sink.shutdown_monitor.SinkEarlyShutdownMonitor
import com.nexla.inmemory_connector.pipeline.sink.task.BasicSinkTask
import com.nexla.inmemory_connector.pipeline.sink.{InMemorySinkTask, SinkFlow}
import com.nexla.inmemory_connector.pipeline.source.SourceFlow.SourceDone
import com.nexla.inmemory_connector.pipeline.source.stopping_condition.SourceStoppingCondition
import com.nexla.inmemory_connector.pipeline.source.task.BasicSourceTask
import com.nexla.inmemory_connector.pipeline.source.{InMemorySourceTask, SourceFlow}
import com.nexla.inmemory_connector.state.FilesState.FileInfo
import com.nexla.inmemory_connector.state.{FilesState, ResourceState}
import com.nexla.listing.client.AdaptiveFlowTask
import com.nexla.telemetry.NoopTelemetry
import org.mockito.Mockito
import org.scalatest.concurrent.PatienceConfiguration.Timeout
import org.scalatest.concurrent.ScalaFutures.convertScalaFuture
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers

import java.util
import java.util.concurrent.atomic.AtomicBoolean
import scala.collection.mutable.ListBuffer
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}

class FilesHeartbeatingLifecycle extends AnyFunSpec with Matchers {
  implicit val system: ActorSystem = ActorSystem()
  implicit val ec: ExecutionContext = system.dispatcher

  val props: AppProps = Mockito.mock(classOf[AppProps])
  Mockito.when(props.sinkBatchTime).thenReturn(1.milli)
  Mockito.when(props.sinkBatchSize).thenReturn(1)

  class DummySourceTask(records: List[RecordWithOffset]) extends BasicSourceTask {
    val stopCondition = new SourceStoppingCondition(warmupTime = 0.seconds, stopOnEmptyPollCount = 1)
    var shouldStop = false

    val dataSent = new AtomicBoolean(false)

    def start(): Future[Unit] = Future.unit

    def poll(): Future[List[RecordWithOffset]] = synchronized {
      if (dataSent.get()) {
        Future.successful(List.empty)
      } else {
        dataSent.set(true)
        Future.successful(records)
      }
    }.transform { r =>
      val isEmpty = r.map(_.isEmpty).getOrElse(true)
      shouldStop = stopCondition.shouldStop(isEmpty)
      r
    }

    def stop(): Future[Unit] = Future.unit

    def isStoppingConditionReached(): Boolean = shouldStop
  }

  private def recordWithFileOffset(i: Int, eof: Boolean) = {
    val m = new util.LinkedHashMap[String, AnyRef] {
      {
        put("msgId", s"msg$i")
      }
    }
    RecordWithOffset(None, new NexlaMessage(m), FileOffset(i, s"file$i", 0, i, 1, eof = eof, datasetId = None, message = None))
  }

  val sinkFlusherFactory: SinkFlusherFactory[BasicSinkTask] = (t: BasicSinkTask) =>  new SinkFlusher {
    def intermediateFlush(): Future[FlushingResults] = t.flushData()
    def finalFlush(): Future[FlushingResults] = t.flushData()
  }

  private val noopEarlyShutdownMonitor: SinkEarlyShutdownMonitor = () => Future.successful(false)

  private def resourceSourceState(): ResourceState = new ResourceState(new Resource(1, ResourceType.SOURCE))

  private def resourceSinkState(): ResourceState = new ResourceState(new Resource(1, ResourceType.SINK))

  describe("FilesHeartbeating logic in source") {
    it("should add files for heartbeat if records have FileOffset") {
      val records = List(recordWithFileOffset(1, eof = true), recordWithFileOffset(2, eof=true), recordWithFileOffset(3, eof=false))

      val state = new FilesState
      val flow = new SourceFlow("", new InMemorySourceTask("", new DummySourceTask(records)), state, new InMemoryProgressTracker,  new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1))

      state.getFiles() shouldBe empty
      val _: SourceDone = flow.applySource(resourceSourceState()).to(Sink.ignore).run().futureValue(Timeout(1.second))
      state.getFiles() should contain theSameElementsAs Set(FileInfo(1, "file1", 1, FilesState.DuringProcessing), FileInfo(2, "file2", 1, FilesState.DuringProcessing), FileInfo(3, "file3", 1, FilesState.DuringProcessing))
    }
  }

  describe("FilesHeartbeating logic in sink") {

    it("should remove files from heartbeat if eof offsets were committed") {

      val state = new FilesState
      val fileOffsetCommitter = new FileOffsetCommitter {
        override def commit(sourceId: Int, fileId: Long, fileStatus: ListingFileStatus, maybeOffset: Option[Long], message: Option[String]): Future[Unit] = Future.unit
      }
      val metricsSender = new MetricsSender {
        def publishMetric(resourceId: Int, resourceType: ResourceType, dataSetId: Option[Int], records: Long, fileSize: Long, filePath: String, eof: Option[Boolean], maybeAdaptiveFlowTask: Option[AdaptiveFlowTask] = Option.empty): Future[Unit] = Future.unit
      }
      val mapOffsetCommitter = new MapOffsetCommitter {
        def commit(sourceId: Int, sinkId: Int, offsets: Map[String, String]): Future[Unit] = Future.unit
      }
      val metricsOffsetReporter = new MetricsOffsetReporterImpl(fileOffsetCommitter, mapOffsetCommitter, state, metricsSender)
      val dummySinkTask: BasicSinkTask = new BasicSinkTask {
        val fileOffsets = new ListBuffer[FileOffset]
        def start(): Future[Unit] = Future.unit
        def putData(messages: Seq[BasicMessage]): Future[Unit] = {
          fileOffsets ++= messages.collect { case r: RecordWithOffset => r.offset.asInstanceOf[FileOffset]}
          Future.unit
        }
        def flushData(): Future[SinkFlusher.FlushingResults] = {
          Future.successful(FlushingResults(fileOffsets.toList, Nil))
        }
        def forceFlushData(): Future[FlushingResults] = flushData()
        def stop(): Future[Unit] = Future.unit
      }

      val flow = new SinkFlow("", props, new InMemorySinkTask("", dummySinkTask, sinkFlusherFactory), resourceSinkState(), metricsOffsetReporter, noopEarlyShutdownMonitor, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1), intermediateFlushDelay = 10.seconds, maxIntermediateFlushAwaitInterval = 0.seconds)

      List(1, 2, 3).foreach(i => state.addFile(i.toLong, s"file$i", i, FilesState.DuringProcessing))
      state.getFiles() should contain theSameElementsAs Set(FileInfo(1, "file1", 1, FilesState.DuringProcessing), FileInfo(2, "file2", 2, FilesState.DuringProcessing), FileInfo(3, "file3", 3, FilesState.DuringProcessing))

      Source.single(recordWithFileOffset(1, eof = false)).viaMat(flow.applySink())(Keep.right).to(Sink.ignore).run().futureValue(Timeout(200.seconds))
      state.getFiles() should contain theSameElementsAs Set(FileInfo(1, "file1", 1, FilesState.DuringProcessing), FileInfo(2, "file2", 2, FilesState.DuringProcessing), FileInfo(3, "file3", 3, FilesState.DuringProcessing))

      Source.single(recordWithFileOffset(1, eof = true)).viaMat(flow.applySink())(Keep.right).to(Sink.ignore).run().futureValue(Timeout(200.seconds))
      state.getFiles() should contain theSameElementsAs Set(FileInfo(2, "file2", 2, FilesState.DuringProcessing), FileInfo(3, "file3", 3, FilesState.DuringProcessing))
    }
  }

}
