package com.nexla.inmemory_connector.pipeline.sink.flusher

import akka.actor.ActorSystem
import com.nexla.inmemory_connector.compat.{BasicMessage, FileOffset, MapOffset, RecordWithOffset}
import com.nexla.inmemory_connector.pipeline.sink.flusher.SinkFlusher.FlushingResults
import com.nexla.inmemory_connector.pipeline.sink.task.BasicSinkTask
import org.scalatest.concurrent.ScalaFutures.convertScalaFuture
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers

import java.util.concurrent.atomic.AtomicBoolean
import scala.concurrent.{ExecutionContext, Future}

class SinkFlusherForSoapSpec extends AnyFunSpec with Matchers {
  implicit val system: ActorSystem = ActorSystem()
  implicit val ec: ExecutionContext = system.dispatcher

  class DummyTask(offsetsToReturn: FlushingResults, flushDataCalled: AtomicBoolean) extends BasicSinkTask {
    def start(): Future[Unit] = Future.unit

    def putData(messages: Seq[BasicMessage]): Future[Unit] = Future.unit

    def flushData(): Future[FlushingResults] = {
      flushDataCalled.set(true)
      Future.successful(offsetsToReturn)
    }
    def forceFlushData(): Future[FlushingResults] = flushData()

    def stop(): Future[Unit] = Future.unit
  }

  val readyToCommitOffsets: FlushingResults = FlushingResults(List(FileOffset(123L, "file123", 0, 1L, 1, eof = false, datasetId = None, message = None), MapOffset(1, 2, 3, Map.empty)), Nil)

  describe("SinkFlusher for soap") {

    it("should flush data and provide offset to flush on intermediate flush") {
      val flushDataCalled = new AtomicBoolean(false)
      val factory: SinkFlusherFactory[DummyTask] = SinkFlusherFactory.forSoap[DummyTask]
      val task = new DummyTask(readyToCommitOffsets, flushDataCalled)
      val sinkFlusher = factory.apply(task)

      sinkFlusher.intermediateFlush().futureValue shouldEqual readyToCommitOffsets
      flushDataCalled.get() shouldEqual true
    }

    it("should flush data and provide offset to flush on final flush") {
      val flushDataCalled = new AtomicBoolean(false)
      val factory: SinkFlusherFactory[DummyTask] = SinkFlusherFactory.forSoap[DummyTask]
      val task = new DummyTask(readyToCommitOffsets, flushDataCalled)
      val sinkFlusher = factory.apply(task)

      sinkFlusher.finalFlush().futureValue shouldEqual readyToCommitOffsets
      flushDataCalled.get() shouldEqual true
    }

  }
}