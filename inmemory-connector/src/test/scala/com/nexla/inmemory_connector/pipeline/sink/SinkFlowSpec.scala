package com.nexla.inmemory_connector.pipeline.sink

import akka.actor.ActorSystem
import akka.stream.KillSwitches
import akka.stream.scaladsl.{Keep, Sink, Source}
import com.nexla.common.{NexlaMessage, Resource, ResourceType}
import com.nexla.connector.config.FlowType
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.compat.{BasicMessage, MapOffset, RecordWithOffset}
import com.nexla.inmemory_connector.monitoring.MetricsOffsetReporter
import com.nexla.inmemory_connector.monitoring.telemetry.InMemoryTelemetry
import com.nexla.inmemory_connector.pipeline.sink.SinkFlow.SinkDone
import com.nexla.inmemory_connector.pipeline.sink.flusher.SinkFlusher.FlushingResults
import com.nexla.inmemory_connector.pipeline.sink.flusher.{Sink<PERSON><PERSON><PERSON>, SinkFlusherFactory}
import com.nexla.inmemory_connector.pipeline.sink.shutdown_monitor.SinkEarlyShutdownMonitor
import com.nexla.inmemory_connector.pipeline.sink.task.BasicSinkTask
import com.nexla.inmemory_connector.state.{FilesState, ResourceState}
import com.nexla.sc.client.job_scheduler.PipelineTaskStateEnum
import com.nexla.telemetry.NoopTelemetry
import org.mockito.Mockito
import org.scalatest.concurrent.Eventually.eventually
import org.scalatest.concurrent.PatienceConfiguration.Timeout
import org.scalatest.concurrent.ScalaFutures.convertScalaFuture
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers

import java.util
import java.util.concurrent.atomic.AtomicBoolean
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContextExecutor, Future}

class SinkFlowSpec extends AnyFunSpec with Matchers {
  implicit val system: ActorSystem = ActorSystem()
  implicit val ec: ExecutionContextExecutor = system.dispatcher
  val props: AppProps = Mockito.mock(classOf[AppProps])
  Mockito.when(props.sinkBatchTime).thenReturn(1.milli)
  Mockito.when(props.sinkBatchSize).thenReturn(1)

  private def resourceState(): ResourceState = new ResourceState(new Resource(1, ResourceType.SINK))

  private def recordWithOffset(i: Int) = {
    val m = new util.LinkedHashMap[String, AnyRef] {
      {
        put("msgId", s"msg$i")
      }
    }
    RecordWithOffset(None, new NexlaMessage(m), MapOffset(1, 1, i, Map.empty[String, String]))
  }

  private val noopEarlyShutdownMonitor: SinkEarlyShutdownMonitor = () => Future.successful(false)

  def noopSinkFlusherFactory[T <: BasicSinkTask]: SinkFlusherFactory[T] = (_: T) => new SinkFlusher {

    def finalFlush(): Future[FlushingResults] = Future.successful(FlushingResults.Empty)

    def intermediateFlush(): Future[FlushingResults] = Future.successful(FlushingResults.Empty)
  }

  val noopMetricsOffsetReporter: MetricsOffsetReporter = (_: FlushingResults) => Future.unit

  describe("SinkFlow") {
    it("should call start method before data processing") {
      val startCalled = new AtomicBoolean(false)
      class DummySinkTask extends BasicSinkTask {
        def start(): Future[Unit] = {
          startCalled.set(true)
          Future.unit
        }

        def stop(): Future[Unit] = Future.unit

        def putData(messages: Seq[BasicMessage]): Future[Unit] = Future.unit

        def flushData(): Future[FlushingResults] = Future.successful(FlushingResults.Empty)
        def forceFlushData(): Future[FlushingResults] = flushData()
      }
      val inMemorySinkTask = new InMemorySinkTask("", new DummySinkTask, noopSinkFlusherFactory)
      val flow = new SinkFlow("", props, inMemorySinkTask, resourceState(), noopMetricsOffsetReporter, noopEarlyShutdownMonitor, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1), intermediateFlushDelay = 10.seconds, maxIntermediateFlushAwaitInterval = 0.seconds)
      flow.applySink()
      eventually {
        startCalled.get() shouldEqual true
      }
    }
    it("should call stop method after data processing") {
      val stopCalled = new AtomicBoolean(false)
      class DummySinkTask extends BasicSinkTask {
        def start(): Future[Unit] = Future.unit

        def stop(): Future[Unit] = {
          stopCalled.set(true)
          Future.unit
        }

        def putData(messages: Seq[BasicMessage]): Future[Unit] = Future.unit

        def flushData(): Future[FlushingResults] = Future.successful(FlushingResults.Empty)
        def forceFlushData(): Future[FlushingResults] = flushData()
      }
      val inMemorySinkTask = new InMemorySinkTask("", new DummySinkTask, noopSinkFlusherFactory)
      val flow = new SinkFlow("", props, inMemorySinkTask, resourceState(), noopMetricsOffsetReporter, noopEarlyShutdownMonitor, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1), intermediateFlushDelay = 10.seconds, maxIntermediateFlushAwaitInterval = 0.seconds)
      val _: SinkDone = Source.single(recordWithOffset(1)).viaMat(flow.applySink())(Keep.right).toMat(Sink.ignore)(Keep.left).run().futureValue(Timeout(5.seconds))
      stopCalled.get() shouldEqual true
    }

    it("should call stop method if starting task fails") {
      val stopCalled = new AtomicBoolean(false)
      class DummySinkTask extends BasicSinkTask {
        def start(): Future[Unit] = Future.failed(new Exception("something went wrong with starting the task"))

        def putData(messages: Seq[BasicMessage]): Future[Unit] = Future.unit

        def flushData(): Future[FlushingResults] = Future.successful(FlushingResults.Empty)
        def forceFlushData(): Future[FlushingResults] = flushData()

        def stop(): Future[Unit] = {
          stopCalled.set(true)
          Future.unit
        }
      }

      val inMemorySinkTask = new InMemorySinkTask("", new DummySinkTask, noopSinkFlusherFactory)
      val flow = new SinkFlow("", props, inMemorySinkTask, resourceState(), noopMetricsOffsetReporter, noopEarlyShutdownMonitor, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1), intermediateFlushDelay = 10.seconds, maxIntermediateFlushAwaitInterval = 0.seconds)
      Source.single(recordWithOffset(1)).viaMat(flow.applySink())(Keep.right).toMat(Sink.ignore)(Keep.left).run().failed.futureValue
      stopCalled.get() shouldEqual true
    }

    it("should fail stream if starting task fails") {
      class DummySinkTask extends BasicSinkTask {
        def start(): Future[Unit] = Future.failed(new Exception("something went wrong with starting the task"))

        def putData(messages: Seq[BasicMessage]): Future[Unit] = Future.unit

        def flushData(): Future[FlushingResults] = Future.successful(FlushingResults.Empty)
        def forceFlushData(): Future[FlushingResults] = flushData()

        def stop(): Future[Unit] = Future.unit
      }

      val inMemorySinkTask = new InMemorySinkTask("", new DummySinkTask, noopSinkFlusherFactory)
      val flow = new SinkFlow("", props, inMemorySinkTask, resourceState(), noopMetricsOffsetReporter, noopEarlyShutdownMonitor, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1), intermediateFlushDelay = 10.seconds, maxIntermediateFlushAwaitInterval = 0.seconds)
      val exception = Source.single(recordWithOffset(1)).via(flow.applySink()).run().failed.futureValue
      exception.getMessage shouldEqual "something went wrong with starting the task"
    }
    it("should not fail if stopping fails") {
      val stopCalled = new AtomicBoolean(false)
      class DummySinkTask extends BasicSinkTask {
        def start(): Future[Unit] = Future.unit

        def putData(messages: Seq[BasicMessage]): Future[Unit] = Future.unit

        def flushData(): Future[FlushingResults] = Future.successful(FlushingResults.Empty)
        def forceFlushData(): Future[FlushingResults] = flushData()

        def stop(): Future[Unit] = {
          stopCalled.set(true)
          Future.failed(new Exception("sth wrong"))
        }
      }
      val inMemorySinkTask = new InMemorySinkTask("", new DummySinkTask, noopSinkFlusherFactory)
      val flow = new SinkFlow("", props, inMemorySinkTask, resourceState(), noopMetricsOffsetReporter, noopEarlyShutdownMonitor, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1), intermediateFlushDelay = 10.seconds, maxIntermediateFlushAwaitInterval = 0.seconds)
      val _: SinkDone = Source.single(recordWithOffset(1)).viaMat(flow.applySink())(Keep.right).to(Sink.ignore).run().futureValue
      stopCalled.get() shouldEqual true
      succeed
    }

    it("mark sink as started as soon as the source pipeline is started, even if there's no data") {
      class DummySinkTask extends BasicSinkTask {
        def start(): Future[Unit] = Future.unit

        def putData(messages: Seq[BasicMessage]): Future[Unit] = Future.unit

        def flushData(): Future[FlushingResults] = Future.successful(FlushingResults.Empty)
        def forceFlushData(): Future[FlushingResults] = flushData()

        def stop(): Future[Unit] = Future.unit
      }

      val state = resourceState()
      val inMemorySinkTask = new InMemorySinkTask("", new DummySinkTask, noopSinkFlusherFactory)
      val flow = new SinkFlow("", props, inMemorySinkTask, state, noopMetricsOffsetReporter, noopEarlyShutdownMonitor, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1), intermediateFlushDelay = 10.seconds, maxIntermediateFlushAwaitInterval = 0.seconds)

      state.getStatus() shouldEqual PipelineTaskStateEnum.NotRunning
      val ks = Source.repeat(recordWithOffset(1)).viaMat(KillSwitches.single)(Keep.right).via(flow.applySink()).to(Sink.ignore).run()
      eventually {
        state.getStatus() shouldEqual PipelineTaskStateEnum.Running
      }
      ks.shutdown()
    }

    it("should mark sink as finished when pipeline finishes") {
      class DummySinkTask extends BasicSinkTask {
        def start(): Future[Unit] = Future.unit

        def putData(messages: Seq[BasicMessage]): Future[Unit] = Future.unit

        def flushData(): Future[FlushingResults] = Future.successful(FlushingResults.Empty)
        def forceFlushData(): Future[FlushingResults] = flushData()

        def stop(): Future[Unit] = Future.unit
      }

      val state = resourceState()
      val inMemorySinkTask = new InMemorySinkTask("", new DummySinkTask, noopSinkFlusherFactory)
      val flow = new SinkFlow("", props, inMemorySinkTask, state, noopMetricsOffsetReporter, noopEarlyShutdownMonitor, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1), intermediateFlushDelay = 10.seconds, maxIntermediateFlushAwaitInterval = 0.seconds)

      state.getStatus() shouldEqual PipelineTaskStateEnum.NotRunning
      val _: SinkDone = Source.single(recordWithOffset(1)).viaMat(flow.applySink())(Keep.right).to(Sink.ignore).run().futureValue
      state.getStatus() shouldEqual PipelineTaskStateEnum.Finished
    }

    it("should mark sink as failed when putData fails") {
      class DummySinkTask extends BasicSinkTask {
        def start(): Future[Unit] = Future.unit

        def putData(messages: Seq[BasicMessage]): Future[Unit] = Future.failed(new Exception("sth wrong"))

        def flushData(): Future[FlushingResults] = Future.successful(FlushingResults.Empty)
        def forceFlushData(): Future[FlushingResults] = flushData()

        def stop(): Future[Unit] = Future.unit
      }

      val state = resourceState()
      val inMemorySinkTask = new InMemorySinkTask("", new DummySinkTask, noopSinkFlusherFactory)
      val flow = new SinkFlow("", props, inMemorySinkTask, state, noopMetricsOffsetReporter, noopEarlyShutdownMonitor, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1), intermediateFlushDelay = 10.seconds, maxIntermediateFlushAwaitInterval = 0.seconds)

      state.getStatus() shouldEqual PipelineTaskStateEnum.NotRunning
      intercept[Exception] {
        val _: SinkDone = Source.single(recordWithOffset(1)).viaMat(flow.applySink())(Keep.right).to(Sink.ignore).run().futureValue
      }

      state.getStatus() shouldEqual PipelineTaskStateEnum.Failed
    }

    it("should NOT mark sink as failed when sink flusher intermediate flush fails") {
      class DummySinkTask extends BasicSinkTask {
        def start(): Future[Unit] = Future.unit

        def putData(messages: Seq[BasicMessage]): Future[Unit] = Future.unit

        def flushData(): Future[FlushingResults] = Future.successful(FlushingResults.Empty)
        def forceFlushData(): Future[FlushingResults] = flushData()

        def stop(): Future[Unit] = Future.unit
      }

      val flusherFactory: SinkFlusherFactory[DummySinkTask] = (_: DummySinkTask) => new SinkFlusher {

        def finalFlush(): Future[FlushingResults] = Future.successful(FlushingResults.Empty)

        def intermediateFlush(): Future[FlushingResults] = Future.failed(new Exception("sth wrong"))
      }

      val state = resourceState()
      val inMemorySinkTask = new InMemorySinkTask("", new DummySinkTask, flusherFactory)
      val flow = new SinkFlow("", props, inMemorySinkTask, state, noopMetricsOffsetReporter, noopEarlyShutdownMonitor, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1), intermediateFlushDelay = 0.seconds, maxIntermediateFlushAwaitInterval = 0.seconds)

      state.getStatus() shouldEqual PipelineTaskStateEnum.NotRunning
      val _: SinkDone = Source.single(recordWithOffset(1)).viaMat(flow.applySink())(Keep.right).to(Sink.ignore).run().futureValue

      state.getStatus() shouldEqual PipelineTaskStateEnum.Finished
    }
    it("should mark sink as failed when sink flusher final flush fails") {
      class DummySinkTask extends BasicSinkTask {
        def start(): Future[Unit] = Future.unit

        def putData(messages: Seq[BasicMessage]): Future[Unit] = Future.unit

        def flushData(): Future[FlushingResults] = Future.successful(FlushingResults.Empty)
        def forceFlushData(): Future[FlushingResults] = flushData()

        def stop(): Future[Unit] = Future.unit
      }

      val flusherFactory: SinkFlusherFactory[DummySinkTask] = (_: DummySinkTask) => new SinkFlusher {

        def finalFlush(): Future[FlushingResults] = Future.failed(new Exception("sth wrong"))

        def intermediateFlush(): Future[FlushingResults] = Future.successful(FlushingResults.Empty)
      }

      val state = resourceState()
      val inMemorySinkTask = new InMemorySinkTask("", new DummySinkTask, flusherFactory)
      val flow = new SinkFlow("", props, inMemorySinkTask, state, noopMetricsOffsetReporter, noopEarlyShutdownMonitor, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1), intermediateFlushDelay = 10.seconds, maxIntermediateFlushAwaitInterval = 0.seconds)

      state.getStatus() shouldEqual PipelineTaskStateEnum.NotRunning
      intercept[Exception] {
        val _: SinkDone = Source.single(recordWithOffset(1)).viaMat(flow.applySink())(Keep.right).to(Sink.ignore).run().futureValue
      }

      state.getStatus() shouldEqual PipelineTaskStateEnum.Failed
    }

    // mark state as failed when pipeline fails
  }

}