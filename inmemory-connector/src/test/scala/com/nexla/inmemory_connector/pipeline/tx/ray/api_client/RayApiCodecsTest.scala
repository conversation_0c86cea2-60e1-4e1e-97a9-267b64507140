package com.nexla.inmemory_connector.pipeline.tx.ray.api_client

import com.nexla.admin.client.{DataCredentials, ReferencedResourceIds}
import com.nexla.connector.config.file.AWSAuthConfig
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig.MezzanineAuthConfig
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiClient._
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiCodecs.RayApiMetadata
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers
import spray.json._

import scala.jdk.CollectionConverters.seqAsJavaListConverter

class RayApiCodecsTest extends AnyFunSuite with Matchers {

  private val metadata = RayApiMetadata(runId = 1, sourceId = Some(2), orgId = Some(3), dataSetId = Some(4), sinkId = Some(5), referencedResourceIds = None)
  private val codecs = new RayApiCodecs(metadata)

  test("jobIdDecoder should decode valid JSON with job_id") {
    val json = """{"job_id": "job-123"}""".parseJson
    codecs.jobIdDecoder.read(json) shouldEqual JobId("job-123")
  }

  test("should encode SubmitDirectoryJobRequest correctly") {
    val request = SubmitDirectoryJobRequest(
      privatePackages = Some(PrivatePackages("user1", "token123", List("url1", "url2"))),
      customCode = "base64Code",
      driverFunction = "main",
      packages = List("pkg1", "pkg2"),
      raySource = S3MezzanineRaySrc("bucket/input", MezzanineAuthConfig("bucket", new AWSAuthConfig(new java.util.HashMap(), 123), new DataCredentials())),
      rayDestination = S3MezzanineRayDst("bucket/output", MezzanineAuthConfig("bucket", new AWSAuthConfig(new java.util.HashMap(), 123), new DataCredentials())),
      extraData = None,
      entrypointScriptName = "entrypointScriptName1"
    )
    val encodedJson = codecs.submitDirectoryJobRequestEncoder.write(request)
    val expectedJson =
      """
        |{
        |    "custom_code": {
        |        "args": [],
        |        "base64_code": "base64Code",
        |        "driver_function": "main",
        |        "kwargs": {},
        |        "packages": [
        |            "pkg1",
        |            "pkg2"
        |        ],
        |        "pvt_packages": {
        |            "auth": {
        |                "token": "token123",
        |                "username": "user1"
        |            },
        |            "urls": [
        |                "url1",
        |                "url2"
        |            ]
        |        }
        |    },
        |    "destination": {
        |        "credentials": {
        |            "access_key": "",
        |            "bucket": "bucket",
        |            "secret_key": ""
        |        },
        |        "output_dir": "output",
        |        "type": "s3"
        |    },
        |    "job_config": {
        |        "entrypoint": {
        |            "script": "entrypointScriptName1"
        |        }
        |    },
        |    "metadata": {
        |        "dataset_id": 4,
        |        "org_id": 3,
        |        "run_id": 1,
        |        "source_id": 2,
        |        "sink_id": 5
        |    },
        |    "referenced_resource_ids": {
        |        "data_maps": [],
        |        "data_credentials": [],
        |        "data_sets": [],
        |        "code_containers": []
        |    },
        |    "source": {
        |        "credentials": {
        |            "access_key": "",
        |            "bucket": "bucket",
        |            "secret_key": ""
        |        },
        |        "input_dir": "input",
        |        "type": "s3"
        |    }
        |}
        |""".stripMargin
    encodedJson shouldEqual JsonParser(expectedJson)
  }

  test("should encode optimized SubmitDirectoryJobRequest correctly") {
    val request = SubmitDirectoryJobRequest(
      privatePackages = Some(PrivatePackages("user1", "token123", List("url1", "url2"))),
      customCode = "base64Code",
      driverFunction = "main",
      packages = List("pkg1", "pkg2"),
      raySource = BYOOptimizedFlowRaySrc,
      rayDestination = BYOOptimizedFlowRayDst,
      extraData = None,
      entrypointScriptName = "entrypointScriptName2",
    )
    val encodedJson = codecs.submitDirectoryJobRequestEncoder.write(request)
    val expectedJson =
      """
        |{
        |    "custom_code": {
        |        "args": [],
        |        "base64_code": "base64Code",
        |        "driver_function": "main",
        |        "kwargs": {},
        |        "packages": [
        |            "pkg1",
        |            "pkg2"
        |        ],
        |        "pvt_packages": {
        |            "auth": {
        |                "token": "token123",
        |                "username": "user1"
        |            },
        |            "urls": [
        |                "url1",
        |                "url2"
        |            ]
        |        }
        |    },
        |    "destination": {
        |        "type": "probe-http"
        |    },
        |    "job_config": {
        |        "entrypoint": {
        |            "script": "entrypointScriptName2"
        |        }
        |    },
        |    "referenced_resource_ids": {
        |        "data_maps": [],
        |        "data_credentials": [],
        |        "data_sets": [],
        |        "code_containers": []
        |    },
        |    "metadata": {
        |        "dataset_id": 4,
        |        "org_id": 3,
        |        "run_id": 1,
        |        "source_id": 2,
        |        "sink_id": 5
        |    },
        |    "source": {
        |        "type": "probe-http"
        |    }
        |}
        |""".stripMargin
    encodedJson shouldEqual JsonParser(expectedJson)
  }

  test("should merge extraData with priority into SubmitDirectoryJobRequest") {
    val request = SubmitDirectoryJobRequest(
      privatePackages = Some(PrivatePackages("user1", "token123", List("url1", "url2"))),
      customCode = "base64Code",
      driverFunction = "main",
      packages = List("pkg1", "pkg2"),
      raySource = S3MezzanineRaySrc("bucket/input", MezzanineAuthConfig("bucket", new AWSAuthConfig(new java.util.HashMap(), 123), new DataCredentials())),
      rayDestination = S3MezzanineRayDst("bucket/output", MezzanineAuthConfig("bucket", new AWSAuthConfig(new java.util.HashMap(), 123), new DataCredentials())),
      extraData = Some(JsObject("custom_code" -> JsObject("driver_function" -> JsString("process"), "kwargs" -> JsObject("a" -> JsNumber(1))), "job_config" -> JsObject("files_per_job" -> JsNumber(10)))),
      entrypointScriptName = "entrypointScriptName3",
    )
    val encodedJson = codecs.submitDirectoryJobRequestEncoder.write(request)
    val expectedJson =
      """
        |{
        |    "custom_code": {
        |        "args": [],
        |        "base64_code": "base64Code",
        |        "driver_function": "process",
        |        "kwargs": {
        |            "a": 1
        |        },
        |        "packages": [
        |            "pkg1",
        |            "pkg2"
        |        ],
        |        "pvt_packages": {
        |            "auth": {
        |                "token": "token123",
        |                "username": "user1"
        |            },
        |            "urls": [
        |                "url1",
        |                "url2"
        |            ]
        |        }
        |    },
        |    "destination": {
        |        "credentials": {
        |            "access_key": "",
        |            "bucket": "bucket",
        |            "secret_key": ""
        |        },
        |        "output_dir": "output",
        |        "type": "s3"
        |    },
        |    "job_config": {
        |        "entrypoint": {
        |            "script": "entrypointScriptName3"
        |        },
        |        "files_per_job": 10
        |    },
        |    "metadata": {
        |        "dataset_id": 4,
        |        "org_id": 3,
        |        "run_id": 1,
        |        "source_id": 2,
        |        "sink_id": 5
        |    },
        |    "referenced_resource_ids": {
        |        "data_maps": [],
        |        "data_credentials": [],
        |        "data_sets": [],
        |        "code_containers": []
        |    },
        |    "source": {
        |        "credentials": {
        |            "access_key": "",
        |            "bucket": "bucket",
        |            "secret_key": ""
        |        },
        |        "input_dir": "input",
        |        "type": "s3"
        |    }
        |}
        |""".stripMargin
    encodedJson shouldEqual JsonParser(expectedJson)
  }


  test("jobDetailsDecoder should decode valid JSON with job details") {
    val json = """{"status": "SUCCEEDED"}""".parseJson
    val expected = JobDetails(JobStatus.Succeeded)
    codecs.jobDetailsDecoder.read(json) shouldEqual expected
  }

  test("jobErrorDetailsDecoder should decode JSON with error reasons") {
    val json = """{"errors": [{"reason": "Error1"}, {"reason": "Error2"}]}""".parseJson
    val expected = JobErrorDetails(List("Error1", "Error2"))
    codecs.jobErrorDetailsDecoder.read(json) shouldEqual expected
  }

  test("directoryJobDetailsDecoder should decode valid JSON with directory job details") {
    val json = """{"status": "RUNNING", "batches": {"job-1": {}, "job-2": {}}}""".parseJson
    val expected = DirectoryJobDetails(JobStatus.Running, Set(JobId("job-1"), JobId("job-2")))
    codecs.directoryJobDetailsDecoder.read(json) shouldEqual expected
  }

  test("should encode referenced_resource_ids correctly") {
    val request = SubmitDirectoryJobRequest(
      privatePackages = None,
      customCode = "base64Code",
      driverFunction = "main",
      packages = List.empty,
      raySource = BYOOptimizedFlowRaySrc,
      rayDestination = BYOOptimizedFlowRayDst,
      extraData = None,
      entrypointScriptName = "entrypointScriptName4",
    )
    val referencedResourceIds = new ReferencedResourceIds(List(11, 12, 13).map(Int.box).asJava, List(21, 22).map(Int.box).asJava, List(31).map(Int.box).asJava, List(41, 42, 43, 44).map(Int.box).asJava)
    val metadata = RayApiMetadata(runId = 1, sourceId = Some(2), orgId = Some(3), dataSetId = Some(4), sinkId = Some(5), referencedResourceIds = Some(referencedResourceIds))
    val codecs = new RayApiCodecs(metadata)
    val encodedJson = codecs.submitDirectoryJobRequestEncoder.write(request)
    val expectedJson =
      """
        |{
        |    "custom_code": {
        |        "args": [],
        |        "base64_code": "base64Code",
        |        "driver_function": "main",
        |        "kwargs": {},
        |        "packages": [],
        |        "pvt_packages": null
        |    },
        |    "destination": {
        |        "type": "probe-http"
        |    },
        |    "job_config": {
        |        "entrypoint": {
        |            "script": "entrypointScriptName4"
        |        }
        |    },
        |    "referenced_resource_ids": {
        |        "data_maps": [11, 12, 13],
        |        "data_credentials": [21, 22],
        |        "data_sets": [31],
        |        "code_containers": [41, 42, 43, 44]
        |    },
        |    "metadata": {
        |        "dataset_id": 4,
        |        "org_id": 3,
        |        "run_id": 1,
        |        "source_id": 2,
        |        "sink_id": 5
        |    },
        |    "source": {
        |        "type": "probe-http"
        |    }
        |}
        |""".stripMargin
    encodedJson shouldEqual JsonParser(expectedJson)
  }
}
