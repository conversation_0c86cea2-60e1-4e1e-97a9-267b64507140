package com.nexla.inmemory_connector.pipeline.sink.flusher

import akka.actor.ActorSystem
import com.nexla.connect.common.postponedFlush.InMemoryProgressTracker
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode
import com.nexla.inmemory_connector.compat.{BasicMessage, FileOffset, MapOffset}
import com.nexla.inmemory_connector.pipeline.sink.flusher.SinkFlusher.FlushingResults
import com.nexla.inmemory_connector.pipeline.sink.flusher.sink_specials.JdbcSinkSpecials
import com.nexla.inmemory_connector.pipeline.sink.task.BasicSinkTask
import org.scalatest.concurrent.ScalaFutures.convertScalaFuture
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers

import java.util.concurrent.atomic.AtomicBoolean
import scala.concurrent.{ExecutionContext, Future}

class SinkFlusherForWarehouseJdbcSpec extends AnyFunSpec with Matchers {
  implicit val system: ActorSystem = ActorSystem()
  implicit val ec: ExecutionContext = system.dispatcher

  class DummyTask(offsetsToReturn: FlushingResults, flushDataCalled: AtomicBoolean) extends BasicSinkTask {
    val pt: InMemoryProgressTracker = new InMemoryProgressTracker

    def start(): Future[Unit] = Future.unit

    def putData(messages: Seq[BasicMessage]): Future[Unit] = Future.unit

    def flushData(): Future[FlushingResults] = {
      flushDataCalled.set(true)
      Future.successful(offsetsToReturn)
    }
    def forceFlushData(): Future[FlushingResults] = flushData()

    def stop(): Future[Unit] = Future.unit
  }

  val jdbcInsertSpecials: JdbcSinkSpecials[DummyTask] = new JdbcSinkSpecials[DummyTask] {
    def isWarehouse(task: DummyTask): Boolean = true

    def insertMode(task: DummyTask): JdbcSinkConnectorConfig.InsertMode = InsertMode.INSERT

    def progressTracker(task: DummyTask): InMemoryProgressTracker = task.pt
  }

  val jdbcUpsertSpecials: JdbcSinkSpecials[DummyTask] = new JdbcSinkSpecials[DummyTask] {
    def isWarehouse(task: DummyTask): Boolean = true

    def insertMode(task: DummyTask): JdbcSinkConnectorConfig.InsertMode = InsertMode.UPSERT

    def progressTracker(task: DummyTask): InMemoryProgressTracker = task.pt
  }

  val readyToCommitOffsets: FlushingResults = FlushingResults(List(FileOffset(123L, "file123", 0, 1L, 1, eof = false, datasetId = None, message = None), MapOffset(1, 2, 3, Map.empty)), Nil)

  describe("SinkFlusher for warehouse JDBC in INSERT mode") {

    it("should flush data and provide offsets to flush on intermediate flush") {
      val flushDataCalled = new AtomicBoolean(false)
      implicit val jdbcSpecials: JdbcSinkSpecials[DummyTask] = jdbcInsertSpecials
      val factory: SinkFlusherFactory[DummyTask] = SinkFlusherFactory.forJdbc[DummyTask]
      val task = new DummyTask(readyToCommitOffsets, flushDataCalled)
      val sinkFlusher = factory.apply(task)

      sinkFlusher.intermediateFlush().futureValue shouldEqual readyToCommitOffsets
      flushDataCalled.get() shouldEqual true
    }

    it("should flush data and provide offsets to flush on final flush") {
      val flushDataCalled = new AtomicBoolean(false)
      implicit val jdbcSpecials: JdbcSinkSpecials[DummyTask] = jdbcInsertSpecials
      val factory: SinkFlusherFactory[DummyTask] = SinkFlusherFactory.forJdbc[DummyTask]
      val task = new DummyTask(readyToCommitOffsets, flushDataCalled)
      val sinkFlusher = factory.apply(task)

      sinkFlusher.finalFlush().futureValue shouldEqual readyToCommitOffsets
      flushDataCalled.get() shouldEqual true
    }

  }

  describe("SinkFlusher for warehouse JDBC in UPSERT mode") {

    it("should flush data but NOT provide offsets to flush on intermediate flush") {
      val flushDataCalled = new AtomicBoolean(false)

      implicit val jdbcSpecials: JdbcSinkSpecials[DummyTask] = jdbcUpsertSpecials
      val factory: SinkFlusherFactory[DummyTask] = SinkFlusherFactory.forJdbc[DummyTask]
      val task = new DummyTask(readyToCommitOffsets, flushDataCalled)
      val sinkFlusher = factory.apply(task)

      sinkFlusher.intermediateFlush().futureValue shouldEqual FlushingResults.Empty
      flushDataCalled.get() shouldEqual true
    }

    it("should flush data and offset on final flush") {
      val flushDataCalled = new AtomicBoolean(false)

      implicit val jdbcSpecials: JdbcSinkSpecials[DummyTask] = jdbcUpsertSpecials
      val factory: SinkFlusherFactory[DummyTask] = SinkFlusherFactory.forJdbc[DummyTask]
      val task = new DummyTask(readyToCommitOffsets, flushDataCalled)
      val sinkFlusher = factory.apply(task)

      sinkFlusher.finalFlush().futureValue shouldEqual readyToCommitOffsets
      flushDataCalled.get() shouldEqual true
    }
  }
}
