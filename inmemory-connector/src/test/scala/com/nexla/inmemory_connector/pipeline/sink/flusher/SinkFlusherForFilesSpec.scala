package com.nexla.inmemory_connector.pipeline.sink.flusher

import akka.actor.ActorSystem
import com.nexla.connect.common.{PostponedFlush, ReadyToFlush}
import com.nexla.inmemory_connector.compat.{BasicMessage, FileOffset, MapOffset}
import com.nexla.inmemory_connector.pipeline.sink.flusher.SinkFlusher.FlushingResults
import com.nexla.inmemory_connector.pipeline.sink.task.BasicSinkTask
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures.convertScalaFuture
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers

import java.util.concurrent.atomic.AtomicBoolean
import scala.concurrent.{ExecutionContext, Future}

class SinkFlusherForFilesSpec extends AnyFunSpec with Matchers {
  implicit val system: ActorSystem = ActorSystem()
  implicit val ec: ExecutionContext = system.dispatcher

  class DummyTask(offsetsToReturn: FlushingResults, flushDataCalled: AtomicBoolean, pf: PostponedFlush) extends BasicSinkTask {
    val postponedFlush: PostponedFlush = pf

    def start(): Future[Unit] = Future.unit

    def putData(messages: Seq[BasicMessage]): Future[Unit] = Future.unit

    def flushData(): Future[FlushingResults] = {
      flushDataCalled.set(true)
      Future.successful(offsetsToReturn)
    }

    def forceFlushData(): Future[FlushingResults] = flushData()

    def stop(): Future[Unit] = Future.unit
  }

  val readyToCommitOffsets: FlushingResults = FlushingResults(List(FileOffset(123L, "file123", 0, 1L, 1, eof = false, datasetId = None, message = None), MapOffset(1, 2, 3, Map.empty)), Nil)

  describe("SinkFlusher for files") {

    it("should flush data and provide offsets to flush on final flush if postponed flush reports true") {
      val flushDataCalled = new AtomicBoolean(false)

      val pf = Mockito.mock(classOf[PostponedFlush])
      val factory: SinkFlusherFactory[DummyTask] = SinkFlusherFactory.forFile[DummyTask]
      val task = new DummyTask(readyToCommitOffsets , flushDataCalled, pf)
      val sinkFlusher = factory.apply(task)

      Mockito.when(pf.readyToFlush()).thenReturn(ReadyToFlush.FINAL_FLUSH)
      pf.readyToFlush().flush shouldEqual true
      sinkFlusher.finalFlush().futureValue shouldEqual readyToCommitOffsets
      flushDataCalled.get() shouldEqual true
    }

    it("should flush data and provide offsets to flush on final flush even if postponed flush reports false") {
      val flushDataCalled = new AtomicBoolean(false)

      val pf = Mockito.mock(classOf[PostponedFlush])
      val factory: SinkFlusherFactory[DummyTask] = SinkFlusherFactory.forFile[DummyTask]
      val task = new DummyTask(readyToCommitOffsets , flushDataCalled, pf)
      val sinkFlusher = factory.apply(task)

      Mockito.when(pf.readyToFlush()).thenReturn(ReadyToFlush.NOT_READY)
      pf.readyToFlush().flush shouldEqual false
      sinkFlusher.finalFlush().futureValue shouldEqual readyToCommitOffsets
      flushDataCalled.get() shouldEqual true
    }

    it("should flush data and provide offsets to flush on intermediate flush if postponed flush reports true") {
      val flushDataCalled = new AtomicBoolean(false)

      val pf = Mockito.mock(classOf[PostponedFlush])
      val factory: SinkFlusherFactory[DummyTask] = SinkFlusherFactory.forFile[DummyTask]
      val task = new DummyTask(readyToCommitOffsets , flushDataCalled, pf)
      val sinkFlusher = factory.apply(task)

      Mockito.when(pf.readyToFlush()).thenReturn(ReadyToFlush.FINAL_FLUSH)
      pf.readyToFlush().flush shouldEqual true
      sinkFlusher.intermediateFlush().futureValue shouldEqual readyToCommitOffsets
      flushDataCalled.get() shouldEqual true
    }

  }

}