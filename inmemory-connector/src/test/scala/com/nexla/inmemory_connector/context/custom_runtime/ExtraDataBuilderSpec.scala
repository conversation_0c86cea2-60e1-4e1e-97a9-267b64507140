package com.nexla.inmemory_connector.context.custom_runtime

import com.nexla.inmemory_connector.context.custom_runtime.ExtraDataBuilder.PrivatePackages
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import spray.json._

class ExtraDataBuilderSpec extends AnyFlatSpec with Matchers {

  "ExtraDataBuilder" should "extract private packages correctly" in {
    val json = """{
      "custom_code": {
        "pvt_packages": {
          "credential_id": 12345,
          "urls": ["http://example.com"]
        }
      }
    }""".parseJson

    val builder = new ExtraDataBuilder
    val result = builder.parse(json)

    result.isSuccess shouldBe true
    result.get.privatePackage shouldBe Some(PrivatePackages(12345, List("http://example.com")))
  }

  it should "return None if private packages are not present" in {
    val json = """{
      "custom_code": {}
    }""".parseJson

    val builder = new ExtraDataBuilder
    val result = builder.parse(json)

    result.isSuccess shouldBe true
    result.get.privatePackage shouldBe None
  }

  it should "remove private packages from the JSON" in {
    val json = """{
      "custom_code": {
        "pvt_packages": {
          "credential_id": 12345,
          "urls": ["http://example.com"]
        },
        "other_field": "value"
      },
      "job_config": {
        "files_per_job": 10
      }
    }""".parseJson

    val builder = new ExtraDataBuilder
    val result = builder.parse(json)

    result.get.extraData shouldBe """{
      "custom_code": {
        "other_field": "value"
      },
      "job_config": {
        "files_per_job": 10
      }
    }""".parseJson
  }

  it should "return the original JSON if private packages are not present" in {
    val json = """{
      "custom_code": {
        "other_field": "value"
      }
    }""".parseJson

    val builder = new ExtraDataBuilder
    val result = builder.parse(json)

    result.get.extraData shouldBe json
  }

  it should "parse the extra data correctly" in {
    val json = """{
      "custom_code": {
        "pvt_packages": {
          "credential_id": 12345,
          "urls": ["http://example.com"]
        },
        "other_field": "value"
      }
    }""".parseJson

    val builder = new ExtraDataBuilder
    val result = builder.parse(json)

    result.isSuccess shouldBe true
    val extraData = result.get
    extraData.privatePackage shouldBe Some(PrivatePackages(12345, List("http://example.com")))
    extraData.extraData shouldBe """{
      "custom_code": {
        "other_field": "value"
      }
    }""".parseJson
  }
}
