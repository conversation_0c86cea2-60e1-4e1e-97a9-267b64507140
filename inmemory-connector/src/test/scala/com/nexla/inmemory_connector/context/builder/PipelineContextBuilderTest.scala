package com.nexla.inmemory_connector.context.builder

import com.bazaarvoice.jolt.JsonUtils
import com.nexla.admin.client.flownode._
import com.nexla.admin.client.{AdminApiClient, DataCredentials, DataSink, DataSource, ResourceStatus}
import com.nexla.common.ConnectionType
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.connector.config.{FlowType, IngestionMode}
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.monitoring.DirectExecutionContext
import com.nexla.listing.client.AdaptiveFlowTask
import org.mockito.Mockito.{mock, when}
import org.scalatest.TagAnnotation
import org.scalatest.funsuite.AnyFunSuite

import java.util
import java.util.{Collections, Optional}
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext, Future}

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class PipelineContextBuilderTest extends AnyFunSuite {
// ensure the built context depends on 2 factors
// 1. the info we've got from admin api
  // 2. the config we've got from the adaptive flow task
  test("enrichment of the configs must be done for adaptive flow") {
    val flowId = 1
    val runId = 1L
    val adminApi = mock(classOf[AdminApiClient])
    val stubCreds = new DataCredentials
    stubCreds.setId(999)
    stubCreds.setCredentialsType(ConnectionType.MONGO)

    val ds = new DataSource()
    ds.setId(1)
    ds.setConnectionType(ConnectionType.MONGO)
    ds.setDataCredentials(stubCreds)
    val srcCfg = new util.HashMap[String, AnyRef]()
    srcCfg.put("adaptive.flow", "true")
    ds.setSourceConfig(srcCfg)

    val dataSink = new DataSink()
    dataSink.setId(1)
    dataSink.setConnectionType(ConnectionType.MONGO)
    dataSink.setDataCredentials(stubCreds)
    val sinkCfg = new util.HashMap[String, AnyRef]()
    dataSink.setSinkConfig(sinkCfg)

    val creds100 = new DataCredentials
    creds100.setId(100)
    creds100.setCredentialsType(ConnectionType.S3_ICEBERG)
    creds100.setCredentialsEnc("enc")
    creds100.setCredentialsEncIv("encIv")
    val creds101 = new DataCredentials
    creds101.setId(101)

    when(adminApi.getDataCredentials(100)).thenReturn(Optional.of(creds100))
    when(adminApi.getDataCredentials(101)).thenReturn(Optional.of(creds101))

    val taskDef: AdaptiveFlowTask = JsonUtils.streamToType(getClass.getClassLoader.getResourceAsStream("./adaptiveTaskDef.json"), classOf[AdaptiveFlowTask])
    when(adminApi.getDataSource(1)).thenReturn(Optional.of(ds))
    when(adminApi.getNexlaFlow(1)).thenReturn(Optional.of(
      new NexlaFlow(
        Collections.singletonList(new AdminApiFlow(1, 1, 1, 1, ResourceStatus.ACTIVE, FlowType.IN_MEMORY, IngestionMode.FULL_INGESTION, Collections.emptyList[FlowNodeElement])),
        Collections.singletonList(new FlowNodeDatasource(1, 2, 3, 4, 1, "", "", ResourceStatus.ACTIVE, Collections.emptyList[String], 7, 8, 9, ConnectionType.MONGO, ConnectionType.MONGO, ConnectionType.MONGO)),
        Collections.emptyList[FlowNodeDataset],
        Collections.singletonList(new FlowNodeDatasink(1, 2, 3, 4, 1, "", "", ResourceStatus.ACTIVE, Collections.emptyList[String], 7, 8, 9, 10, ConnectionType.MONGO, ConnectionType.MONGO, ConnectionType.MONGO)),
      )
    ))
    when(adminApi.getDataSink(1)).thenReturn(Optional.of(dataSink))

    val props: AppProps = mock(classOf[AppProps])// Mocked AppProps
    when(props.defaultFileHeartbeatBatchSize).thenReturn(10)
    val maybeAdaptiveFlowTask = Option(taskDef) // Mocked AdaptiveFlowTask
    val nexlaMessageProducer = mock(classOf[NexlaMessageProducer]) // Mocked NexlaMessageProducer

    implicit val ec: ExecutionContext = DirectExecutionContext

    val pipelineContextBuilder = new PipelineContextBuilder(flowId, runId, adminApi, props, maybeAdaptiveFlowTask, nexlaMessageProducer)(ec)

    // Call the method to be tested
    val result = pipelineContextBuilder.createPipelineContext()

    // Assertions to verify the expected behavior
    assert(result.isInstanceOf[Future[_]])
    val result2 = Await.result(result, Duration.Inf)
    assert(result2.dataSource.getSourceConfig.get("adaptive.flow") == "true") // this must be still there
    assert(result2.dataSource.getSourceConfig.get("another_var") == "active") // this must be enriched and pushed in
  }
}
