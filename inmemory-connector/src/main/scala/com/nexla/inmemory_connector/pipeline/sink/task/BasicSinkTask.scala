package com.nexla.inmemory_connector.pipeline.sink.task

import com.nexla.inmemory_connector.compat.BasicMessage
import com.nexla.inmemory_connector.pipeline.sink.flusher.SinkFlusher.FlushingResults

import scala.concurrent.Future

trait BasicSinkTask {
  def start(): Future[Unit]

  def putData(messages: Seq[BasicMessage]): Future[Unit]

  def flushData(): Future[FlushingResults]

  def forceFlushData(): Future[FlushingResults]

  def stop(): Future[Unit]
}
