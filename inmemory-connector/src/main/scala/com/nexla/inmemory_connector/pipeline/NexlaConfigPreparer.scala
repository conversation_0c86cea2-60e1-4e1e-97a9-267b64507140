package com.nexla.inmemory_connector.pipeline

import com.nexla.admin.client.{AdminApiClient, DataSink, DataSource}
import com.nexla.admin.config.ConfigUtils
import com.nexla.connector.config.{BaseConnectorConfig, NexlaConfigDef}
import com.nexla.inmemory_connector.AppProps
import com.nexla.sc.config.ConfigEnricher
import com.nexla.sc.util.Async

import java.util
import scala.concurrent.Future

class NexlaConfigPreparer(props: AppProps, adminApiClient: AdminApiClient, runId: Long) {
  private val configEnricher = new ConfigEnricher {}
  private val taskEc = Async.ioExecutorContext

  def prepareSinkConnectorConfig(dataSink: DataSink, configDef: NexlaConfigDef): Future[util.Map[String, String]] = Future {
    val resultConfig = configEnricher.fullDataSinkConfig(props.config, dataSink, props.enrichSinkParams, configDef)
    ConfigUtils.enrichWithDataCredentials(adminApiClient, resultConfig)
    configEnricher.enrichWithRunIdOverride(resultConfig, runId)
    resultConfig.put(BaseConnectorConfig.FAST_MODE, "true")
    resultConfig
  }(taskEc)

  def prepareSourceConnectorConfig(dataSource: DataSource, configDef: NexlaConfigDef): Future[util.Map[String, String]] = Future {
    val resultConfig = configEnricher.fullDataSourceConfig(props.config, dataSource, props.enrichSourceParams, configDef)
    ConfigUtils.enrichWithDataCredentials(adminApiClient, resultConfig)
    configEnricher.enrichWithRunIdOverride(resultConfig, runId)
    resultConfig.put(BaseConnectorConfig.FAST_MODE, "true")
    resultConfig
  }(taskEc)
}

