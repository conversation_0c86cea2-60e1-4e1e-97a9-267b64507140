package com.nexla.inmemory_connector.pipeline

import akka.stream.Attributes.name
import akka.stream.stage.{GraphStage, GraphStageLogic, InHandler, OutHandler}
import akka.stream.{Attributes, Inlet, Outlet, UniformFanOutShape}
import com.nexla.common.NexlaMessage
import com.nexla.inmemory_connector.compat.RecordWithOffset

import java.util
import java.util.Collections

object CloningBroadcast {

  /**
   * Create a new `CloningBroadcast` with the specified number of output ports.
   *
   * @param outputPorts number of output ports
   * @param eagerCancel if true, broadcast cancels upstream if any of its downstreams cancel.
   */
  def apply[T](outputPorts: Int, eagerCancel: Boolean = false): CloningBroadcast[T] =
    new CloningBroadcast(outputPorts, eagerCancel)

}

/**
 * Fan-out the stream to several streams, COPYING OVER A NEW ELEMENT AND emitting A SEPARATE COPY OF IT to all downstream consumers.
 * It will not shut down until the subscriptions for at least two downstream subscribers have been established.
 *
 * '''Emits when''' all of the outputs stops backpressuring and there is an input element available
 *
 * '''Backpressures when''' any of the outputs backpressure
 *
 * '''Completes when''' upstream completes
 *
 * '''Cancels when'''
 *   If eagerCancel is enabled: when any downstream cancels; otherwise: when all downstreams cancel
 *
 */
final class CloningBroadcast[T](val outputPorts: Int, val eagerCancel: Boolean) extends GraphStage[UniformFanOutShape[T, T]] {
  // one output might seem counter intuitive but saves us from special handling in other places
  require(outputPorts >= 1, "A CloningBroadcast must have one or more output ports")
  val in: Inlet[T] = Inlet[T]("CloningBroadcast.in")
  val out: scala.collection.immutable.IndexedSeq[Outlet[T]] = Vector.tabulate(outputPorts)(i => Outlet[T]("CloningBroadcast.out" + i))

  override def initialAttributes: Attributes = name("cloningBroadcast")

  override val shape: UniformFanOutShape[T, T] = UniformFanOutShape(in, out: _*)

  override def createLogic(inheritedAttributes: Attributes): GraphStageLogic =
    new GraphStageLogic(shape) with InHandler {
      private var pendingCount = outputPorts
      private val pending = Array.fill[Boolean](outputPorts)(true)
      private var downstreamsRunning = outputPorts

      private val copiesOfOrig: util.List[T] = Collections.synchronizedList(new util.ArrayList[T](outputPorts))

      def onPush(): Unit = {
        pendingCount = downstreamsRunning
        val originalElem = grab(in)
        // here, we must do X copies of this message before pushing it further
        for (_ <- out.indices) {
          val newElem = originalElem.asInstanceOf[Seq[T]].head match {
            case rwo: RecordWithOffset =>
              //              println(s"OLD MSG: ${rwo.message.get}")
              val msg = rwo.message
              val newMsg = new NexlaMessage()
              newMsg.setRawMessage(msg.getRawMessage) // shallow copy is ok for us here

              Option(msg.getNexlaMetaData).foreach { md =>
                newMsg.setNexlaMetaData(md.deepCopyMetadata()) // but not here, as metadata is stateful
              }


              val newRwo = rwo.copy(message = newMsg)
              //              println(s"NEW MSG: ${newRwo.message.get}")
              Seq(newRwo)
            case _ =>
              Seq(originalElem)
          }

          copiesOfOrig.add(newElem.asInstanceOf[T])
        }

        val size = out.size
        var idx = 0
        while (idx < size) {
          val o = out(idx)
          if (!isClosed(o)) {
            push(o, copiesOfOrig.get(idx))
            pending(idx) = true
          }
          idx += 1
        }
      }

      setHandler(in, this)

      private def tryPull(): Unit =
        if (pendingCount == 0 && !hasBeenPulled(in)) pull(in)

      {
        val size = out.size
        var idx = 0
        while (idx < size) {
          val o = out(idx)
          val i = idx // close over val
          setHandler(
            o,
            new OutHandler {
              override def onPull(): Unit = {
                pending(i) = false
                pendingCount -= 1
                tryPull()
              }

              override def onDownstreamFinish(cause: Throwable): Unit = {
                if (eagerCancel) cancelStage(cause)
                else {
                  downstreamsRunning -= 1
                  if (downstreamsRunning == 0) cancelStage(cause)
                  else if (pending(i)) {
                    pending(i) = false
                    pendingCount -= 1
                    tryPull()
                  }
                }
              }
            })
          idx += 1
        }
      }

    }

  override def toString = "CloningBroadcast"

}
