package com.nexla.inmemory_connector.pipeline.sink.flusher.sink_specials

import com.nexla.connect.common.postponedFlush.InMemoryProgressTracker
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode
import com.nexla.connector.sql.sink.JdbcSinkTask
import com.nexla.inmemory_connector.pipeline.sink.task.KafkaConnectSinkTask

trait JdbcSinkSpecials[-T] {
  def isWarehouse(task: T): <PERSON><PERSON>an

  def insertMode(task: T): InsertMode

  def progressTracker(task: T): InMemoryProgressTracker
}

object JdbcSinkSpecials {
  implicit class JdbcSpecialsOps[T: JdbcSinkSpecials](task: T) {
    val wrapper: JdbcSinkSpecials[T] = implicitly

    def isWarehouse(): Boolean = wrapper.isWarehouse(task)

    def insertMode(): InsertMode = wrapper.insertMode(task)

    def progressTracker(): InMemoryProgressTracker = wrapper.progressTracker(task)
  }

  def jdbcSinkTaskJdbcWithInsertMode(pt: InMemoryProgressTracker): JdbcSinkSpecials[KafkaConnectSinkTask[JdbcSinkTask]] = new JdbcSinkSpecials[KafkaConnectSinkTask[JdbcSinkTask]] {
    def isWarehouse(task: KafkaConnectSinkTask[JdbcSinkTask]): Boolean = task.underlying.isCopyMode
    def insertMode(task: KafkaConnectSinkTask[JdbcSinkTask]): InsertMode = task.underlying.config.insertMode
    def progressTracker(task: KafkaConnectSinkTask[JdbcSinkTask]): InMemoryProgressTracker = pt
  }
}
