package com.nexla.inmemory_connector.pipeline.tx.ray.api_client

import akka.actor.ActorSystem
import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport._
import akka.http.scaladsl.marshalling.Marshal
import akka.http.scaladsl.model.{RequestEntity, Uri}
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiClient._
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiCodecs.RayApiMetadata
import com.nexla.sc.config.NexlaCredsConf
import com.nexla.sc.util.{BaseHttpClient, NexlaBasicAuth}
import spray.json.JsValue

import scala.concurrent.{ExecutionContext, Future}

class RayApiClientImpl(rayClusterUrl: String, val nexlaCreds: NexlaCredsConf, rayApiMetadata: RayApiMetadata)(implicit ec: ExecutionContext, system: ActorSystem) extends RayApiClient with BaseHttpClient with NexlaBasicAuth {
  private val rayApiCodecs = new RayApiCodecs(rayApiMetadata)

  def submitJobV2(submitJobRequest: SubmitDirectoryJobRequest): Future[JobId] = {
    val requestJson: JsValue = rayApiCodecs.submitDirectoryJobRequestEncoder.write(submitJobRequest)
    logger.info(s"Submitting job for ray V2 with body: ${requestJson.compactPrint}")
    for {
      entity <- Marshal[JsValue](requestJson).to[RequestEntity]
      url = s"$rayClusterUrl/v2/submit-job"
      httpResponse <- post(Uri(url), Seq(basicAuthHeader, acceptJson), entity)
      response <- handle[JsValue](url, Some(requestJson.compactPrint), httpResponse, "/v2/submit-job")
      jobId <- Future(rayApiCodecs.jobIdDecoder.read(response))
    } yield jobId
  }

  def fetchJobDetails(jobId: JobId): Future[JobDetails] = {
    val url = s"$rayClusterUrl/job/details/${jobId.id}"
    for {
      httpResponse <- get(Uri(url), Seq(basicAuthHeader, acceptJson))
      response <- handle[JsValue](url, None, httpResponse, s"/job/details/${jobId.id}")
      jobId <- Future(rayApiCodecs.jobDetailsDecoder.read(response))
    } yield jobId
  }

  def fetchErrorsDetails(jobId: JobId): Future[JobErrorDetails] = {
    val url = s"$rayClusterUrl/job/failed-files/${jobId.id}"
    for {
      httpResponse <- get(Uri(url), Seq(basicAuthHeader, acceptJson))
      response <- handle[JsValue](url, None, httpResponse, s"/job/failed-files/${jobId.id}")
      jobErrorDetails <- Future(rayApiCodecs.jobErrorDetailsDecoder.read(response))
    } yield jobErrorDetails
  }

  def fetchDirectoryJobDetails(jobId: JobId): Future[DirectoryJobDetails] = {
    val url = s"$rayClusterUrl/job/details/${jobId.id}"
    for {
      httpResponse <- get(Uri(url), Seq(basicAuthHeader, acceptJson))
      response <- handle[JsValue](url, None, httpResponse, s"/job/details/${jobId.id}")
      jobId <- Future(rayApiCodecs.directoryJobDetailsDecoder.read(response))
    } yield jobId
  }

  def terminateJob(jobId: JobId): Future[JobTerminationDetails] = {
    val url = s"$rayClusterUrl/job/stop/${jobId.id}"
    for {
      httpResponse <- put(Uri(url), Seq(basicAuthHeader, acceptJson))
      response <- handle[JsValue](url, None, httpResponse, s"/job/stop/${jobId.id}")
    } yield JobTerminationDetails(response)
  }

}
