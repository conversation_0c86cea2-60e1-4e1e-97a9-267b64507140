package com.nexla.inmemory_connector.pipeline.tx

import akka.actor.{ActorSystem, Scheduler}
import cats.implicits.{catsSyntaxTuple2Semigroupal, toTraverseOps}
import com.bazaarvoice.jolt.JsonUtils.toJsonString
import com.nexla.admin.client.{DataSet, NexlaSchema}
import com.nexla.common.NotificationUtils.getStacktraceForDb
import com.nexla.common._
import com.nexla.common.exception.NexlaErrorMessage
import com.nexla.common.metrics.MetricWithErrors
import com.nexla.connect.common.transformation.TransformMetricsScheduleCallback
import com.nexla.connector.config.FlowType
import com.nexla.inmemory_connector.compat.{BasicMessage, JustOffset, RecordWithOffset}
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryMessageProducer
import com.nexla.inmemory_connector.{AppProps, LogPrefix}
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.nexla.sc.util.{Async, StrictNexlaLogging}
import com.nexla.transform.scripts.EvaluatorMetadata
import com.nexla.transform.{TransformService, Transformer, TransformerResult}

import java.util.concurrent.Executors
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future, TimeoutException}
import scala.jdk.CollectionConverters.iterableAsScalaIterableConverter
import scala.util.{Failure, Success, Try}

class NexlaTxTask(props: AppProps, runId: Long, flowType: FlowType, txService: TransformService, dataSet: DataSet, messageProducer: InMemoryMessageProducer, recordTimeout: FiniteDuration)(implicit system: ActorSystem, ec: ExecutionContext) extends TxTask with StrictNexlaLogging {
  override def logPrefix: Option[String] = Some(LogPrefix.forTx(dataSet))
  private implicit val sch: Scheduler = system.scheduler

  private val taskEc = Async.ioExecutorContext
  private var metricsAccumulator: NexlaScheduledAccumulator[java.util.Map[java.lang.Long, MetricWithErrors]] = _

  private val outputValidationSchema: Option[NexlaSchema] = {
    val schemaValidationEnabled = Option(dataSet.getOutputSchemaValidationEnabled).map(_.booleanValue()).getOrElse(props.validateOutputSchema)
    if (schemaValidationEnabled) {
      Option(dataSet.getOutputValidationSchema).filter(s => Option(s.getSchema).isDefined)
    } else None
  }

  override def start(): Future[Unit] = {
    val callback = new TransformMetricsScheduleCallback(NexlaNamingUtils.nameDataSetTopic(dataSet.getId), dataSet, messageProducer, flowType)

    metricsAccumulator = new NexlaScheduledAccumulator[java.util.Map[java.lang.Long, MetricWithErrors]](callback.onAggregate _, callback.onSchedule _, props.metricsWindowMs, Executors.newScheduledThreadPool(2))
    Future.unit
  }

  override def transform(msg: BasicMessage): Future[Try[List[BasicMessage]]] = msg match {
    case t: RecordWithOffset => doTransform(t).transformWith(t => Future.successful(t))
    case other => Future.successful(Success(List(other)))
  }

  private def doTransform(originalRecord: RecordWithOffset): Future[List[BasicMessage]] = {
    val msg = originalRecord.message
    val transformedMessagesF = for {
      results <- processMessageWithTimeout(msg)
      errors = results.filterNot(_.isSuccess)
      successes = results.filter(_.isSuccess)
      _ <- handleErrors(errors, msg)
      successfulResults = handleSuccesses(successes, msg)
      _ = accumulateMetrics(successfulResults, errors)
    } yield successfulResults.map(transformedMessage => originalRecord.copy(message = transformedMessage))

    transformedMessagesF.map { transformedMessages =>
      if (transformedMessages.nonEmpty) transformedMessages
      else List(JustOffset(originalRecord.headDatasetId, originalRecord.offset))
    }
  }

  private def processMessageWithTimeout(msg: NexlaMessage) = {
    Future{
      EvaluatorMetadata.Holder.put(EvaluatorMetadata.builder().build())
      try {
        // TODO this should probably be cached and reused across multiple transforms!
        val transformer = new Transformer(dataSet.getTransformHistory.getTransforms)
        txService.nexlaTransform(msg, transformer, outputValidationSchema.orNull)
      } finally {
        EvaluatorMetadata.Holder.clear()
      }
    }(taskEc).timeout(recordTimeout).map(_.asScala.toList).transformWith {
      case Failure(e: TimeoutException) =>
        logger.error(s"Message processing timed out after $recordTimeout")
        Future.successful(List(new TransformerResult(e)))
      case Failure(e) =>
        logger.error("Message processing failed", e)
        Future.successful(List(new TransformerResult(e)))
      case Success(v) => Future.successful(v)
    }
  }

  private def accumulateMetrics(successfulResults: List[NexlaMessage], failedRecords: List[TransformerResult]): Unit = {
    val records = successfulResults.length
    val bytes = successfulResults.map(m => MetricUtils.calcBytes(m.getRawMessage) + toJsonString(m.getNexlaMetaData).length).sum
    val errors = failedRecords.length
    val metricWithErrors = new java.util.HashMap[java.lang.Long, MetricWithErrors] {
      {
        put(runId, new MetricWithErrors(records, bytes, errors))
      }
    }
    metricsAccumulator.accumulate(metricWithErrors)
  }

  private def handleErrors(erroredResults: List[TransformerResult], inputMessage: NexlaMessage): Future[Unit] = {
    erroredResults.traverse(r => sendTransformErrorNotifications(r, inputMessage)).map(_ => ())
  }

  private def handleSuccesses(successfulResults: List[TransformerResult], inputMessage: NexlaMessage): List[NexlaMessage] = {
    successfulResults.filter(_.getOutput != null)
      .filterNot(_.getOutput.asInstanceOf[java.util.Map[String, java.lang.Object]].isEmpty)
      .map(result => new java.util.LinkedHashMap[String, AnyRef](result.getOutput.asInstanceOf[java.util.Map[String, Object]]))
      .map(output => new NexlaMessage(output, inputMessage.getNexlaMetaData))
  }

  private def sendTransformErrorNotifications(transformerResult: TransformerResult, inputMessage: NexlaMessage): Future[Unit] = {
    val resource = Resource.dataSet(dataSet.getId)
    val quarantineWriteTopic = s"${NexlaConstants.TRANSFORM_QUARANTINE_PREFIX}${NexlaNamingUtils.nameDataSetTopic(dataSet.getId)}"

    getErrorMessage(transformerResult).traverse { error =>
      logger.error(s"Error during transformation: ${error.toString}")
      val sendFirstEvent = if (transformerResult.hasTransformErrors) {
        Future(messageProducer.publishTransformError(resource, runId, transformerResult.getErrorMessage, transformerResult.getErrors))
      } else if (transformerResult.hasJavaExceptions) {
        Future(messageProducer.publishTransformException(resource, runId, transformerResult.getCause))
      } else {
        Future.unit
      }
      val sendSecondEvent = Future(messageProducer.publishTransformIOMessage(inputMessage, error, quarantineWriteTopic))
      (sendFirstEvent, sendSecondEvent).tupled.map(_ => ())
    }.map(_ => ())
  }

  private def getErrorMessage(transformerResult: TransformerResult): Option[NexlaErrorMessage] =
    if (transformerResult.hasTransformErrors) {
      Some(new NexlaErrorMessage(transformerResult.getErrorMessage, toJsonString(transformerResult.getErrors)))
    } else if (transformerResult.hasJavaExceptions) {
      val exp = transformerResult.getCause
      Some(new NexlaErrorMessage(exp.getMessage, getStacktraceForDb(exp)))
    } else {
      None
    }

  override def stop(): Future[Unit] = {
    Option(metricsAccumulator).foreach(_.close())
    Future.unit
  }
}
