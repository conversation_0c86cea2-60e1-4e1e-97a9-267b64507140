package com.nexla.inmemory_connector.pipeline.source

import akka.Done
import akka.stream.scaladsl.{Sink, Source}
import com.nexla.connect.common.postponedFlush.InMemoryProgressTracker
import com.nexla.inmemory_connector.compat._
import com.nexla.inmemory_connector.monitoring.telemetry.InMemoryTelemetry
import com.nexla.inmemory_connector.pipeline.source.SourceFlow.SourceDone
import com.nexla.inmemory_connector.state.{FilesState, ResourceState}
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.nexla.sc.util.StrictNexlaLogging

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class SourceFlow(loggingPrefix: String, task: InMemorySourceTask[_], inFlightFilesState: FilesState, progressTracker: InMemoryProgressTracker, telemetry: InMemoryTelemetry)(implicit ec: ExecutionContext) extends StrictNexlaLogging {
  override def logPrefix: Option[String] = Some(loggingPrefix)
  private val histPoll = telemetry.createFutureHistogram("imc_source_poll")

  def applySource(resourceState: ResourceState): Source[BasicMessage, Future[SourceDone]] = {
    Source.futureSource {
      for {
        _ <- Future.successful(resourceState.markAsStarted())
        _ <- task.start().tap(_ => logger.info("Task started")).tapError(logger.error("Task failed to start", _))
      } yield createSourceFlow()
    }.watchTermination() { (eventualDone, _) =>
      eventualDone.flatten
        .transformWith { r =>
          task.stop().tap(_ => logger.info("Task stopped successfully")).tapError(logger.error("Task failed to stop", _))
            .transformWith(_ => Future.fromTry(r))
        }
        .transformWith {
          case Success(_) =>
            logger.info("Source pipeline finished normally")
            progressTracker.markPipelineAsFinished()
            resourceState.markAsDone()
            Future.successful(SourceDone())
          case Failure(err) =>
            logger.error("Source pipeline finished with error", err)
            progressTracker.markPipelineAsFinished()
            resourceState.markAsFailed()
            Future.failed(err)
        }
    }
  }

  private def createSourceFlow(): Source[BasicMessage, Future[Done]] = {
    Source
      .repeat(())
      .mapAsync(1) { _ =>
        histPoll.measureTime {
          task.poll()
        }
      }
      .takeWhile {
        case InMemorySourceTask.NoMoreData => false
        case _: InMemorySourceTask.DataBatch => true
      }
      .collect { case InMemorySourceTask.DataBatch(xs) => xs }
      .mapConcat(x => x)
      .alsoTo(addFilesForHeartbeat)
      .watchTermination()((_, eventualDone) => eventualDone)
  }

  private val addFilesForHeartbeat: Sink[BasicMessage, Future[Done]] =
    Sink.foreach[BasicMessage] { el =>
      BasicMessage.extractOffsets(el).foreach {
        case f: FileOffset => inFlightFilesState.addFile(f.fileId, f.filePath, f.sourceId, FilesState.DuringProcessing)
        case _: MapOffset => ()
      }
    }

}

object SourceFlow {
  case class SourceDone()
}
