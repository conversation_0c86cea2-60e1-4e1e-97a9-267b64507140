package com.nexla.inmemory_connector.pipeline

import com.nexla.admin.client.flownode.{FlowNodeDatasink, FlowNodeDatasource}
import com.nexla.admin.client.{DataSink, DataSource}
import com.nexla.common.metrics.NexlaRawMetric
import com.nexla.listing.client.AdaptiveFlowTask
import com.nexla.sc.util.StrictNexlaLogging

import java.util.Optional
import scala.compat.java8.OptionConverters.RichOptionalGeneric

object PipelineUtils extends StrictNexlaLogging {
  def toNewDataSinkFormat(dataSink: DataSink): FlowNodeDatasink = {

    val newFormat = new FlowNodeDatasink(
      dataSink.getId,
      dataSink.getOwner.getId,
      dataSink.getOrg.getId,
      dataSink.getId,
      dataSink.getId,
      dataSink.getName,
      "",
      dataSink.getStatus,
      java.util.List.of[String](),
      null,
      dataSink.getDataCredentials.getId,
      dataSink.getDataSet.getParentDataSetId,
      null,
      null,
      dataSink.getConnectionType,
      dataSink.getConnectionType
    )

    newFormat
  }

  def toNewDataSourceFormat(dataSource: DataSource): FlowNodeDatasource = {
    val newFormat = new FlowNodeDatasource(
      dataSource.getId,
      dataSource.getOwner.getId,
      dataSource.getOrg.getId,
      dataSource.getId,
      dataSource.getId,
      dataSource.getName,
      "",
      dataSource.getStatus,
      java.util.List.of[String](),
      null,
      Option(dataSource.getDataCredentials).map(_.getId).orNull,
      dataSource.getDataSinkId,
      dataSource.getConnectionType,
      dataSource.getConnectionType,
      dataSource.getConnectionType
    )

    newFormat
  }

  def putNameForAdaptiveFlowTask(nexlaRawMetric: NexlaRawMetric, task: AdaptiveFlowTask): Unit = {
    val taskName = getName(task)
    nexlaRawMetric.getTags.put(NexlaRawMetric.NAME, taskName)
    logger.debug(s"put $taskName into task tags")

    val displayName: String = Optional.ofNullable(task.getParameters)
      .asScala
      .flatMap(x => Option(x.get("name")))
      .map(x => x.toString)
      .getOrElse(task.getId.toString)

    nexlaRawMetric.getTags.put(NexlaRawMetric.DISPLAY_PATH, s"#${String.valueOf(task.getId)} $displayName")
    logger.debug(s"put $displayName into task tags")
  }

  private def getName(task: AdaptiveFlowTask): String = {
    String.valueOf(task.getId)
  }
}
