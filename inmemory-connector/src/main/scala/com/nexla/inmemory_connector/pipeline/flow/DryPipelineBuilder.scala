package com.nexla.inmemory_connector.pipeline.flow

import akka.actor.ActorSystem
import com.nexla.common.notify.transport.{NexlaMessageProducer, NoopNexlaMessageTransport}
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.compat.DirectoryTransformationMessage
import com.nexla.inmemory_connector.context.Context.DryRunContext
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig.{NoCustomRuntimeConfig, RayRuntimeConfig}
import com.nexla.inmemory_connector.dry_run.{DryRunResultsUploader, DryRunSource}
import com.nexla.inmemory_connector.pipeline.flow.PipelineBuilder.PipelineBlueprint
import com.nexla.inmemory_connector.pipeline.tx.ray.RayTxTask
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiClientImpl
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiCodecs.RayApiMetadata

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class DryPipelineBuilder(props: AppProps, dryRunContext: DryRunContext)(implicit system: ActorSystem, ec: ExecutionContext) extends PipelineBuilder {

  private val rayApi = new RayApiClientImpl(props.rayApiServer, props.nexlaCreds, RayApiMetadata.justRunId(dryRunContext.runId))
  private val noopMessageProducer = new NexlaMessageProducer(new NoopNexlaMessageTransport)

  private def runRayTx(rayTx: RayTxTask)(msg: DirectoryTransformationMessage): Future[DirectoryTransformationMessage] =
    rayTx.transform(msg)
      .flatMap(Future.fromTry)
      .transformWith {
        case Success(List(d: DirectoryTransformationMessage)) => Future.successful(d)
        case Success(other) => Future.failed(new Throwable(s"Unexpected message type: $other"))
        case Failure(ex) => Future.failed(ex)
      }


  def build(): Try[PipelineBlueprint] = Try {
    // 2 cases:
    // 1) IMC_DRY_RUN_CUSTOM_CODE is set -- run ray job
    // 2) IMC_DRY_RUN_CUSTOM_CODE is not set -- run only source and push sample to probe

    val src = new DryRunSource(props, dryRunContext)
    val txFn: DirectoryTransformationMessage => Future[DirectoryTransformationMessage] = dryRunContext.customRuntimeConfig match {
      case txConfig: RayRuntimeConfig => (msg: DirectoryTransformationMessage) => runRayTx(new RayTxTask("", None, 0, txConfig, rayApi, noopMessageProducer))(msg)
      case NoCustomRuntimeConfig => (msg: DirectoryTransformationMessage) => Future.successful(msg)
    }
    val resultsUploader = new DryRunResultsUploader(dryRunContext)

    val runFn = () => {
      resultsUploader.cleanupResultFiles()
        .flatMap(_ => src.replicate())
        .map(result => DirectoryTransformationMessage(None, List.empty, result.fullFilePath))
        .flatMap(txFn)
        .transformWith(resultsUploader.uploadResults)
    }
    PipelineBlueprint(runFn, () => (), List())
  }
}

