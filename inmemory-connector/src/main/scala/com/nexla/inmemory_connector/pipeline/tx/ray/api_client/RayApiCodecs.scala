package com.nexla.inmemory_connector.pipeline.tx.ray.api_client

import com.nexla.admin.client.{DataSource, ReferencedResourceIds}
import com.nexla.connector.config.file.AWSAuthConfig
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiClient._
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiCodecs.{RayApiMetadata, deepMergeJsons}
import spray.json.{DeserializationException, JsArray, JsNull, JsNumber, JsObject, JsString, JsValue, RootJsonReader, RootJsonWriter}

import scala.compat.java8.OptionConverters.RichOptionalGeneric
import scala.jdk.CollectionConverters.asScalaBufferConverter

class RayApiCodecs(metadata: RayApiMetadata) {

  val jobIdDecoder: RootJsonReader[JobId] = (json: JsValue) => {
    json.asJsObject.fields.get("job_id") match {
      case Some(JsString(jobId)) => JobId(jobId)
      case _ => throw DeserializationException(s"Failed to parse json: ${json.compactPrint}")
    }
  }

  val submitDirectoryJobRequestEncoder: RootJsonWriter[SubmitDirectoryJobRequest] = (obj: SubmitDirectoryJobRequest) => {

    val pvtPackagesJson = obj.privatePackages.map(pp => JsObject(
      "auth" -> JsObject(
        "username" -> JsString(pp.username),
        "token" -> JsString(pp.token),
      ),
      "urls" -> JsArray(pp.urls.map(JsString.apply).toVector),
    ))
    val original = JsObject(
      "metadata" -> JsObject(
        "org_id" -> metadata.orgId.map(JsNumber(_)).getOrElse(JsNull),
        "run_id" -> JsNumber(metadata.runId),
        "source_id" -> metadata.sourceId.map(JsNumber(_)).getOrElse(JsNull),
        "dataset_id" -> metadata.dataSetId.map(JsNumber(_)).getOrElse(JsNull),
        "sink_id" -> metadata.sinkId.map(JsNumber(_)).getOrElse(JsNull),
      ),
      "custom_code" -> JsObject(
        "base64_code" -> JsString(obj.customCode),
        "args" -> JsArray.empty,
        "kwargs" -> JsObject.empty,
        "driver_function" -> JsString(obj.driverFunction),
        "packages" -> JsArray(obj.packages.map(JsString.apply).toVector),
        "pvt_packages" -> pvtPackagesJson.getOrElse(JsNull),
      ),
      "job_config" -> JsObject(
        "entrypoint" -> JsObject(
          "script" -> JsString(obj.entrypointScriptName),
        )
      ),
      "referenced_resource_ids" -> referencedResourceIdsEncoder.write(metadata.referencedResourceIds),
      "source" -> srcTypeEncoder.write(obj.raySource),
      "destination" -> dstTypeEncoder.write(obj.rayDestination),
    )
    obj.extraData match {
      case Some(extraData) => deepMergeJsons(original, extraData)
      case None => original
    }
  }

  private val srcTypeEncoder: RootJsonWriter[RaySrc] = {
    case S3MezzanineRaySrc(fullSourceDir, sourceS3Bucket) =>
      val srcBucketPrefix = AWSAuthConfig.toBucketPrefix(fullSourceDir, false)
      JsObject(
        "type" -> JsString("s3"),
        "credentials" -> JsObject(
          "bucket" -> JsString(srcBucketPrefix.bucket),
          "access_key" -> JsString(sourceS3Bucket.authConfig.accessKeyId),
          "secret_key" -> JsString(sourceS3Bucket.authConfig.secretKey),
        ),
        "input_dir" -> JsString(srcBucketPrefix.prefix),
      )
    case BYOOptimizedFlowRaySrc => JsObject("type" -> JsString("probe-http"))
  }

  private val dstTypeEncoder: RootJsonWriter[RayDst] = {
    case S3MezzanineRayDst(fullDestinationDir, destS3Bucket) =>
      val dstBucketPrefix = AWSAuthConfig.toBucketPrefix(fullDestinationDir, false)
      JsObject(
        "type" -> JsString("s3"),
        "credentials" -> JsObject(
          "bucket" -> JsString(dstBucketPrefix.bucket),
          "access_key" -> JsString(destS3Bucket.authConfig.accessKeyId),
          "secret_key" -> JsString(destS3Bucket.authConfig.secretKey),
        ),
        "output_dir" -> JsString(dstBucketPrefix.prefix),
      )
    case BYOOptimizedFlowRayDst => JsObject("type" -> JsString("probe-http"))
  }

  private val jobStatusDecoder: RootJsonReader[JobStatus] = {
    case JsString("PENDING") => JobStatus.Pending
    case JsString("RUNNING") => JobStatus.Running
    case JsString("STOPPED") => JobStatus.Stopped
    case JsString("SUCCEEDED") => JobStatus.Succeeded
    case JsString("FAILED") => JobStatus.Failed
    case json => throw DeserializationException(s"Failed to parse json: ${json.compactPrint}")
  }

  private val referencedResourceIdsEncoder: RootJsonWriter[Option[ReferencedResourceIds]] = {
    case Some(obj) => JsObject(
      "data_maps" -> JsArray(obj.getDataMaps().asScala.map(JsNumber(_)).toVector),
      "data_credentials" -> JsArray(obj.getDataCredentials().asScala.map(JsNumber(_)).toVector),
      "data_sets" -> JsArray(obj.getDataSets().asScala.map(JsNumber(_)).toVector),
      "code_containers" -> JsArray(obj.getCodeContainers().asScala.map(JsNumber(_)).toVector)
    )
    case None => JsObject(
      "data_maps" -> JsArray.empty,
      "data_credentials" -> JsArray.empty,
      "data_sets" -> JsArray.empty,
      "code_containers" -> JsArray.empty,
    )
  }

  val jobDetailsDecoder: RootJsonReader[JobDetails] = (json: JsValue) => {
    val fields = json.asJsObject.fields
    val jobStatusRaw = fields.getOrElse("status", throw DeserializationException(s"Failed to extract status field: ${json.compactPrint}"))
    val jobStatus = jobStatusDecoder.read(jobStatusRaw)
    JobDetails(jobStatus)
  }

  val jobErrorDetailsDecoder: RootJsonReader[JobErrorDetails] = (json: JsValue) => {
    val reasons = json.asJsObject.fields.values.flatMap {
      case JsArray(elements) => elements.collect {
        case obj: JsObject => obj.fields.get("reason").collect {
          case JsString(reason) => reason
        }
      }.flatten
      case _ => Seq.empty
    }.toList
    JobErrorDetails(reasons)
  }

  val directoryJobDetailsDecoder: RootJsonReader[DirectoryJobDetails] = (json: JsValue) => {
    val fields = json.asJsObject.fields
    val jobStatusRaw = fields.getOrElse("status", throw DeserializationException(s"Failed to extract status field: ${json.compactPrint}"))
    val jobStatus = jobStatusDecoder.read(jobStatusRaw)
    val childJobs = fields.get("batches").map(_.asJsObject.fields.keySet.map(JobId)).getOrElse(Set.empty)
    DirectoryJobDetails(jobStatus, childJobs)

  }
}

object RayApiCodecs {
  case class RayApiMetadata(runId: Long, sourceId: Option[Int], orgId: Option[Int], dataSetId: Option[Int], sinkId: Option[Int], referencedResourceIds: Option[ReferencedResourceIds])

  object RayApiMetadata {
    def fromDataSource(runId: Long, dataSource: DataSource, sinkId: Int): RayApiMetadata = {
      val dataSetId = dataSource.getDatasets.asScala.headOption.flatMap(d => Option(d.getId)).map(_.toInt)

      val referencedResourceIds = dataSource.getReferencedResourceIds.asScala
      RayApiMetadata(runId = runId, sourceId = Option(dataSource.getId()), orgId = Option(dataSource.getOrgId()), dataSetId = dataSetId, sinkId = Some(sinkId), referencedResourceIds = referencedResourceIds)
    }

    def justRunId(runId: Long): RayApiMetadata = {
      RayApiMetadata(runId = runId, sourceId = None, orgId = None, dataSetId = None, sinkId = None, referencedResourceIds = None)
    }
  }

  private def deepMergeJsons(j1: JsValue, j2: JsValue): JsValue = (j1, j2) match {
    case (JsObject(fields1), JsObject(fields2)) =>
      // Merge two JsObjects recursively
      JsObject(fields1 ++ fields2.map {
        case (key, j2Value) =>
          key -> (fields1.get(key) match {
            case Some(j1Value) => deepMergeJsons(j1Value, j2Value) // Recursively merge nested JsObjects
            case None => j2Value // J1 doesn't have the key, so take J2's value
          })
      })
    case (_, j2Value) =>
      // If they're not both JsObjects, override J1's value with J2's
      j2Value
  }
}