package com.nexla.inmemory_connector.pipeline.flow.newimpl

import java.util.concurrent.atomic.{AtomicInteger, AtomicLong}

object GlobalFileNaming {

  private val receivedListingFileId = new AtomicLong(0)
  private val fileSuffixCounter = new AtomicInteger(0)

  def setReceivedListingFileId(fileId: Long): Unit = {
    receivedListingFileId.compareAndSet(0, fileId)
  }

  def getNextSuffix(): String = {
    val globalPart = (receivedListingFileId.get() % 1000000).toString
    val suffix = fileSuffixCounter.incrementAndGet()

    val padWidth = 12 - globalPart.length
    if (padWidth > 0) {
      val formatString = s"%0${padWidth}d"
      globalPart + formatString.format(suffix)
    } else {
      globalPart + suffix.toString
    }
  }

}