package com.nexla.inmemory_connector.pipeline.source.task

import com.nexla.inmemory_connector.compat.BasicMessage

import java.util.concurrent.atomic.AtomicBoolean
import scala.concurrent.Future

class SingleMessageSource(msg: BasicMessage) extends BasicSourceTask  {
  private val alreadyEmitted = new AtomicBoolean(false)

  def start(): Future[Unit] = Future.unit

  def poll(): Future[List[BasicMessage]] = {
    if (alreadyEmitted.get()) {
      Future.successful(List.empty)
    } else {
      alreadyEmitted.set(true)
      Future.successful(List(msg))
    }
  }

  def stop(): Future[Unit] = Future.unit

  def isStoppingConditionReached(): Boolean = alreadyEmitted.get()
}
