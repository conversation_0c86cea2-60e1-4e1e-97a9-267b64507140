package com.nexla.inmemory_connector.pipeline.source.parsers

import com.nexla.inmemory_connector.compat.{MapOffset, RecordWithOffset}
import org.apache.kafka.connect.source.SourceRecord

import java.util.concurrent.atomic.AtomicInteger
import scala.jdk.CollectionConverters.mapAsScalaMapConverter


class MapOffsetSourceRecordParser extends SourceRecordParser {
  private val messageId = new AtomicInteger(0)

  def parse(sourceRecord: SourceRecord): Option[RecordWithOffset] =
    SourceRecordParser.parseToNexlaMessage(sourceRecord)
      .map(nexlaMessage => (nexlaMessage, sourceRecord.sourceOffset().asScala))
      .map { case (nexlaMessage, sourceOffset) =>
        val currentMessageId = messageId.getAndIncrement()
        val offsetMap = sourceOffset.collect { case (key, value) if value != null => key -> value.toString }.toMap
        val datasetId = Option(nexlaMessage.getNexlaMetaData.getDatasetId).map(_.toInt)
        RecordWithOffset(datasetId, nexlaMessage, MapOffset(nexlaMessage.getNexlaMetaData.getResourceId, 0, currentMessageId, offsetMap))
      }
}