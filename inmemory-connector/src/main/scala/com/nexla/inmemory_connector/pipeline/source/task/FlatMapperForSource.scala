package com.nexla.inmemory_connector.pipeline.source.task

import akka.stream.Materializer
import akka.stream.scaladsl.{Flow, Sink, Source}
import akka.{Done, NotUsed}
import com.amazonaws.services.s3.model.S3ObjectSummary
import com.nexla.admin.client.AdminApiClient
import com.nexla.common._
import com.nexla.connect.common.postponedFlush.InMemoryProgressTracker
import com.nexla.connector.config.file.AWSAuthConfig
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.compat._
import com.nexla.inmemory_connector.context.Context.PipelineContext
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig.RayRuntimeConfig
import com.nexla.inmemory_connector.monitoring.telemetry.InMemoryTelemetry
import com.nexla.inmemory_connector.pipeline.NexlaConfigPreparer
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryMessageProducer
import com.nexla.inmemory_connector.pipeline.source.SourceFlow.SourceDone
import com.nexla.inmemory_connector.pipeline.source.task.FlatMapperForSource.FlatMapperDone
import com.nexla.inmemory_connector.pipeline.source.{InMemorySourceTask, SourceFlow}
import com.nexla.inmemory_connector.state.{FilesState, ResourceState}
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.listing.client.{FileVaultClient, ListingClient}
import com.nexla.probe.s3.S3ConnectorService
import com.nexla.sc.util.{Async, StrictNexlaLogging}
import one.util.streamex.StreamEx

import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters._
import scala.util.{Failure, Success}

class FlatMapperForSource(
                           ctx: PipelineContext,
                           configPreparer: NexlaConfigPreparer,
                           adminApiClient: AdminApiClient,
                           fileVaultClient: FileVaultClient,
                           pipelineMessageProducer: InMemoryMessageProducer,
                           props: AppProps,
                           probeFactory: ProbeFactory,
                           javaListingClient: ListingClient,
                           telemetry: InMemoryTelemetry,
                         )(implicit ec: ExecutionContext, mat: Materializer)
  extends StrictNexlaLogging {

  override def logPrefix: Option[String] = Some("flatmapper")

  private val taskEc = Async.ioExecutorContext

  private val histListDirectory = telemetry.createFutureHistogram("imc_flatmapper_source_list_directory")
  private val s3ConnectorService = new S3ConnectorService()
  private val rayRuntimeConfig: RayRuntimeConfig = ctx.runtimeConfig match {
    case x: CustomRuntimeConfig.RayRuntimeConfig => x
    case _ => throw new Exception("Ray runtime config not found")
  }

  def applyFlatMapFilesFlow(): Flow[BasicMessage, BasicMessage, Future[FlatMapperDone]] =
    Flow[BasicMessage]
      .collect { case x: DirectoryTransformationMessage => x }
      .mapAsync(1) { x =>
        for {
          listedFiles <- histListDirectory.measureTime(listDirectory(x))
          sourceFlow = createSourceFlow(listedFiles, x)
          offsetsFlow = Source(x.offsets).map(o => JustOffset(o.datasetId, o))
        } yield sourceFlow.concat(offsetsFlow)
      }
      .flatMapConcat(identity)
      .watchTermination() { (_, eventualDone: Future[Done]) =>
        eventualDone
          .transformWith {
            case Success(_) =>
              logger.info("FlatMap pipeline finished normally")
              Future.successful(FlatMapperDone())
            case Failure(ex) =>
              logger.error("FlatMap pipeline finished with error", ex)
              Future.failed(ex)
          }
      }

  private def createSourceFlow(files: List[NexlaFile], directoryTransformationMessage: DirectoryTransformationMessage): Source[BasicMessage, Future[SourceDone]] = {
    val reader = new FlatMapperFileSourceReader(files, directoryTransformationMessage, rayRuntimeConfig)(ctx, configPreparer, adminApiClient , props , pipelineMessageProducer, javaListingClient , fileVaultClient , probeFactory )
    val task = new InMemorySourceTask("", reader)
    new SourceFlow("", task, new FilesState, new InMemoryProgressTracker, telemetry).applySource(new ResourceState(new Resource(-1, ResourceType.SOURCE)))
  }

  private def listFiles(directoryPath: String): Source[S3ObjectSummary, NotUsed] = {
    val bucketPrefix = AWSAuthConfig.toBucketPrefix(directoryPath, false)
    val stream: StreamEx[S3ObjectSummary] = s3ConnectorService.listS3Objects(rayRuntimeConfig.sourceS3Credentials.authConfig, null, bucketPrefix.bucket, bucketPrefix.prefix)
    Source.fromIterator(() => stream.iterator().asScala)
  }

  private def listDirectory(x: DirectoryTransformationMessage): Future[List[NexlaFile]] = {
    val bucketPrefix = AWSAuthConfig.toBucketPrefix(x.fullDirectoryPath, false)
    val result = listFiles(x.fullDirectoryPath)
      .filter(file => !file.getKey().endsWith("/"))
      .map(file => new NexlaFile(file.getKey(), file.getSize(), bucketPrefix.bucket, file.getETag(), file.getLastModified().getTime(), file.getLastModified().getTime(), ListingResourceType.FILE))
      .runWith(Sink.seq)
      .map(_.toList)
    logger.info("List mezzanine directory {}: result: {}", x, result)
    result
  }
}

object FlatMapperForSource {

  final case class FlatMapperDone()

}