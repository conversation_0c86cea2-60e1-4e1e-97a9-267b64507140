package com.nexla.inmemory_connector.pipeline.storage

import org.apache.kafka.connect.source.SourceConnectorContext
import org.apache.kafka.connect.storage.OffsetStorageReader

class InMemorySourceConnectorContext(offsetStorageReader: OffsetStorageReader) extends SourceConnectorContext {

  override def offsetStorageReader(): OffsetStorageReader = offsetStorageReader

  override def requestTaskReconfiguration(): Unit = ()

  override def raiseError(e: Exception): Unit = ()
}
