package com.nexla.inmemory_connector.pipeline.sink.offsets

import com.nexla.sc.client.listing.CoordinationAppClient

import scala.concurrent.Future

trait MapOffsetCommitter {
  def commit(sourceId: Int, sinkId: Int, offsets: Map[String, String]): Future[Unit]
}

object MapOffsetCommitter {
  def withCoordinationClient(coordination: CoordinationAppClient): MapOffsetCommitter = (sourceId: Int, sinkId: Int, offsets: Map[String, String]) => coordination.updateFastOffsets(sourceId, sinkId, offsets)
}
