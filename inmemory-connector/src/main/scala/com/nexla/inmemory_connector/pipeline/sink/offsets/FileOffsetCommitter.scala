package com.nexla.inmemory_connector.pipeline.sink.offsets

import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.control.ListingFileStatus
import com.nexla.control.coordination.SetFileStatusCoordination
import com.typesafe.scalalogging.StrictLogging

import java.util.UUID
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}

trait FileOffsetCommitter {
  def commit(sourceId: Int, fileId: Long, fileStatus: ListingFileStatus, maybeOffset: Option[Long], message: Option[String]): Future[Unit]
}

object FileOffsetCommitter extends StrictLogging {
  def withKafka(nexlaMessageProducer: NexlaMessageProducer)(implicit ec: ExecutionContext): FileOffsetCommitter = (sourceId: Int, fileId: Long, fileStatus: ListingFileStatus, maybeOffset: Option[Long], message: Option[String]) => {
    val offset = maybeOffset.map(java.lang.Long.valueOf).orNull
    val event = new SetFileStatusCoordination(UUID.randomUUID().toString, sourceId, fileId, fileStatus, offset, message.getOrElse(""), System.currentTimeMillis() + 1.hours.toMillis)

    logger.info(s"Setting file status: sourceId=$sourceId, fileId=$fileId, status=$fileStatus, message=$message, offset=$offset")
    Future(nexlaMessageProducer.sendCoordinationEvent(event))
  }
}