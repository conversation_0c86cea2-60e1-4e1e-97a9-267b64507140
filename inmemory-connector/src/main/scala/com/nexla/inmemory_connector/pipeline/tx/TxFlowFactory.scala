package com.nexla.inmemory_connector.pipeline.tx

import akka.actor.ActorSystem
import com.nexla.admin.client.{DataSet, DataSink, DataSource}
import com.nexla.connector.config.FlowType
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.compat.PipelineConf.NexlaTxConfig
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig.RayRuntimeConfig
import com.nexla.inmemory_connector.monitoring.telemetry.InMemoryTelemetry
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryMessageProducer
import com.nexla.inmemory_connector.pipeline.tx.ray.RayTxTask
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiClientImpl
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiCodecs.RayApiMetadata
import com.nexla.inmemory_connector.state.FilesState
import com.nexla.sc.config.NexlaCredsConf
import com.nexla.transform.TransformService

import scala.concurrent.ExecutionContext

class TxFlowFactory(props: AppProps, runId: Long, flowType: FlowType, nexlaCreds: NexlaCredsConf, filesState: FilesState, messageProducer: InMemoryMessageProducer, telemetry: InMemoryTelemetry)(implicit system: ActorSystem, ec: ExecutionContext) {

  def forRay(dataSource: DataSource, rayTxConfig: RayRuntimeConfig, dataSink: DataSink): TxFlow = {
    val loggingPrefix = s"RAY-TX-${dataSource.getId}"
    val rayApi = new RayApiClientImpl(props.rayApiServer, nexlaCreds, RayApiMetadata.fromDataSource(runId, dataSource, dataSink.getId))

    val transformer = new RayTxTask(loggingPrefix, Some(dataSource), runId, rayTxConfig, rayApi, messageProducer)

    new TxFlow(1, loggingPrefix, transformer, filesState, telemetry)
  }

  def forNexla(nexlaTxConfig: NexlaTxConfig, dataSet: DataSet, props: AppProps, transformService: TransformService): TxFlow = {
    val loggingPrefix = s"NEXLA-TX-${dataSet.getId}"
    val transformer = new NexlaTxTask(props, runId, flowType, transformService, dataSet, messageProducer, nexlaTxConfig.recordProcessingTimeout)
    new TxFlow(nexlaTxConfig.parallelism, loggingPrefix, transformer, filesState, telemetry)
  }

}
