package com.nexla.inmemory_connector.pipeline.sink.offsets

import com.nexla.inmemory_connector.compat._

import java.util.concurrent.ConcurrentHashMap
import scala.jdk.CollectionConverters.collectionAsScalaIterableConverter


class OffsetAggregator {
  private val postponedOffsets = new ConcurrentHashMap[String, FastConnectorOffset]()

  private def determineKey(o: FastConnectorOffset): String = o match {
    case fo: FileOffset => s"${fo.sourceId}-${fo.fileId}"
    case mo: MapOffset => s"${mo.sourceId}"
  }

  def rememberOffsets(records: Seq[BasicMessage]): Unit = postponedOffsets.synchronized {
    records.flatMap(BasicMessage.extractOffsets).foreach { offset =>
      val key = determineKey(offset)
      postponedOffsets.put(key, offset)
    }
  }

  def handAndCleanOffsets(): List[FastConnectorOffset] = postponedOffsets.synchronized {
    val offsets = postponedOffsets.values().asScala.toList
    postponedOffsets.clear()
    offsets
  }

}
