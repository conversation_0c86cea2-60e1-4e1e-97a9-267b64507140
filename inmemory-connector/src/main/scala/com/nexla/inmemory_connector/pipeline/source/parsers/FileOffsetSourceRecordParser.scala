package com.nexla.inmemory_connector.pipeline.source.parsers

import com.nexla.common.{NexlaMessage, NexlaMetaData}
import com.nexla.inmemory_connector.compat.{FileOffset, RecordWithOffset}
import com.nexla.inmemory_connector.pipeline.source.parsers.FileOffsetSourceRecordParser.nexlaMessageToRecordWithOffset
import com.nexla.inmemory_connector_common.utils.TappingOps.tryOps
import com.nexla.sc.util.StrictNexlaLogging
import org.apache.kafka.connect.source.SourceRecord

import scala.jdk.CollectionConverters.mapAsScalaMapConverter
import scala.util.Try

class FileOffsetSourceRecordParser extends SourceRecordParser with StrictNexlaLogging {
  def parse(sourceRecord: SourceRecord): Option[RecordWithOffset] = {
    SourceRecordParser.parseToNexlaMessage(sourceRecord).map(nexlaMessageToRecordWithOffset)
  }
}

object FileOffsetSourceRecordParser extends StrictNexlaLogging {

  def nexlaMessageToRecordWithOffset(nexlaMessage: NexlaMessage): RecordWithOffset = {
    val meta = nexlaMessage.getNexlaMetaData
    val tags = meta.getTags.asScala
    val fileId = Try(tags.get(NexlaMetaData.FILE_ID).flatMap(Option(_)).map(_.toString.toLong).get).tapError(e => logger.error(s"Could not read ${NexlaMetaData.FILE_ID} from metadata", e))
    val fileSize = Try(tags.get(NexlaMetaData.FILE_SIZE).flatMap(Option(_)).map(_.toString.toLong).get).tapError(e => logger.error(s"Could not read ${NexlaMetaData.FILE_SIZE} from metadata", e))

    val fo = FileOffset(fileId.getOrElse(0L), meta.getSourceKey, fileSize.getOrElse(0L), meta.getSourceOffset, meta.getResourceId, Option(meta.getDatasetId), meta.isEof, None)
    RecordWithOffset(Option(meta.getDatasetId), nexlaMessage, fo)
  }

}
