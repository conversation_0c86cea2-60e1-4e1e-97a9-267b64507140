package com.nexla.inmemory_connector.pipeline.storage

import org.apache.kafka.common
import org.apache.kafka.connect.sink.SinkTaskContext

import java.{lang, util}
import scala.collection.JavaConverters._

class EmptySinkTaskContext extends SinkTaskContext {

  override def offset(offsets: util.Map[common.TopicPartition, lang.Long]): Unit = ???

  override def offset(tp: common.TopicPartition, offset: Long): Unit = ???

  override def timeout(timeoutMs: Long): Unit = ???

  override def assignment(): util.Set[common.TopicPartition] = Set.empty[common.TopicPartition].asJava

  override def pause(partitions: common.TopicPartition*): Unit = ???

  override def resume(partitions: common.TopicPartition*): Unit = ???

  override def requestCommit(): Unit = ???

  override def configs(): util.Map[String, String] = ???
}
