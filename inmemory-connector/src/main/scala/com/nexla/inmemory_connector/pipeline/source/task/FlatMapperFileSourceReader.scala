package com.nexla.inmemory_connector.pipeline.source.task

import com.nexla.admin.client.{AdminApiClient, DataSource}
import com.nexla.common.ResourceType.SOURCE
import com.nexla.common.logging.NexlaLogger
import com.nexla.common.{ConnectionType, NexlaFile}
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils
import com.nexla.connector.config.file.FileSourceConnectorConfig
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.connector.file.source._
import com.nexla.file.service.FileConnectorService
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.compat.{BasicMessage, DirectoryTransformationMessage}
import com.nexla.inmemory_connector.context.Context.PipelineContext
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig.RayRuntimeConfig
import com.nexla.inmemory_connector.pipeline.NexlaConfigPreparer
import com.nexla.inmemory_connector.pipeline.metrics.{FlatMapperMessageProducer, InMemoryMessageProducer}
import com.nexla.inmemory_connector.pipeline.source.parsers.FileOffsetSourceRecordParser
import com.nexla.inmemory_connector.pipeline.source.stopping_condition.SourceStoppingCondition
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.nexla.listing.client.{FileVaultClient, ListingClient}
import com.nexla.sc.util._
import org.slf4j.LoggerFactory

import java.util
import java.util.Optional
import java.util.Optional.empty
import java.util.concurrent.atomic.AtomicBoolean
import scala.collection.JavaConverters._
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}


class FlatMapperFileSourceReader(files: List[NexlaFile],
                                 directoryTransformationMessage: DirectoryTransformationMessage,
                                 rayRuntimeCfg: RayRuntimeConfig,
                                )(
                                  ctx: PipelineContext,
                                  configPreparer: NexlaConfigPreparer,
                                  adminApiClient: AdminApiClient,
                                  props: AppProps,
                                  messageProducer: InMemoryMessageProducer,
                                  javaListingClient: ListingClient,
                                  fileVaultClient: FileVaultClient,
                                  probeFactory: ProbeFactory,
                                )(implicit ec: ExecutionContext)
  extends BasicSourceTask {

  private val taskEc = Async.ioExecutorContext
  private val logger = new NexlaLogger(LoggerFactory.getLogger(this.getClass), "flatmapper")
  private val flatMapperMessageProducer = new FlatMapperMessageProducer(messageProducer.messageProducer, ctx.maybeAdaptiveFlowTask)
  private val offsetWriter = new NoopOffsetWriter

  private var fileReader: TransportFileReader = _
  private var consumer: BaseKafkaFileMessageReader = _
  private val sourceStoppingCondition = new SourceStoppingCondition(0.seconds, 2)
  private val stoppingConditionReached = new AtomicBoolean(false)

  private def prepareConfig(): Future[util.Map[String, String]] = {
    val srcF = Future {
      val dummySource = new DataSource()
      dummySource.setId(ctx.dataSource.getId)
      dummySource.setConnectionType(ConnectionType.S3)
      dummySource.setFlowType(ctx.flowType)
      dummySource.setDataCredentials(rayRuntimeCfg.sourceS3Credentials.dataCredentials)
      dummySource.setOwner(ctx.dataSource.getOwner)

      val config = util.Map.of[String, Object](
        "path", directoryTransformationMessage.fullDirectoryPath,
        "create.topics", "false",
        "listing.enabled", "false",
        "schema.detection.once", "true",
      )
      dummySource.setSourceConfig(config)
      dummySource
    }
    srcF.flatMap { src =>
      configPreparer.prepareSourceConnectorConfig(src, FileSourceConnectorConfig.configDef())
    }.tapError(logger.error("Failed to prepare source connector config", _))
  }


  def start(): Future[Unit] = prepareConfig().flatMap { resultConfig =>
    Future {
      val sourceConfig = new FileSourceConnectorConfig(resultConfig)
      val fileSourceContext = new FileSourceContext(sourceConfig, empty(), ctx.runId)
      val messageGrouper = new KafkaFileSourceMessageGrouper(fileSourceContext, logger, ctx.runId)
      val notificationSender = new FileSourceNotificationSender(flatMapperMessageProducer, ctx.flowType, fileSourceContext, logger)
      val probeService: FileConnectorService[_ <: BaseAuthConfig] = {
        val ps = probeFactory.getProbeService(adminApiClient, javaListingClient, props.decryptKey, sourceConfig.sourceType, fileVaultClient)
        ps.initLogger(SOURCE, -1, Optional.empty())
        ps.asInstanceOf[FileConnectorService[_ <: BaseAuthConfig]]
      }
      val schemaDetectionUtils = SchemaDetectionUtils.createSchemaDetection(sourceConfig, adminApiClient, flatMapperMessageProducer, empty())
      fileReader = new TransportFileReader(notificationSender, probeService, schemaDetectionUtils, offsetWriter, messageGrouper, logger, empty(), empty(), empty(), fileSourceContext, empty(), false)
      consumer = new BaseKafkaFileMessageReader(offsetWriter, messageGrouper, fileSourceContext, logger, probeService, notificationSender)

      val transportFiles = files.map(f => new TransportFile(f, 0, false))
      fileReader.addFiles(transportFiles.asJava)
      ()
    }
  }

  def poll(): Future[List[BasicMessage]] = Future {
    val data: ReadBatchResult[NexlaMessageFile] = fileReader.readNextBatch(consumer, adminApiClient)

    Option(data.messages).toList.flatMap(_.asScala).map(_.message).map(FileOffsetSourceRecordParser.nexlaMessageToRecordWithOffset)
  }(taskEc).map { msgs =>
    val shouldStop = sourceStoppingCondition.shouldStop(msgs.isEmpty)
    if (shouldStop) {
      stoppingConditionReached.set(true)
    }
    msgs
  }

  def stop(): Future[Unit] = Future {
    fileReader.stop()
  }(taskEc)

  def isStoppingConditionReached(): Boolean = stoppingConditionReached.get()
}
