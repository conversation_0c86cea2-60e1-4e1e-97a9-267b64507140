package com.nexla.inmemory_connector.pipeline.tx.ray.api_client

import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig.MezzanineAuthConfig
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiClient._
import spray.json.JsValue

import scala.concurrent.Future

trait RayApiClient {

  def submitJobV2(submitJobRequest: SubmitDirectoryJobRequest): Future[JobId]

  def fetchJobDetails(jobId: JobId): Future[JobDetails]

  def fetchErrorsDetails(jobId: JobId): Future[JobErrorDetails]

  def fetchDirectoryJobDetails(jobId: JobId): Future[DirectoryJobDetails]

  def terminateJob(jobId: JobId): Future[JobTerminationDetails]

}

object RayApiClient {
  final case class JobId(id: String)

  final case class SubmitDirectoryJobRequest(
                                              customCode: String,
                                              driverFunction: String,
                                              packages: List[String],
                                              privatePackages: Option[PrivatePackages],
                                              raySource: RaySrc,
                                              rayDestination: RayDst,
                                              extraData: Option[JsValue],
                                              entrypointScriptName: String,
                                            )
  final case class PrivatePackages(username: String, token: String, urls: List[String])

  sealed trait RaySrc
  case class S3MezzanineRaySrc(fullSourceDir: String, sourceS3Bucket: MezzanineAuthConfig) extends RaySrc
  case object BYOOptimizedFlowRaySrc extends RaySrc

  sealed trait RayDst
  case class S3MezzanineRayDst(fullDestinationDir: String, destS3Bucket: MezzanineAuthConfig) extends RayDst
  case object BYOOptimizedFlowRayDst extends RayDst


  sealed trait JobStatus

  object JobStatus {
    case object Pending extends JobStatus
    case object Running extends JobStatus
    case object Stopped extends JobStatus
    case object Succeeded extends JobStatus
    case object Failed extends JobStatus
  }

  final case class JobDetails(status: JobStatus)
  final case class DirectoryJobDetails(status: JobStatus, childJobs: Set[JobId])
  final case class JobTerminationDetails(body: JsValue)

  final case class JobErrorDetails(reasons: List[String])

}