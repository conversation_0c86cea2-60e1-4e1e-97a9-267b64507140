package com.nexla.inmemory_connector.pipeline.flow

import com.nexla.inmemory_connector.pipeline.flow.PipelineBuilder.PipelineBlueprint
import com.nexla.inmemory_connector.state.ResourceState

import scala.concurrent.Future
import scala.util.Try

trait PipelineBuilder {
  def build(): Try[PipelineBlueprint]
}

object PipelineBuilder {
  final case class PipelineBlueprint(run: () => Future[Unit], stop: () => Unit, resourceStates: List[ResourceState])
}
