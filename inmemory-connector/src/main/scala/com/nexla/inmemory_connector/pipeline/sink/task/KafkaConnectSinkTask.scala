package com.nexla.inmemory_connector.pipeline.sink.task

import akka.actor.ActorSystem
import com.nexla.admin.client.DataSink
import com.nexla.connect.common.{BaseSinkTask, ReadyToFlush}
import com.nexla.connector.config.NexlaConfigDef
import com.nexla.connector.push.sink.FileSinkTask
import com.nexla.connector.redis.sink.RedisSinkTask
import com.nexla.connector.sql.sink.JdbcSinkTask
import com.nexla.inmemory_connector.LogPrefix
import com.nexla.inmemory_connector.compat.{BasicMessage, RecordWithOffset}
import com.nexla.inmemory_connector.pipeline.NexlaConfigPreparer
import com.nexla.inmemory_connector.pipeline.sink.flusher.SinkFlusher.FlushingResults
import com.nexla.inmemory_connector.pipeline.sink.offsets.OffsetAggregator
import com.nexla.inmemory_connector.pipeline.storage.EmptySinkTaskContext
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.nexla.sc.util.{Async, StrictNexlaLogging}
import org.apache.kafka.clients.consumer.OffsetAndMetadata

import java.util
import scala.collection.JavaConverters._
import scala.concurrent.{ExecutionContext, Future}

class KafkaConnectSinkTask[T <: BaseSinkTask[_]](configPreparer: NexlaConfigPreparer, runId: Long)
                                                (task: T, dataSink: DataSink, configDef: NexlaConfigDef)(implicit system: ActorSystem) extends BasicSinkTask with StrictNexlaLogging {
  override def logPrefix: Option[String] = Some(LogPrefix.forSink(dataSink))

  private val taskEc = Async.ioExecutorContext
  private implicit val ec: ExecutionContext = system.dispatcher

  private val topicPartition = new com.nexla.common.sink.TopicPartition(s"sink-${dataSink.getId}", 0)
  private val offsetAggregator = new OffsetAggregator()
  val underlying: T = task

  private def startTask(taskConfig: util.Map[String, String]): Future[Unit] = Future {
    task.initialize(new EmptySinkTaskContext)
    task.start(taskConfig)
  }(taskEc)

  def start(): Future[Unit] = for {
    taskConfig <- configPreparer.prepareSinkConnectorConfig(dataSink, configDef).tapError(logger.error("Failed to prepare task config", _))
    _ <- startTask(taskConfig).tapError(logger.error("Failed to start task", _))
  } yield ()

  def putData(messages: Seq[BasicMessage]): Future[Unit] = Future {
    val messagesWithData = messages.collect { case r: RecordWithOffset => r }.map(_.message)
    task.putNexlaMessages(messagesWithData.asJava, topicPartition)
    offsetAggregator.rememberOffsets(messages)
  }(taskEc)

  def flushData(): Future[FlushingResults] = Future {
    val readyToFlush = task.readyToFlush()
    logger.info("BaseSinkTask flush readiness check returned: {}", readyToFlush);
    if (task.doFlush(readyToFlush)) {
      FlushingResults(offsetAggregator.handAndCleanOffsets(), Nil)
    } else {
      FlushingResults(Nil, Nil)
    }
  }(taskEc)

  def forceFlushData(): Future[FlushingResults] = Future {
    task match {
      case t: JdbcSinkTask => t.doFlush(ReadyToFlush.FLUSH)
      case t: FileSinkTask => t.doFlush(ReadyToFlush.FLUSH)
      case t: RedisSinkTask => t.doFlush(ReadyToFlush.FLUSH)
      case _ => task.flush(Map.empty[org.apache.kafka.common.TopicPartition, OffsetAndMetadata].asJava)
    }
    FlushingResults(offsetAggregator.handAndCleanOffsets(), Nil)
  }(taskEc)

  def stop(): Future[Unit] = Future(task.stop())(taskEc)
}
