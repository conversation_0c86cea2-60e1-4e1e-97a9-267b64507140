package com.nexla.inmemory_connector.pipeline.flow

import akka.actor.{ActorSystem, Scheduler}
import akka.stream._
import akka.stream.scaladsl.{Broadcast, Flow, GraphDSL, Keep, Merge, RunnableGraph, Sink, Source}
import com.nexla.admin.client.AdminApiClient
import com.nexla.admin.client.flownode.{FlowNodeDatasource, FlowNodeElement}
import com.nexla.common.{ConnectionType, Resource, ResourceType}
import com.nexla.connect.common.postponedFlush.InMemoryProgressTracker
import com.nexla.connector.config.FlowType
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.compat.BasicMessage
import com.nexla.inmemory_connector.context.Context.PipelineContext
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig
import com.nexla.inmemory_connector.monitoring.MetricsOffsetReporter
import com.nexla.inmemory_connector.monitoring.telemetry.InMemoryTelemetry
import com.nexla.inmemory_connector.pipeline.byo.ByoMetricSender
import com.nexla.inmemory_connector.pipeline.flow.PipelineBuilder.PipelineBlueprint
import com.nexla.inmemory_connector.pipeline.flow.newimpl._
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryMessageProducer
import com.nexla.inmemory_connector.pipeline.sink.SinkFlow.SinkDone
import com.nexla.inmemory_connector.pipeline.sink.SinkFlowFactory
import com.nexla.inmemory_connector.pipeline.source.SourceFlow.SourceDone
import com.nexla.inmemory_connector.pipeline.source.SourceFlowFactory
import com.nexla.inmemory_connector.pipeline.source.task.FlatMapperForSource
import com.nexla.inmemory_connector.pipeline.tx.TxFlowFactory
import com.nexla.inmemory_connector.pipeline.{CloningBroadcast, PipelineUtils}
import com.nexla.inmemory_connector.state.{FilesState, ResourceState}
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.listing.client.{FileVaultClient, ListingClient}
import com.nexla.sc.client.OffsetSaver
import com.nexla.sc.client.listing.{CoordinationAppClient, ListingAppClient}
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import com.nexla.transform.TransformService

import java.util
import scala.collection.JavaConverters._
import scala.collection.mutable
import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

class RealPipelineBuilder(listingClient: ListingAppClient,
                          adminApiClient: AdminApiClient,
                          coordinationClient: CoordinationAppClient,
                          javaListingClient: ImcListingClient,
                          fileVaultClient: FileVaultClient,
                          transformService: TransformService,
                          messageProducer: InMemoryMessageProducer,
                          props: AppProps,
                          offsetSaver: OffsetSaver,
                          metricsOffsetReporter: MetricsOffsetReporter,
                          ctx: PipelineContext,
                          filesState: FilesState,
                          probeFactory: ProbeFactory,
                          progressTracker: InMemoryProgressTracker,
                          byoMetricSender: ByoMetricSender,
                          telemetry: InMemoryTelemetry,
                          intermediateFlushDelay: FiniteDuration,
                          maxIntermediateFlushAwaitInterval: FiniteDuration,
                         )
                         (implicit system: ActorSystem,
                          mat: Materializer,
                          ec: ExecutionContext,
                          sch: Scheduler) extends PipelineBuilder with StrictNexlaLogging with WithLogging {

  private val inMemorySourceFactory = new SourceFlowFactory(props, ctx, filesState, adminApiClient, coordinationClient, messageProducer, progressTracker, telemetry, javaListingClient)
  private val inMemorySinkFactory = new SinkFlowFactory(props, ctx.runId, adminApiClient, javaListingClient, fileVaultClient, metricsOffsetReporter, progressTracker, messageProducer, probeFactory, telemetry, intermediateFlushDelay, maxIntermediateFlushAwaitInterval, ctx.maybeAdaptiveFlowTask)
  private val txFactory = new TxFlowFactory(props, ctx.runId, ctx.flowType, props.nexlaCreds, filesState, messageProducer, telemetry)

  private def buildSource(flowNodeDatasource: FlowNodeDatasource): (ResourceState, Source[BasicMessage, Future[SourceDone]]) = {
    val state = new ResourceState(new Resource(flowNodeDatasource.getId, ResourceType.SOURCE))
    logger.info(s"building source [${flowNodeDatasource.getConnectionType}-${flowNodeDatasource.getId}]")
    val flow = flowNodeDatasource.getConnectorType match {
      case connectionType if connectionType.isFile =>
        ctx.runtimeConfig match {
          case c: CustomRuntimeConfig.RayRuntimeConfig =>
            if (isDirectFlow) {
              val sourceFlow = inMemorySourceFactory.forRayFile(ctx.dataSource).applySource(state)
              val rayFlow = txFactory.forRay(ctx.dataSource, c, ctx.dataSink).applyTransformation()
              logger.info(s"Adding flatMap after Ray source [${flowNodeDatasource.getConnectionType}-${flowNodeDatasource.getId}]")
              val flatMapperFlow: Flow[BasicMessage, BasicMessage, Future[FlatMapperForSource.FlatMapperDone]] = inMemorySourceFactory.sourceFlatMapper(fileVaultClient, javaListingClient, probeFactory).applyFlatMapFilesFlow()
              sourceFlow.viaMat(rayFlow)(Keep.both).viaMat(flatMapperFlow)(Keep.both)
                .mapMaterializedValue { case ((srcDone, txDone), flatMapperDone) =>
                  for {
                    _ <- txDone
                    _ <- flatMapperDone
                    r <- srcDone
                  } yield r
                }
            } else {
              val sourceFlow = inMemorySourceFactory.forBYOFile(ctx.dataSource, ctx.dataSets.head.getId, c.byoOptimized).applySource(state)
              val rayFlow = txFactory.forRay(ctx.dataSource, c, ctx.dataSink).applyTransformation()
              sourceFlow.viaMat(rayFlow)(Keep.both).viaMat(byoMetricSender.sendingMetricsFlow)(Keep.both)
                .mapMaterializedValue { case ((srcDone, txDone), _) =>
                  for {
                    _ <- txDone
                    r <- srcDone
                  } yield r
                }
            }
          case CustomRuntimeConfig.NoCustomRuntimeConfig =>
            inMemorySourceFactory.forFile(ctx.dataSource).applySource(state)
        }
      case connectionType if connectionType.isDatabase => inMemorySourceFactory.forJdbc(ctx.dataSource).applySource(state)
      case connectionType if connectionType.isVectorDB => inMemorySourceFactory.forVectorDb(ctx.dataSource).applySource(state)
      case connectionType if connectionType == ConnectionType.REST => inMemorySourceFactory.forRest(ctx.dataSource).applySource(state)
      case connectionType if connectionType == ConnectionType.SOAP => inMemorySourceFactory.forSoap(ctx.dataSource).applySource(state)
    }
    state -> flow
  }

  private def isDirectFlow = FlowType.IN_MEMORY.toString.equalsIgnoreCase(ctx.dataSource.getOriginalFlowType)

  private def buildIgnoreSink(elem: FlowNodeElement): Sink[BasicMessage, _] = {
    // this sink will just ignore the elements of the leaf transform nodes without sinks
    Sink.ignore
  }

  // technically this is not an akka sink because it needs to pass the info to the final sink-ignorer, the fan-in
  def buildSink(elem: FlowNodeElement): (ResourceState, Flow[BasicMessage, Any, Future[SinkDone]]) = {
    val state = new ResourceState(new Resource(elem.datasinkId, ResourceType.SINK))
    val dataSink = if (ctx.dataSink.getId == elem.datasinkId) ctx.dataSink
    else throw new Exception(s"Data sink with id: ${elem.datasinkId} was not found")

    val sinkFlow = if (dataSink.getConnectionType.equals(ConnectionType.BIGQUERY)) {
      val actualSink = new DirectBigQuerySink(PipelineUtils.toNewDataSinkFormat(dataSink), adminApiClient, ctx, listingClient, coordinationClient, messageProducer, offsetSaver, props)
      actualSink.recordsSinkFlow(state)
    } else if (dataSink.getConnectionType.isFile) {
      ctx.runtimeConfig match {
        case raySourceConfig: CustomRuntimeConfig.RayRuntimeConfig if !isDirectFlow =>
          inMemorySinkFactory.forBYOFile(dataSink, raySourceConfig, state).applySink()
        case _: CustomRuntimeConfig.RayRuntimeConfig if isDirectFlow =>
          inMemorySinkFactory.forFile(dataSink, state).applySink()
        case CustomRuntimeConfig.NoCustomRuntimeConfig =>
          inMemorySinkFactory.forFile(dataSink, state).applySink()
      }
    } else if (dataSink.getConnectionType.isDatabase) {
      inMemorySinkFactory.forJdbc(dataSink, state).applySink()
    } else if (dataSink.getConnectionType.isVectorDB) {
      inMemorySinkFactory.forVectorDb(dataSink, state).applySink()
    } else if (dataSink.getConnectionType.equals(ConnectionType.REST)) {
      inMemorySinkFactory.forRest(dataSink, state).applySink()
    } else if (dataSink.getConnectionType.equals(ConnectionType.SOAP)) {
      inMemorySinkFactory.forSoap(dataSink, state).applySink()
    } else if (dataSink.getConnectionType.equals(ConnectionType.DATA_MAP)) {
      inMemorySinkFactory.forRedis(dataSink, state).applySink()
    } else {
      throw new IllegalArgumentException(s"Sink with connection type ${dataSink.getConnectionType} is not yet supported")
    }
    (state, sinkFlow)
  }

  def buildTx(elem: FlowNodeElement): Flow[BasicMessage, BasicMessage, _] = {
    val dataSet = ctx.dataSets.find(_.getId == elem.datasetId).getOrElse(throw new Exception(s"Dataset with id: ${elem.datasetId} was not found"))

    if (dataSet.getTransformHistory.getTransforms.isEmpty) Flow[BasicMessage]
    else {
      val txConf = ctx.pipelineConf.nexlaTxConfigs.getOrElse(dataSet.getId, throw new Exception(s"No nexla tx config found for dataset: ${dataSet.getId}"))
      txFactory.forNexla(txConf, dataSet, props, transformService).applyTransformation()
    }
  }

  private def processGraphNode(headFlowRoot: Option[FlowNodeElement],
                               sinkLeaves: util.HashMap[Int, Outlet[_]],
                               signalFromOutside: Option[(Int, Outlet[BasicMessage])],
                               sendTo: Option[Int],
                               states: mutable.Set[ResourceState],
                               sinksMatValue: mutable.Set[Future[SinkDone]],
                               sharedKSW: SharedKillSwitch,
                              )(implicit b: GraphDSL.Builder[Future[akka.Done]]): Unit = {
    import GraphDSL.Implicits._
    logger.debug(s"[flow-assembly] processGraphNode(headFlowRoot=${headFlowRoot.map(_.toShortString)}),(sinkLeaves=$sinkLeaves),(signalFromOutside=$signalFromOutside),(sendTo=$sendTo)")
    if (headFlowRoot.isEmpty) {
      logger.debug("[flow-assembly] headFlowRoot is empty, returning")
    } else {
      val actualFlowElem = headFlowRoot.get
      val itsChildren = actualFlowElem.getChildren

      if (itsChildren.size() == 0 || (actualFlowElem.datasetId == 0)) { // children == 1 && dataSetId == 0 for restsink with webhook
        // it's a leaf. can be either a sink (OK for us, we'll write smth)
        if (actualFlowElem.datasinkId != 0) {
          // then it's a sink leaf, add it to execution
          logger.debug(s"[flow-assembly] ${actualFlowElem.datasinkId} is a sink leaf")
          val (resourceState, tmpSinkFlow) = buildSink(actualFlowElem)
          val sinkFlow = tmpSinkFlow.mapMaterializedValue { done =>
            sinksMatValue.add(done)
            done
          }
          states.add(resourceState)
          val sinkFlowShape = b.add(sinkFlow)

          if (signalFromOutside.isEmpty) {
            throw new IllegalArgumentException("traverse error, signal from outside must be present for a sink-leaf")
          } else {
            // connect this sink with its data provider
            logger.debug(s"[flow-assembly] connecting outside signal (${signalFromOutside.get._1}) for sink-leaf with sink-leaf ${actualFlowElem.datasinkId} inlet ${sinkFlowShape.in} and putting SHARED_KILLSWITCH before this sink")
            signalFromOutside.get._2.via(sharedKSW.flow) ~> sinkFlowShape.in
          }
          // and add it to our list for the final processing
          logger.debug(s"[flow-assembly] this sink (${actualFlowElem.datasinkId}) will now be saved for the final processing, ${sinkFlow.shape.out}")
          sinkLeaves.put(actualFlowElem.datasinkId, sinkFlowShape.out)

        } else if (actualFlowElem.datasetId != 0) {
          // or a dataset - it's ok too, we'll just ignore its output
          logger.debug(s"[flow-assembly] unfinished branch: dataset id (${actualFlowElem.datasetId}), ignoring since no sink present")
          val sinkFlowShape = b.add(buildIgnoreSink(actualFlowElem))
          logger.debug(s"[flow-assembly] connecting outside signal (${signalFromOutside.get._1}) for ignorer inlet ${sinkFlowShape.in}")
          signalFromOutside.get._2 ~> sinkFlowShape.in
        } else {
          // both dataset id and datasink ids are 0 - this should not happen
          logger.warn("[flow-assembly] leaf node has both dataset id and datasink id empty, ignoring")
          val sinkFlowShape = b.add(buildIgnoreSink(actualFlowElem))
          logger.debug(s"[flow-assembly] connecting outside signal (${signalFromOutside.get._1}) for ignorer inlet ${sinkFlowShape.in}")
          signalFromOutside.get._2 ~> sinkFlowShape.in
        }

      } else if (itsChildren.size() == 1) {
        // it's a transform, we need to build it
        logger.debug(s"[flow-assembly] ${actualFlowElem.datasetId} is a tx")
        val txFlow = buildTx(actualFlowElem)
        val txFlowShape = b.add(txFlow)

        if (signalFromOutside.isEmpty) {
          throw new IllegalArgumentException("traverse error, signal from outside must be present for a tx")
        } else if (sendTo.isEmpty) {
          logger.debug(s"[flow-assembly] ${actualFlowElem.datasetId} is a pass-through detected dataset")
          logger.debug(s"[flow-assembly] connecting signal from outside $signalFromOutside to this tx (${actualFlowElem.toShortString}) flow.in")
          signalFromOutside.get._2 ~> txFlowShape.in
        } else {
          // connect this tx with its data provider
          logger.debug(s"[flow-assembly] connecting signal from outside ${signalFromOutside.get._1} for tx with its inlet")
          signalFromOutside.get._2 ~> txFlowShape.in
          // and with the next node
          processGraphNode(None, sinkLeaves, Option(actualFlowElem.datasetId, txFlowShape.out), sendTo, states, sinksMatValue, sharedKSW)
        }
        // and move on further to the next point in this path
        val nextElem = itsChildren.get(0)
        val nextId = if (nextElem.datasinkId != 0) {
          nextElem.datasinkId
        } else if (nextElem.datasetId != 0) {
          nextElem.datasetId
        } else {
          throw new IllegalArgumentException("no inlet to send info to, both datasink and dataset ids are 0")
        }
        processGraphNode(Option(actualFlowElem.getChildren.get(0)), sinkLeaves, Option(actualFlowElem.getDatasetId, txFlowShape.out), Option(nextId), states, sinksMatValue, sharedKSW)
      } else if (itsChildren.size() > 1) {
        val numberOfBranches = itsChildren.size()
        logger.debug(s"[flow-assembly] ${actualFlowElem.datasetId} is a tx splitter for ($numberOfBranches) children, checking them")
        val txFlow = buildTx(actualFlowElem)
        val txFlowShape = b.add(txFlow)

        // it's a splitter tx node, and it still needs the signal from the outside
        val splitter: UniformFanOutShape[BasicMessage, BasicMessage] = if (props.trackerDisabled) {
          b.add(Broadcast[BasicMessage](numberOfBranches))
        } else {
          b.add(CloningBroadcast[BasicMessage](numberOfBranches))
        }
        logger.debug(s"[flow-assembly] connecting signal from the outside (${signalFromOutside.get._1}) with this tx node ${actualFlowElem.datasetId} and then with splitter node inlet")
        signalFromOutside.get._2 ~> txFlowShape.in
        txFlowShape.out ~> splitter.in
        // and then let's do it all over again for the children
        val splitterId = actualFlowElem.datasetId
        for (i <- 0 until itsChildren.size()) {
          logger.debug(s"[flow-assembly] ${actualFlowElem.datasetId} ->> child #($i)")
          val childBranchHead = itsChildren.get(i)

          if (childBranchHead.getChildren.size() > 0) {
            val possibleDestination = childBranchHead.getChildren.get(0)
            val nextId = if (possibleDestination.datasinkId != 0) {
              possibleDestination.datasinkId
            } else if (possibleDestination.datasetId != 0) {
              possibleDestination.datasetId
            } else {
              throw new IllegalArgumentException("both dataset and datasink IDs of destination are 0, dunno where to send")
            }
            processGraphNode(Option(childBranchHead), sinkLeaves, Option(splitterId, splitter.out(i)), Option(nextId), states, sinksMatValue, sharedKSW)
          } else {
            val nextId = if (childBranchHead.datasinkId != 0) {
              childBranchHead.datasinkId
            } else if (childBranchHead.datasetId != 0) {
              childBranchHead.datasetId
            } else {
              throw new IllegalArgumentException("both dataset and datasink IDs of destination are 0, dunno where to send")
            }
            processGraphNode(Option(childBranchHead), sinkLeaves, Option(splitterId, splitter.out(i)), Option(nextId), states, sinksMatValue, sharedKSW)
          }
        }
      }
    }
  }

  private def filterFlow(allowDatasetId: Int): Flow[BasicMessage, BasicMessage, _] = {
    Flow[BasicMessage].filter(_.headDatasetId.contains(allowDatasetId))
  }

  // the source id is the root of this DAG and its beginning
  // it produces a stream of something. if we are doing TxPipeline - it must be a stream of NexlaMessages,
  // if not - then a sequence of files which need to be downloaded, then uploaded elsewhere.
  def build(): Try[PipelineBlueprint] = Try {
    val sharedKSW = KillSwitches.shared("for-all-sinks")
    val states: mutable.Set[ResourceState] = mutable.Set.empty
    val sinksMatValue: mutable.Set[Future[SinkDone]] = mutable.Set.empty
    val sourceMatValue: mutable.Set[Future[SourceDone]] = mutable.Set.empty

    val graph = RunnableGraph.fromGraph(
      // sink.ignore because we are interested just in the fact sink finishes. all the interim processing is done by relevant sink classes
      GraphDSL.create(Sink.ignore) { implicit b: GraphDSL.Builder[Future[akka.Done]] =>
        ignoreFinalSink =>
          import GraphDSL.Implicits._
          val (resourceState, tmpSource) = buildSource(PipelineUtils.toNewDataSourceFormat(ctx.dataSource))
          val source = tmpSource.mapMaterializedValue { done =>
            sourceMatValue.add(done)
            done
          }
          states.add(resourceState)
          val initialListingSource = b.add(source)


          val sinkLeaves = new util.HashMap[Int, Outlet[_]]()
          // source splitter must have outlets for all the detected datasets
          val sourceSplitter: UniformFanOutShape[BasicMessage, BasicMessage] = b.add(Broadcast[BasicMessage](ctx.flow.getChildren.size()))
          initialListingSource.out ~> sourceSplitter.in

          var i = 0
          ctx.flow.getChildren.asScala.foreach {
            detectedDataset =>
              logger.debug(s"[flow-assembly] processing flow for dataset ${detectedDataset.getDatasetId}, attaching to source splitter port $i")
              val datasetIdFilter = b.add(filterFlow(detectedDataset.getDatasetId))
              sourceSplitter.out(i) ~> datasetIdFilter.in

              processGraphNode(Option(detectedDataset), sinkLeaves, Option(ctx.dataSource.getId, datasetIdFilter.out), None, states, sinksMatValue, sharedKSW)
              i = i + 1
          }

          val mainIgnoreMerger: UniformFanInShape[Any, _] = b.add(Merge[Any](sinkLeaves.size()))

          var j = 0
          sinkLeaves.asScala.foreach {
            case (k, v) =>
              logger.debug(s"[flow-assembly] attaching $k (akka stream ${v.toString()}) to main ignore merger, port $j")
              v ~> mainIgnoreMerger
              j = j + 1
          }

          // and finally connect the main ignore merger with the sink for the Future[Done]
          logger.debug("[flow-assembly] attaching main ignore merger with final sink")
          mainIgnoreMerger ~> ignoreFinalSink
          ClosedShape
      }
    )
    val runFn: () => Future[Unit] = () => for {
      _ <- graph.run()
      _ <- Future.sequence(sourceMatValue.toList)
      _ <- Future.sequence(sinksMatValue.toList)
    } yield ()
    PipelineBlueprint(runFn, () => sharedKSW.shutdown(), states.toList)
  }
}

