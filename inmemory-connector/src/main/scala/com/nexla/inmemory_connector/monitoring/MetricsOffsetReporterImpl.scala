package com.nexla.inmemory_connector.monitoring


import cats.implicits.toTraverseOps
import com.nexla.common.ResourceType
import com.nexla.control.ListingFileStatus
import com.nexla.inmemory_connector.compat.{FileOffset, MapOffset, SinkMetric}
import com.nexla.inmemory_connector.pipeline.sink.flusher.SinkFlusher.FlushingResults
import com.nexla.inmemory_connector.pipeline.sink.offsets.{FileOffsetCommitter, MapOffsetCommitter}
import com.nexla.inmemory_connector.state.FilesState
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.nexla.sc.util.StrictNexlaLogging

import scala.concurrent.{ExecutionContext, Future}


class MetricsOffsetReporterImpl(fileOffsetCommitter: FileOffsetCommitter, mapOffsetCommitter: MapOffsetCommitter, filesState: FilesState, metricsSender: MetricsSender)(implicit ec: ExecutionContext) extends MetricsOffsetReporter with StrictNexlaLogging {

  def handle(offsets: FlushingResults): Future[Unit] = {
    val handleSourceOffsetsEff = offsets.sourceOffsets.traverse {
      case fo: FileOffset => handleFileOffset(fo)
      case mo: MapOffset => handleMapOffset(mo)
    }.map(_ => ())
    val handleSinkMetricsEff = offsets.sinkMetrics.traverse(handleSinkMetrics).map(_ => ())

    for {
      _ <- handleSourceOffsetsEff.tapError(logger.error(s"Failed to handle source offsets: ${offsets.sourceOffsets}", _))
      _ <- handleSinkMetricsEff.tapError(logger.error(s"Failed to handle sink metrics: ${offsets.sinkMetrics}", _))
    } yield ()
  }

  private def handleSinkMetrics(sinkMetric: SinkMetric): Future[Unit] = {
    metricsSender.publishMetric(sinkMetric.sinkId, ResourceType.SINK, None, 0, sinkMetric.size, sinkMetric.filePath, None, sinkMetric.maybeAdaptiveFlowTask)
  }


  private def handleFileOffset(fo: FileOffset): Future[Unit] = {
    val fileStatus = if (fo.eof) ListingFileStatus.DONE else ListingFileStatus.STARTED
    if (fileStatus == ListingFileStatus.DONE) {
      filesState.removeFile(fo.fileId)
    }
    fileOffsetCommitter.commit(fo.sourceId, fo.fileId, fileStatus, Some(fo.offset), fo.message)
  }

  private def handleMapOffset(offset: MapOffset): Future[Unit] = mapOffsetCommitter.commit(offset.sourceId, offset.sinkId, offset.offset)

}

