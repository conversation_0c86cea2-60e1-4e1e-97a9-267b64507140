package com.nexla.inmemory_connector.monitoring

import com.nexla.admin.client.{DataSink, DataSource, OwnerAndOrg}
import com.nexla.common.ResourceType
import com.nexla.common.ResourceType.SINK
import com.nexla.common.notify.monitoring.{NexlaMonitoringLogEvent, NexlaMonitoringLogSeverity, NexlaMonitoringLogType}
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.typesafe.scalalogging.Logger

class FileConnectorFlowLogs(messageProducer: NexlaMessageProducer) {

  def publishMonitoringLog(
                            log: String,
                            resourceId: Int,
                            resourceType: ResourceType,
                            maybeOrganizationId: Option[Int],
                            runId: Long,
                            severity: NexlaMonitoringLogSeverity): Unit = {
    publishMonitoringLog(log, resourceId, resourceType, maybeOrganizationId, runId, NexlaMonitoringLogType.LOG, severity)
  }

  def publishMonitoringLog(
                                    log: String,
                                    resourceId: Int,
                                    resourceType: ResourceType,
                                    maybeOrganizationId: Option[Int],
                                    runId: Long,
                                    logType: NexlaMonitoringLogType,
                                    severity: NexlaMonitoringLogSeverity): Unit = {
    val orgId: Int = maybeOrganizationId.getOrElse(0)
    val monitoringLogEvent = NexlaMonitoringLogEvent.of(
      orgId, runId, resourceId, resourceType, log, logType, severity, System.currentTimeMillis()
    )
    messageProducer.publishMonitoringLog(monitoringLogEvent)
  }

  private def getOrgId(o: OwnerAndOrg): Option[Int] = Option(o.getOrg).flatMap(o => Option(o.getId))

  def publishSinkStartingFlowLogs(dataSink: DataSink, runId: Long): Unit = {
    val orgId = getOrgId(dataSink)
    publishMonitoringLog("Task initialization is done", dataSink.getId, ResourceType.SINK, orgId, runId, NexlaMonitoringLogType.EVENT, NexlaMonitoringLogSeverity.INFO)
    publishMonitoringLog("Start processing data", dataSink.getId, ResourceType.SINK, orgId, runId, NexlaMonitoringLogType.EVENT, NexlaMonitoringLogSeverity.INFO)
  }

  def publishSinkEndingFlowLogs(dataSink: DataSink, runId: Long, processedRecords: Long): Unit = {
    val orgId = getOrgId(dataSink)
    publishMonitoringLog("Writing done", dataSink.getId, ResourceType.SINK, orgId, runId, NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.INFO)
    publishMonitoringLog("Stopping task...", dataSink.getId, ResourceType.SINK, orgId, runId, NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.INFO)
    publishMonitoringLog(s"Total of $processedRecords records processed", dataSink.getId, ResourceType.SINK, orgId, runId, NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.INFO)
  }

  def logAndPublishSinkMonitoringLog(logger: Logger, dataSink: DataSink, log: String, runId: Long): Unit = {
    logger.info(log)
    publishMonitoringLog(log, dataSink.getId, SINK, getOrgId(dataSink), runId, NexlaMonitoringLogSeverity.INFO)
  }

  def logAndPublishSinkMonitoringError(logger: Logger, dataSink: DataSink, log: String, runId: Long, e: Throwable): Unit = {
    logger.error(log, e)
    publishMonitoringLog(log, dataSink.getId, SINK, getOrgId(dataSink), runId, NexlaMonitoringLogSeverity.ERROR)
  }

  def logAndPublishSourceMonitoringLog(logger: Logger, dataSource: Option[DataSource], log: String, runId: Long): Unit = {
    logger.info(log)
    dataSource.foreach(ds => publishMonitoringLog(log, ds.getId, ResourceType.SOURCE, getOrgId(ds), runId, NexlaMonitoringLogSeverity.INFO))
  }

  def logAndPublishSourceMonitoringError(logger: Logger, dataSource: Option[DataSource], log: String, runId: Long, e: Throwable): Unit = {
    logger.error(log, e)
    dataSource.foreach(ds => publishMonitoringLog(log, ds.getId, ResourceType.SOURCE, getOrgId(ds), runId, NexlaMonitoringLogSeverity.ERROR))
  }

}
