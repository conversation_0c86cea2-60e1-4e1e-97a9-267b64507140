package com.nexla.inmemory_connector.monitoring

import com.nexla.common.ResourceType
import com.nexla.common.metrics.NexlaRawMetric
import com.nexla.connector.config.FlowType
import com.nexla.inmemory_connector.pipeline.PipelineUtils.putNameForAdaptiveFlowTask
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryMessageProducer
import com.nexla.listing.client.AdaptiveFlowTask
import com.nexla.sc.metric.MetadataBuilder
import com.nexla.sc.util.Async

import scala.compat.java8.OptionConverters.RichOptionForJava8
import scala.concurrent.{ExecutionContext, Future}

class MetricsSenderImpl(messageProducer: InMemoryMessageProducer, flowType: FlowType, runId: Long) extends MetricsSender {
  protected val ioEx: ExecutionContext = Async.ioExecutorContext

  def publishMetric(resourceId: Int, resourceType: ResourceType, dataSetId: Option[Int], records: Long, fileSize: Long, filePath: String, eof: Option[Boolean], maybeAdaptiveFlowTask: Option[AdaptiveFlowTask] = Option.empty): Future[Unit] = Future {
    val metadataBuilder = if (maybeAdaptiveFlowTask.isDefined) {
      // adaptive flows case - just use it instead
      new MetadataBuilder().withName(getName(maybeAdaptiveFlowTask.get))
    } else {
      // usual scenario
      new MetadataBuilder().withName(filePath)
    }
    val metricEvent = NexlaRawMetric.create(resourceType, resourceId, records, fileSize, 0,
      System.currentTimeMillis(), Some(java.lang.Long.valueOf(runId)).asJava, eof.map(java.lang.Boolean.valueOf).asJava,
      None.asJava, dataSetId.map(java.lang.Integer.valueOf).asJava, None.asJava, None.asJava, Some(metadataBuilder.build()).asJava, flowType,
      None.asJava, None.asJava, None.asJava, null, null)

    maybeAdaptiveFlowTask.foreach { task =>
      putNameForAdaptiveFlowTask(metricEvent, task)
    }

    messageProducer.publishMetrics(metricEvent)
  }(ioEx)

  private def getName(task: AdaptiveFlowTask): String = {
    String.valueOf(task.getId)
  }
}
