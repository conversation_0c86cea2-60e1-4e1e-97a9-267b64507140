package com.nexla.inmemory_connector.monitoring

import com.nexla.common.ResourceType
import com.nexla.listing.client.AdaptiveFlowTask

import scala.concurrent.Future

trait MetricsSender {
  def publishMetric(resourceId: Int, resourceType: ResourceType, dataSetId: Option[Int], records: Long, fileSize: Long, filePath: String, eof: Option[Boolean], maybeAdaptiveFlowTask: Option[AdaptiveFlowTask] = Option.empty): Future[Unit]
}
