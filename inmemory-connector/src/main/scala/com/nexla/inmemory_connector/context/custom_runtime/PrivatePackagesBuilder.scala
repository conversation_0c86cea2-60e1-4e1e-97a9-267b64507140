package com.nexla.inmemory_connector.context.custom_runtime

import com.nexla.common.NexlaDataCredentials
import com.nexla.connector.config.rest.RestAuthConfig
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiClient
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.typesafe.scalalogging.StrictLogging

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class PrivatePackagesBuilder(props: AppProps, builderCommons: BuilderCommons)(implicit ec: ExecutionContext) extends StrictLogging {

  def handlePrivatePackages(p: ExtraDataBuilder.PrivatePackages): Future[RayApiClient.PrivatePackages] = {
    for {
      dataCreds <- builderCommons.fetchDataCredentials(p.credentialId).tapError(e => logger.error("Data credentials for private packages not found", e))
      creds <- Future(NexlaDataCredentials.getCreds(props.decryptKey, dataCreds.getCredentialsEnc, dataCreds.getCredentialsEncIv))
      restAuth <- Future(new RestAuthConfig(creds, p.credentialId))
      username <- Future.fromTry(extractUsername(restAuth)).tapError(e => logger.error("Username not found", e))
      password <- Future.fromTry(extractPassword(restAuth)).tapError(e => logger.error("Password not found", e))
    } yield RayApiClient.PrivatePackages(username, password, p.urls)
  }

  private def extractUsername(restAuthConfig: RestAuthConfig): Try[String] =
    Option(restAuthConfig.basicUsername) match {
      case Some(username) => Success(username)
      case None => Failure(new Exception("Username not found"))
    }

  private def extractPassword(restAuthConfig: RestAuthConfig): Try[String] =
    Option(restAuthConfig.basicPassword) match {
      case Some(username) => Success(username)
      case None => Failure(new Exception("Password not found"))
    }
}
