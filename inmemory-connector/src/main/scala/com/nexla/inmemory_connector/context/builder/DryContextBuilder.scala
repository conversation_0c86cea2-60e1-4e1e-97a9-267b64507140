package com.nexla.inmemory_connector.context.builder

import cats.implicits.toTraverseOps
import com.bazaarvoice.jolt.JsonUtils
import com.nexla.admin.client._
import com.nexla.common.{ConnectionType, NexlaDataCredentials}
import com.nexla.connector.config.file.AWSAuthConfig
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.context.Context.{DryRunContext, DryRunMode}
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig.{MezzanineAuthConfig, NoCustomRuntimeConfig, RayRuntimeConfig}
import com.nexla.inmemory_connector.context.custom_runtime.ExtraDataBuilder.ExtraData
import com.nexla.inmemory_connector.context.custom_runtime.{AdditionalDryRunMetadata, BuilderCommons, ExtraDataBuilder, PrivatePackagesBuilder}
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.nexla.sc.util.StrictNexlaLogging
import spray.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsObject, JsString, JsValue, JsonParser}

import java.util
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters.mapAsScalaMapConverter
import scala.util.Try

class DryContextBuilder(adminApi: AdminApiClient, props: AppProps)(implicit ec: ExecutionContext) extends StrictNexlaLogging {
  private val builderCommons = new BuilderCommons(adminApi, props)
  private val privatePackagesBuilder = new PrivatePackagesBuilder(props, builderCommons)

  private def extractSourceCredsId(): Try[Int] = Try {
    props.imcDryRunSourceCredsId.getOrElse(throw new Exception("Dry run source creds id not found"))
  }

  private def extractMezzanineCredsId(): Try[Int] = Try {
    props.imcMezzanineCredsId.getOrElse(throw new Exception("Mezzanine creds id not found"))
  }

  private def extractRunId(): Long = props.imcRunId.getOrElse(-1)

  private def extractDriverFunction(): Try[String] = Try {
    props.imcDryRunDriverFunction.getOrElse(throw new Exception("Driver function was not found"))
  }

  private def extractPackages(): Try[List[String]] = Try {
    props.imcDryRunPackages.map(_.split(",").toList).getOrElse(List())
  }

  private def extractExtraData(): Try[Option[ExtraData]] = for {
    maybeRawJson <- Try(props.imcDryRunExtraData.map(JsonParser(_)))
    extraData <- maybeRawJson.traverse(new ExtraDataBuilder().parse)
  } yield extraData

  private def extractSourceConfig(): Try[Map[String, AnyRef]] = for {
    maybeRawSrcConfig <- Try(props.imcDryRunSrcConfig.map(JsonUtils.jsonToMap).map(_.asScala.toMap))
    srcConfig <- Try(maybeRawSrcConfig.getOrElse(throw new Exception("Source config not found")))
  } yield srcConfig

  private def extractSourceFilePath(): Try[String] = Try {
    props.imcDryRunInputFile.getOrElse(throw new Exception("Dry run input file not found"))
  }

  private def extractAdditionalMetadata(): Try[Option[AdditionalDryRunMetadata]] = for {
    maybeRawJson <- Try(props.imcDryRunAdditionalMetadata.map(JsonParser(_)))
    additionalMetaData <- maybeRawJson.traverse(AdditionalDryRunMetadata.parse)
  } yield additionalMetaData

  private def extractOutputFilename(): Try[String] = Try {
    props.imcDryRunOutputFileName.getOrElse(throw new Exception("Dry run output filename not found"))
  }

  def createPipelineContext(): Future[DryRunContext] = {
    for {
      sourceCredentials <- Future.fromTry(extractSourceCredsId()).flatMap(builderCommons.fetchDataCredentials)
      mezzanineCredentials <- Future.fromTry(extractMezzanineCredsId()).flatMap(builderCommons.fetchDataCredentials)
      mezzanineBucketPrefix <- Future.fromTry(builderCommons.extractMezzanineBucketPrefix())
      sourceFilePath <- Future.fromTry(extractSourceFilePath())
      runId = extractRunId()
      outputBaseFileName <- Future.fromTry(extractOutputFilename())
      dryRunUploadPath <- Future.fromTry(builderCommons.extractDryRunUploadPath())
      driverFunction <- Future.fromTry(extractDriverFunction())
      packages <- Future.fromTry(extractPackages())
      extraData <- Future.fromTry(extractExtraData()).tapError(logger.error("Failed to extract ExtraData", _)).recover { case _ => None }
      srcConfig <- Future.fromTry(extractSourceConfig()).tapError(logger.error("Failed to extract source config", _))
      maybeAdditionalMetadata <- Future.fromTry(extractAdditionalMetadata()).tapError(logger.error("failed to extract additional metadata", _)).recover { case _ => None }
      additionalMetadata = maybeAdditionalMetadata.getOrElse(AdditionalDryRunMetadata.Empty)
      mezzanineAuthConfig = new AWSAuthConfig(NexlaDataCredentials.getCreds(props.decryptKey, mezzanineCredentials.getCredentialsEnc, mezzanineCredentials.getCredentialsEncIv), mezzanineCredentials.getId)
      mezzanineS3Creds = MezzanineAuthConfig(mezzanineBucketPrefix, mezzanineAuthConfig, mezzanineCredentials)
      privatePackages <- extraData.flatMap(_.privatePackage).traverse(privatePackagesBuilder.handlePrivatePackages)
      directoryTxConfig = props.imcDryRunCustomCode.map(imcCustomCode => RayRuntimeConfig(imcCustomCode, driverFunction, packages, privatePackages, mezzanineS3Creds, mezzanineS3Creds, extraData.map(_.extraData), false)).getOrElse(NoCustomRuntimeConfig)
      dryRunMode = if (outputBaseFileName.toLowerCase.startsWith("l")) DryRunMode.ListingMode else DryRunMode.DesignMode
    } yield DryRunContext(runId, sourceCredentials, sourceFilePath, outputBaseFileName, dryRunUploadPath, mezzanineS3Creds, srcConfig, directoryTxConfig, dryRunMode, additionalMetadata)
  }
    .tap(ctx => logger.info(s"Successfully created context for pipeline: $ctx"))
    .tapError(err => logger.error("Failed to create a context for pipeline", err))

}

