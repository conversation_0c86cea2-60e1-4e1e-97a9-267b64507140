package com.nexla.inmemory_connector.context.builder

import com.nexla.admin.client.AdminApiClient
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.context.Context
import com.nexla.listing.client.AdaptiveFlowTask
import com.nexla.sc.util.StrictNexlaLogging

import scala.concurrent.{ExecutionContext, Future}

class ContextBuilder(adminApi: AdminApiClient, props: AppProps, nexlaMessageProducer: NexlaMessageProducer)(implicit ec: ExecutionContext) extends StrictNexlaLogging {

  def createPipelineContext(maybeAdaptiveFlowTask: Option[AdaptiveFlowTask], flowId: Int, runId: Long): Future[Context] = if (props.imcDryRunEnabled) {
    new DryContextBuilder(adminApi, props).createPipelineContext()
  } else {
    new PipelineContextBuilder(flowId, runId, adminApi, props, maybeAdaptiveFlowTask, nexlaMessageProducer).createPipelineContext()
  }
}
