package com.nexla.inmemory_connector.context.custom_runtime

import cats.implicits.toTraverseOps
import com.nexla.admin.client.{AdminApiClient, CodeContainer, DataCredentials, DataSink, DataSource}
import com.nexla.common.{NexlaConstants, NexlaDataCredentials}
import com.nexla.connector.config.file.AWSAuthConfig
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig.{MezzanineAuthConfig, RayRuntimeConfig}
import com.nexla.inmemory_connector.context.custom_runtime.ExtraDataBuilder.ExtraData
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiClient.PrivatePackages
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.nexla.sc.util.StrictNexlaLogging
import spray.json.{<PERSON>s<PERSON><PERSON><PERSON>, JsonParser}

import scala.compat.java8.OptionConverters.RichOptionalGeneric
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters.mapAsScalaMapConverter
import scala.util.Try

class RayContextBuilder(adminApi: AdminApiClient, props: AppProps)(implicit ec: ExecutionContext) extends StrictNexlaLogging {
  private val builderCommons = new BuilderCommons(adminApi, props)
  private val privatePackagesBuilder = new PrivatePackagesBuilder(props, builderCommons)

  def buildRayConfig(dataSource: DataSource, codeContainer: CodeContainer, dataSink: DataSink, runId: Long): Future[RayRuntimeConfig] = {
    for {
      mezzanineCreds <- fetchMezzanineDataCredentials()
      mezzanineBucketPrefix <- Future.fromTry(builderCommons.extractMezzanineBucketPrefix())
      extraData <- Future.fromTry(extractExtraData(codeContainer)).tapError(logger.error("Failed to extract ExtraData from code container", _)).recover { case _ => None }
      privatePackages <- extraData.flatMap(_.privatePackage).traverse(privatePackagesBuilder.handlePrivatePackages)
      _ = enrichSourceConfig(dataSource, mezzanineCreds, s"$mezzanineBucketPrefix/ray-input/${dataSource.getId}/$runId")
      mezzanineAuthConfig = MezzanineAuthConfig(mezzanineBucketPrefix, new AWSAuthConfig(NexlaDataCredentials.getCreds(props.decryptKey, mezzanineCreds.getCredentialsEnc, mezzanineCreds.getCredentialsEncIv), mezzanineCreds.getId), mezzanineCreds)
      rayConfig <- Future.fromTry(buildDirectoryTxRayConfig(codeContainer, mezzanineAuthConfig, codeContainer.getCode, extraData.map(_.extraData), privatePackages, dataSource, dataSink)).tapError(logger.error("Failed to build ray tx configs", _))
    } yield rayConfig
  }

  private def enrichSourceConfig(dataSource: DataSource, tempCreds: DataCredentials, uploadPath: String): Unit = {
    dataSource.getSourceConfig.put(NexlaConstants.CUSTOM_RT_CREDS_ENC, tempCreds.getCredentialsEnc)
    dataSource.getSourceConfig.put(NexlaConstants.CUSTOM_RT_CREDS_ENCIV, tempCreds.getCredentialsEncIv)
    dataSource.getSourceConfig.put(NexlaConstants.CUSTOM_RT_UPLOAD_PATH, uploadPath)
    dataSource.getSourceConfig.put(NexlaConstants.CUSTOM_RT_MESSAGE_EMISSION_MODE, "full_ingestion")
  }

  private def extractExtraData(codeContainer: CodeContainer): Try[Option[ExtraData]] = for {
    maybeRawStr <- Try(codeContainer.getCodeConfig.asScala.flatMap(_.getExtraData.asScala).filter(_.nonEmpty))
    maybeRawJson <- Try(maybeRawStr.map(JsonParser(_)))
    extraData <- maybeRawJson.traverse(new ExtraDataBuilder().parse)
  } yield extraData

  private def fetchMezzanineDataCredentials(): Future[DataCredentials] =
    props.imcMezzanineCredsId match {
      case Some(credsId) => builderCommons.fetchDataCredentials(credsId).tapError(logger.error(s"Failed to fetch mezzanine credentials $credsId", _))
      case None => Future.failed(new Exception("No mezzanine creds id found"))
    }

  private def canRunWithBYOOptimizedFlow(dataSource: DataSource, dataSink: DataSink): Boolean = {
    // Determines if BYO optimized flow should be used for file-to-file transfers.
    // Priority: source config "imc.byo.optimized.flow.enabled" > env var "IMC_BYO_OPTIMIZED_FLOW_ENABLED" > false
    val envDefault = props.imcByoOptimizedFlowEnabled
    val sourceConfigOption = Try(
      dataSource.getSourceConfig.asScala
        .get("imc.byo.optimized.flow.enabled")
        .flatMap(Option(_))
    ).getOrElse(None)
    
    val byoOptimizedFlowEnabled = sourceConfigOption match {
      case Some(value) => Try(value.toString.toBoolean).getOrElse(envDefault)
      case None => envDefault
    }
    
    if (byoOptimizedFlowEnabled) {
      if (dataSource.getConnectionType.isFile && dataSink.getConnectionType.isFile) {
        logger.info(s"Optimized BYO flow is enabled (env default: $envDefault, source config: $sourceConfigOption, final: $byoOptimizedFlowEnabled)")
        true
      } else {
        logger.info(s"Optimized BYO flow is enabled but the source or sink is not a file (env default: $envDefault, source config: $sourceConfigOption, final: $byoOptimizedFlowEnabled)")
        false
      }
    } else {
      false
    }
  }

  private def buildDirectoryTxRayConfig(codeContainer: CodeContainer, mezzanineAuthConfig: MezzanineAuthConfig, txCode: String, extraData: Option[JsValue], privatePackages: Option[PrivatePackages], dataSource: DataSource, dataSink: DataSink): Try[RayRuntimeConfig] = Try {
    val packages = codeContainer.getCodeConfig.asScala.flatMap(_.getPackages.asScala).getOrElse("")
    val arrayedPackages = if (packages == "") List.empty else packages.split(",").toList
    RayRuntimeConfig(
      customCode = txCode,
      driverFunction = HardcodedValues.driverFunction,
      packages = arrayedPackages,
      privatePackages = privatePackages,
      destS3Credentials = mezzanineAuthConfig,
      sourceS3Credentials = mezzanineAuthConfig,
      extraData = extraData,
      byoOptimized = canRunWithBYOOptimizedFlow(dataSource, dataSink),
    )
  }

  private object HardcodedValues {
    val driverFunction = "process"
  }
}

