package com.nexla.inmemory_connector.context.custom_runtime

import com.nexla.admin.client.{AdminApiClient, CodeContainer, DataSink, DataSource}
import com.nexla.connector.config.FlowType
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig.NoCustomRuntimeConfig
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.nexla.sc.util.StrictNexlaLogging

import scala.concurrent.{ExecutionContext, Future}

class CustomRuntimeConfigBuilder(adminApi: AdminApiClient, props: AppProps)(implicit ec: ExecutionContext) extends StrictNexlaLogging {

  private def fetchCodeContainer(dataSource: DataSource): Future[Option[CodeContainer]] = {
    Option(dataSource.getCodeContainerId) match {
      case Some(codeContainerId) => Future(adminApi.getCodeContainer(codeContainerId)).map(Some(_)).tapError(logger.error(s"Failed to fetch code container $codeContainerId", _))
      case None => Future.successful(None)
    }
  }

  def buildCustomRuntimeConfig(flowType: FlowType, dataSource: DataSource, dataSink: DataSink, runId: Long): Future[CustomRuntimeConfig] = {
    fetchCodeContainer(dataSource).flatMap {
      case Some(codeContainer) => new RayContextBuilder(adminApi, props).buildRayConfig(dataSource, codeContainer, dataSink, runId)
      case None =>
        if (flowType == FlowType.CUSTOM) Future.failed(new Exception("With custom flow a code has to be always defined"))
        else Future.successful(NoCustomRuntimeConfig)
    }
  }.tapError(logger.error("Failure while building custom runtime config. Falling back to NoCustomRuntime", _))

}
