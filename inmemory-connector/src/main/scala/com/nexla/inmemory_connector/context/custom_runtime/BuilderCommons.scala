package com.nexla.inmemory_connector.context.custom_runtime

import com.nexla.admin.client.{AdminApiClient, DataCredentials}
import com.nexla.connector.config.file.AWSAuthConfig
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector_common.utils.TappingOps.tryOps
import com.typesafe.scalalogging.StrictLogging

import scala.compat.java8.OptionConverters.RichOptionalGeneric
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Success, Try}

class BuilderCommons(adminApi: AdminApiClient, props: AppProps)(implicit ec: ExecutionContext) extends StrictLogging {
  def fetchDataCredentials(credsId: Int): Future[DataCredentials] = {
    Future(adminApi.getDataCredentials(credsId)).map(_.asScala).flatMap {
      case Some(value) => Future.successful(value)
      case None => Future.failed(new Exception(s"Data credentials with id $credsId not found"))
    }
  }

  def extractMezzanineBucketPrefix(): Try[String] = {
    val bucketPathStr = props.imcMezzanineBucketPrefix
    val bucketPathStrWithoutSlash = if (bucketPathStr.endsWith("/")) bucketPathStr.dropRight(1) else bucketPathStr
    for {
      bucketPrefix <- Try(AWSAuthConfig.toBucketPrefix(bucketPathStrWithoutSlash, false)).tapError(logger.error("Mezzanine bucket prefix has invalid value", _))
      prefix = Option(bucketPrefix.prefix).fold("")(p => s"/$p")
    } yield s"${bucketPrefix.bucket}$prefix"
  }

  def extractDryRunUploadPath(): Try[String] =
    props.imcDryRunUploadPath.map(uploadPath => if (uploadPath.endsWith("/")) uploadPath.dropRight(1) else uploadPath) match {
      case Some(value) => Success(value)
      case None =>
        val defaultVal = "nexla-shared-s3/probe-run"
        logger.warn(s"Dry run upload path is not defined, using default value: $defaultVal")
        Success(defaultVal)
    }

}
