package com.nexla.inmemory_connector.context.custom_runtime

import com.bazaarvoice.jolt.JsonUtils
import com.nexla.inmemory_connector_common.utils.TappingOps.tryOps
import com.typesafe.scalalogging.StrictLogging
import spray.json.{JsObject, JsString, JsValue}

import scala.util.{Failure, Success, Try}
import java.util

case class AdditionalDryRunMetadata(fileMetadata: Option[util.Map[String, AnyRef]])

object AdditionalDryRunMetadata extends StrictLogging {
  private def extractFileMetadata(jsObject: JsObject): Option[util.Map[String, AnyRef]] = {
    jsObject.fields.get("fileMetadata") match {
      case Some(JsString(value)) =>
        Try(JsonUtils.jsonToMap(value)).tapError(logger.error("Failed to parse fileMetadata", _)).toOption
      case _ =>
        logger.info("Using empty fileMetadata")
        None
    }
  }

  def parse(additionalMetadata: JsValue): Try[AdditionalDryRunMetadata] = {
    additionalMetadata match {
      case jsObject: JsObject =>
        val fileMetadata = extractFileMetadata(jsObject)
        Success(AdditionalDryRunMetadata(fileMetadata))

      case other =>
        val errMsg = s"Failed to read additional metadata. Expected JsObject but got $other"
        logger.error(errMsg)
        Failure(new Exception(errMsg))
    }
  }

  val Empty: AdditionalDryRunMetadata = AdditionalDryRunMetadata(None)
}
