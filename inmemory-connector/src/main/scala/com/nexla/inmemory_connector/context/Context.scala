package com.nexla.inmemory_connector.context

import com.nexla.admin.client.flownode.AdminApiFlow
import com.nexla.admin.client.{DataCredentials, DataSet, DataSink, DataSource}
import com.nexla.connector.config.FlowType
import com.nexla.inmemory_connector.compat.PipelineConf
import com.nexla.inmemory_connector.context.custom_runtime.{AdditionalDryRunMetadata, CustomRuntimeConfig}
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig.MezzanineAuthConfig
import com.nexla.listing.client.AdaptiveFlowTask

sealed trait Context

object Context {

  final case class PipelineContext(
                                    runId: Long,
                                    flowType: FlowType,
                                    flow: AdminApiFlow,
                                    dataSource: DataSource,
                                    dataSets: List[DataSet],
                                    dataSink: DataSink,
                                    pipelineConf: PipelineConf,
                                    runtimeConfig: CustomRuntimeConfig,
                                    maybeAdaptiveFlowTask: Option[AdaptiveFlowTask] = Option.empty
                                  ) extends Context

  final case class DryRunContext(
                                  runId: Long,
                                  sourceCredentials: DataCredentials,
                                  fullSourceFilePath: String,
                                  dryRunIdentifier: String,
                                  dryRunUploadPath: String,
                                  mezzanineCredentials: MezzanineAuthConfig,
                                  srcConfig: Map[String, AnyRef],
                                  customRuntimeConfig: CustomRuntimeConfig,
                                  dryRunMode: DryRunMode,
                                  additionalMetadata: AdditionalDryRunMetadata,
                                ) extends Context

  sealed trait DryRunMode
  object DryRunMode {
    case object ListingMode extends DryRunMode

    case object DesignMode extends DryRunMode
  }

}
