package com.nexla.inmemory_connector

import akka.actor.{ActorSystem, Scheduler}
import akka.http.scaladsl.Http
import akka.http.scaladsl.server.Route
import akka.stream.{KillSwitches, Materializer}
import akka.stream.scaladsl.{Keep, Sink}
import com.nexla.admin.client.{AdminApiClient, AdminApiClientBuilder}
import com.nexla.admin.utils.SourceUtils
import com.nexla.common.NexlaConstants.{TOPIC_METRICS, TOPIC_NOTIFY}
import com.nexla.common.datetime.DateTimeUtils
import com.nexla.common.exception.{NexlaError, NexlaErrorMessage}
import com.nexla.common.metrics.HealthMetricAggregator
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.common.{AppType, NexlaConstants, ResourceType, RestTemplateBuilder}
import com.nexla.connect.common.NexlaConnectorUtils
import com.nexla.connector.config.FlowType
import com.nexla.control.message.{ControlEventType, SourceControlMessage}
import com.nexla.inmemory_connector.context.builder.ContextBuilder
import com.nexla.inmemory_connector.pipeline.flow.newimpl.ImcListingClient
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryMessageProducer
import com.nexla.inmemory_connector.state.FilesState
import com.nexla.inmemory_connector_common.PipelineKiller
import com.nexla.inmemory_connector_common.api.ApiHandler
import com.nexla.inmemory_connector_common.listing.AdaptiveFlowTasksStreamSource
import com.nexla.inmemory_connector_common.probe.ProbeFactoryImpl
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.nexla.kafka.{AdminControlUpdatesListener, CtrlTopics, KafkaMessageTransport}
import com.nexla.listing.client.{AdaptiveFlowTask, FileVaultClient, ListingClient}
import com.nexla.sc.client.OffsetSaver
import com.nexla.sc.client.listing._
import com.nexla.sc.util.{AppUtils, StrictNexlaLogging}
import com.nexla.telemetry.jmx.JmxExporter
import com.nexla.transform.TransformServiceImpl
import com.nexla.transform.cache.{CustomScriptService, DataMapService}
import com.nexla.transform.schema.FormatDetector
import fr.davit.akka.http.metrics.core.HttpMetricsRegistry
import fr.davit.akka.http.metrics.core.scaladsl.server.HttpMetricsRoute
import org.slf4j.bridge.SLF4JBridgeHandler
import org.apache.commons.lang.StringUtils

import java.util.concurrent.ConcurrentHashMap
import java.util.{Optional, UUID}
import scala.compat.java8.OptionConverters._
import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

object InMemFlowConnectorApp
  extends App
    with AppUtils
    with StrictNexlaLogging {

  // Needed to redirect java.util.logging to slf4j
  SLF4JBridgeHandler.removeHandlersForRootLogger()
  SLF4JBridgeHandler.install()

  private implicit val (props, _, envMap) = loadProps(AppType.IN_MEMORY_CONNECTOR, new AppProps(_))
  private val (telemetry, httpMetricsRegistry) = initTelemetry(props.telemetryConf, AppType.IN_MEMORY_CONNECTOR.name, Some(props.dataDog), Some(props.prometheus))
  private val jmxExporter: JmxExporter = new JmxExporter(telemetry)

  implicit val system: ActorSystem = ActorSystem(defaultActorSystemName, loadAkkaConfig(None))
  implicit val materializer: Materializer = Materializer(system)
  implicit val scheduler: Scheduler = system.scheduler
  implicit val executionContext: ExecutionContext = system.dispatcher

  private val nodeId = props.nodeId
    .getOrElse(UUID.randomUUID().toString)

  private val appSslContext = nexlaSslContext(props)
  private val restTemplate = new RestTemplateBuilder().withSSL(appSslContext).build()

  private val nexlaMessageProducer = new InMemoryMessageProducer(new NexlaMessageProducer(new KafkaMessageTransport(props.bootstrapServers, nexlaSslContext(props), TOPIC_METRICS, TOPIC_NOTIFY)))

  private val adminApi: AdminApiClient = new AdminApiClientBuilder()
    .setAppName(s"${AppType.IN_MEMORY_CONNECTOR.appName}-$nodeId")
    .setDataPlaneUid(props.dataplaneUid)
    .setEnrichmentUrl(props.credEnrichmentUrl)
    .setNoCache(false)
    .create(props.apiCredentialsServer, props.apiAccessKey, restTemplate)

  private val ctrlTopics = new CtrlTopics(Some(UUID.randomUUID()), props, appSslContext, system)
  private val metricAggregator = HealthMetricAggregator.getInstance(nexlaMessageProducer)
  new AdminControlUpdatesListener(AppType.IN_MEMORY_CONNECTOR, ctrlTopics, metricAggregator).startMonitoring()

  FormatDetector.initDefault()

  // create static service
  new DataMapService(
    props.redisCreds.hosts, 100, 2,
    props.redisCreds.clusterEnabled, props.redisCreds.password.orNull, adminApi,
    props.redisCreds.tlsContext.asJava,
    props.lookupDataModelVersioningEnabled
  )

  private val transformServiceImpl: TransformServiceImpl = new TransformServiceImpl()
  val listingClient = new ListingAppClient(props.listingAppServer, props.nexlaCreds)
  val coordinationClient = new CoordinationAppClient(props.coordinationAppServer, props.nexlaCreds)
  val javaListingClient = new ImcListingClient(props.listingAppServer, props.nexlaCreds.username, props.nexlaCreds.password, restTemplate)

  new CustomScriptService(
    props.redisCreds.hosts, props.redisLruCacheCapacity, props.redisLruExpirationMin,
    props.redisCreds.clusterEnabled, props.redisCreds.password.orNull, adminApi,
    props.redisCreds.tlsContext.asJava)

  private val fileVault = new FileVaultClient(props.fileVaultUrl, props.nexlaCreds.username, props.nexlaCreds.password, restTemplate)

  val offsetSaver = new OffsetSaver(coordinationClient, nexlaMessageProducer)
  val probeFactory = new ProbeFactoryImpl()

  configureHttpClientSslContext(appSslContext)
  logger.info(s"using adaptive flow tasks service URL (listing app): ${props.listingAppServer}")

  private val pipelineRunner = new PipelineRunner(adminApi, listingClient, javaListingClient, coordinationClient, fileVault, probeFactory, props, transformServiceImpl, offsetSaver, nexlaMessageProducer, telemetry)
  private val pipelineContextBuilder = new ContextBuilder(adminApi, props, nexlaMessageProducer)
  private val pipelineKiller = new PipelineKiller()
  private val api = new ApiHandler(httpMetricsRegistry, envMap, pipelineKiller)

  private val Interface = "0.0.0.0"

  private def startMetricsServer(port: Int, route: Route, registry: HttpMetricsRegistry): Future[Unit] = {
    val routeWithMetrics = HttpMetricsRoute(route).recordMetrics(registry)
    val bindingFuture = Http(system).newServerAt(Interface, port).bindFlow(routeWithMetrics)

    jmxExporter.enable()

    bindingFuture.onComplete {
      case Success(_) => logger.info(s"Metrics server started on $Interface:$port")
      case Failure(ex) => logger.error(s"Could not start Metrics server on port $Interface:$port", ex)
    }
    bindingFuture.map(_ => ())
  }

  private def startInternalApiServer(port: Int, route: Route): Future[Unit] = {
    val bindingFuture = Http(system).newServerAt(Interface, port).bindFlow(route)
    bindingFuture.onComplete {
      case Success(_) => logger.info(s"API server started on $Interface:$port")
      case Failure(ex) => logger.error(s"Could not start API server on port $Interface:$port", ex)
    }
    bindingFuture.map(_ => ())
  }

  val flowId = props.imcFlowId.getOrElse(throw new Exception("Flow id needs to be defined"))
  val runId = props.imcRunId.getOrElse(throw new Exception("Run id needs to be defined"))

  private def createPipelineFuture(maybeAdaptiveFlowTask: Option[AdaptiveFlowTask]) = for {
    ctx <- pipelineContextBuilder.createPipelineContext(maybeAdaptiveFlowTask, flowId, runId)
    _ <- pipelineRunner.run(ctx, pipelineKiller, new FilesState())
  } yield ()

  private def startWaitUntilKilledFuture(): Future[Unit] = {
    Future {
      while (true) {
        logger.info("App finished. Waiting to be killed externally")
        Thread.sleep(20000)
      }
    }(system.dispatcher)
  }

  sys.addShutdownHook {
    logger.info("Shutting down app")
    val f = Future.unit
      .transformWith { result =>
        logger.info("Shutting down pipeline")
        pipelineKiller.stop()
        pipelineFuture.tap(_ => logger.info("Pipeline shutdown normally")).tapError(logger.error("Pipeline shutdown failed", _))
          .transformWith(_ => Future.fromTry(result))
      }
      .transformWith { result: Try[_] =>
        logger.info("Closing kafka producer")
        Future(nexlaMessageProducer.close()).tap(_ => logger.info("Kafka producer closed normally")).tapError(logger.error("Failed to close kafka producer", _))
          .transformWith(_ => Future.fromTry(result))
      }
      .transformWith { result: Try[_] =>
        logger.info("Shutting down http pool")
        Http().shutdownAllConnectionPools().tap(_ => logger.info("Http pool shutdown normally")).tapError(logger.error("Http pool shutdown failed", _))
          .transformWith(_ => Future.fromTry(result))
      }
      .transformWith { result: Try[_] =>
        logger.info("Terminating actor system")
        system.terminate().tap(_ => logger.info("Actor system terminated normally")).tapError(logger.error("Actor system termination failed", _))
          .transformWith(_ => Future.fromTry(result))
      }
    Await.ready(f, 5.minutes)
  }

  // here we create a source, which emits "pipeline futures" based on the cfg
  // violating the isolation principles - but allowing us to control it on the basic level
  val source = adminApi.getNexlaFlow(flowId).get().getDataSources.get(0)
  private val adaptiveEnabled = Optional.ofNullable(source.getSourceConfig.get("adaptive.flow")).asScala.map(_.toString).exists(_.toBoolean)
  private val startedAdaptiveTasks = new ConcurrentHashMap[Long, AdaptiveFlowTask]()

  private def updateAdaptiveFlowTaskState(tb: Try[Unit], task: AdaptiveFlowTask): Unit = Try {
    if (tb.isSuccess) {
      logger.info(s"try was a success, marking task id ${task.getId} as DONE")
      javaListingClient.updateAdaptiveTaskStatus(source.getId, task.getId, AdaptiveFlowTask.TaskStatus.DONE, Optional.of("Success"))
    } else {
      logger.error(s"try was a failure, marking task id ${task.getId} as DONE")
      // Mark task as DONE with failure message to avoid it being retried on next iteration.
      javaListingClient.updateAdaptiveTaskStatus(source.getId, task.getId, AdaptiveFlowTask.TaskStatus.DONE, Optional.of("Failure: " + StringUtils.substring(tb.failed.get.getMessage, 0, 250)))
      adminApi.getDataSource(source.getId).asScala
        .foreach(ds => {
          val datasetId: Int = if (ds.getDatasets == null || ds.getDatasets.isEmpty) {
            logger.warn(s"source ${source.getId} has no datasets"); 0
          } else {
            ds.getDatasets.get(0).getId
          }
          publishErrorNotification(ds.getId, ResourceType.SOURCE, ds.getOrgId, ds.getOwnerId, datasetId, runId, String.valueOf(task.getId), tb.failed.get)
        })

    }
    startedAdaptiveTasks.remove(task.getId)
  }.failed.foreach(e => logger.error("Failed to update status for task", e))

  logger.info(s"App is starting [nodeId: $nodeId, podIp: ${props.podIp}, flowId: ${props.imcFlowId}, runId: ${props.imcRunId}]")
  httpMetricsRegistry.fold(Future.unit)(r => startMetricsServer(props.nexlaMetricsPort, api.metricsRoute, r))
  startInternalApiServer(8083, api.internalRoutes)

    private val pipelineFuture  = if (adaptiveEnabled) {
      logger.info("detected adaptive enabled, using adaptive tasks loop")
      val (killSwitch, adaptiveFlowFuture) = new AdaptiveFlowTasksStreamSource(javaListingClient, pipelineKiller, 1.minute)
        .listTasks(source.getId, () => ())
        .map{task => startedAdaptiveTasks.put(task.getId, task); task}
        .viaMat(KillSwitches.single)(Keep.right)
        .mapAsync(1)(task =>
          createPipelineFuture(Option(task))
            .transform(tb => {
              updateAdaptiveFlowTaskState(tb, task)
              tb
            })
            .recover { case ex => logger.error(s"Pipeline failed for task id ${task.getId}, continuing with next task", ex); () }
        )
      .toMat(Sink.ignore)(Keep.both)
      .run()
      pipelineKiller.addKillFunction { () => killSwitch.shutdown(); true }
      adaptiveFlowFuture.onComplete { _ =>
        startedAdaptiveTasks.values().forEach { task =>
          logger.info(s"Pipeline stopped, marking task id ${task.getId} as STOPPED")
          javaListingClient.updateAdaptiveTaskStatus(source.getId, task.getId, AdaptiveFlowTask.TaskStatus.STOPPED, Optional.of("Pipeline stopped"))
        }
        emitEventForCtrlToFreeUpResources()
      }
      adaptiveFlowFuture
  } else {
    createPipelineFuture(Option.empty)
      .transformWith {
        case Success(_) =>
          logger.info(s"Pipeline terminated normally")
          startWaitUntilKilledFuture()
        case Failure(ex) =>
          logger.error(s"Pipeline terminated with error", ex)
          startWaitUntilKilledFuture()
      }
  }

  private def emitEventForCtrlToFreeUpResources(): Unit = Try {
    val dataSource = adminApi.getDataSource(source.getId).get()
    val dockerInstances = dataSource.getSourceConfig.getOrDefault("docker.instances", "1").toString.toInt
    if (dockerInstances == 1) {
      val context = java.util.Map.of(NexlaConstants.HARD_STOP, String.valueOf(true))
      val stopMsg = new SourceControlMessage(
        UUID.randomUUID(), dataSource.getId, ControlEventType.PAUSE, dataSource.getConnectionType,
        AppType.IN_MEMORY_CONNECTOR.appName, context, Option(SourceUtils.toResourceDto(dataSource)).asJava, None.asJava)
      nexlaMessageProducer.sendControlMessage(stopMsg)
      logger.info("Final state report and STOP message sent")
    } else {
      logger.info(s"Skipping Pause message for adaptive flow because docker.instances > 1")
    }
  }.recover { case e: Exception => logger.error("Failed to emit control message to free up resources", e) }

  private def publishErrorNotification(resourceId: Int, resourceType: ResourceType, orgId: Int, ownerId: Int, dataSetId: Int, runId: Long, taskName: String, err: Throwable): Unit = {
    val errorMessage = new NexlaErrorMessage(err, "", Optional.empty())
    NexlaConnectorUtils.publishMetrics(nexlaMessageProducer, resourceType, resourceId, taskName, 0L, 0L, 1,     // the total number of records is not known here, so we use 1
      DateTimeUtils.nowUTC().getMillis, Optional.of(runId), Optional.of(true), Optional.empty(), Optional.empty(), Optional.of(dataSetId),
      Optional.empty(), Optional.empty(), Optional.of(errorMessage), Optional.empty(), FlowType.IN_MEMORY, orgId, ownerId)
    NexlaConnectorUtils.publishException(nexlaMessageProducer, runId, resourceType, resourceId, 0L, err.getMessage, NexlaError.getErrorDetails(err, Optional.of(java.lang.Long.valueOf(0L))))
  }
}
