package com.nexla.inmemory_connector.state

import com.nexla.common.Resource
import com.nexla.sc.client.job_scheduler.PipelineTaskStateEnum
import com.nexla.sc.client.job_scheduler.PipelineTaskStateEnum.PipelineTaskState

class ResourceState(val resource: Resource) {

  private var status: PipelineTaskState = PipelineTaskStateEnum.NotRunning

  def markAsStarted(): Unit = {
    status = PipelineTaskStateEnum.Running
  }

  def markAsDone(): Unit = {
    status = PipelineTaskStateEnum.Finished
  }

  def markAsFailed(): Unit = {
    status = PipelineTaskStateEnum.Failed
  }

  def getStatus(): PipelineTaskState = status

  def summary(): String = {
    val metrics = List[(String, String)](
      "status" -> getStatus().toString,
    )
    s"State ${resource.`type`}-${resource.id}: ${metrics.mkString(";")}"
  }
}