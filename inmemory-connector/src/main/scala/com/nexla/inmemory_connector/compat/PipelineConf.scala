package com.nexla.inmemory_connector.compat

import com.nexla.admin.client.{DataSet, DataSource}
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.compat.PipelineConf.NexlaTxConfig

import java.util.concurrent.TimeUnit
import scala.collection.JavaConverters._
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.util.Try

object PipelineConf {

  private val TxParallelismPath = "tx.parallelism"
  private val TxRecordTransformTimeoutSecondsPath = "tx.record.transform.timeout.seconds"
  private val FileHeartbeatBatchSizePath = "imc.file.heartbeat.batch.size"

  def getPipelineConf(props: AppProps, dataSource: DataSource, dataSets: List[DataSet]): PipelineConf = {
    val fileHeartbeatBatchSize = dataSource.getSourceConfig.asScala.get(FileHeartbeatBatchSizePath).flatMap(Option(_)).flatMap(f => Try(f.toString.toInt).toOption).getOrElse(props.defaultFileHeartbeatBatchSize)
    val txConfigs = dataSets.map { dataSet =>
      val id = dataSet.getId
      val runtimeConfig = dataSet.getRuntimeConfig.asScala
      val parallelism = runtimeConfig.get(TxParallelismPath).map(_.toInt).getOrElse(props.txParallelismDefault)
      val recordProcessingTimeout = runtimeConfig.get(TxRecordTransformTimeoutSecondsPath).map(_.toInt).map(FiniteDuration(_, TimeUnit.SECONDS)).getOrElse(props.txRecordTransformTimeoutSecondsDefault.seconds)
      (id.toInt, NexlaTxConfig(parallelism, recordProcessingTimeout))
    }.toMap

    val dockerInstances = dataSource.getSourceConfig.getOrDefault("docker.instances", "1").toString.toInt
    PipelineConf(fileHeartbeatBatchSize = fileHeartbeatBatchSize, nexlaTxConfigs = txConfigs, dockerInstances = dockerInstances)
  }

  case class NexlaTxConfig(parallelism: Int, recordProcessingTimeout: FiniteDuration)
}

case class PipelineConf(fileHeartbeatBatchSize: Int, nexlaTxConfigs: Map[Int, NexlaTxConfig], dockerInstances: Int)
