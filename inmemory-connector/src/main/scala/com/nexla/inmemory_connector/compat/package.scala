package com.nexla.inmemory_connector.compat

import com.nexla.common.NexlaMessage
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat
import com.nexla.listing.client.AdaptiveFlowTask

import java.io.File

sealed trait FastConnectorOffset
case class FileOffset(fileId: Long, filePath: String, fileSize: Long, offset: Long, sourceId: Int, datasetId: Option[Int], eof: Boolean, message: Option[String], maybeAdaptiveFlowTask: Option[AdaptiveFlowTask] = Option.empty) extends FastConnectorOffset
case class MapOffset(sourceId: Int, sinkId: Int, messageId: Long, offset: Map[String, String]) extends FastConnectorOffset

sealed trait BasicMessage {
  val headDatasetId: Option[Int]
}

object BasicMessage {
  def extractOffsets(bm: BasicMessage): List[FastConnectorOffset] = bm match {
    case r: RecordWithOffset => List(r.offset)
    case jo: JustOffset => List(jo.offset)
    case d: DirectoryTransformationMessage => d.offsets
    case _: BYOOptimizedFlowMessage => List.empty
  }
}

case class RecordWithOffset(headDatasetId: Option[Int], message: NexlaMessage, offset: FastConnectorOffset) extends BasicMessage
case class JustOffset(headDatasetId: Option[Int], offset: FastConnectorOffset) extends BasicMessage

case class DirectoryTransformationMessage(headDatasetId: Option[Int], offsets: List[FileOffset], fullDirectoryPath: String) extends BasicMessage
case class BYOOptimizedFlowMessage(headDatasetId: Option[Int]) extends BasicMessage

case class ReplicationSourceFile(listedId: Option[Long],
                                 storagePath: String,
                                 nativeStoragePath: String,
                                 fileConfig: Option[WarehouseCopyFileFormat],
                                 file: Option[File],
                                 recordCount: Option[Long])

case class ReplicationSourceLocal(files: Seq[ReplicationSourceFile], localDir: File)

final case class SinkMetric(sinkId: Int, filePath: String, size: Long, maybeAdaptiveFlowTask: Option[AdaptiveFlowTask])
