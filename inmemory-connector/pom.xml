<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<artifactId>inmemory-connector</artifactId>
	<version>3.2.1-SNAPSHOT</version>

	<parent>
		<groupId>com.nexla</groupId>
		<artifactId>backend-connectors</artifactId>
		<version>3.2.1-SNAPSHOT</version>
	</parent>

	<dependencies>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>${logback.version}</version>
		</dependency>
		<dependency>
			<groupId>net.logstash.logback</groupId>
			<artifactId>logstash-logback-encoder</artifactId>
			<version>${logstash-logback-encoder.version}</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-s3</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka-clients</artifactId>
		</dependency>

		<dependency>
			<groupId>org.scala-lang.modules</groupId>
			<artifactId>scala-collection-compat_${scala.short.version}</artifactId>
			<version>${scala-collection-compat.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common</artifactId>
			<version>${nexla-backend-common.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-connect-bigquery-sink</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.htrace</groupId>
			<artifactId>htrace-core4</artifactId>
			<version>4.2.0-incubating</version>
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-databind</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>transform-service</artifactId>
			<version>develop-a1a5dd9-47</version>
		</dependency>

		<dependency>
			<groupId>org.typelevel</groupId>
			<artifactId>cats-core_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>com.enragedginger</groupId>
			<artifactId>akka-quartz-scheduler_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>file-source-sink</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>iceberg-probe</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-connect-jdbc-sink</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>net.logstash.log4j</groupId>
					<artifactId>jsonevent-layout</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.nexla</groupId>
					<artifactId>kafka-utils</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
			</exclusions>

		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-connect-jdbc-source</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>net.logstash.log4j</groupId>
					<artifactId>jsonevent-layout</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-connect-file-source</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>net.logstash.log4j</groupId>
					<artifactId>jsonevent-layout</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-connect-file-sink</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>net.logstash.log4j</groupId>
					<artifactId>jsonevent-layout</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.module</groupId>
			<artifactId>jackson-module-scala_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-connect-rest-source</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>net.logstash.log4j</groupId>
					<artifactId>jsonevent-layout</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-connect-vectordb-source</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>net.logstash.log4j</groupId>
					<artifactId>jsonevent-layout</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-connect-rest-sink</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>net.logstash.log4j</groupId>
					<artifactId>jsonevent-layout</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-connect-vectordb-sink</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>net.logstash.log4j</groupId>
					<artifactId>jsonevent-layout</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-connect-soap-sink</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>net.logstash.log4j</groupId>
					<artifactId>jsonevent-layout</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-connect-soap-source</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>net.logstash.log4j</groupId>
					<artifactId>jsonevent-layout</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
			</exclusions>
		</dependency>


		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common-sc</artifactId>
			<version>${nexla-backend-common.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.github.mwiede</groupId>
			<artifactId>jsch</artifactId>
			<version>${jsch.version}</version>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-http_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-http-spray-json_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>io.spray</groupId>
			<artifactId>spray-json_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-actor_${scala.short.version}</artifactId>
		</dependency>


		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>parsers</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>servlet-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jersey</groupId>
					<artifactId>jersey-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jersey</groupId>
					<artifactId>jersey-client</artifactId>
				</exclusion>
                <exclusion>
                    <groupId>org.apache.kafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-bom</artifactId>
			<version>${aws.sdk.version}</version>
			<type>pom</type>
			<scope>import</scope>
		</dependency>

		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-ec2</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-connect-redis-sink</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>redis-utils</artifactId>
			<version>${nexla-backend-common.version}</version>
		</dependency>
		
		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-stream-kafka_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>io.confluent</groupId>
			<artifactId>kafka-avro-serializer</artifactId>
			<version>${confluent.version}</version>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.scala-lang</groupId>
			<artifactId>scala-library</artifactId>
		</dependency>

		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-http-testkit_${scala.short.version}</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-testkit_${scala.short.version}</artifactId>
			<version>${akka.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>connector-properties</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-utils</artifactId>
			<version>${nexla-backend-common.version}</version>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-stream-testkit_${scala.short.version}</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-stream-kafka-testkit_${scala.short.version}</artifactId>
			<version>${akka-stream-kafka.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>connect-runtime</artifactId>
			<version>${kafka.version}</version>
		</dependency>
		<dependency>
			<groupId>com.dimafeng</groupId>
			<artifactId>testcontainers-scala-scalatest_${scala.short.version}</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.dimafeng</groupId>
			<artifactId>testcontainers-scala-kafka_${scala.short.version}</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.dimafeng</groupId>
			<artifactId>testcontainers-scala-mysql_${scala.short.version}</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.wiremock</groupId>
			<artifactId>wiremock-standalone</artifactId>
			<version>3.3.1</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>inmemory-connector-common</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka-clients</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>

	<build>

		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<requiresUnpack>
						<dependency>
							<groupId>org.python</groupId>
							<artifactId>jython-standalone</artifactId>
						</dependency>
					</requiresUnpack>
				</configuration>
			</plugin>

		</plugins>

	</build>

</project>
