# Setting up Local FastConnector for Test and QA Environments

These scripts are for the local deployment of FastConnector in a Test or QA environment, primarily for debugging purposes.

## Running in Development Environment (similar steps for QA):

### Step 1: Directory Navigation

Because of Bash path resolving, navigate to this directory:

```bash
cd path/to/backend/fast-connector/local-run
```

### Step 2: Deploy <PERSON><PERSON><PERSON> and Zookeeper

Execute the following command to deploy a clean Kafka and Zookeeper environment:

```bash
docker-compose -f docker-compose.yml up
```

### Step 3: Setting Up Proxies

To set up proxies for real services running in the selected environment, run the following command:

```bash
bash run_dev.sh
```

This command establishes a Kubernetes port forward to specific pods on a jumpbox. The port forward will remain active for 60 minutes before automatically terminating (this is for cleaning up active port forwards). Then, an SSH port forward is established between your local machine and the jumpbox. FastConnector will use an Nginx container, deployed during this step, to communicate through these port tunnels. This is necessary to avoid SSL certificates issues.

**Note:** The `run_dev.sh` command is idempotent. In case of any issues, you can safely rerun it.

#### Running SSH Commands without sshpass:

To run SSH commands without the usage of `sshpass`, you should change the `USE_SSHPASS` flag to `false` in the `establish_tunnel.sh` file. Look for the `USE_SSHPASS` variable within that file and set it as follows:

```bash
USE_SSHPASS=false
```

### Step 4: Running FastConnector with environment variables

Now, you should locally run FastConnector with environment variables loaded from the `dev.env` file.

---

### Adding Additional Proxies:

If you need to set up another proxy, you should add the necessary configurations in files: `lib/nginx-docker/nginx.conf`, `lib/setup_remote_proxy.sh`, `dev.env` similar to the existing services.


### Diagram of the setup:
![Diagram](diagrams/diagram.png)

