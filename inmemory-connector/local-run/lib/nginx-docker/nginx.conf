server {
    listen 80;
    server_name localhost;

    location /ctrl-nodetaskmanager {
        rewrite ^/ctrl-nodetaskmanager(/.*)$ $1 break;
        proxy_pass https://host.docker.internal:9901;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /listing-app {
        rewrite ^/listing-app(/.*)$ $1 break;
        proxy_pass https://host.docker.internal:9902;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
