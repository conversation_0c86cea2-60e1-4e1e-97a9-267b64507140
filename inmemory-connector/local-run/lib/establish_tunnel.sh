#!/bin/bash

# Configuration: Set to false if you dont use vault to ssh into jumbox
USE_SSHPASS=true

if [ "$#" -ne 3 ]; then
    echo "Usage: $0 <server-ip> <service-name> <port>"
    exit 1
fi
SERVER_IP="$1"
SERVICE_NAME="$2"
PORT="$3"

get_dev_token() {
    TTL=`vault token lookup -format json | jq .data.ttl`
    if [ -z "$TTL" ] || [ "$TTL" -eq "0" ]; then
        vault login -method=oidc > /dev/null
    fi

    KEY=`vault write --format=json ssh/creds/otp_key_role ip=${SERVER_IP} | jq -r .data.key`
    echo $KEY
}

echo "INFO: Setting up kubectl port forward on ${SERVER_IP} for ${SERVICE_NAME} ${PORT}:8080..."
if [ "$USE_SSHPASS" = true ]; then
    logs=$(sshpass -p "$(get_dev_token)" ssh ubuntu@${SERVER_IP} 'bash -s' < lib/maybe_port_forward.sh "${SERVICE_NAME}" "${PORT}" '8080')
else
    logs=$(ssh ubuntu@${SERVER_IP} 'bash -s' < lib/maybe_port_forward.sh "${SERVICE_NAME}" "${PORT}" '8080')
fi
exit_code=$?
echo $logs
if [ $exit_code -ne 0 ]; then
    echo "ERROR: Setting up kubectl port forward failed"
    exit 1
else
    echo "SUCCESS: Successfully set up kubectl port forward on ${SERVER_IP} for ${SERVICE_NAME} ${PORT}:8080"
fi


echo "INFO: Setting up ssh port forward to jumpbox ${SERVER_IP} on port ${PORT}..."

KILLME=`lsof -nPi | grep LISTEN | grep :${PORT} | awk '{print $2}' | head -n 1`
if [ ! -z "${KILLME}" ]; then
    echo "INFO: killing locally running process on port ${PORT} with pid $KILLME"
    kill $KILLME
fi

if [ "$USE_SSHPASS" = true ]; then
    logs=$(sshpass -p "$(get_dev_token)" ssh -f -N -L ${PORT}:localhost:${PORT} ubuntu@${SERVER_IP})
else
    logs=$(ssh -f -N -L ${PORT}:localhost:${PORT} ubuntu@${SERVER_IP})
fi
exit_code=$?
echo $logs
if [ $exit_code -ne 0 ]; then
    echo "ERROR: Setting up ssh port forward to jumpbox failed"
    exit 1
else
    echo "SUCCESS: Successfully set up ssh port forward to jumpbox ${SERVER_IP} on port ${PORT}."
fi
