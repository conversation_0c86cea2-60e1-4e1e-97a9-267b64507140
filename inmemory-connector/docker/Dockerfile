# FROM ubuntu:latest is suitable for out-of-the-box TTU experience, but we need at least openJDK 11
FROM adoptopenjdk/openjdk11:jdk-11.0.22_7-alpine
MAINTAINER Avinash "<EMAIL>"

RUN apk -U upgrade
RUN apk add --update --no-cache wget curl bind-tools iputils netcat-openbsd net-tools nmap openssl \
    coreutils openssl perl lsof socat procps vim nano htop jq

# our stuff
ENV NEXLA_APP_DIR "/app"
ADD target/*.jar $NEXLA_APP_DIR/app.jar

# copy custom java security overrides
COPY docker/custom_java.security $NEXLA_APP_DIR/custom_java.security

RUN sh -c 'touch $NEXLA_APP_DIR/app.jar'

USER nexla

ENTRYPOINT exec java $JAVA_OPTS -Dcom.sun.management.jmxremote=true -Dcom.sun.management.jmxremote.port=2000 -Dcom.sun.management.jmxremote.ssl=false \
                     -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.local.only=false \
                     -Djava.security.properties=$NEXLA_APP_DIR/custom_java.security -jar /app/app.jar
