ALL = [
        'http-sink',
        'kafka-connect-bigquery-sink',
        'kafka-connect-bigquery-source',
        'kafka-connect-file-sink',
        'kafka-connect-file-source',
        'kafka-connect-jdbc-sink',
        'kafka-connect-jdbc-source',
        'kafka-connect-documentdb-sink',
        'kafka-connect-documentdb-source',
        'kafka-connect-redis-sink',
        'kafka-connect-rest-sink',
        'kafka-connect-rest-source',
        'kafka-connect-soap-sink',
        'kafka-connect-soap-source',
        'kafka-connect-spreadsheets-sink',
        'kafka-connect-iceberg-source',
        'kafka-connect-iceberg-sink',
        'kafka-connect-api-streams-source',
        'kafka-connect-vectordb-source',
        'kafka-connect-vectordb-sink',
        'probe-http'
]

NON_CONNECTOR_APPS = [
        'http-sink',
        'probe-http'
]

// @NonCPS // has to be NonCPS or the build breaks on the call to .each
def projects() {
    return params.projects
            .split(",")
            .collect { it.trim().toLowerCase() }
            .collect {macro ->
                switch (macro) {
                    case "all": return ALL
                    default: macro
                }
            }
            .flatten()
}

// @NonCPS // has to be NonCPS or the build breaks on the call to .each
def allToBuild() {
    return ALL
            .findAll { projects().contains(it) }
}

def getLastCommitSha() {
    return sh (script: "git log -n 1 --pretty=format:'%H'", returnStdout: true)
}

def generateReport(proj) {
    registry = params.ECR
            ? "433433586750.dkr.ecr.us-east-1.amazonaws.com"
            : "nexla"

    image = """${proj.replace("/", "-")}:${tag()}"""

    sh "trivy image --no-progress --exit-code 0 --format template --template \"@html.tpl\" -o reports/${image}.html ${registry}/${image}"
}

def tag() {
    env.PRJ_VERSION = sh(script: "mvn help:evaluate -Dexpression=project.version -q -DforceStdout", returnStdout: true).trim()
    return "${PRJ_VERSION}_${BRANCH}_${BUILD_NUMBER}_${GIT_COMMIT}"
    // return "${BRANCH}-${BUILD_NUMBER}-${GIT_COMMIT}"
}

def buildAndPushToDockerHub(proj) {
    dir("${proj}/") {
        sh "echo \$(pwd)"
        withDockerRegistry([ credentialsId: "nexlabuild", url: "" ]) {
            dockerImage = docker.build("nexla/${proj.replace("/", "-")}:${tag()}", "-f docker/Dockerfile .")
            dockerImage.push()
        }
    }
}

def buildAndPushToECR(proj) {
    dir("${proj}/") {
        sh "echo \$(pwd)"
        withDockerRegistry([credentialsId: "nexlabuild", url: ""]) {
            dockerImage = docker.build("433433586750.dkr.ecr.us-east-1.amazonaws.com/${proj.replace("/", "-")}:${tag()}", "-f docker/Dockerfile .")
        }
    }

    sh "echo ${env.ecr_token} | docker login --username AWS --password-stdin 433433586750.dkr.ecr.us-east-1.amazonaws.com"
    dockerImage.push()
}

pipeline {
    parameters {
        string(name: 'branch', defaultValue: '')
        string(name: 'projects', defaultValue: 'ALL', description: """
Comma separated, supported macro: ALL

ALL --
${ALL.join('\n')}
--
        """)
        booleanParam(name: 'Generate vulnerabilities report', defaultValue: false)
        booleanParam(name: 'ECR', defaultValue: false)
        booleanParam(name: 'DOCKERHUB', defaultValue: false)
    }

    agent {
        kubernetes {
            yaml '''
---
metadata:
spec:
  serviceAccountName: jenkins-agent
  tolerations:
   - key: "jenkins-agent"
     operator: "Equal"
     value: "true"
     effect: NoExecute
   - key: "jenkins-agent"
     operator: "Equal"
     value: "true"
     effect: NoSchedule
  volumes:
    - name: docker-socket
      emptyDir: {}
    - name: maven-cache
      persistentVolumeClaim:
        claimName: maven-cache
    - name: tmp-docker-cache
      persistentVolumeClaim:
        claimName: docker-cache
  affinity:
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        - topologyKey: "kubernetes.io/hostname"
          labelSelector:
            matchExpressions:
              - key: app
                operator: In
                values:
                  - jenkins-keepalive


  containers:
    - name: aws
      image: amazon/aws-cli:2.11.15
      resources:
          requests:
            memory: "24Gi"
            cpu: "4"
      command:
        - sleep
      args:
        - 99d
      volumeMounts:
        - name: docker-socket
          mountPath: /var/run
    - name: docker
      image: 433433586750.dkr.ecr.us-east-1.amazonaws.com/backend-builder:jenkins_migration-19-96ecfcd5a728aa46909d6a48bf2329a3f8f4f185
      command:
        - sleep
      args:
        - 99d
      volumeMounts:
        - name: docker-socket
          mountPath: /var/run
        - name: maven-cache
          mountPath: /root/.m2
        - name: tmp-docker-cache
          mountPath: /tmp/dockercache
    - name: docker-daemon
      image: docker:19.03.1-dind
      securityContext:
        privileged: true
      volumeMounts:
        - name: docker-socket
          mountPath: /var/run
        - name: maven-cache
          mountPath: /root/.m2
        - name: tmp-docker-cache
          mountPath: /tmp/dockercache
'''
        }
    }

    options {
        // Timeout counter starts AFTER agent is allocated
        timeout(time: 32000, unit: 'SECONDS')
        timestamps()
    }

    stages {

        stage ('backend - Checkout') {
            options {
                skipDefaultCheckout()
            }
            steps {
                script {
                    def scmVars = checkout([$class: 'GitSCM', branches: [[name: "origin/${BRANCH}"]], doGenerateSubmoduleConfigurations: false, extensions: [], submoduleCfg: [], userRemoteConfigs: [[credentialsId: 'generated_by_kotliar', url: params.gitUrl]]])
                    env.GIT_COMMIT = scmVars.GIT_COMMIT
                    env.GIT_BRANCH = scmVars.GIT_BRANCH
                    BRANCH = BRANCH.replace("/", "-")

                    println params.DOCKER_REGISTRY
                    env.CURRENT_VERSION = sh(script: "cat pom.xml | grep version | head -n 1 | grep -Eo '([0-9]*)\\.([0-9]*)\\.([0-9]*)([^<]*)'", returnStdout: true).trim()
                    env.BUILD_TS = sh(script: "date +\"%Y%m%d%H%M\"", returnStdout: true).trim()

                    println "allToBuild:"
                    println allToBuild()
                }
            }
        }

        stage('pre-build and code coverage') {
            parallel {

                stage('Login to ECR') {
                    steps {
                        container('aws') {
                            script {
                                env.ecr_token = sh(returnStdout: true, script: '''aws ecr get-login-password --region us-east-1 ''').trim()
                            }
                        }
                    }
                }

            }
        }

        stage('Build Connector Apps') {
            steps {
                container('docker') {
                    script {
                        if (!allToBuild().isEmpty()) {
                            env.projs = (allToBuild().findAll{ !NON_CONNECTOR_APPS.contains(it) }.collect { "-pl " + it }.join(" "))

                            sh """
                                mvn --version
                                echo "curr version is ${env.CURRENT_VERSION}"
                                mvn versions:set -DnewVersion="${env.CURRENT_VERSION}" -DgenerateBackupPoms=false
                                set +x
                                export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain nexla --domain-owner 433433586750 --query authorizationToken --output text`
                                set -x
                                export MAVEN_OPTS="-Xms8192M -Xmx16384M  -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/root/.m2/heapdump " && mvn --settings=.mvn/local-settings.xml -U -T ${NEXLA_PARALLELISM} clean install -P build-connector --also-make -Dmaven.test.skip -DskipTests -e ${env.projs}
                            """
                        }
                    }
                }
            }
        }

        stage('Build Non-Connector Apps') {
            steps {
                container('docker') {
                    script {
                        if (!allToBuild().isEmpty()) {
                            env.projs = (allToBuild().findAll{ NON_CONNECTOR_APPS.contains(it) }.collect { "-pl " + it }.join(" "))

                            sh """
                                mvn --version
                                echo "curr version is ${env.CURRENT_VERSION}"
                                mvn versions:set -DnewVersion="${env.CURRENT_VERSION}" -DgenerateBackupPoms=false
                                set +x
                                export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain nexla --domain-owner 433433586750 --query authorizationToken --output text`
                                set -x
                                export MAVEN_OPTS="-Xms8192M -Xmx16384M  -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/root/.m2/heapdump " && mvn --settings=.mvn/local-settings.xml -U -T ${NEXLA_PARALLELISM} clean install --also-make -Dmaven.test.skip -DskipTests -e ${env.projs}
                            """
                        }
                    }
                }
            }
        }

        stage('Push Apps to Repository') {
            parallel {
                stage('ECR') {
                    when {
                        expression { params.ECR }
                    }
                    steps {
                        container('docker') {
                            script {
                                allToBuild().each { proj ->
                                    buildAndPushToECR(proj)
                                }
                            }
                        }
                    }
                }
                stage('DOCKERHUB') {
                    when {
                        expression { params.DOCKERHUB }
                    }
                    steps {
                        container('docker') {
                            script {
                                allToBuild().each {
                                    buildAndPushToDockerHub(it)
                                }
                            }
                        }
                    }
                }
            }
        }

        stage("Vulnerability Scan") {
            when {
                expression { params['Generate vulnerabilities report'] }
            }
            steps {
                container('docker') {
                    script {
                        // Install trivy
                        sh 'apk --no-cache add curl'
                        sh 'curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin v0.47.0'
                        sh 'curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/html.tpl > html.tpl'
                        // Scan all vuln levels
                        sh 'mkdir -p reports'

                        allToBuild().each { proj ->
                            generateReport(proj)
                        }

                        def htmlFiles = []
                        dir ('reports') {
                            htmlFiles = findFiles glob: '*.*'
                        }

                        publishHTML([
                                reportDir: 'reports',
                                reportFiles: htmlFiles.join(','),
                                reportName: 'Trivy Scan',
                                reportTitles: 'Trivy Scan',
                                allowMissing: true,
                                alwaysLinkToLastBuild: true,
                                keepAll: true
                        ])
                    }
                }
            }
        }
    }

    post {
        success {
            container('docker') {
                script {
                    GIT_COMMIT_SHA = getLastCommitSha()
                    P_APPS = allToBuild();

                    rtp parserName: 'HTML', stableText: "<br><code style=\"font-size: 14px; background-color: #dce5e3; padding: 10px; border-radius: 3px\">${tag()}</code><br><br><br><code style=\"font-size: 14px; background-color: #939B62; padding: 10px; border-radius: 3px\">APPS: ${P_APPS}</code><br><br><br><code style=\"font-size: 14px; background-color: #FFD56F; padding: 10px; border-radius: 3px\"></code><br><br><br><code style=\"font-size: 14px; background-color: #FFB26B; padding: 10px; border-radius: 3px\"></code><br><br>"
                    jacoco()

                    // finally clean up everything not to have the params cached anywhere
                    cleanWs()
                }
            }
        }

        failure {
            cleanWs()
        }
    }
}
