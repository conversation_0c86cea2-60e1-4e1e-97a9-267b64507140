IGNORED_MODULES = [
        'kafka-connect-kinesis-source',
        'kafka-connect-kinesis-sink'
]

pipeline {

    agent {
        kubernetes {
            yaml '''
---
metadata:
spec:
  serviceAccountName: jenkins-agent
  tolerations:
   - key: "jenkins-agent"
     operator: "Equal"
     value: "true"
     effect: NoExecute
   - key: "jenkins-agent"
     operator: "Equal"
     value: "true"
     effect: NoSchedule
  volumes:
    - name: docker-socket
      emptyDir: {}
    - name: maven-cache
      persistentVolumeClaim:
        claimName: maven-cache

  containers:
    - name: aws
      image: amazon/aws-cli:2.11.15
      command:
        - sleep
      args:
        - 99d
      volumeMounts:
        - name: docker-socket
          mountPath: /var/run
    - name: docker
      image: ************.dkr.ecr.us-east-1.amazonaws.com/backend-builder:jenkins_migration-19-96ecfcd5a728aa46909d6a48bf2329a3f8f4f185
      resources:
          requests:
            memory: "24Gi"
            cpu: "8"
      command:
        - sleep
      args:
        - 99d
      volumeMounts:
        - name: docker-socket
          mountPath: /var/run
        - name: maven-cache
          mountPath: /root/.m2
    - name: docker-daemon
      image: docker:19.03.1-dind
      securityContext:
        privileged: true
      volumeMounts:
        - name: docker-socket
          mountPath: /var/run
        - name: maven-cache
          mountPath: /root/.m2
'''
        }
    }

    options {
        // Timeout counter starts AFTER agent is allocated
        timeout(time: 32000, unit: 'SECONDS')
        timestamps()
    }

    stages {

        stage ('backend - Checkout') {
            steps {
                script {
                    def scmVars = checkout([$class: 'GitSCM', branches: [[name: ':^(origin/${BRANCH}$).*']], doGenerateSubmoduleConfigurations: false, extensions: [], submoduleCfg: [], userRemoteConfigs: [[credentialsId: 'generated_by_kotliar', url: params.gitUrl]]])
                    env.GIT_COMMIT = scmVars.GIT_COMMIT
                    env.GIT_BRANCH = scmVars.GIT_BRANCH

                    env.BRANCH = env.BRANCH.replace("/", "-")
                    env.CURRENT_VERSION = sh(script: "cat pom.xml | grep version | head -n 1 | grep -Eo '([0-9]*)\\.([0-9]*)\\.([0-9]*)([^<]*)'", returnStdout: true).trim()
                    env.BRANCH = sh(script: "echo ${env.GIT_BRANCH} | sed 's#origin/##g' | sed 's#/#-#g'", returnStdout: true).trim()
                }

            }
        }

        stage('pre-build and code coverage') {
            parallel {
                stage('Install trivy') {
                    steps {
                        container('docker') {
                            script {
                                // Install trivy
                                sh 'apk --no-cache add curl'
                                sh 'curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin v0.40.0'
                                sh 'curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/html.tpl > html.tpl'
                                // Scan all vuln levels
                                sh 'mkdir -p reports'
                            }
                        }
                    }
                }
                stage('Prepare file for tags') {
                    steps {
                        container('docker') {
                            script {
                                sh 'echo "" > image_versions.txt'
                            }
                        }
                    }
                }
            }
        }
        stage('Build & Push to Local repo') {
            steps {
                container('docker') {
                    script {
                        env.BUILD_TS = sh(script: "date +\"%Y%m%d%H%M\"", returnStdout: true).trim()
                        env.ignored_projs = (IGNORED_MODULES.collect { "-pl '!" + it + "'" }.join(" "))

                        sh """
                            mvn versions:set -DnewVersion="${VERSION}" -DgenerateBackupPoms=false -DprocessAllModules
                            set +x
                            export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain nexla --domain-owner ************ --query authorizationToken --output text`
                            set -x 
                            export _JAVA_OPTIONS='-Xms8192M -Xmx16384M -XX:+CMSClassUnloadingEnabled -Daether.enhancedLocalRepository.trackingFilename=some_dummy_file_name' && mvn -e clean source:jar deploy --settings ./.mvn/local-settings.xml -DskipTests ${env.ignored_projs}
                        """
                    }
                }
            }
        }
    }
}