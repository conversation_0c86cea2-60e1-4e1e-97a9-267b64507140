package com.nexla.probe.ftp;

import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.ListingResourceType;
import com.nexla.common.NexlaBucket;
import com.nexla.common.NexlaFile;
import com.nexla.common.ResourceType;
import com.nexla.common.exception.AuthFailException;
import com.nexla.common.exception.NexlaExceptionChecker;
import com.nexla.common.io.CloseableInputStream;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.probe.ProbeException;
import com.nexla.connector.config.file.DirScanningMode;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.file.FtpAuthConfig;
import com.nexla.file.service.FileConnectorService;
import com.nexla.file.service.FileDetails;
import com.nexla.listing.client.ListingClient;
import com.nexla.probe.ftp.impl.NexlaFtpClient;
import com.nexla.telemetry.Telemetry;
import lombok.Getter;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.kafka.common.config.AbstractConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.nexla.common.FileUtils.checkWhiteBlackLists;
import static com.nexla.common.FileUtils.checkWhiteBlackListsFirstLayer;
import static com.nexla.common.ListingResourceType.FOLDER;
import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;
import static com.nexla.connector.ConnectorService.AuthResponse.authError;
import static com.nexla.connector.config.file.DirScanningMode.BOTH;
import static com.nexla.file.service.FileWalk.LevelFile;
import static com.nexla.file.service.FileWalk.walkFileTreeDfs;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;

@Getter
public class FtpConnectorService extends FileConnectorService<FtpAuthConfig> {
	private static final int DEFAULT_CONNECTION_POOL_SIZE = 1;

	private static Logger logger = LoggerFactory.getLogger(FtpConnectorService.class);

	private final FtpClientProvider ftpClientProvider;

	public FtpConnectorService() {
		this(null, null, null);
	}

	public FtpConnectorService(AdminApiClient adminApiClient, ListingClient listingClient, String credentialsDecryptKey) {
		this(adminApiClient, listingClient, credentialsDecryptKey, DEFAULT_CONNECTION_POOL_SIZE);
	}

	public FtpConnectorService(AdminApiClient adminApiClient, ListingClient listingClient, String credentialsDecryptKey, int connectionPoolSize) {
		super(adminApiClient, listingClient, credentialsDecryptKey);
		logger.info("Initialize new FtpConnectorService #{} ({})", System.identityHashCode(this), connectionPoolSize);
		this.ftpClientProvider = connectionPoolSize > 0 ? new PooledFtpClientProvider(connectionPoolSize) : new FtpClientProvider();
	}

	public void setTelemetry(Telemetry telemetry) {
		this.ftpClientProvider.setTelemetry(telemetry);
	}

	private synchronized boolean makeDirectories(NexlaFtpClient ftpClient, String dirPath) {

		if (changeWorkingDirectory(ftpClient, dirPath)) {
			return true;
		}

		String[] pathElements = dirPath.substring(1).split("/");
		if (pathElements.length <= 0) {
			return true;
		}
		String currDir = "";
		for (String singleDir : pathElements) {
			currDir += "/" + singleDir;
			boolean existed = changeWorkingDirectory(ftpClient, currDir);
			if (existed) {
				continue;
			}

			boolean created = ftpClient.makeDirectory(currDir);
			if (created) {
				changeWorkingDirectory(ftpClient, currDir);
			} else {
				return false;
			}
		}

		return true;
	}

	private boolean changeWorkingDirectory(NexlaFtpClient ftpClient, String dirPath) {
		logger.info("changeWorkingDirectory {}", dirPath);
		try {
			if (ftpClient.changeWorkingDirectory(dirPath)) {
				return true;
			}
		} catch (Exception e) {
			logger.error("Failed to changeWorkingDirectory, considering not existing", e);
		}
		return false;
	}

	@Override
	public void initLogger(ResourceType resourceType, Integer resourceId, Optional<Integer> taskId) {
		this.logger = new NexlaLogger(logger, new NexlaLogKey(resourceType, resourceId, taskId));
	}

	@Override
	public AuthResponse authenticate(FtpAuthConfig authConfig) {
		return retryIfException(() -> ftpClientProvider.withFtpClient(
				authConfig,
				ftp -> {
					try {
						ftp.isConnected();
						return SUCCESS;
					} catch (Exception e) {
						logger.error("FTP Authentication failed", e);
						return authError(e);
					}
				}));
	}

	@SneakyThrows
	@Override
	public FileDetails writeInternal(FileConnectorAuth config, String filePath, File file) {
		try (FileInputStream fis = new FileInputStream(file)) {
			return writeInternal(config, filePath, fis);
		}
	}

	@Override
	public FileDetails writeInternal(FileConnectorAuth config, String filePath, InputStream inputStream) {
		File file = new File(filePath);
		File parentDir = file.getParentFile();

		ofNullable(parentDir).ifPresent(parentFile -> {
			ftpClientProvider.withFtpClient((AbstractConfig) config, ftp -> {
				String dirPath = parentDir.getPath();
				if (!makeDirectories(ftp, dirPath)) {
					throw new RuntimeException("Could not create directory=" + dirPath);
				}
				ftp.storeFile(file.getAbsolutePath(), inputStream);
			});
		});
		return new FileDetails(filePath, empty(), empty());
	}

	@Override
	public StreamEx<NexlaBucket> listBuckets(AbstractConfig config) {
		FileSourceConnectorConfig fileConfig = (FileSourceConnectorConfig) config;
		FtpAuthConfig authConfig = (FtpAuthConfig) fileConfig.getAuthConfig();
		return retryIfException(() -> ftpClientProvider.withFtpClient(
				authConfig,
				ftp -> {
					try {
						return executedStream(ftp.listDirectories().map(f -> new NexlaBucket(f.getName())));
					} catch (Exception e) {
						logger.error("Error while listing buckets", e);
						throw e;
					}
				}));
	}

	@Override
	public StreamEx<NexlaFile> listBucketContents(AbstractConfig c) {
		FileSourceConnectorConfig config = (FileSourceConnectorConfig) c;
		FtpAuthConfig authConfig = (FtpAuthConfig) config.getAuthConfig();
		return retryIfException(() -> ftpClientProvider.withFtpClient(
				authConfig,
				(NexlaFtpClient ftp) -> executedStream(getFiles(ftp, config.path, config.dirScanningMode, config.depth, config))));
	}

	public static StreamEx<NexlaFile> getFiles(NexlaFtpClient ftpClient,
											   String rootPath,
											   DirScanningMode scanningMode,
											   Integer maxDepth,
											   FileSourceConnectorConfig config) {

		Supplier<StreamEx<LevelFile<FTPFile>>> firstLayer = () -> ftpClient.listFiles(rootPath)
				.filter(FtpConnectorService::isFileNotHidden)
				.filter(x -> checkWhiteBlackListsFirstLayer(config.whiteList, config.blackList, Paths.get(rootPath, x.getName()).toString()))
				.map(file -> new LevelFile(1, file, path(file, rootPath), isFolder(file)));

		StreamEx<LevelFile<FTPFile>> fileStream = walkFileTreeDfs(scanningMode, firstLayer, file -> nextStream(ftpClient, maxDepth, file));
		StreamEx<NexlaFile> streamWithAllFiles = executedStream(fileStream.map(FtpConnectorService::toNexlaFile));

		return streamWithAllFiles.filter(x -> checkWhiteBlackLists(config.whiteList, config.blackList, x.getFullPath()));
	}

	private static String path(FTPFile file, String path) {
		boolean hasExtension = !FilenameUtils.getExtension(path).isBlank();
		boolean isDirectory = file.isDirectory();
		boolean sameName = ofNullable(Paths.get(path).getFileName())
				.filter(p -> file.getName().equals(p.toString()))
				.isPresent();

		// the case when a specific file is getting listed
		if (hasExtension && sameName && !isDirectory) {
			return Paths.get(path).getParent().toString();
		}

		return path;
	}

	private static StreamEx<LevelFile<FTPFile>> nextStream(
			NexlaFtpClient ftpClient,
			Integer maxDepth,
			LevelFile<FTPFile> levelFile
	) {
		FTPFile currFile = levelFile.file;
		String nextDirPath = Paths.get(levelFile.dirPath, currFile.getName()).toString();
		if (levelFile.level == maxDepth || !isFolder(currFile)) {
			return StreamEx.empty();
		} else {
			return ftpClient.listFiles(nextDirPath)
					.filter(FtpConnectorService::isFileNotHidden)
					.map(f -> new LevelFile(levelFile.level + 1, f, nextDirPath, isFolder(f)));
		}
	}

	private static boolean isFolder(FTPFile file) {
		return file.isDirectory();
	}

	private static NexlaFile toNexlaFile(LevelFile<FTPFile> levelFile) {
		FTPFile file = levelFile.file;
		Path filePath = Paths.get(levelFile.dirPath, file.getName());
		ListingResourceType type = isFolder(file) ? FOLDER : ListingResourceType.FILE;
		return new NexlaFile(filePath.toString(), file.getSize(), levelFile.dirPath, null, time(file), time(file), type);
	}

	private static Long time(FTPFile file) {
		return file.getTimestamp() != null
				? file.getTimestamp().getTime().getTime()
				: null;
	}

	private static boolean isFileNotHidden(FTPFile file) {
		return !file.getName().startsWith(".");
	}

	@Override
	public CloseableInputStream readInputStreamInternal(FileConnectorAuth config, String file) {
		return ftpClientProvider.streamFromFtpClient(
				(AbstractConfig) config,
				ftp -> ftp.retrieveFileStream(file));
	}

	@Override
	public boolean checkWriteAccess(AbstractConfig config) {
		return false;
	}

	@Override
	@SneakyThrows
	public boolean doesFileExistsInternal(FileConnectorAuth config, String fullPath) {
		try (CloseableInputStream inputStream = readInputStreamInternal(config, fullPath)) {
			return inputStream.read() >= 0;
		} catch (Exception e) {
			//noinspection ConstantValue(SftpException is not detected by IDEA beacause of sneaky throws block)
			if (e instanceof SftpException && ((SftpException) e).id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
				return false;
			}
			logger.debug("Failed to read input streamInternal while checking if file exists. Use oldSchoolFileExists", e);
			return oldSchoolFileExists(config, fullPath);
		}
	}

	public Boolean oldSchoolFileExists(FileConnectorAuth config, String fullPath) {
		FtpAuthConfig ftpAuthConfig = (FtpAuthConfig) config.getAuthConfig();
		return retryIfException(() -> ftpClientProvider.withFtpClient(
				ftpAuthConfig,
				(NexlaFtpClient ftp) -> ftp.fileExists(fullPath)));
	}

	@Override
	public StreamEx<NexlaFile> listTopLevelBuckets(AbstractConfig configTemp) {
		FileSourceConnectorConfig config = (FileSourceConnectorConfig) configTemp;
		FtpAuthConfig authConfig = (FtpAuthConfig) config.getAuthConfig();
		return retryIfException(() -> ftpClientProvider.withFtpClient(
				authConfig,
				ftp -> {
					String path = config.path;
					if ("/".equals(path)) {
						path = ftp.calculateRootPath();
					}
					return executedStream(getFiles(ftp, path, BOTH, config.depth, config));
				}));
	}

	@Override
	public void close() {
		logger.info("Close FtpConnectorService #{}", System.identityHashCode(this));
		ftpClientProvider.close();
	}

	@Override
	public void deleteByName(AbstractConfig c, List<String> keys) {
		ftpClientProvider.withFtpClient(c, ftp -> {
			keys.forEach(filePath -> {
				try {
					ftp.deleteFile(filePath);
				} catch (Exception e) {
					logger.error("Error to delete file: " + filePath, e);
				}
			});
		});
	}

	@Override
	public void clearBucket(AbstractConfig config) {
		ftpClientProvider.withFtpClient(config, ftp -> {
			ftp.listFiles(((FileConnectorAuth) config).getPath())
					.forEach(fileName -> {
						try {
							ftp.deleteFile(fileName.getName());
						} catch (Exception e) {
							logger.error("Error to delete file: {}", fileName, e);
						}
					});
		});
	}

	@SneakyThrows
	@Override
	public void createDestination(AbstractConfig c, Optional<Integer> sourceId) {
		FileConnectorAuth config = (FileConnectorAuth) c;
		FtpAuthConfig ftpAuthConfig = (FtpAuthConfig) config.getAuthConfig();
		retryIfException(() -> ftpClientProvider.withFtpClient(
				ftpAuthConfig,
				(NexlaFtpClient ftp) -> doCreateDestination(config, ftp)));
	}

	private void doCreateDestination(FileConnectorAuth config, NexlaFtpClient ftp) {
		String targetFilePath = config.getPath();
		if (targetFilePath.endsWith("/")) {
			targetFilePath = targetFilePath.substring(0, targetFilePath.length() - 1);
		}

		if (!makeDirectories(ftp, targetFilePath)) {
			throw new ProbeException("Could not create folder=" + targetFilePath);
		}
	}

	// Execute stream operations in order to prevent leakage of FTP clients inside lazy stream executions
	private static <T> StreamEx<T> executedStream(StreamEx<T> streamEx) {
		try (streamEx) {
			return StreamEx.of(streamEx.collect(Collectors.toList()));
		}
	}

	@SneakyThrows
	private void retryIfException(Runnable runnable) {
		retryIfException(() -> {
			runnable.run();
			return null;
		});
	}

	@SneakyThrows
	private <T> T retryIfException(Callable<T> callable) {
		try {
			return RetryerBuilder
					.<T>newBuilder()
					.retryIfException()
					.withStopStrategy(StopStrategies.stopAfterAttempt(2))
					.build()
					.call(callable);
		} catch (ExecutionException | RetryException e) {
			logger.warn("Failed to retry operation: {}", e.getMessage());
			throw e.getCause() != null ? e.getCause() : e;
		}
	}
}
