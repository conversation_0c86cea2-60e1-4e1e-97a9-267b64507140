package com.nexla.probe.ftp.impl;

import com.jcraft.jsch.*;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import com.nexla.common.FileUtils;
import com.nexla.common.exception.AuthFailException;
import com.nexla.connector.config.file.FtpAuthConfig;
import com.nexla.connector.config.ssh.HostPort;
import com.nexla.connector.config.ssh.tunnel.SshTunnel;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileWriter;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import static com.jcraft.jsch.ChannelSftp.OVERWRITE;
import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.connector.config.ssh.tunnel.SshTunnel.IPV4_PATTERN;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCause;

public class SftpClientImpl implements NexlaFtpClient {

	private static final Logger logger = LoggerFactory.getLogger(SftpClientImpl.class);
	public static final int FILE_NOT_FOUND = 2;
	public static final int ACCESS_DENIED = 3;
	private static int SFTP_CONNECTION_TIMEOUT_MS;

	static {
		JSch.setLogger(new SftpClientImplLogAdapter());
		try {
			SFTP_CONNECTION_TIMEOUT_MS = ofNullable(System.getenv("SFTP_CONNECTION_TIMEOUT_MS"))
					.map(Integer::parseInt)
					.orElse(120_000);
			logger.info("SFTP_CONNECTION_TIMEOUT_MS is set to {}", SFTP_CONNECTION_TIMEOUT_MS);
		} catch (Exception e) {
			logger.error("Failed to parse SFTP_CONNECTION_TIMEOUT_MS, using the default value", e);
			SFTP_CONNECTION_TIMEOUT_MS = 120_000;
		}
	}

	private final ChannelSftp channelSftp;
	private final Optional<SshTunnel> sshTunnel;

	private SftpClientImpl(ChannelSftp channelSftp, final Optional<SshTunnel> sshTunnel) {
		this.channelSftp = channelSftp;
		this.sshTunnel = sshTunnel;
		logger.info("SFTP: Connection established for {}", printSessionDetails());
	}

	@SneakyThrows
	public static SftpClientImpl create(FtpAuthConfig authConfig) {
		logger.info("SFTP: create connection with config={}", authConfig);

		Optional<SshTunnel> sshTunnel = authConfig.sshTunnelConfig
				.map(ignored -> new FtpTunnelSupport(authConfig))
				.flatMap(tunnelSupport -> tunnelSupport.createTunnel(logger));
		Session session = null;
		try {
			HostPort hostPort = sshTunnel
					// Use localhost only if host is an IP address. Don't replace otherwise, host will be redirected by MAPPED_HOST_RESOLVER in SshTunnel class
					.map(ignored -> new HostPort(
							IPV4_PATTERN.matcher(authConfig.host).matches() ? "localhost" : authConfig.host,
							authConfig.getSshTunnelPorts().get(0)))
					.orElseGet(() -> new HostPort(authConfig.host, authConfig.port));

			JSch jsch = new JSch();
			session = jsch.getSession(authConfig.username, hostPort.getHost(), hostPort.getPort());

			if (authConfig.privateKey != null) {

				Path privateKeyFile = Files.createTempFile("key_", null);

				try (FileWriter fileWriter = new FileWriter(privateKeyFile.toFile())) {
					fileWriter.append(authConfig.privateKey);
				}
				jsch.addIdentity(privateKeyFile.toString(), authConfig.privateKeyPassphrase);
				privateKeyFile.toFile().delete();

			} else if (authConfig.password != null) {
				session.setPassword(authConfig.password);
			} else {
				//It should be a different specific exception that also supports non-retry functionality.
				throw new AuthFailException("Invalid Input, one of password or private key should be specified");
			}

			// http://stackoverflow.com/questions/2003419/com-jcraft-jsch-jschexception-unknownhostkey
			Properties config = new Properties();
			config.put("StrictHostKeyChecking", "no");

			logger.warn("Deprecated algorithms are allowed to be used for this SFTP, adding ssh-rsa, ssh-dss to the accepted algorithms.");
			String existingServerHostKey = JSch.getConfig("server_host_key");
			String existingPubkeyAcceptedAlgorithms = JSch.getConfig("PubkeyAcceptedAlgorithms");
			boolean hostKeyNonEmpty = StringUtils.isNotEmpty(existingServerHostKey);
			boolean acceptedAlgoNonEmpty = StringUtils.isNotEmpty(existingPubkeyAcceptedAlgorithms);
			String serverHost;
			if (hostKeyNonEmpty) {
				serverHost = existingServerHostKey + ",ssh-rsa,ssh-dss";
			} else {
				serverHost = "ssh-rsa,ssh-dss";
			}
			logger.info("Setting server_host_key to {}", serverHost);
			config.put("server_host_key", serverHost);

			String acceptedAlgo;
			if (acceptedAlgoNonEmpty) {
				acceptedAlgo = existingServerHostKey + ",ssh-rsa,ssh-dss";
			} else {
				acceptedAlgo = "ssh-rsa,ssh-dss";
			}
			logger.info("Setting PubkeyAcceptedAlgorithms to {}", acceptedAlgo);
			config.put("PubkeyAcceptedAlgorithms", acceptedAlgo);

			// required for some legacy ftp servers,
			// see: https://github.com/mwiede/jsch?tab=readme-ov-file#is-this-fork-100-compatible-with-original-jsch-because-the-connection-to-my-server-does-not-work-any-more
			config.put("cipher.c2s", session.getConfig("cipher.c2s") + ",aes128-cbc");
			config.put("cipher.s2c", session.getConfig("cipher.s2c") + ",aes128-cbc");

			session.setConfig(config);
			session.setServerAliveInterval(30000);

			session.connect(SFTP_CONNECTION_TIMEOUT_MS);
			logger.info("Session connected to server, server version {}", session.getServerVersion());

			ChannelSftp channel = (ChannelSftp) session.openChannel("sftp");
			channel.setInputStream(System.in);
			channel.setOutputStream(System.out);

			channel.connect(SFTP_CONNECTION_TIMEOUT_MS);

			return new SftpClientImpl(channel, sshTunnel);
		} catch (Exception e) {
			if (session != null) {
				FileUtils.closeSilently(session::disconnect);
			}
			sshTunnel.ifPresent(FileUtils::closeSilently);

			logger.error("Failed '{}' SFTP connection", authConfig.host, e);
			// distinguish between retriable and non-retriable exceptions here.
			if(e.getMessage().contains("Auth fail")) {
				throw new AuthFailException(authConfig.host + " - SFTP Credentials are invalid", e);
			}
			throw new Exception("Failed to connect to SFTP", e);
		}

	}

	@Override
	public boolean isConnected() {
		try {
			boolean connected = channelSftp.isConnected();
			changeWorkingDirectory(".");
			return connected;
		} catch (Exception e) {
			logger.info("Recreating FTP connection, ping failed");
			return false;
		}
	}

	@SneakyThrows
	@Override
	public InputStream retrieveFileStream(String file) {
		return channelSftp.get(file, new ProgressLogger());
	}

	@SneakyThrows
	@Override
	public boolean changeWorkingDirectory(String currDir) {
		try {
			channelSftp.cd(currDir);
			return true;
		} catch (SftpException e) {
			Throwable root = ofNullable(getRootCause(e)).orElse(e);
			if (root.getMessage() != null && root.getMessage().toLowerCase().contains("no such")) {
				return false;
			} else {
				throw e;
			}
		}
	}

	@SneakyThrows
	@Override
	public boolean storeFile(String fullFilePath, InputStream inputStream) {
		channelSftp.put(inputStream, fullFilePath, OVERWRITE);
		return true;
	}

	@SneakyThrows
	@Override
	public boolean makeDirectory(String currDir) {
		channelSftp.mkdir(currDir);
		return true;
	}

	@SneakyThrows
	@Override
	public boolean fileExists(String file) {
		try {
			return !channelSftp.lstat(file).isDir();
		} catch (SftpException e) {
			if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
				return false;
			} else {
				throw e;
			}
		}
	}

	@SneakyThrows
	@Override
	public StreamEx<FTPFile> listFiles(String path) {
		try {
			List<FTPFile> res = StreamEx
					.of(((Vector<LsEntry>) channelSftp.ls(path)))
					.map(this::toFtpFile)
					.toList();

			if (res.isEmpty() && path.endsWith("/")) {
				try {
					List<FTPFile> res2 = StreamEx
							.of(((Vector<LsEntry>) channelSftp.ls(path + "*")))
							.map(this::toFtpFile)
							.toList();
					res.addAll(res2);
				} catch (Exception e) {

				}
			}

			return StreamEx.of(res);
		} catch (SftpException e) {
			switch (e.id) {
				case FILE_NOT_FOUND:
					return StreamEx.empty();
				case ACCESS_DENIED:
					return listFiles(path + "/*");
				default:
					throw e;
			}
		}
	}

	private FTPFile toFtpFile(LsEntry lsEntry) {
		FTPFile ftpFile = new FTPFile();
		ftpFile.setName(lsEntry.getFilename());
		ftpFile.setRawListing(lsEntry.getLongname());

		SftpATTRS attrs = lsEntry.getAttrs();
		ftpFile.setSize(attrs.getSize());

		ftpFile.setTimestamp(getLastModifiedTime(attrs.getMTime()));
		ftpFile.setType(calculateFileType(attrs));

		return ftpFile;
	}

	private Calendar getLastModifiedTime(int timeInSecond) {
		Calendar lastModifiedTime = Calendar.getInstance();
		long timeInMilliSec = TimeUnit.SECONDS.toMillis(timeInSecond);
		lastModifiedTime.setTimeInMillis(timeInMilliSec);

		return lastModifiedTime;
	}

	private int calculateFileType(SftpATTRS attrs) {
		if (attrs.isDir()) {
			return FTPFile.DIRECTORY_TYPE;
		} else if (attrs.isLink()) {
			return FTPFile.SYMBOLIC_LINK_TYPE;
		} else if (attrs.isReg()) {
			return FTPFile.FILE_TYPE;
		} else {
			return FTPFile.UNKNOWN_TYPE;
		}
	}

	@SneakyThrows
	@Override
	public StreamEx<FTPFile> listDirectories() {
		return StreamEx
				.of((Vector<LsEntry>) channelSftp.ls(calculateRootPath()))
				.filter(lsEntry -> lsEntry.getAttrs().isDir())
				.map(this::toFtpFile);
	}

	@SneakyThrows
	public String calculateRootPath() {
		if (channelSftp.getHome() == null) {
			return "/";
		} else {
			return channelSftp.getHome();
		}
	}

	@SneakyThrows
	@Override
	public void close() {
		logger.info("SFTP: Close connection: {}", printSessionDetails());
		Session session = channelSftp.getSession();
		closeSilently(channelSftp::disconnect);
		closeSilently(session::disconnect);
		sshTunnel.ifPresent(FileUtils::closeSilently);
	}

	@SneakyThrows
	@Override
	public void deleteFile(String fileName) {
		channelSftp.rm(fileName);
	}

	private String printSessionDetails() {
		try {
			Session session = channelSftp.getSession();
			return String.format("%s@%s:%d", session.getUserName(), session.getHost(), session.getPort());
		} catch (Exception e) {
			return "NA: " + e.getMessage();
		}
	}

	private static class ProgressLogger implements SftpProgressMonitor {
		private static final long REPORT_INTERVAL = 60000;
		private AtomicLong maxSize = new AtomicLong(0);
		private AtomicLong nextReportTime = new AtomicLong(System.currentTimeMillis() + REPORT_INTERVAL);

		@Override
		public void init(int op, String source, String target, long max) {
			logger.info("SFTP: start downloading. Expected size: {}", max);
			maxSize.set(max);
			nextReportTime.set(System.currentTimeMillis() + REPORT_INTERVAL);
		}

		@Override
		public boolean count(long downloaded) {
			long currentTime = System.currentTimeMillis();
			if (currentTime > nextReportTime.get()) {
				var message = String.format("SFTP: downloading progress: %.2f%%", (((double)downloaded) / maxSize.get()) * 100);
				logger.info(message);
				nextReportTime.set(currentTime + REPORT_INTERVAL);
			}
			return true;
		}

		@Override
		public void end() {
			logger.info("SFTP: downloading ended");
		}
	}
}
