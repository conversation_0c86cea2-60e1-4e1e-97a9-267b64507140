package com.nexla.probe.ftp.impl;

import com.jcraft.jsch.SftpProgressMonitor;
import com.nexla.common.FileUtils;
import com.nexla.common.exception.AuthFailException;
import com.nexla.connector.config.file.FtpAuthConfig;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.net.ProtocolCommandEvent;
import org.apache.commons.net.ProtocolCommandListener;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.commons.net.ftp.FTPSClient;
import org.apache.commons.net.io.CopyStreamEvent;
import org.apache.commons.net.io.CopyStreamListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.concurrent.atomic.AtomicLong;

import static com.nexla.connector.config.file.FtpConstants.FTPS;
import static com.nexla.connector.config.file.FtpConstants.PASSIVE_LOCAL;
import static com.nexla.connector.config.file.FtpConstants.PASSIVE_REMOTE;

public class FtpClientImpl implements NexlaFtpClient {

	private static final Logger logger = LoggerFactory.getLogger(FtpClientImpl.class);

	public final FTPClient ftpClient;

	public FtpClientImpl(FTPClient ftpClient) {

		this.ftpClient = ftpClient;

		ftpClient.addProtocolCommandListener(new ProtocolCommandListener() {

			@Override
			public void protocolReplyReceived(ProtocolCommandEvent event) {
				logger.trace("Received response from FTP {} {} {} {} {}", event.getCommand(),
						event.getMessage(), event.getReplyCode(), event.isCommand(), event.isReply());
			}

			@Override
			public void protocolCommandSent(ProtocolCommandEvent event) {
				logger.trace("Command sent to FTP {} {} {} {} {}", event.getCommand(), event.getMessage(),
						event.getReplyCode(), event.isCommand(), event.isReply());
			}
		});
	}

	@SneakyThrows
	public static FtpClientImpl create(FtpAuthConfig authConfig) {
		FTPClient ftpClient = null;
		try {
			ftpClient = FTPS.equals(authConfig.ftpType) ? new FTPSClient() : new FTPClient();

			ftpClient.setStrictReplyParsing(authConfig.strictReplyParsing);
			ftpClient.connect(authConfig.host, authConfig.port);
			logger.info("Connected to " + authConfig.host + ".");
			logger.info(ftpClient.getReplyString());

			// After connection attempt, you should check the reply code to verify success.
			int reply = ftpClient.getReplyCode();

			if (!FTPReply.isPositiveCompletion(reply)) {
				throw new IllegalStateException("FTP server refused connection.");
			}

			switch (authConfig.ftpMode) {

				case PASSIVE_LOCAL:
					ftpClient.enterLocalPassiveMode();
					break;

				case PASSIVE_REMOTE:
					ftpClient.enterRemotePassiveMode();
					break;
			}

			if (!authConfig.anonymous) {
				boolean result = ftpClient.login(authConfig.username, authConfig.password);

				if (!result) {
					logger.error("Failed to login to FTP '{}'", authConfig.host);
					throw new AuthFailException("Cannot login to ftp - " + authConfig.host);
				}

				if (FTPS.equals(authConfig.ftpType)) {
					FTPSClient ftpsClient = (FTPSClient) ftpClient;
					ftpsClient.execPBSZ(0);
					// Set data channel protection to private
					ftpsClient.execPROT("P");
				}
			}

			ftpClient.setFileType(org.apache.commons.net.ftp.FTP.BINARY_FILE_TYPE);
			ftpClient.setCopyStreamListener(new CopyStreamProgressLogger());

			return new FtpClientImpl(ftpClient);
		} catch (IOException e) {
			if (ftpClient != null) {
				FileUtils.closeSilently(ftpClient::disconnect);
			}
			logger.error("Failed to connect to FTP (IO problem)", e);
			throw new RuntimeException(e);
		} catch (Exception ex) {
			if (ftpClient != null) {
				FileUtils.closeSilently(ftpClient::disconnect);
			}
			logger.error("Failed to connect to FTP", ex);
			throw ex;
		}
	}

	@Override
	public boolean isConnected() {
		try {
			boolean connected = ftpClient.isConnected();
			ftpClient.sendNoOp();
			return connected;
		} catch (Exception e) {
			logger.info("Recreating FTP connection, ping failed");
			return false;
		}
	}

	@Override
	public InputStream retrieveFileStream(String file) {
		try {
			InputStream inputStream = ftpClient.retrieveFileStream(file);
			if (inputStream == null) {
				String errorMessage = "Cannot open file '" + file + "': " + ftpClient.getReplyString();
				logger.error(errorMessage);
				throw new IllegalArgumentException(errorMessage);
			}
			return inputStream;
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public boolean changeWorkingDirectory(String currDir) {
		try {
			return ftpClient.changeWorkingDirectory(currDir);
		} catch (IOException e) {
			return false;
		}
	}

	@Override
	public boolean storeFile(String fullFilePath, InputStream inputStream) {
		try {
			return ftpClient.storeFile(fullFilePath, inputStream);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public boolean makeDirectory(String currDir) {
		try {
			return ftpClient.makeDirectory(currDir);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	@SneakyThrows
	@Override
	public boolean fileExists(String file) {
		FTPFile[] ftpFiles = ftpClient.listFiles(file);
		return ftpFiles.length > 0 && ftpFiles[0].isFile();
	}

	@SneakyThrows
	@Override
	public StreamEx<FTPFile> listFiles(String path) {
		return StreamEx.of(ftpClient.listFiles(path));
	}

	@SneakyThrows
	@Override
	public StreamEx<FTPFile> listDirectories() {
		return StreamEx.of(ftpClient.listDirectories());
	}

	@Override
	public void close() {
		try {
			ftpClient.disconnect();
		} catch (Exception ignored) {
		}
	}

	@Override
	public boolean completePendingCommand() {
		try {
			return ftpClient.completePendingCommand();
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	public FTPClient getFtpClient() {
		return ftpClient;
	}

	@SneakyThrows
	@Override
	public void deleteFile(String fileName) {
		ftpClient.deleteFile(fileName);
	}

	private static class CopyStreamProgressLogger implements CopyStreamListener {
		private static final long REPORT_INTERVAL = 60000;
		private AtomicLong nextReportTime = new AtomicLong(System.currentTimeMillis() + REPORT_INTERVAL);

		@Override
		public void bytesTransferred(CopyStreamEvent event) {
			bytesTransferred(event.getTotalBytesTransferred(), event.getBytesTransferred(), event.getStreamSize());
		}

		@Override
		public void bytesTransferred(long totalBytesTransferred, int bytesTransferred, long streamSize) {
			long currentTime = System.currentTimeMillis();
			if (currentTime > nextReportTime.get()) {
				logger.info("FTP: downloaded: {}", readableFileSize(totalBytesTransferred));
				nextReportTime.set(currentTime + REPORT_INTERVAL);
			}
		}

		public String readableFileSize(long size) {
			if (size <= 0) return "0";
			final String[] units = new String[] { "B", "kB", "MB", "GB", "TB", "PB", "EB" };
			int digitGroups = (int) (Math.log10(size)/Math.log10(1024));
			return new DecimalFormat("#,##0.#").format(size/Math.pow(1024, digitGroups)) + " " + units[digitGroups];
		}
	}
}
