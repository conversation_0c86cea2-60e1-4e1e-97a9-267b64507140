package com.nexla.probe.ftp.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.connector.config.file.FtpAuthConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.ssh.HostPort;
import com.nexla.connector.config.ssh.tunnel.SshTunnelSupport;
import com.nexla.connector.config.vault.CredentialsStore;

import java.util.List;
import java.util.Optional;

import static com.nexla.connector.config.vault.VaultUtils.createNexlaCredentialsStore;
import static java.util.Optional.of;

class FtpTunnelSupport implements SshTunnelSupport {

	private static final CredentialsStore nexlaCredentialsStore = createNexlaCredentialsStore(Maps.newHashMap());

	private final BaseAuthConfig authConfig;

	FtpTunnelSupport(BaseAuthConfig authConfig) {
		this.authConfig = authConfig;
	}

	@Override
	public Optional<BaseAuthConfig> authConfig() {
		return of(authConfig);
	}

	@Override
	public CredentialsStore credentialsStore() {
		return nexlaCredentialsStore;
	}

	public List<HostPort> getHostPorts() {
		FtpAuthConfig cfg = (FtpAuthConfig) authConfig;
		return Lists.newArrayList(new HostPort(cfg.host, cfg.port));
	}
}