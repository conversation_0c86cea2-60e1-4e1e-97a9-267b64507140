package com.nexla.probe.ftp

import com.nexla.common.ListingResourceType.{FILE, FOLDER}
import com.nexla.common.NexlaConstants._
import com.nexla.common.{ConnectionType, NexlaFile}
import com.nexla.connect.common.TestFtpServer
import com.nexla.connect.common.TestFtpServer.{FTP_PASSWORD, FTP_USERNAME}
import com.nexla.connector.config.file.FtpConstants._
import com.nexla.connector.config.file.{FileSourceConnectorConfig, FtpConstants}
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import org.scalatest.{BeforeAndAfter, BeforeAndAfterAll, TagAnnotation}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import java.io.{File, FileWriter}
import java.nio.file.{Files, Path}
import java.util
import java.util.stream.{Collector, Collectors}
import scala.collection.JavaConverters._
import scala.collection.mutable.ListBuffer

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class FtpConnectorServiceTest extends AnyFlatSpecLike with Matchers with BeforeAndAfter with BeforeAndAfterAll {

  private val dir: Path = Files.createTempDirectory("FtpConnectorServiceTest")
  dir.toFile.deleteOnExit()

  private val server = new TestFtpServer(dir)

  override def beforeAll() {
    server.before()
  }

  override def afterAll() {
    server.after()
  }

  after {
    deleteFilesAndFolders(dir.toFile)
  }

  it should "list root directory by default" in {

    val filePath = "/test.txt"
    val writer = new FileWriter(dir.toString + filePath)
    writer.append("test data")
    writer.close()

    val service = new FtpConnectorService()
    service
      .listBucketContents(
        new FileSourceConnectorConfig(
          Map(
            LISTING_APP_SERVER_URL -> "",
            SOURCE_ID -> "1",
            SOURCE_TYPE -> ConnectionType.FTP.name(),
            CREDENTIALS_DECRYPT_KEY -> "1",
            CREDS_ENC -> "1",
            CREDS_ENC_IV -> "1",
            "unit.test" -> "true",
            FtpConstants.FTP_MODE -> PASSIVE_LOCAL,
            FtpConstants.PORT -> server.port.toString,
            FtpConstants.USERNAME -> FTP_USERNAME,
            FtpConstants.PASSWORD -> FTP_PASSWORD)
            .asJava)).iterator().asScala.map(_.getFullPath).toList shouldBe List(filePath)

    service.close()
  }

  it should "list files in listTopLevelBuckets" in {

    val filePath = "/test.txt"
    val writer = new FileWriter(dir.toString + filePath)
    writer.append("test data")
    writer.close()

    val collector: Collector[NexlaFile, _, util.List[NexlaFile]] = Collectors.toList()

    val service = new FtpConnectorService()
    val fileList =
      service
        .listTopLevelBuckets(new FileSourceConnectorConfig(
          Map(
            LISTING_APP_SERVER_URL -> "",
            SOURCE_ID -> "1",
            SOURCE_TYPE -> ConnectionType.FTP.name(),
            CREDENTIALS_DECRYPT_KEY -> "1",
            CREDS_ENC -> "1",
            CREDS_ENC_IV -> "1",
            "unit.test" -> "true",
            FTP_MODE -> PASSIVE_LOCAL,
            PORT -> server.port.toString,
            USERNAME -> FTP_USERNAME,
            PASSWORD -> FTP_PASSWORD
          ).asJava
        ))
        .collect(collector)
    service.close()

    fileList shouldBe List(new NexlaFile("/test.txt", 9L, "/", null, null, fileList.get(0).getLastModified, FILE)).asJava
  }

  it should "list buckets in listTopLevelBuckets to depth 3" in {

    createFolders("/level1/level2/level3/level4/")

    val filePath = "/level1/level2/level3/level4/test.txt"
    val writer = new FileWriter(dir.toString + filePath)
    writer.append("test data")
    writer.close()

    val collector: Collector[NexlaFile, _, util.List[NexlaFile]] = Collectors.toList()

    val service = new FtpConnectorService()
    val fileList = service
      .listTopLevelBuckets(
        new FileSourceConnectorConfig(
          Map(
            LISTING_APP_SERVER_URL -> "",
            SOURCE_ID -> "1",
            SOURCE_TYPE -> ConnectionType.FTP.name(),
            CREDENTIALS_DECRYPT_KEY -> "1",
            CREDS_ENC -> "1",
            CREDS_ENC_IV -> "1",
            DEPTH -> "3",
            "unit.test" -> "true",
            FTP_MODE -> PASSIVE_LOCAL,
            PORT -> server.port.toString,
            USERNAME -> FTP_USERNAME,
            PASSWORD -> FTP_PASSWORD)
            .asJava))
      .collect(collector)
    service.close()

    fileList shouldBe List(
      new NexlaFile("/level1", 0L, "/", null, null, fileList.get(0).getLastModified, FOLDER),
      new NexlaFile("/level1/level2", 0L, "/level1", null, null, fileList.get(1).getLastModified, FOLDER),
      new NexlaFile("/level1/level2/level3", 0L, "/level1/level2", null, null, fileList.get(2).getLastModified, FOLDER)
    ).asJava
  }

  it should "listBucketContents with whitelist and prefix" in {

    val filesToCreateInEachFolder = 1000
    val basePath = "/levelA/levelB"
    val whiteListWithWildCard = "/levelA/levelB/levelC/**/*.txt"
    val folders = 5
    val expectedFiles = 1000
    val foldersNames = new ListBuffer[String]()
    foldersNames += "/"

    val externalFolderName = "/levelA/levelB/levelC/level1/"
    createFolders(externalFolderName)
    foldersNames += externalFolderName

    val writer = new FileWriter(dir.toString + externalFolderName + s"file-test.json")
    writer.append("test data")
    writer.close()

    for (i <- 0 until folders) {
      val folderName = s"/levelA/levelB/science/level${i + 1}/"
      createFolders(folderName)
      foldersNames += folderName
    }

    for (filePath <- foldersNames.toList) {
      for (i <- 0 until filesToCreateInEachFolder) {
        val writer = new FileWriter(dir.toString + filePath + s"file-test-$i.txt")
        writer.append("test data")
        writer.close()
      }
    }

    val collector: Collector[NexlaFile, _, util.List[NexlaFile]] = Collectors.toList()

    val service = new FtpConnectorService()
    val fileList = service
      .listBucketContents(
        new FileSourceConnectorConfig(
          Map(
            LISTING_APP_SERVER_URL -> "",
            SOURCE_ID -> "1",
            SOURCE_TYPE -> ConnectionType.FTP.name(),
            CREDENTIALS_DECRYPT_KEY -> "1",
            CREDS_ENC -> "1",
            CREDS_ENC_IV -> "1",
            DEPTH -> "6",
            "unit.test" -> "true",
            PATH -> basePath,
            WHITELIST_PATHMATCHERS -> whiteListWithWildCard,
            FTP_MODE -> PASSIVE_LOCAL,
            PORT -> server.port.toString,
            USERNAME -> FTP_USERNAME,
            PASSWORD -> FTP_PASSWORD)
            .asJava))
      .collect(collector)
    service.close()
    print(fileList)
    fileList.size() shouldBe expectedFiles

  }

  it should "listBucketContents with whitelist and without prefix" in {

    val filesToCreateInEachFolder = 1000
    val whiteListWithWildCard = "/**/level1/*.txt"
    val folders = 5
    val expectedFiles = 2000
    val foldersNames = new ListBuffer[String]()
    foldersNames += "/"

    val externalFolderName = "/levelA/levelB/levelC/level1/"
    createFolders(externalFolderName)
    foldersNames += externalFolderName

    val writer = new FileWriter(dir.toString + externalFolderName + s"file-test.json")
    writer.append("test data")
    writer.close()

    for(i <- 0 until folders) {
      val folderName = s"/home/<USER>/data/science/level${i+1}/"
      createFolders(folderName)
      foldersNames += folderName
    }

    for (filePath <- foldersNames.toList) {
      for (i <- 0 until filesToCreateInEachFolder) {
        val writer = new FileWriter(dir.toString + filePath + s"file-test-$i.txt")
        writer.append("test data")
        writer.close()
      }
    }

    val collector: Collector[NexlaFile, _, util.List[NexlaFile]] = Collectors.toList()

    val service = new FtpConnectorService()
    val fileList = service
      .listBucketContents(
        new FileSourceConnectorConfig(
          Map(
            LISTING_APP_SERVER_URL -> "",
            SOURCE_ID -> "1",
            SOURCE_TYPE -> ConnectionType.FTP.name(),
            CREDENTIALS_DECRYPT_KEY -> "1",
            CREDS_ENC -> "1",
            CREDS_ENC_IV -> "1",
            DEPTH -> "6",
            "unit.test" -> "true",
            WHITELIST_PATHMATCHERS -> whiteListWithWildCard,
            FTP_MODE -> PASSIVE_LOCAL,
            PORT -> server.port.toString,
            USERNAME -> FTP_USERNAME,
            PASSWORD -> FTP_PASSWORD)
            .asJava))
      .collect(collector)
    service.close()

    fileList.size() shouldBe expectedFiles

  }

  it should "read sample" in {

    createFolders("/level1/level2/")

    val filePath = "/level1/level2/test.json"
    val writer = new FileWriter(dir.toString + filePath)
    val jsonString = "{\"a\": 5}\n{\"b\": 10}"

    writer.append(jsonString)
    writer.close()

    val service = new FtpConnectorService()
    val readSample = service
      .readSample(
        new FileSourceConnectorConfig(
          Map(
            LISTING_APP_SERVER_URL -> "",
            SOURCE_ID -> "1",
            SOURCE_TYPE -> ConnectionType.FTP.name(),
            CREDENTIALS_DECRYPT_KEY -> "1",
            CREDS_ENC -> "1",
            CREDS_ENC_IV -> "1",
            DEPTH -> "3",
            "unit.test" -> "true",
            FTP_MODE -> PASSIVE_LOCAL,
            PORT -> server.port.toString,
            USERNAME -> FTP_USERNAME,
            PASSWORD -> FTP_PASSWORD,
            PATH -> "/level1/level2/test.json")
            .asJava), false)
    service.close()

    readSample.getResult.asScala shouldBe List("{\"a\": 5}", "{\"b\": 10}")
  }

  it should "read raw sample" in {

    createFolders("/level1/level2/")

    val filePath = "/level1/level2/test.json"
    val writer = new FileWriter(dir.toString + filePath)
    val jsonString = "{\"a\": 5}\n{\"b\": 10}"

    writer.append(jsonString)
    writer.close()

    val service = new FtpConnectorService()
    val readSampleRaw = service
      .readSample(
        new FileSourceConnectorConfig(
          Map(
            SOURCE_ID -> "1",
            SOURCE_TYPE -> ConnectionType.FTP.name(),
            CREDENTIALS_DECRYPT_KEY -> "1",
            CREDS_ENC -> "1",
            CREDS_ENC_IV -> "1",
            DEPTH -> "3",
            "unit.test" -> "true",
            FTP_MODE -> PASSIVE_LOCAL,
            PORT -> server.port.toString,
            USERNAME -> FTP_USERNAME,
            PASSWORD -> FTP_PASSWORD,
            PATH -> "/level1/level2/test.json")
            .asJava)
        , true)
    service.close()

    readSampleRaw.getResult.asScala shouldBe List("{\"a\": 5}", "{\"b\": 10}")
  }

  it should "read binary data" in {

    createFolders("/level1/level2/")

    val filePath = "/level1/level2/test.file"
    val dest = new File(dir.toString + filePath)

    val src = new File(this.getClass.getClassLoader.getResource("test.au").getFile)
    FileUtils.copyFile(src, dest)

    val service = new FtpConnectorService()
    val readSampleRaw = service
      .readSample(
        new FileSourceConnectorConfig(
          Map(
            LISTING_APP_SERVER_URL -> "",
            SOURCE_ID -> "1",
            SOURCE_TYPE -> ConnectionType.FTP.name(),
            CREDENTIALS_DECRYPT_KEY -> "1",
            CREDS_ENC -> "1",
            CREDS_ENC_IV -> "1",
            DEPTH -> "3",
            "unit.test" -> "true",
            FTP_MODE -> PASSIVE_LOCAL,
            PORT -> server.port.toString,
            USERNAME -> FTP_USERNAME,
            PASSWORD -> FTP_PASSWORD,
            PATH -> "/level1/level2/test.file")
            .asJava)
        , false)
    service.close()

    readSampleRaw.isBinary shouldBe true
  }

  def deleteFilesAndFolders(file: File): Unit = {
    if (file.isDirectory)
      file.listFiles.foreach(deleteFilesAndFolders)
    else
      file.delete
  }

  def createFolders(folders: String): Unit = {
    doCreateFolders(folders, 2)
  }

  def doCreateFolders(folders: String, numFolder: Integer): Unit = {
    val endIndex = StringUtils.ordinalIndexOf(folders, "/", numFolder)
    if (endIndex == -1)
      return

    val subFolder = folders.substring(0, endIndex)
    val fileDir = new File(dir.toString + subFolder)
    fileDir.mkdir()
    doCreateFolders(folders, numFolder + 1)
  }

}