package com.nexla.telemetry.akka

import com.nexla.telemetry.{Telemetry, TelemetryContext}
import org.apache.commons.lang3.ThreadUtils
import org.slf4j.LoggerFactory

import java.util
import java.util.concurrent.{Executors, ScheduledExecutorService, TimeUnit}
import scala.collection.mutable

object AkkaThreadStateMonitor {
  val DEFAULT_INTERVAL_IN_SECONDS = 60
  private val logger = LoggerFactory.getLogger(AkkaThreadStateMonitor.getClass)
  private val supportedThreadStates = Set(Thread.State.BLOCKED, Thread.State.WAITING, Thread.State.TIMED_WAITING, Thread.State.RUNNABLE)

  def start(systemName: String, interval: Int, delay: Int, timeUnit: TimeUnit = TimeUnit.SECONDS): Unit = {
    logger.info(s"Scheduling akka thread state monitoring to check actor system $systemName every $interval $timeUnit")
    AkkaMonitoringPool.pool.scheduleWithFixedDelay(() => {
      val telemetry = TelemetryContext.get()
      val threadStates = mutable.Map[String, mutable.Map[Thread.State, Int]]()
      ThreadUtils.getAllThreads.stream()
        .filter(t => t.getName.startsWith(systemName) && supportedThreadStates.contains(t.getState))
        .forEach(t => {
          val threadName = getThreadName(t.getName)
          threadStates.get(threadName) match {
            case Some(threadStateCount) =>
              threadStateCount(t.getState) += 1
            case None =>
              val defaultCounts = supportedThreadStates.foldLeft(mutable.Map[Thread.State, Int]()) { (m, s) =>  m(s) = 0; m }
              defaultCounts(t.getState) = 1
              threadStates(threadName) = defaultCounts
          }
        })
      threadStates.foreach {
        case (threadName, value) =>
          value.foreach {
            case (state, count) => {
              telemetry.recordGauge(s"akka_thread_states", count, util.List.of(new Telemetry.Label("state", state.toString),
                                              new Telemetry.Label("name", threadName), new Telemetry.Label("system", systemName)))
            }
          }
      }
    }, delay, interval, timeUnit)
  }

  private def getThreadName(threadName: String): String = {
    val threadNumIndex: Int = threadName.lastIndexOf("-")
    if (threadNumIndex > 0)
      threadName.substring(0, threadNumIndex)
    else
      threadName
  }
}

object AkkaMonitoringPool {
  lazy val pool: ScheduledExecutorService = Executors.newScheduledThreadPool(1)
}
