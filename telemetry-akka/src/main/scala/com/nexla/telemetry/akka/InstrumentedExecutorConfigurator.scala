package com.nexla.telemetry.akka

import akka.dispatch._
import com.nexla.telemetry.Telemetry.Label
import com.nexla.telemetry.TelemetryContext
import com.typesafe.config.Config
import org.slf4j.{Logger, LoggerFactory}

import java.util
import java.util.concurrent._
import scala.util.Try

/**
 * Wrapper ExecutorServiceConfigurators that schedule instrumentation to run at a configurable interval.
 * They delegates to the built-in ForkJoinExecutorConfigurator and ThreadPoolExecutorConfigurator for the actual work
 *
 * Currently, instrumentation of affinity pool executor is not supported. For custom configurator,
 * you should add instrumentation in the configurator itself.
 *
 * To use this, configure the executor on the dispatcher using the FQN of this class. See the akka-actor
 * section of the configuration doc: https://doc.akka.io/docs/akka/current/general/configuration-reference.html
 */
class InstrumentedForkJoinPoolConfigurator(val config: Config, val prerequisites: DispatcherPrerequisites)
  extends ExecutorServiceConfigurator(config, prerequisites) {

  private val logger = LoggerFactory.getLogger(classOf[InstrumentedForkJoinPoolConfigurator])

  override def createExecutorServiceFactory(id: String, threadFactory: ThreadFactory): ExecutorServiceFactory = {
    val delegateConfigurator = new ForkJoinExecutorConfigurator(config.getConfig("fork-join-executor"), prerequisites)
    val delegateExecutorFactory = delegateConfigurator.createExecutorServiceFactory(id, threadFactory)

    new ExecutorServiceFactory {
      override def createExecutorService: ExecutorService = {
        val delegate = delegateExecutorFactory.createExecutorService
        logger.info(s"Creating ForkJoinPool for $id")
        delegate match {
          case fj: ForkJoinPool =>
            InstrumentedExecutorConfigurator.instrumentForkJoinPool(id, config, logger, fj)
          case es =>
            // shouldn't happen but just in case
            throw new IllegalArgumentException(s"Required ForkJoinPool but got ${es.getClass.getName}")
        }
        delegate
      }
    }
  }
}

class InstrumentedThreadPoolExecutorConfigurator(val config: Config, val prerequisites: DispatcherPrerequisites)
  extends ThreadPoolExecutorConfigurator(config.getConfig("thread-pool-executor"), prerequisites) {
  private val logger = LoggerFactory.getLogger(classOf[InstrumentedThreadPoolExecutorConfigurator])

  override def createExecutorServiceFactory(id: String, threadFactory: ThreadFactory): ExecutorServiceFactory = {
    val delegate = super.createExecutorServiceFactory(id, threadFactory)

    new ExecutorServiceFactory {
      override def createExecutorService: ExecutorService = {
        val es = delegate.createExecutorService
        logger.info(s"Creating ThreadPoolExecutor for $id")
        es match {
          case tp: ThreadPoolExecutor =>
            InstrumentedExecutorConfigurator.instrumentThreadPool(id, config, logger, tp)
          case es =>
            // shouldn't happen but just in case
            throw new IllegalArgumentException(s"Required ThreadPoolExecutor but got ${es.getClass.getName}")
        }
        es
      }
    }
  }
}

object InstrumentedExecutorConfigurator {
  val nexlaPrefix = "nexla-telemetry"

  def getIntervalInSeconds(config: Config): Int = {
    Try(config.getInt(s"$nexlaPrefix.interval-in-seconds")).getOrElse(60)
  }

  def getInitialDelayInSeconds(config: Config): Int = {
    Try(config.getInt(s"$nexlaPrefix.initial-delay-in-seconds")).getOrElse(120)
  }

  def instrumentThreadPool(id: String, config: Config, logger: Logger, tp: ThreadPoolExecutor): ScheduledFuture[_] = {
    val intervalInSec = getIntervalInSeconds(config)
    logger.info(s"Scheduling monitoring to check ThreadPoolExecutor for $id every $intervalInSec seconds")
    AkkaMonitoringPool.pool.scheduleWithFixedDelay(() => {
      val telemetry = TelemetryContext.get()
      val labels = util.List.of(new Label("type", "thread_pool_executor"), new Label("dispatcher", id))

      telemetry.recordGauge("akka_executor_pool_size", tp.getPoolSize, labels)
      telemetry.recordGauge("akka_executor_core_pool_size", tp.getCorePoolSize, labels)
      telemetry.recordGauge("akka_executor_active", tp.getActiveCount, labels)
      telemetry.recordGauge("akka_executor_task", tp.getTaskCount, labels)
      telemetry.recordGauge("akka_executor_queue_size", tp.getQueue.size(), labels)
    }, getInitialDelayInSeconds(config), intervalInSec, TimeUnit.SECONDS)
  }

  def instrumentForkJoinPool(id: String, config: Config, logger: Logger, fj: ForkJoinPool) = {
    val intervalInSec: Int = InstrumentedExecutorConfigurator.getIntervalInSeconds(config)
    logger.info(s"Scheduling monitoring to check ForkJoinPool for $id every $intervalInSec seconds")
    AkkaMonitoringPool.pool.scheduleWithFixedDelay(() => {
      val telemetry = TelemetryContext.get()
      val labels = util.List.of(new Label("type", "fork_join_pool"), new Label("dispatcher", id))

      telemetry.recordGauge("akka_executor_pool_size", fj.getPoolSize, labels)
      telemetry.recordGauge("akka_executor_active", fj.getActiveThreadCount, labels)
      telemetry.recordGauge("akka_executor_running", fj.getRunningThreadCount, labels)
      telemetry.recordGauge("akka_executor_queued_submitted", fj.getQueuedSubmissionCount, labels)
      telemetry.recordGauge("akka_executor_steal", fj.getStealCount, labels)
      telemetry.recordGauge("akka_executor_queued_task", fj.getQueuedTaskCount, labels)
      telemetry.recordGauge("akka_executor_parallelism", fj.getParallelism, labels)
    }, getInitialDelayInSeconds(config), intervalInSec, TimeUnit.SECONDS)
  }
}
