package com.nexla.common

import com.nexla.common.StreamUtils.skipLastElements
import one.util.streamex.StreamEx
import org.scalatest.Tag
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import java.util.concurrent.atomic.AtomicBoolean
import scala.collection.JavaConverters._

class StreamUtilsTest extends Tag("com.nexla.test.ScalaUnitTests") with AnyFlatSpecLike with Matchers {

  it should "skip last N elements, positive case" in {
    val streamEx = StreamEx.of(1, 2, 3, 4, 5)
    skipLastElements(streamEx, 2).asScala.toList shouldBe List(1, 2, 3)
  }

  it should "skip last N elements, skipping more than size of stream" in {
    val streamEx = StreamEx.of(1, 2, 3, 4, 5)
    skipLastElements(streamEx, 7).asScala.toList shouldBe List()
  }

  it should "skip last N elements in empty stream" in {
    val streamEx = StreamEx.empty[Int]()
    skipLastElements(streamEx, 2).asScala.toList shouldBe List()
  }

  it should "close common stream" in {
    val closed: AtomicBoolean = new AtomicBoolean(false)
    val stream = java.util.stream.Stream.of(1, 2, 3).onClose(() => closed.set(true))
    StreamUtils.autoClosedStream(stream).forEach(n => println(n))
    closed.get() shouldBe true
  }

  it should "close stream wrapped into StreamEx" in {
    val closed: AtomicBoolean = new AtomicBoolean(false)
    val commonStream = java.util.stream.Stream.of(1, 2, 3).onClose(() => closed.set(true))
    val streamEx = StreamEx.of(StreamUtils.autoClosedStream(commonStream))
    streamEx.forEach(n => println(n))
    closed.get() shouldBe true
  }

}