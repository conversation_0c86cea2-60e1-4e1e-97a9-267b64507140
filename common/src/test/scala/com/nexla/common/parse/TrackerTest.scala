package com.nexla.common.parse

import com.nexla.common.tracker.Tracker.TrackerMode
import com.nexla.common.tracker.{SetItem, SinkItem, SourceItem, Tracker}
import org.scalatest.Tag
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._

class TrackerTest extends Tag("com.nexla.test.ScalaUnitTests") with AnyFlatSpecLike with Matchers {

  behavior of "Tracker"

  private val trackerId = {
    val tracker = new Tracker()
    tracker.setSource(SourceItem.fullTracker(123, 1, "file.csv", 1L, 1, 100L))
    tracker.setSets(Seq(new SetItem(1, 1, 255)).asJava)
    tracker.setSink(new SinkItem(1, 1, 266))
    tracker
  }

  it should "convert to string in FILE mode" in {
    trackerId.toString(TrackerMode.FILE) shouldBe "f123:1:file.csv;1:1;1:1:266"
  }

  it should "convert to string in RECORD mode" in {
    trackerId.toString(TrackerMode.RECORD) shouldBe "r123:1:file.csv:1:1:100;1:1;1:1:266"
  }

  it should "convert to empty string in NONE mode" in {
    trackerId.toString(TrackerMode.NONE) shouldBe empty
  }

  it should "convert to string in FULL mode" in {
    trackerId.toString(TrackerMode.FULL) shouldBe "u123:1:file.csv:1:1:100;1:1:255;1:1:266"
  }

  it should "old parse source sets" in {
    // read in RECORD by default
    val trackerIdValue = "5010:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;NA"
    val trackerId = Tracker.parse(trackerIdValue)

    trackerId shouldNot be(null)
    checkItemNoDataSetId(trackerId.getSource)
    val sets = trackerId.getSets
    sets shouldNot be(null)
    sets.size shouldBe 2

    checkItem(sets.get(0), 7187, 2)
    checkItem(sets.get(1), 7188, 2)

    trackerId.getSink shouldBe null
    // write in FULL
    trackerId.toString() shouldBe "u5010::file.csv:123:1:1499194015000;7187:2:null;7188:2:null;NA"
  }

  it should "old parse source sets sink" in {
    // read in RECORD by default
    val trackerIdValue = "5010:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010"
    val trackerId = Tracker.parse(trackerIdValue)
    trackerId shouldNot be(null)
    checkItemNoDataSetId(trackerId.getSource)
    val sets = trackerId.getSets
    sets shouldNot be(null)
    sets.size shouldBe 2
    checkItem(sets.get(0), 7187, 2)
    checkItem(sets.get(1), 7188, 2)
    checkItem(trackerId.getSink)
    // write in FULL
    trackerId.toString() shouldBe "u5010::file.csv:123:1:1499194015000;7187:2:null;7188:2:null;4444:1:1010"
  }

  it should "old parser source" in {
    val trackerIdValue = "5010:file.csv:123:5:1:1499194015000;NA"
    val trackerId = Tracker.parse(trackerIdValue)
    trackerId shouldNot be(null)
    checkItemNoDataSetId(trackerId.getSource)
    trackerId.getSets.isEmpty shouldBe true
    trackerId.getSink shouldBe null
  }


  it should "old parser source sink" in {
    val trackerIdValue = "5010:file.csv:123:5:1:1499194015000;4444:1:1010"
    val trackerId = Tracker.parse(trackerIdValue)
    trackerId shouldNot be(null)
    checkItemNoDataSetId(trackerId.getSource)
    trackerId.getSets.isEmpty shouldBe true
    checkItem(trackerId.getSink)
  }


  it should "old parser source set sink" in {
    val trackerIdValue = "5010:file.csv:123:5:1:1499194015000;7187:2:250;4444:1:1010"
    val trackerId = Tracker.parse(trackerIdValue)
    trackerId shouldNot be(null)
    checkItemNoDataSetId(trackerId.getSource)
    val sets = trackerId.getSets
    sets shouldNot be(null)
    sets.size shouldBe 1
    checkItem(sets.get(0), 7187, 2)
    checkItem(trackerId.getSink)
    trackerIdValue shouldBe trackerIdValue
  }

  it should "old parser source set" in {
    val trackerIdValue = "5010:file.csv:123:5:1:1499194015000;7187:2:250;NA"
    val trackerId = Tracker.parse(trackerIdValue)
    trackerId shouldNot be(null)
    checkItemNoDataSetId(trackerId.getSource)
    val sets = trackerId.getSets
    sets shouldNot be(null)
    sets.size shouldBe 1
    checkItem(sets.get(0), 7187, 2)
    trackerId.getSink shouldBe null
    trackerIdValue shouldBe trackerIdValue
  }

  it should "parse source sets" in {
    val trackerIdValue = "u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;NA"
    val trackerId = Tracker.parse(trackerIdValue)

    trackerId shouldNot be(null)
    checkItem(trackerId.getSource)
    val sets = trackerId.getSets
    sets shouldNot be(null)
    sets.size shouldBe 2

    checkItem(sets.get(0), 7187, 2, 250L)
    checkItem(sets.get(1), 7188, 2, 750L)

    trackerId.getSink shouldBe null
    trackerId.toString shouldBe "u5010:123:file.csv:123:1:1499194015000;7187:2:250;7188:2:750;NA"
  }

  it should "parse source sets sink" in {
    val trackerIdValue = "u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010"
    val trackerId = Tracker.parse(trackerIdValue)
    trackerId shouldNot be(null)
    checkItem(trackerId.getSource)
    val sets = trackerId.getSets
    sets shouldNot be(null)
    sets.size shouldBe 2
    checkItem(sets.get(0), 7187, 2, 250L)
    checkItem(sets.get(1), 7188, 2, 750L)
    checkItem(trackerId.getSink)
    trackerId.toString shouldBe "u5010:123:file.csv:123:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010"
  }

  it should "parser source" in {
    val trackerIdValue = "u5010:123:file.csv:123:5:1:1499194015000;NA"
    val trackerId = Tracker.parse(trackerIdValue)
    trackerId shouldNot be(null)
    checkItem(trackerId.getSource)
    trackerId.getSets.isEmpty shouldBe true
    trackerId.getSink shouldBe null
  }


  it should "parser source sink" in {
    val trackerIdValue = "u5010:123:file.csv:123:5:1:1499194015000;4444:1:1010"
    val trackerId = Tracker.parse(trackerIdValue)
    trackerId shouldNot be(null)
    checkItem(trackerId.getSource)
    trackerId.getSets.isEmpty shouldBe true
    checkItem(trackerId.getSink)
  }


  it should "parser source set sink" in {
    val trackerIdValue = "u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;4444:1:1010"
    val trackerId = Tracker.parse(trackerIdValue)
    trackerId shouldNot be(null)
    checkItem(trackerId.getSource)
    val sets = trackerId.getSets
    sets shouldNot be(null)
    sets.size shouldBe 1
    checkItem(sets.get(0), 7187, 2, 250L)
    checkItem(trackerId.getSink)
    trackerId.toString shouldBe "u5010:123:file.csv:123:1:1499194015000;7187:2:250;4444:1:1010"
  }

  it should "parser source set" in {
    val trackerIdValue = "u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;NA"
    val trackerId = Tracker.parse(trackerIdValue)
    trackerId shouldNot be(null)
    checkItem(trackerId.getSource)
    val sets = trackerId.getSets
    sets shouldNot be(null)
    sets.size shouldBe 1
    checkItem(sets.get(0), 7187, 2, 250L)
    trackerId.getSink shouldBe null
    trackerId.toString shouldBe "u5010:123:file.csv:123:1:1499194015000;7187:2:250;NA"
  }


  private def checkItem(item: SourceItem) = {
    item.getSourceId shouldBe 5010
    item.getDataSetId shouldBe 123
    item.getSource shouldBe "file.csv"
    item.getRecordNumber shouldBe 123L
    item.getVersion shouldBe 1
    item.getInitialIngestTimestamp shouldBe 1499194015000L
  }

  private def checkItemNoDataSetId(item: SourceItem) = {
    item.getSourceId shouldBe 5010
    item.getSource shouldBe "file.csv"
    item.getRecordNumber shouldBe 123L
    item.getVersion shouldBe 1
    item.getInitialIngestTimestamp shouldBe 1499194015000L
  }

  private def checkItem(item: SetItem, id: Int, version: Int, time: Long) = {
    item.getId shouldBe id
    item.getVersion shouldBe version
    item.getOffsetFromIngest shouldBe time
  }

  private def checkItem(item: SetItem, id: Int, version: Int) = {
    item.getId shouldBe id
    item.getVersion shouldBe version
    item.getOffsetFromIngest shouldBe null
  }

  private def checkItem(item: SinkItem) = {
    item.getId shouldBe 4444
    item.getVersion shouldBe 1
    item.getOffsetFromIngest shouldBe 1010L
  }
}