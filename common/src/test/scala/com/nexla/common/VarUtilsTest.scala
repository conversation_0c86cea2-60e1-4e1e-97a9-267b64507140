package com.nexla.common

import java.util
import java.util.Optional
import com.nexla.common.ConfigUtils.opt
import com.nexla.common.time.VarUtils
import com.nexla.common.time.VarUtils.{VarInfo, getFolderStructure, processStringWithVars}
import org.joda.time.format.DateTimeFormat
import org.scalatest.{Tag, TagAnnotation}
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class VarUtilsTest extends AnyFunSuite with Matchers {

  test("json") {
    val template = """{"id":{id},"city":"{location.city}","country":"{location.country}"}"""
    val replacementMap = Map("id" -> "1", "location.city" -> "SF", "location.country" -> "USA")
    VarUtils.replaceVars(new VarInfo(template, replacementMap.keySet.asJava, Map.empty[String, String].asJava), replacementMap.asJava) shouldBe """{"id":1,"city":"SF","country":"USA"}"""
  }

  test("parse json") {
    val template = """{"country":"country"}"""
    val parsed = VarUtils.processStringWithVars(template)
    parsed.template shouldBe template
  }

  test("parse json with variable") {
    val template = """{"country":"{var}"}"""
    val replacementMap = Map("var" -> "country")
    val parsed = VarUtils.processStringWithVars(template)
    VarUtils.replaceVars(parsed, replacementMap.asJava) shouldBe """{"country":"country"}"""
  }

  test("parse json with default array value") {
    val template = """{obj=["a","b","c"]}"""
    val replacementMap = Map("obj" -> "[\"1\"]")
    val parsed = VarUtils.processStringWithVars(template)
    VarUtils.replaceVars(parsed, replacementMap.asJava) shouldBe """["1"]"""
  }

  test("parse json with default empty object value") {
    val template = """{obj={}}""".stripMargin
    val replacementMap = Map("obj" -> "{\"a\":\"b\"}")
    val parsed = VarUtils.processStringWithVars(template)
    VarUtils.replaceVars(parsed, replacementMap.asJava) shouldBe """{"a":"b"}"""
  }

  test("parse json with default object value") {
    val template = """[
                     |    {val1={
                     |        "a": {
                     |            "b": "c"
                     |        },
                     |        "d": [
                     |            {
                     |                "e": "f"
                     |            },
                     |            {
                     |                "g": "h"
                     |            }
                     |        ]
                     |    }},
                     |    {val2={
                     |        "key": "value"
                     |    }}
                     |]""".stripMargin
    val replacementMap = Map("val1" -> "1", "val2" -> "2")
    val parsed = VarUtils.processStringWithVars(template)
    VarUtils.replaceVars(parsed, replacementMap.asJava) shouldBe """[
                                                                   |    1,
                                                                   |    2
                                                                   |]""".stripMargin
  }

  test("parse json with complex json") {
    val template = """{
                     |    "manager": {manager={
                     |        "name": {
                     |            "first": "Eleanora",
                     |            "middle": "Nico",
                     |            "last": "Ritchie"
                     |        },
                     |        "location": {
                     |            "street": "288 Keven View",
                     |            "city": "Jeramiefield",
                     |            "state": "Virginia",
                     |            "country": "Italy",
                     |            "zip": "03650-4607",
                     |            "coordinates": {
                     |                "latitude": "-74.621",
                     |                "longitude": "-131.2483"
                     |            }
                     |        }
                     |    }},
                     |    "client": {client={
                     |        "phoneNumber": "(*************",
                     |        "phoneVariation": "+90 352 834 10 68",
                     |        "status": "active",
                     |        "name": {
                     |            "first": "Sierra",
                     |            "middle": "Drew",
                     |            "last": "Batz"
                     |        },
                     |        "location": {
                     |            "street": "766 W South Street",
                     |            "city": "Fort Trudie",
                     |            "state": "Pennsylvania",
                     |            "country": "Czechia",
                     |            "zip": "67900",
                     |            "coordinates": {
                     |                "latitude": "40.6716",
                     |                "longitude": "-92.6475"
                     |            }
                     |        }
                     |    }}
                     |}""".stripMargin
    val replacementMap = Map("manager" -> "{\"name\":{\"first\":\"Bob\"},\"status\":\"disabled\",\"location\":{\"street\":\"847 Charley Overpass\",\"city\":\"South Violet\",\"state\":\"Michigan\",\"country\":\"Turks and Caicos Islands\",\"zip\":\"62907-3126\",\"coordinates\":{\"latitude\":\"-87.8252\",\"longitude\":\"-116.13\"}}}")
    val parsed = VarUtils.processStringWithVars(template)
    VarUtils.replaceVars(parsed, replacementMap.asJava) shouldBe """{
                                                                   |    "manager": {"name":{"first":"Bob"},"status":"disabled","location":{"street":"847 Charley Overpass","city":"South Violet","state":"Michigan","country":"Turks and Caicos Islands","zip":"62907-3126","coordinates":{"latitude":"-87.8252","longitude":"-116.13"}}},
                                                                   |    "client": {
                                                                   |        "phoneNumber": "(*************",
                                                                   |        "phoneVariation": "+90 352 834 10 68",
                                                                   |        "status": "active",
                                                                   |        "name": {
                                                                   |            "first": "Sierra",
                                                                   |            "middle": "Drew",
                                                                   |            "last": "Batz"
                                                                   |        },
                                                                   |        "location": {
                                                                   |            "street": "766 W South Street",
                                                                   |            "city": "Fort Trudie",
                                                                   |            "state": "Pennsylvania",
                                                                   |            "country": "Czechia",
                                                                   |            "zip": "67900",
                                                                   |            "coordinates": {
                                                                   |                "latitude": "40.6716",
                                                                   |                "longitude": "-92.6475"
                                                                   |            }
                                                                   |        }
                                                                   |    }
                                                                   |}""".stripMargin
  }

  test("parse json while ignoring open curly brackets within strings") {
    val template = """{"a": {obj="1{\"\"{{{\"}}\"\"\"2"}}""".stripMargin
    val replacementMap = Map("obj" -> "{\"a\":\"b\"}")
    val parsed = VarUtils.processStringWithVars(template)
    VarUtils.replaceVars(parsed, replacementMap.asJava) shouldBe """{"a": {"a":"b"}}""".stripMargin
  }

  test("parse json while ignoring closing curly brackets within strings") {
    val template = """{"a": {obj="{\"\"{\"}}}}}\"\"\""}}""".stripMargin
    val replacementMap = Map("obj" -> "{\"a\":\"b\"}")
    val parsed = VarUtils.processStringWithVars(template)
    VarUtils.replaceVars(parsed, replacementMap.asJava) shouldBe """{"a": {"a":"b"}}"""
  }

  test("default no structure") {
    val date = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").parseDateTime("2017-09-12 13:14:15")
    "" shouldBe getFolderStructure(date, new util.HashMap[String, String](), Optional.empty(), true)
  }

  test("sql query with inner query as variable") {
    val template = """select * from data where email = {email=<EMAIL>}"""
    val replacementMap = Map("email" -> "(select '<EMAIL>' from dual where 1=1)")
    val parsed = VarUtils.processStringWithVars(template)
    VarUtils.replaceVars(parsed, replacementMap.asJava) shouldBe """select * from data where email = (select '<EMAIL>' from dual where 1=1)"""
  }

  test("url with sql query param and query as variable") {
    val template = """/services/data/v45.0/query/?q={query=SELECT Name, Id from Account}"""
    val replacementMap = Map("query" -> "SELECT Name, Id from Account WHERE Id='0011U00001v8rWGQAY'")
    val parsed = VarUtils.processStringWithVars(template)
    VarUtils.replaceVars(parsed, replacementMap.asJava) shouldBe """/services/data/v45.0/query/?q=SELECT Name, Id from Account WHERE Id='0011U00001v8rWGQAY'"""
  }

  test("url with sql query param and query (with where condition) as variable") {
    val template = """/services/data/v45.0/query/?q={query=SELECT Name, Id from Account WHERE Id='1'}"""
    val replacementMap = Map("query" -> "SELECT Name, Id from Account WHERE Id='2'")
    val parsed = VarUtils.processStringWithVars(template)
    VarUtils.replaceVars(parsed, replacementMap.asJava) shouldBe """/services/data/v45.0/query/?q=SELECT Name, Id from Account WHERE Id='2'"""
  }

  test("getFolderStructure padded") {
    val date = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").parseDateTime("2017-09-12 13:14:15")
    "data/2017-09-12/13_14_15" shouldEqual getFolderStructure(
      date, new util.HashMap[String, String](), opt("data/{yyyy}-{MM}-{dd}/{HH}_{mm}_{ss}"), true)
  }

  test("getFolderStructure not padded") {
    val date = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").parseDateTime("2017-09-12 01:05:09")
    "data/2017-9-12/1_5_9" shouldEqual getFolderStructure(
      date, new util.HashMap[String, String](), opt("data/{yyyy}-{MM}-{dd}/{HH}_{mm}_{ss}"), false)
  }

  test("replaceVars") {
    val date = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").parseDateTime("2017-09-12 01:05:09")
    "data/2017-9-12/1_5_9" shouldEqual getFolderStructure(
      date, new util.HashMap[String, String](), opt("data/{yyyy}-{MM}-{dd}/{HH}_{mm}_{ss}"), false)
  }

  test("processStringWithVars") {
    val varInfo = processStringWithVars("{a}-{b}-data-{record.city=NY}-{record.vehicleType}-{record.location.city=SF}.txt")

    "{a}-{b}-data-{city}-{vehicleType}-{location.city}.txt" shouldEqual varInfo.template
    Set("a", "b", "city", "vehicleType", "location.city").asJava shouldEqual varInfo.variables
    Map("city" -> "NY", "location.city" -> "SF").asJava shouldEqual varInfo.defaults
  }

  test("trimNotReplaced") {
    val template = """{a}-{b}-data-{record.city=NY}-{record.vehicleType}-{record.location.city=SF}.txt"""
    val replacementMap = Map("b" -> "[b-value]")
    val parsed = VarUtils.processStringWithVars(template)
    VarUtils.replaceVars(parsed, replacementMap.asJava, true) shouldBe """-[b-value]-data-NY--SF.txt"""
  }
}
