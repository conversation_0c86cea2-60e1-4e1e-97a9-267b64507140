package com.nexla.spark_agent.utils

import com.nexla.admin.client.{AdminApiClient, DataSet, DataSink, DataSource}
import com.nexla.common.{ConnectionType, MinimalNexlaFile, NexlaConstants, ResourceType}
import com.nexla.common.metrics.NexlaRawMetric
import com.nexla.common.notify.monitoring.{NexlaMonitoringLogEvent, NexlaMonitoringLogSeverity, NexlaMonitoringLogType}
import com.nexla.connector.config.FlowType
import com.nexla.sc.api.helper.{AdminApiHelper, SparkTransform}
import com.nexla.spark_agent.infra.NexlaSparkSchemaConverter
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.SparkConf

import java.awt.GraphicsEnvironment
import java.util.{Optional, Properties}
import scala.collection.JavaConverters._
import scala.collection.immutable

object GeneralUtils extends LazyLogging {

  def preparePaths(src: DataSource, nexlaFiles: immutable.Seq[MinimalNexlaFile], noHive: Boolean): List[String] = {
    if (noHive) {
      val filesToProcess = nexlaFiles.map(_.getFullPath)

      val fullSrcPath = src.getSourceConfig.get("path").asInstanceOf[String]
      val pathPrefix = src.getConnectionType.name().toLowerCase + "://" + fullSrcPath.substring(0, fullSrcPath.indexOf("/")) + "/"
      filesToProcess.map(a => pathPrefix + a).toList
    } else {
      nexlaFiles.map(_.getFullPath).toList
    }
  }

  def devMode(): Boolean = {
    logger.debug("Trying to detect where the Nexla Spark Agent is running")
    val xpcsce = System.getenv("XPC_SERVICE_NAME")
    logger.debug(s"Detected xpc service name: $xpcsce")
    val classPath = System.getProperty("java.class.path")
    val hasGUI = try {
      val env = GraphicsEnvironment.getLocalGraphicsEnvironment
      env.getScreenDevices.length != 0
    } catch {
      case t: Throwable =>
        logger.debug("Graphics environment is not accessible, most probably server env", t)
        false
    }
    // -> devmode macOS
    val runningIdeOnMacOS: Boolean = if (xpcsce != null) {
      !xpcsce.equals("0")
    } else {
      false
    }
    val runningInIdea = classPath.toLowerCase.contains("idea_rt.jar")
    val result = Seq(runningInIdea, runningIdeOnMacOS, hasGUI).reduce(_ || _)
    logger.debug(s"Local developer environment: [$result]")
    result
  }

  def loadSparkCfgFromFile(fileName: String): SparkConf = {
    val sparkCfg = new SparkConf()
    val sparkHiveProps = new Properties()
    sparkHiveProps.load(getClass.getResourceAsStream(fileName))
    sparkHiveProps.entrySet().asScala.foreach { entry =>
      sparkCfg.set(entry.getKey.toString, entry.getValue.toString)
    }
    sparkCfg
  }

  def prepareMetricsForSource(src: DataSource, records: Long, bytes: Long, runId: Long): NexlaRawMetric = {
    val now = System.currentTimeMillis()
    val metricForSource = NexlaRawMetric.create(
      ResourceType.SOURCE,
      src.getId,
      records,
      bytes,
      0L, // errorCount
      now,
      Optional.ofNullable(runId),
      Optional.of(true), // eof
      Optional.empty(), // hash
      Optional.empty(), // datasetId
      Optional.of(now), // lastModified
      Optional.empty(), // messageNumber
      Optional.empty(), // metadata
      FlowType.SPARK,
      java.util.Optional.of(src.getFlowNodeId),
      java.util.Optional.of(src.originNodeId),
      Optional.empty(),
      src.getOrgId,
      src.getOwnerId
    )
    metricForSource
  }

  def prepareErrorMetricsForSource(src: DataSource, errors: Long, errorBytes: Long, runId: Long): NexlaRawMetric = {
    val now = System.currentTimeMillis()
    val metricForSource = NexlaRawMetric.create(
      ResourceType.SOURCE,
      src.getId,
      errors,
      errorBytes,
      errors, // errorCount
      now,
      Optional.ofNullable(runId),
      Optional.of(true), // eof
      Optional.empty(), // hash
      Optional.empty(), // datasetId
      Optional.of(now), // lastModified
      Optional.empty(), // messageNumber
      Optional.empty(), // metadata
      FlowType.SPARK,
      java.util.Optional.of(src.getFlowNodeId),
      java.util.Optional.of(src.originNodeId),
      Optional.empty(),
      src.getOrg.getId,
      src.getOwner.getId
    )
    metricForSource
  }

  def prepareErrorMetricsForSink(sink: DataSink, errors: Long, runId: Long): NexlaRawMetric = {
    val now = System.currentTimeMillis()
    val metricForSink = NexlaRawMetric.create(
      ResourceType.SINK,
      sink.getId,
      0,
      0,
      errors, // errorCount
      now,
      Optional.ofNullable(runId),
      Optional.of(true), // eof
      Optional.empty(), // hash
      Optional.of(sink.getDataSetId), // datasetId
      Optional.of(now), // lastModified
      Optional.empty(), // messageNumber
      Optional.empty(), // metadata
      FlowType.SPARK,
      java.util.Optional.of(sink.getFlowNodeId),
      java.util.Optional.of(sink.originNodeId),
      java.util.Optional.empty(),
      sink.getOrg.getId,
      sink.getOwner.getId
    )
    metricForSink
  }

  def prepareErrorMetricsForDataset(set: DataSet, records: Long, bytes: Long, runId: Long): NexlaRawMetric = {
    val now = System.currentTimeMillis()
    val metricForDataset = NexlaRawMetric.create(
      ResourceType.DATASET,
      set.getId,
      records,
      bytes,
      0L, // errorCount
      now,
      Optional.ofNullable(runId),
      Optional.of(true), // eof
      Optional.empty(), // hash
      Optional.empty(), // datasetId
      Optional.of(now), // lastModified
      Optional.empty(), // messageNumber
      Optional.empty(), // metadata
      FlowType.SPARK,
      java.util.Optional.of(set.getFlowNodeId),
      java.util.Optional.of(set.originNodeId),
      java.util.Optional.empty(),
      set.getOrg.getId,
      set.getOwner.getId
    )
    metricForDataset
  }

  def prepareMetricsForDataset(set: DataSet, records: Long, bytes: Long, runId: Long): NexlaRawMetric = {
    val now = System.currentTimeMillis()
    val metricForDataset = NexlaRawMetric.create(
      ResourceType.DATASET,
      set.getId,
      records,
      bytes,
      0L, // errorCount
      now,
      Optional.ofNullable(runId),
      Optional.of(true), // eof
      Optional.empty(), // hash
      Optional.empty(), // datasetId
      Optional.of(now), // lastModified
      Optional.empty(), // messageNumber
      Optional.empty(), // metadata
      FlowType.SPARK,
      java.util.Optional.of(set.getFlowNodeId),
      java.util.Optional.of(set.originNodeId),
      java.util.Optional.empty(),
      set.getOrg.getId,
      set.getOwner.getId
    )
    metricForDataset.setOrgId(set.getOrg.getId)
    metricForDataset.setOwnerId(set.getOwner.getId)
    metricForDataset
  }

  def prepareMetricsForSink(sink: DataSink, records: Long, bytes: Long, runId: Long): NexlaRawMetric = {
    val now = System.currentTimeMillis()
    val metricForSink = NexlaRawMetric.create(
      ResourceType.SINK,
      sink.getId,
      records,
      bytes,
      0, // errorCount
      now,
      Optional.ofNullable(runId),
      Optional.of(true), // eof
      Optional.empty(), // hash
      Optional.of(sink.getDataSetId), // datasetId
      Optional.of(now), // lastModified
      Optional.empty(), // messageNumber
      Optional.empty(), // metadata
      FlowType.SPARK,
      java.util.Optional.of(sink.getFlowNodeId),
      java.util.Optional.of(sink.originNodeId),
      java.util.Optional.empty(),
      sink.getOrg.getId,
      sink.getOwner.getId
    )
    metricForSink
  }

  def prepareLogSourceInfo(src: DataSource, runId: Long, msg: String): NexlaMonitoringLogEvent = {
    prepareMonitoringLogEvent(NexlaMonitoringLogSeverity.INFO, src.getOrg.getId, runId, ResourceType.SOURCE, src.getId, msg)
  }

  def prepareLogSourceWarn(src: DataSource, runId: Long, msg: String): NexlaMonitoringLogEvent = {
    prepareMonitoringLogEvent(NexlaMonitoringLogSeverity.WARNING, src.getOrg.getId, runId, ResourceType.SOURCE, src.getId, msg)
  }

  def prepareLogSourceError(src: DataSource, runId: Long, msg: String): NexlaMonitoringLogEvent = {
    prepareMonitoringLogEvent(NexlaMonitoringLogSeverity.ERROR, src.getOrg.getId, runId, ResourceType.SOURCE, src.getId, msg)
  }

  def prepareLogSinkInfo(sink: DataSink, runId: Long, msg: String): NexlaMonitoringLogEvent = {
    prepareMonitoringLogEvent(NexlaMonitoringLogSeverity.INFO, sink.getOrg.getId, runId, ResourceType.SINK, sink.getId, msg)
  }

  def prepareLogSinkWarn(sink: DataSink, runId: Long, msg: String): NexlaMonitoringLogEvent = {
    prepareMonitoringLogEvent(NexlaMonitoringLogSeverity.WARNING, sink.getOrg.getId, runId, ResourceType.SINK, sink.getId, msg)
  }

  def prepareLogSinkError(sink: DataSink, runId: Long, msg: String): NexlaMonitoringLogEvent = {
    prepareMonitoringLogEvent(NexlaMonitoringLogSeverity.ERROR, sink.getOrg.getId, runId, ResourceType.SINK, sink.getId, msg)
  }

  def prepareLogNexsetInfo(dsOrgId: Int, dsId: Int, runId: Long, msg: String): NexlaMonitoringLogEvent = {
    prepareMonitoringLogEvent(NexlaMonitoringLogSeverity.INFO, dsOrgId, runId, ResourceType.SINK, dsId, msg)
  }

  def prepareLogNexsetWarn(ds: DataSet, runId: Long, msg: String): NexlaMonitoringLogEvent = {
    prepareMonitoringLogEvent(NexlaMonitoringLogSeverity.WARNING, ds.getOrg.getId, runId, ResourceType.SINK, ds.getId, msg)
  }

  def prepareLogNexsetError(ds: DataSet, runId: Long, msg: String): NexlaMonitoringLogEvent = {
    prepareMonitoringLogEvent(NexlaMonitoringLogSeverity.ERROR, ds.getOrg.getId, runId, ResourceType.SINK, ds.getId, msg)
  }

  def prepareMonitoringLogEvent(severity: NexlaMonitoringLogSeverity, orgId: Int, runId: Long,
                                resourceType: ResourceType, resourceId: Int, message: String): NexlaMonitoringLogEvent = {
    val msg = new NexlaMonitoringLogEvent()
    msg.setLog(message)
    msg.setOrgId(orgId)
    msg.setRunId(runId)
    msg.setLogType(NexlaMonitoringLogType.LOG)
    msg.setSeverity(severity)
    msg.setResourceId(resourceId)
    msg.setResourceType(resourceType)
    msg.setEventTimeMillis(System.currentTimeMillis())
    msg
  }

  def stringifyStackTrace(t: Throwable): String = {
    t.getStackTrace.map(_.toString).mkString("\n")
  }

  def isAws(envVars: Map[String, String]): Boolean = {
    val javaHome = envVars.get("JAVA_HOME")
    val executorJavaHome = envVars.get("spark.executorEnv.JAVA_HOME")
    val awsExpectedValue = "/usr/lib/jvm/java-11-amazon-corretto.x86_64"

    val a = javaHome.exists(_.equalsIgnoreCase(awsExpectedValue))
    val b = executorJavaHome.exists(_.equalsIgnoreCase(awsExpectedValue))

    a || b
  }

  def detectCloud(): Cloud = {
    val envVars: Map[String, String] = sys.env
    if (envVars.contains("DATABRICKS_RUNTIME_VERSION")) {
      Databricks
    } else if (isAws(envVars)) {
      AWS
    } else {
      logger.warn("Could not detect the cloud provider, assuming this is local runtime")
      Local
    }
  }

  def unityCatalogEnabledCredentials(credentialsMap: Map[String, String]): Boolean = {
    extractCatalogNameFromCreds(credentialsMap).exists(a => a.nonEmpty && !a.isBlank)
  }

  def extractCatalogNameFromCreds(srcCfg: Map[String, AnyRef]): Option[String] = {
    srcCfg.get("catalog").orElse(srcCfg.get(NexlaConstants.UNITY_CATALOG_NAME)).map(_.toString)
  }

  def unityCatalogEnabled(dataSource: DataSource): Boolean = {
    val srcCfg = dataSource.getSourceConfig.asScala.toMap

    val unityCatalogName = extractCatalogNameFromCreds(srcCfg)

    if (unityCatalogName.isDefined) {
      // so at least something is there. it must not be empty or blank
      unityCatalogName.get.nonEmpty && !unityCatalogName.get.isBlank
    } else {
      false
    }
  }

  def unityCatalogSamplingForAllEnabled(src: DataSource): Boolean = {
    val srcCfg = src.getSourceConfig.asScala.toMap
    val unityCatalogName = srcCfg.get(NexlaConstants.UNITY_CATALOG_SAMPLE_ALL).map(_.toString).map(_.toBoolean)
    unityCatalogName.getOrElse(false)
  }

  def sparkify(finalSql: String, sparkTransform: SparkTransform, ds: DataSet, adminApi: AdminApiHelper, unityEnabled: Boolean): String = {
    val precedingDatasetOrSource = adminApi.getFromSingleDataset(ds).head
    val maybeDs = adminApi.recursiveGetParentDatasets(precedingDatasetOrSource.id()).tail

    val replacement = if (maybeDs.forall(_.getDescription.toLowerCase().contains("detected"))) {
      // then just the source - as the only dataset is detected
      val srcId = adminApi.getParentSource(precedingDatasetOrSource.id()).getId
      s"src_$srcId"
    } else if (maybeDs.head.getId().equals(sparkTransform.id())) {
      // this closure means we need to select from source
      val srcId = adminApi.getParentSource(ds.getId).getId
      s"src_$srcId"
    } else {
      s"nexset_${maybeDs.head.getId()}"
    }

    if (unityEnabled) {
      var finalSqlAfterFix = finalSql

      val columns = new NexlaSparkSchemaConverter().convert(maybeDs.head.getOutputSchema).fields.map(_.name).toList
      columns.foreach(col =>
        finalSqlAfterFix = finalSqlAfterFix.replaceAll(col, s"$replacement.$col")
      )

      finalSqlAfterFix.replaceAll("\\{nexset_this\\}", replacement)
    } else {
      finalSql.replaceAll("\\{nexset_this\\}", replacement)
    }
  }
}
