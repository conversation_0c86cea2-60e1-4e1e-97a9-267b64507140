package com.nexla.spark_agent.tools

import com.bazaarvoice.jolt.JsonUtils
import com.fasterxml.jackson.databind.{DeserializationFeature, ObjectMapper}
import com.nexla.common.MinimalNexlaFile
import com.nexla.control.ListingFileStatus
import com.nexla.sc.util.StrictNexlaLogging
import com.nexla.spark_agent.infra.AdminApiEnv
import com.nexla.spark_agent.tools.Utils.preparePostWithApiKey
import org.apache.http.impl.client.CloseableHttpClient
import org.apache.http.util.EntityUtils

import scala.collection.JavaConverters._

class SparkListingClient(adminApiEnv: AdminApiEnv, debugEnabled: Boolean,
                         httpClient: CloseableHttpClient) extends StrictNexlaLogging {

  private val objectMapper: ObjectMapper = new ObjectMapper()
  objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
  private val useLocalListing: Boolean = Option(System.getProperty("useLocalListing")).isDefined

  private val httpGatewayUrl: String = if (debugEnabled && useLocalListing) {
    "http://localhost:8080"
  } else {
    adminApiEnv.backendHttpGatewayUrl
  }

  def markAsX(processedFile: MinimalNexlaFile, sourceId: Int, status: ListingFileStatus): Unit = {
    val entryToMark = if (Option(processedFile.getId).isDefined) {
      processedFile
    } else {
      throw new IllegalArgumentException(s"File $processedFile doesn't have relevant id on the backend side")
    }

    val listingUrl = httpGatewayUrl + s"/files/$sourceId/${entryToMark.getId}/setStatus"
    val resultsFromHttpGw = httpClient.execute(preparePostWithApiKey(listingUrl, status.name(), adminApiEnv.adminApiAuthToken, objectMapper))
    val stringResponse = EntityUtils.toString(resultsFromHttpGw.getEntity, "UTF-8")
    logger.info(s"Finished marking file ${processedFile.getId} as ${status.name()}, response: [$stringResponse]")
  }

  def markAsDone(f: MinimalNexlaFile, sourceId: Int): Unit = {
    markAsX(f, sourceId, ListingFileStatus.DONE)
  }

  def markAsFailed(f: MinimalNexlaFile, sourceId: Int): Unit = {
    markAsX(f, sourceId, ListingFileStatus.STOPPED)
  }

  def takeFiles(sourceId: Int, howMany: Int): List[MinimalNexlaFile] = {
    val listingUrl = httpGatewayUrl + s"/files/$sourceId/takeMany/$howMany"
    val resultsFromHttpGw = httpClient.execute(preparePostWithApiKey(listingUrl, "", adminApiEnv.adminApiAuthToken, objectMapper))
    val stringResponse = EntityUtils.toString(resultsFromHttpGw.getEntity, "UTF-8")
    logger.debug(s"raw response from httpgw/listing: $stringResponse")
    val responseRaw = JsonUtils.jsonToMap(stringResponse).get("message").asInstanceOf[String]
    val actualResult: List[Object] = JsonUtils.jsonToList(responseRaw).asScala.toList
    val files: List[MinimalNexlaFile] = actualResult.map {
      rawObj =>
        val raw = rawObj.asInstanceOf[java.util.LinkedHashMap[String, Object]]
        val f = new MinimalNexlaFile()
        f.setId(raw.get("id").asInstanceOf[Int])
        f.setFullPath(raw.get("fullPath").asInstanceOf[String])
        f
    }

    resultsFromHttpGw.close()
    files
  }
}
