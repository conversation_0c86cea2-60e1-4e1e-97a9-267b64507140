package com.nexla.spark_agent.infra

import com.nexla.admin.client.NexlaSchema
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.types._

import scala.collection.JavaConverters.asScalaSetConverter
import scala.collection.mutable

class NexlaSparkSchemaConverter extends LazyLogging {

  def convert(origNexlaSchema: NexlaSchema): StructType = {
    val schemaCols = origNexlaSchema.getProperties.asInstanceOf[java.util.LinkedHashMap[String, Object]]
    val sparks = mutable.ArrayBuffer.newBuilder[StructField]

    schemaCols.entrySet().asScala.foreach {
      entry =>
        val columnName = entry.getKey
        sparks.+=(StructField(columnName, StringType, nullable = true))
    }
    StructType(sparks.result().toArray)
  }

}