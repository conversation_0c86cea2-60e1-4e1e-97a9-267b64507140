package com.nexla.spark_agent.infra

import com.nexla.transform.{Transformer, TransformerResult}

import java.util
import java.util.concurrent.ConcurrentHashMap

object NexlaTransformSingletonWrapper {
  println(s"xxxx creating NexlaTransformSingletonWrapper, spec [${TransformerConfig.spec}]")

  @transient lazy val instance: Transformers = {
    println(s"xxxx init transformers, spec hashsum [${TransformerConfig.spec.hashCode()}], spec [${TransformerConfig.spec.toString}]")
    new Transformers(TransformerConfig.spec)
  }

  @transient lazy val errorMap: java.util.Map[String, Object] = {
    java.util.Map.of[String, Object]("ERR", "ERR")
  }

  def processLhm(r: java.util.Map[String, Object], specHashCode: Int): java.util.Map[String, Object] = {
    try {
      val result = instance.transform(r, specHashCode)
      val hmm = result.stream()
        // todo: errors? nulls?
        .filter((tr: TransformerResult) => tr.getOutput != null)
        // XXX: convert to treeMap to sort by keys (as admin api stores output schema in alphabetical order)
        .map[java.util.TreeMap[String, Object]] { a =>
          val b = a.getOutput.asInstanceOf[java.util.LinkedHashMap[String, Object]]
          new util.TreeMap[String, Object](b)
        }
        .reduce((firstMap: java.util.TreeMap[String, Object], secondMap: java.util.TreeMap[String, Object]) => {
          firstMap.putAll(secondMap)
          return new util.TreeMap[String, Object](firstMap)
        }).orElse(null)
      hmm
    } catch {
      case _: Exception =>
        errorMap
    }

  }
}

object TransformerConfig extends Serializable {
  println(s"xxxx creating NexlaTransformSingletonWrapper, spec [${TransformerConfig.spec}]")
  var spec: Object = _
  println(s"xxxx creating NexlaTransformSingletonWrapper, spec [${TransformerConfig.spec}]")
}

class Transformers(initialSpec: Object)  {

  private val transformers = new ConcurrentHashMap[Int, Transformer]()
  println(s"xxxx creating first transformer with spec: ${initialSpec}, hashcode ${initialSpec.hashCode()}")
  val firstTx = new Transformer(initialSpec)
  transformers.put(initialSpec.hashCode(), firstTx)

  def transform(r: java.util.Map[String, Object], specHashCode: Int): java.util.List[TransformerResult] = {
    // if such a tx exists, get it
    // otherwise put to a map, return and use it
    if (transformers.get(specHashCode) != null) {
      val tx = transformers.get(specHashCode)
      tx.transform(r)
    } else {
      val spec = TransformerConfig.spec
      println(s"xxxx warn: no such transformer found, putting a new one with spec: ${spec}, hashcode ${spec.hashCode()}")
      transformers.putIfAbsent(specHashCode, new Transformer(spec))
      val tx = transformers.get(specHashCode)
      tx.transform(r)
    }
  }
}
