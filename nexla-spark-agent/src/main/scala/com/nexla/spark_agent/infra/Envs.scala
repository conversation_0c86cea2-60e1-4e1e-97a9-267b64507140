package com.nexla.spark_agent.infra

import com.typesafe.scalalogging.LazyLogging

case class AdminApiEnv(serverUrl: String, adminApiAuthToken: String, dataplaneUid: String, enrichmentCredsUrl: String, backendHttpGatewayUrl: String)

object Envs extends LazyLogging {

  def envsAndMap(mainUrl: String, nexlaKeyFromSecrets: String): AdminApiEnv = {
    mainUrl match {
      case "https://test.nexla.com/admin-api" => test(nexlaKeyFromSecrets)
      case "https://qa.nexla.com/admin-api" | "https://qa.nexla.com/nexla-cli-api" => qa(nexlaKeyFromSecrets)
      case "https://beta.nexla.com/admin-api" | "https://beta.nexla.com/nexla-cli-api" => beta(nexlaKeyFromSecrets)
      case "https://dataops.nexla.io/admin-api" | "https://dataops.nexla.io/nexla-cli-api" => prod(nexlaKeyFromSecrets)
      case other =>
        logger.warn(s"Unknown Nexla env $other, falling back to test")
        test(nexlaKeyFromSecrets)
    }
  }


  def test(nexlaKeyFromSecrets: String): AdminApiEnv = {
    logger.info("USING TEST.NEXLA.COM")
    AdminApiEnv(
      "https://test.nexla.com/admin-api",
      nexlaKeyFromSecrets,
      "",
      "",
      "https://test-gw.nexla.com"
    )
  }

  def qa(nexlaKeyFromSecrets: String): AdminApiEnv = {
    logger.info("USING QA.NEXLA.COM")
    AdminApiEnv(
      "https://qa.nexla.com/admin-api",
      nexlaKeyFromSecrets,
      "",
      "",
      "https://qa-gw.nexla.com"
    )
  }

  def beta(nexlaKeyFromSecrets: String): AdminApiEnv = {
    logger.info("USING BETA.NEXLA.COM")
    AdminApiEnv(
      "https://beta.nexla.com/admin-api",
      nexlaKeyFromSecrets,
      "",
      "",
      "https://stage-gateway.nexla.com"
    )
  }

  def prod(nexlaKeyFromSecrets: String): AdminApiEnv = {
    logger.info("USING DATAOPS.NEXLA.IO")
    AdminApiEnv(
      "https://dataops.nexla.io/admin-api",
      nexlaKeyFromSecrets,
      "",
      "",
      "https://dataops-gw.nexla.io"
    )
  }
}
