package com.nexla.spark_agent.infra

import scopt.{OParser, OParserBuilder}

case class SparkAgentCfg(mode: String = "", sink: String = "", partitionPath: String = "", runId: String = "")

object SparkAgentConfig {
  val builder: OParserBuilder[SparkAgentCfg] = OParser.builder[SparkAgentCfg]

  def tryParse(b: String): Map[String, List[String]] = {
    b.trim.split(",").map(
      unsplit => {
        val possibleMapEntry = unsplit.split("#")
        if (possibleMapEntry.length == 2) {
          (possibleMapEntry(0), possibleMapEntry(1))
        } else {
          throw new IllegalArgumentException("too many or not enough comma-separated values")
        }
      }
    ).groupBy(_._1)
      .map { case (k, l) => k -> l.map { case (_, v) => v.trim + "/" }.toList }
  }
}