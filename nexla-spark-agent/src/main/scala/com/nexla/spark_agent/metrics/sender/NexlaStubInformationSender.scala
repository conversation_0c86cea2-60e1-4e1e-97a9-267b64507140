package com.nexla.spark_agent.metrics.sender

import com.nexla.common.metrics.NexlaRawMetric
import com.nexla.common.notify.monitoring.NexlaMonitoringLogEvent
import com.typesafe.scalalogging.LazyLogging

class NexlaStubInformationSender extends NexlaInformationSender with LazyLogging {
  override def send(metric: NexlaRawMetric): Unit = {
    logger.info(s"Will send following metric back to Nexla: ${metric.toString}")
  }

  override def sendFlowInsight(flowInsight: NexlaMonitoringLogEvent): Unit = {
    logger.info(s"Will send following flow insight back to Nexla: ${flowInsight.toString}")
  }
}
