package com.nexla.spark_agent.metrics

import com.nexla.admin.client.{AdminApiClient, DataSet, DataSink, DataSource}
import com.nexla.spark_agent.metrics.sender.NexlaInformationSender
import com.nexla.spark_agent.utils.GeneralUtils
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.executor.{InputMetrics, OutputMetrics}
import org.apache.spark.scheduler.{SparkListener, SparkListenerApplicationEnd, SparkListenerApplicationStart, SparkListenerTaskEnd}

import java.util.{Timer, TimerTask}

class InProgressTask(sender: NexlaInformationSender, ds: DataSource, runId: Long) extends TimerTask {
  override def run(): Unit = {
    sender.sendFlowInsight(GeneralUtils.prepareLogSourceInfo(ds, runId, "Data processing in progress ..."))
  }
}

class NexlaMetricsListener(adminApiClient: AdminApiClient, sender: NexlaInformationSender, sourceId: Int, sinkId: Int,
                           runId: Long) extends SparkListener with LazyLogging {

  val src: DataSource = adminApiClient.getDataSource(sourceId).get()
  val detectedDataset: DataSet = adminApiClient.getDataSet(src.getDatasets.get(0).getId).get()
  val sink: DataSink = adminApiClient.getDataSink(sinkId).get()
  val t: Timer = new Timer
  private val thirtySeconds: Long = 30000L

  override def onApplicationStart(applicationStart: SparkListenerApplicationStart): Unit = {
    sender.sendFlowInsight(GeneralUtils.prepareLogSourceInfo(src, runId, "Initialized Nexla Spark metrics listener, starting job execution"))
    t.scheduleAtFixedRate(new InProgressTask(sender, src, runId), thirtySeconds, thirtySeconds)
  }

  override def onApplicationEnd(applicationEnd: SparkListenerApplicationEnd): Unit = {
    sender.sendFlowInsight(GeneralUtils.prepareLogSinkInfo(sink, runId, "Application finished successfully, shutting down the metrics listener."))
    t.cancel()
  }

  override def onTaskEnd(taskEnd: SparkListenerTaskEnd): Unit = {
    val inputMetrics = taskEnd.taskMetrics.inputMetrics
    val outputMetrics = taskEnd.taskMetrics.outputMetrics
    val error: Boolean = taskEnd.taskInfo.killed || taskEnd.taskInfo.failed
    val taskLogTag: String = s"${taskEnd.taskInfo.taskId}@${taskEnd.taskInfo.host}--stage[${taskEnd.stageId}], attempt ${taskEnd.taskInfo.attemptNumber}"
    if (error) {
      sender.send(GeneralUtils.prepareErrorMetricsForSource(src, inputMetrics.recordsRead, inputMetrics.bytesRead, runId))
      sender.send(GeneralUtils.prepareErrorMetricsForDataset(detectedDataset, inputMetrics.recordsRead, inputMetrics.bytesRead, runId))
      sender.send(GeneralUtils.prepareErrorMetricsForSink(sink, outputMetrics.recordsWritten, runId))
      if (inputMetrics.bytesRead != 0 || inputMetrics.recordsRead != 0) {
        sender.sendFlowInsight(GeneralUtils.prepareLogSourceError(src, runId, s"Source task ${taskLogTag} failed, reason: [${taskEnd.reason}]. See executor logs for the exception and root cause."))
      } else {
        sender.sendFlowInsight(GeneralUtils.prepareLogSinkError(sink, runId, s"Sink task ${taskLogTag} failed, reason: [${taskEnd.reason}]. See executor logs for the exception and root cause."))
      }
    } else {
      sender.send(GeneralUtils.prepareMetricsForSource(src, inputMetrics.recordsRead, inputMetrics.bytesRead, runId))
      sender.send(GeneralUtils.prepareMetricsForDataset(detectedDataset, inputMetrics.recordsRead, inputMetrics.bytesRead, runId))
      sender.send(GeneralUtils.prepareMetricsForSink(sink, outputMetrics.recordsWritten, outputMetrics.bytesWritten, runId))
    }

    if (inputMetrics.bytesRead != 0 || inputMetrics.recordsRead != 0) {
      // source
      sender.sendFlowInsight(GeneralUtils.prepareLogSourceInfo(src, runId, s"Task ${taskLogTag} finished, failed/killed: $error, metrics: [${inputMetrics.toReadableString}]"))
    } else {
      sender.sendFlowInsight(GeneralUtils.prepareLogSinkInfo(sink, runId, s"Task ${taskLogTag} finished, failed/killed: $error, metrics: [${outputMetrics.toReadableString}]"))
    }

    super.onTaskEnd(taskEnd)
  }

  implicit class InputMetricWrapper(original: InputMetrics) {
    def toReadableString: String = {
      s"bytes read: [${original.bytesRead}], records read: [${original.recordsRead}]"
    }
  }
  implicit class OutputMetricWrapper(original: OutputMetrics) {
    def toReadableString: String = {
      s"bytes written: [${original.bytesWritten}], records written: [${original.recordsWritten}]"
    }
  }
}
