package com.nexla.spark_agent.handlers

import com.nexla.admin.client.DataSource
import com.nexla.common.{MinimalNexlaFile, NexlaConstants, NexlaDataCredentials}
import com.nexla.sc.api.helper.NexlaSource
import com.nexla.spark_agent.batch.BatchRunner
import com.nexla.spark_agent.infra.{SecretClient, SparkAgentCfg}
import com.nexla.spark_agent.metrics.sender.NexlaInformationSender
import com.nexla.spark_agent.tools.{PrefixAwareLogging, SparkListingClient}
import com.nexla.spark_agent.utils.{Databricks, GeneralUtils}
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.spark.sql.SparkSession
import spray.json._
import DefaultJsonProtocol._

import java.net.URI
import java.util.{Base64, UUID}
import scala.collection.immutable
import scala.jdk.CollectionConverters.mapAsScalaMapConverter

sealed trait SourceType
case object Hive extends SourceType
case object NonHive extends SourceType
case object DatabricksSource extends SourceType

class SourceHandler(secretClient: SecretClient, runId: Long, a: NexlaSource, nx: NexlaInformationSender,
                    sourceType: SourceType, listingClient: SparkListingClient, cfg: SparkAgentCfg) extends PrefixAwareLogging {

  private val globalLogPrefix = s"[src-${a.id()}-source]"
  var errors = 0

  def finishBatch(): Unit = {
    if (this.errors != 0) {
      nexlaFiles.foreach(f => listingClient.markAsFailed(f, a.id()))
    } else {
      nexlaFiles.foreach(f => listingClient.markAsDone(f, a.id()))
    }
  }

  def markErrors(): Unit = {
    val msg = "Errors during the processing detected"
    logger.warn(msg)
    nx.sendFlowInsight(GeneralUtils.prepareLogSourceError(a.dataSource, runId, msg))
    this.errors = 1
  }

  private val nexlaFiles: immutable.Seq[MinimalNexlaFile] = if (sourceType.equals(NonHive)) {
    val msg = "Per-file Spark pipeline detected, contacting Nexla Backend for the list of files to process."
    logger.info(msg)
    nx.sendFlowInsight(GeneralUtils.prepareLogSourceInfo(a.dataSource, runId, msg))
    val filesInternal = listingClient.takeFiles(a.id(), cfg.partitionPath.toInt)
    val fileSequence = filesInternal.map(_.getFullPath)
    if (fileSequence.isEmpty) {
      val msg = "No files to process, finishing"
      logger.warn(msg)
      nx.sendFlowInsight(GeneralUtils.prepareLogSourceInfo(a.dataSource, runId, msg))
    }
    logger.info(s"will process following files: [${fileSequence.mkString(",")}]")
    filesInternal
  } else if (sourceType.equals(Hive)) {
    val msg = "Apache Hive-compatible Spark pipeline detected, processing the requested partitions"
    logger.info(msg)
    nx.sendFlowInsight(GeneralUtils.prepareLogSourceInfo(a.dataSource, runId, msg))
    cfg.partitionPath.split(",").map {
      idAndPartition =>
        val splitPoint = idAndPartition.indexOf(":")
        val id = idAndPartition.substring(0, splitPoint).toLong
        val path = idAndPartition.substring(splitPoint + 1)
        val file = new MinimalNexlaFile()
        file.setId(id)
        file.setFullPath(path)
        file
    }.toList
  } else {
    List.empty
  }

  def handleDatabricks(spark: SparkSession, debugEnabled: Boolean, batchRunner: BatchRunner): Boolean = {
    val src = a.dataSource
    val srcCfg = src.getSourceConfig.asScala.toMap
    val table = srcCfg("table").toString

    val srcCredsDecrypted = secretClient.getNexlaSrcCredentials(src.getId).parseJson.convertTo[Map[String, String]]

    if (unityEnabled(src, srcCredsDecrypted) && GeneralUtils.detectCloud().equals(Databricks)) {
      batchRunner.unityCatalogEnabled.set(true)
      val schemaId = srcCfg.get("database").orElse(Option(s"flow_${src.getFlowNodeId}")).map(_.toString).get
      UnityCatalogHandler.schema.set(schemaId)
      val catalogId = srcCfg.get(NexlaConstants.UNITY_CATALOG_NAME).orElse(srcCfg.get("catalog")).map(_.toString).get
      UnityCatalogHandler.catalog.set(catalogId)
      logger.info(s"Schema set to $schemaId and catalog set to $catalogId")
      val sourceDataframe = spark.sql(s"select * from $catalogId.$schemaId.$table")
      if (debugEnabled) {
        sourceDataframe.show(10)
      }
      batchRunner.registerDatasetSource(Option(src), sourceDataframe, s"src_${a.id()}", Option.empty)
      true
    } else {
      throw new IllegalArgumentException("Running Databricks in Local mode using JDBC bridge is not supported as of now")
//
//      val srcCredsDecrypted = new NexlaDataCredentials(
//        secretClient.getNexlaDecryptKeyFromSecrets,
//        src.getDataCredentials.getCredentialsEnc,
//        src.getDataCredentials.getCredentialsEncIv).decrypt()
//      val database = cfg.get("database").map(_.toString).getOrElse("")
//      val url = srcCredsDecrypted.get("url").replaceAll("jdbc:spark", "jdbc:databricks")
//      // Replace these with your Databricks Warehouse details
//
//      val driverClass = "com.databricks.client.jdbc.Driver"
//      val df = spark.read
//        .format("jdbc")
//        .option("url", url)
//        .option("driver", driverClass)
//        .option("dbtable", s"$database.$table")
//        .load()
//
//      // You can now work with the DataFrame 'df'
//      df.show()
    }
  }

  def unityEnabled(src: DataSource, srcCreds: Map[String, String]): Boolean = {
    GeneralUtils.unityCatalogEnabled(src) || GeneralUtils.unityCatalogEnabledCredentials(srcCreds)
  }

  def turnOnUnityCatalogIfNecessary(src: DataSource, batchRunner: BatchRunner): Unit = {
    val srcCredsDecrypted = secretClient.getNexlaSrcCredentials(src.getId).parseJson.convertTo[Map[String, String]]

    if (unityEnabled(src, srcCredsDecrypted) && GeneralUtils.detectCloud().equals(Databricks)) {
      logger.info("Source got Unity Catalog enabled + Running on Databricks env. Enabling it for the current flow")
      batchRunner.unityCatalogEnabled.set(true)
      val srcCfg = src.getSourceConfig.asScala
      val schemaId = srcCfg.get("database").orElse(Option(s"flow_${src.getFlowNodeId}")).map(_.toString).get
      UnityCatalogHandler.schema.set(schemaId)
      val catalogId = srcCfg.get(NexlaConstants.UNITY_CATALOG_NAME).orElse(srcCfg.get("catalog")).map(_.toString).get
      UnityCatalogHandler.catalog.set(catalogId)
      logger.info(s"Schema set to $schemaId and catalog set to $catalogId")
      if (GeneralUtils.unityCatalogSamplingForAllEnabled(src)) {
        logger.info(s"Additional Unity Catalog sampling enabled. " +
          s"All the entities of this flow will have ${UnityCatalogHandler.SAMPLE_SIZE} rows sent to Unity Catalog.")
        batchRunner.intermediateUnityEnabled.set(true)
      }
    }
  }

  //noinspection ScalaStyle
  def handle(spark: SparkSession, debugEnabled: Boolean, batchRunner: BatchRunner, noHive: Boolean): Boolean = {
    val src = a.dataSource
    turnOnUnityCatalogIfNecessary(src, batchRunner)

    sourceType match {
      case DatabricksSource =>
        handleDatabricks(spark, debugEnabled, batchRunner)
      case _ =>
        if (nexlaFiles.isEmpty) {
          return false
        }
        val actualPaths = GeneralUtils.preparePaths(src, nexlaFiles, noHive)
        nx.sendFlowInsight(GeneralUtils.prepareLogSourceInfo(a.dataSource, runId, s"Starting detection of file format for source [${src.getId}]"))
        val sparkFormat: String = detectFileFormat(spark, actualPaths)
        nx.sendFlowInsight(GeneralUtils.prepareLogSourceInfo(a.dataSource, runId, s"Finished detection of file format for source [${src.getId}]"))

        val dataframeBaseReader = spark.read
        val dataframeReaderWithOptions = sparkFormat match {
          case pqt if pqt.toLowerCase().contains("parquet") =>
            dataframeBaseReader
              .option("mergeSchema", "true").option("inferSchema", "true").format("parquet")
          case csvOrTsv if (csvOrTsv.toLowerCase().contains("csv") || csvOrTsv.toLowerCase.contains("tsv")) =>
            val actualDelimiter = if (csvOrTsv.equalsIgnoreCase("csv")) "," else "\t"
            dataframeBaseReader
              .format("csv")
              .option("delimiter", src.getSourceConfig.getOrDefault("csv.delimiter", actualDelimiter).toString)
              .option("quote", src.getSourceConfig.getOrDefault("csv.quote.char", "\"").toString)
              .option("header", "true").option("inferSchema", "true")
          case json if json.toLowerCase().contains("json") =>
            dataframeBaseReader.format("json")
          case orc if orc.toLowerCase.contains("orc") =>
            dataframeBaseReader
              .option("mergeSchema", "true").option("inferSchema", "true").format("orc")
        }
        val sourceDataframeWithPossiblySpaceColumns = dataframeReaderWithOptions.load(actualPaths :_*)
        val trimmedColumnNames = sourceDataframeWithPossiblySpaceColumns.columns.toSeq.map(_.trim)
        val sourceDataframe = sourceDataframeWithPossiblySpaceColumns.toDF(trimmedColumnNames: _*)
        val suggestedAliasName = s"src_${src.getId}"
        val msg = s"Registered dataframe with alias [$suggestedAliasName] as a data source in the batch processing context"
        nx.sendFlowInsight(GeneralUtils.prepareLogSourceInfo(a.dataSource, runId, msg))
        if (debugEnabled) {
          val logTag = UUID.randomUUID().toString
          info(s"Showing intermediate dataframe in source, alias [$suggestedAliasName]", prefix = s"[$logTag]")
          sourceDataframe.take(5).foreach(elem => info(s"$elem", prefix = s"[$logTag]"))
        }
        batchRunner.registerDatasetSource(Option(src), sourceDataframe, suggestedAliasName, Option(actualPaths.head))
        true
    }
  }

  private def detectFileFormat(spark: SparkSession, partitionPath: Seq[String]): String = {
    val fs: FileSystem = FileSystem.get(spark.sparkContext.hadoopConfiguration)
    info("Filesystem created", globalLogPrefix)
    val dirPaths = FileSystem.get(URI.create(partitionPath.head), fs.getConf).listStatus(new Path(partitionPath.head))
    info("Directory paths ready", globalLogPrefix)
    val firstNonEmptyFileName = dirPaths.filter(_.getLen > 0).map(_.getPath.getName).head
    info(s"First non empty file name found: $firstNonEmptyFileName", globalLogPrefix)
    val extension = getExtension(firstNonEmptyFileName)
    info(s"Source extension detected: $extension", globalLogPrefix)
    extension
  }

  private def getExtension(filename: String): String = {
    val afterLastSlash = filename.substring(filename.lastIndexOf('/') + 1)
    val afterLastBackslash = afterLastSlash.lastIndexOf('\\') + 1
    val dotIndex = afterLastSlash.indexOf('.', afterLastBackslash)
    if (dotIndex == -1) {
      ""
    } else {
      afterLastSlash.substring(dotIndex + 1)
    }
  }
}
