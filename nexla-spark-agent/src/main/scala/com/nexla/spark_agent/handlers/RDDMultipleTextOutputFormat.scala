package com.nexla.spark_agent.handlers

import org.apache.hadoop.conf.Configuration
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.hadoop.mapred.FileOutputFormat.getOutputPath
import org.apache.hadoop.mapred.lib.MultipleTextOutputFormat
import org.apache.hadoop.mapred.{InvalidJobConfException, JobConf, RecordWriter, Reporter}
import org.apache.hadoop.mapreduce.security.TokenCache
import org.apache.hadoop.util.Progressable

import java.util

class RDDMultipleTextOutputFormat[K, V] extends MultipleTextOutputFormat[K, V] {

  // todo pass this from the outside spark job and extension as well
  private var fixedPart: String = sys.env.getOrElse("fixedPart", "")

  override def generateFileNameForKeyValue(key: K, value: V, name: String): String = {
    s"store-${key.toString}" + ".csv"
  }

  /** The following 4 functions are only for visibility purposes
   * (they are used in the class MyRecordWriter) * */
  override def generateLeafFileName(name: String): String = super.generateLeafFileName(name)

  override def generateActualValue(key: K, value: V): V = super.generateActualValue(key, value)

  override def getInputFileBasedOutputFileName(job: JobConf, name: String): String = super.getInputFileBasedOutputFileName(job, name)

  override def getBaseRecordWriter(fs: FileSystem, job: JobConf, name: String, arg3: Progressable): RecordWriter[K, V] = super.getBaseRecordWriter(fs, job, name, arg3)

  override def getRecordWriter(fs: FileSystem, job: JobConf, name: String, arg3: Progressable): RecordWriter[K, V] = {
    val myName = this.generateLeafFileName(name)
    new MyRecordWriter[K, V](this, fs, job, arg3, myName)
  }

  override def checkOutputSpecs(ignored: FileSystem, job: JobConf): Unit = {
    // Ensure that the output directory is set and not already there
    val outDir: Path = getOutputPath(job);
    if (outDir == null) {
      throw new InvalidJobConfException("Output directory not set.");
    }

    val paths = new Array[Path](1)
    paths(0) = outDir
    // get delegation token for outDir's file system
    TokenCache.obtainTokensForNamenodes(job.getCredentials(), paths, job.asInstanceOf[Configuration])

    if (outDir.getFileSystem(job.asInstanceOf[Configuration]).exists(outDir)) {
      /*throw new FileAlreadyExistsException*/ println("Output directory " + outDir + " already exists");
    }
  }
}

class MyRecordWriter[K, V](var rddMultipleTextOutputFormat: RDDMultipleTextOutputFormat[K, V],
                           fs: FileSystem,
                           job: JobConf,
                           arg3: Progressable, private var myName: String) extends RecordWriter[K, V] {
  val recordWriters = new util.TreeMap[String, RecordWriter[K, V]]()

  def write(key: K, value: V): Unit = {
    val keyBasedPath = rddMultipleTextOutputFormat.generateFileNameForKeyValue(key, value, myName)
    val finalPath = rddMultipleTextOutputFormat.getInputFileBasedOutputFileName(job, keyBasedPath)
    val actualValue = rddMultipleTextOutputFormat.generateActualValue(key, value)
    var rw = this.recordWriters.get(finalPath)
    if (rw == null) {
      rw = rddMultipleTextOutputFormat.getBaseRecordWriter(fs, job, finalPath, arg3)
      this.recordWriters.put(finalPath, rw)
    }
    val outputRow = actualValue.asInstanceOf[org.apache.spark.sql.Row]
    // todo escape? specific output format for data types?..
    rw.write(null.asInstanceOf[K], outputRow.mkString(",").asInstanceOf[V])
  }

  def close(reporter: Reporter): Unit = {
    val keys = this.recordWriters.keySet.iterator
    while (keys.hasNext) {
      val rw = this.recordWriters.get(keys.next).asInstanceOf[RecordWriter[K, V]]
      rw.close(reporter)
    }
    this.recordWriters.clear()
  }
}
