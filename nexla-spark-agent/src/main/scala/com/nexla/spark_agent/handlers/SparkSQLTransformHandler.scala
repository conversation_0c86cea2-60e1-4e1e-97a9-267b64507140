package com.nexla.spark_agent.handlers

import com.nexla.admin.client.DataSet
import com.nexla.sc.api.helper.{AdminApi<PERSON><PERSON>per, SparkTransform}
import com.nexla.spark_agent.batch.BatchRunner
import com.nexla.spark_agent.infra.NexlaSparkSchemaConverter
import com.nexla.spark_agent.metrics.sender.NexlaInformationSender
import com.nexla.spark_agent.utils.GeneralUtils
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{Row, SparkSession}

class SparkSQLTransformHandler(adminApi: AdminApiHelper, batchRunner: BatchRunner) extends LazyLogging {

  private def handleInternal(sender: NexlaInformationSender, spark: SparkSession, sp: SparkTransform, runId: Long): Unit = {
    val ds = adminApi.client().getDataSet(sp.datasetId).get()
    sender.sendFlowInsight(GeneralUtils.prepareLogNexsetInfo(ds.getOrg.getId, ds.getId, runId, s"Handling Spark SQL transform below, Dataset Id [${sp.datasetId}], code below ${sp.toString}"))
    val parsedSparkCode = sp.actualSqlCode
    // after handling all the pre-reqs, let's get rid of the placeholder markers and move on to the final SQL
    val sparkSQL = parsedSparkCode.mkString(" ").replaceAll("\\$", "").split(";")
    logger.info(s"this spark transform has [${sparkSQL.size}] statements inside. running each of them")
    val regex = """.*[vV][iI][eE][wW] ([a-zA-Z0-9_]+) [aA][sS].*""".r
    for (statement <- sparkSQL) {
      val finalSql = if (statement.endsWith(";")) {
        statement
      } else {
        s"$statement;"
      }

      finalSql match {
        case regex(viewName) =>
          sender.sendFlowInsight(GeneralUtils.prepareLogNexsetInfo(ds.getOrg.getId, ds.getId, runId, "This statement is registering view, registering dataset source in Spark Context."))
          spark.sql(finalSql)
          batchRunner.registerDatasetSource(Option.empty, null, viewName, None)
        case _ =>
          // not a view, but another SQL
          if (finalSql.toLowerCase.contains("select ")) {
            val processedSql = GeneralUtils.sparkify(finalSql, sp, ds, adminApi, batchRunner.unityCatalogEnabled.get())
            logger.info(s"SQL: $processedSql")
            val df = spark.sql(processedSql)
            batchRunner.registerDatasetSource(Option.empty, df, s"nexset_${ds.getId}", None)

            val df2 = spark.sql(processedSql)
            val bytes = org.apache.spark.util.SizeEstimator.estimate(df2)
            val rows = df2.count()
            sender.send(GeneralUtils.prepareMetricsForDataset(ds, rows, bytes, runId))
            batchRunner.setSuccessBytesReported(ds.getId, bytes)
            batchRunner.setSuccessRowsReported(ds.getId, rows)
            batchRunner.setErrorBytesReported(ds.getId, 0)
            batchRunner.setErrorRowsReported(ds.getId, 0)
          }
      }
      sender.sendFlowInsight(GeneralUtils.prepareLogNexsetInfo(ds.getOrg.getId, ds.getId, runId, s"Successfully handled Spark SQL Transform id [${ds.getId}]."))
    }
  }

  def handleSparkTx(sender: NexlaInformationSender, spark: SparkSession, sp: SparkTransform, runId: Long): Unit = {
    try {
      handleInternal(sender, spark, sp, runId)
    } catch {
      case e: Exception =>
        // report all metrics as error from previous dataset
        val thisDs = adminApi.client().getDataSet(sp.datasetId).get()
        val prevDatasetId = thisDs.getParentDatasets.get(0).getId
        val allRowsFromPrevDataset = batchRunner.getAllRowsReported(prevDatasetId)
        val allBytesFromPrevDataset = batchRunner.getAllBytesReported(prevDatasetId)
        sender.send(GeneralUtils.prepareErrorMetricsForDataset(thisDs, allRowsFromPrevDataset, allBytesFromPrevDataset, runId))
    }
  }
}
