package com.nexla.spark_agent.handlers

import com.nexla.admin.client.{DataSet, DataSource}
import com.nexla.sc.api.helper.{AdminApiHelper, NexlaTransform}
import com.nexla.spark_agent.batch.BatchRunner
import com.nexla.spark_agent.infra._
import com.nexla.spark_agent.metrics.sender.NexlaInformationSender
import com.nexla.spark_agent.tools.PrefixAwareLogging
import com.nexla.spark_agent.utils.GeneralUtils
import org.apache.spark.rdd.RDD
import org.apache.spark.sql.{Row, SparkSession}
import org.apache.spark.util.LongAccumulator

import java.util.UUID
import scala.collection.mutable.ArrayBuffer
import scala.language.implicitConversions

class NexlaTransformationHandler(adminApiHelper: AdminApiHelper, nxTransform: NexlaTransform,
                                 debugEnabled: Boolean, noHive: <PERSON><PERSON><PERSON>, cfg: SparkAgentCfg) extends PrefixAwareLogging {

  private val schemaConverter: NexlaSparkSchemaConverter = new NexlaSparkSchemaConverter
  private val globalLogPrefix = s"[nx-${nxTransform.id()}-tx]"
  val ds: DataSet = adminApiHelper.client().getDataSet(nxTransform.datasetId).get()
  private val prevDataset = ds.getParentDatasets.get(0)
  val parentSource: DataSource = adminApiHelper.getParentSource(nxTransform.datasetId)

  def handle(runId: Long, spark: SparkSession, nx: NexlaInformationSender, batchRunner: BatchRunner): Unit = {
    val msg = s"Handling Nexla transform id [${nxTransform.id()}] using Nexla Wrapped Transformer."
    nx.sendFlowInsight(GeneralUtils.prepareLogNexsetInfo(ds.getOrg.getId, ds.getId, runId, msg))

    val inputAlias = if (prevDataset.getDescription.toLowerCase.contains("detected from"))
      s"src_${parentSource.getId}" else s"nexset_${prevDataset.getId}" // XXX: dirty hack

    val sourceDf = spark.sql(s"SELECT * FROM $inputAlias")
    val targetSchemaInSpark = schemaConverter.convert(ds.getOutputSchema)

    info(s"Broadcasting spec: [${nxTransform.id()} === ${nxTransform.code}]", globalLogPrefix)
    var schemaBc = spark.sparkContext.broadcast[Object](nxTransform.code)
    schemaBc.unpersist() // prevent spec reusing
    schemaBc = spark.sparkContext.broadcast[Object](nxTransform.code)
    info("Broadcasting spec -- SUCCESSFUL, cached transformer instance cleaned up", globalLogPrefix)

    val errorNumAccum: LongAccumulator = spark.sparkContext.longAccumulator(s"errorRowsForTx-${nxTransform.datasetId}")
    val schemaSizeNoMatchAccum: LongAccumulator = spark.sparkContext.longAccumulator(s"schemaErrorAccum-${nxTransform.datasetId}")
    val errorBytesAccum: LongAccumulator = spark.sparkContext.longAccumulator(s"byteErrorAccum-${nxTransform.datasetId}")
    val successfulEntriesAccum: LongAccumulator = spark.sparkContext.longAccumulator(s"successfulEntriesAccum-${nxTransform.datasetId}")
    val successfulBytesAccum: LongAccumulator = spark.sparkContext.longAccumulator(s"successfulBytesAccum-${nxTransform.datasetId}")

    // executor RDD code
    val newDf: RDD[Row] = sourceDf.rdd.flatMap { row =>
      val rowLhm: java.util.LinkedHashMap[String, Object] = new java.util.LinkedHashMap[String, Object]()
      val fieldNames = row.schema.fieldNames.toSeq
      fieldNames.foreach { name =>
        val columnValueObject = row.getAs[Object](name)
        rowLhm.put(name, columnValueObject)
      }
      TransformerConfig.spec = schemaBc.value
      val rx = NexlaTransformSingletonWrapper.processLhm(rowLhm, schemaBc.value.hashCode())
      val rows: ArrayBuffer[Row] = ArrayBuffer[Row]()
      val schema = row.schema
      val size = if (schema != null) {
        row.json.getBytes.length
      } else {
        0L
      }

      if (rx.equals(NexlaTransformSingletonWrapper.errorMap)) {
        errorNumAccum.add(1L)
        errorBytesAccum.add(size)
      } else {
        TxScUtil.handleMap1(rx, rows)
        successfulEntriesAccum.add(1L)
        successfulBytesAccum.add(size)
      }
      rows
    }

    newDf.sparkContext.runJob(newDf, (iter: Iterator[Row]) => {
      while (iter.hasNext) iter.next()
    })
    // finish executor RDD code

    info(s"Total errors: ${errorNumAccum.value}", globalLogPrefix)
    if (debugEnabled) {
      val logTag = globalLogPrefix + "-" + UUID.randomUUID().toString
      info(s"RDD schema, $targetSchemaInSpark", prefix = s"[$logTag]")
      info(s"Showing intermediate RDD before converting it to output Nexla Spark schema", prefix = s"[$logTag]")
      newDf.take(5).foreach(elem => info(s"$elem", prefix = s"[$logTag]"))
    }

    val goodSchemaRdd: RDD[Row] = newDf.map { row =>
      val schema = row.schema
      val size = if (schema != null) {
        row.json.getBytes.length
      } else {
        0L
      }

      if (row.size.equals(targetSchemaInSpark.size)) {
        successfulEntriesAccum.add(1L)
        successfulBytesAccum.add(size)
        Option(row)
      } else {
        schemaSizeNoMatchAccum.add(1L)
        errorBytesAccum.add(size)
        None
      }
    }.filter(_.isDefined).map(_.get)

    goodSchemaRdd.sparkContext.runJob(goodSchemaRdd, (iter: Iterator[Row]) => {
      while (iter.hasNext) iter.next()
    })
    // end stage 2 executor RDD code - filtering

    info(s"Total errors [schema-related]: ${schemaSizeNoMatchAccum.value}", globalLogPrefix)
    val df = spark.createDataFrame(goodSchemaRdd, targetSchemaInSpark)

    if (debugEnabled) {
      val logTag = globalLogPrefix + "-" + UUID.randomUUID().toString
      info(s"DF schema, ${df.schema}", prefix = s"[$logTag]")
      info(s"Showing intermediate dataframe - after executor code, but before partition enrichment", prefix = s"[$logTag]")
      df.take(5).foreach(elem => info(s"$elem", prefix =s"[$logTag]"))
    }

    val dfB = if (noHive) {
      batchRunner.enrichWithPartitionColumns(Option(parentSource), df, None)
    } else {
      batchRunner.enrichWithPartitionColumns(Option(parentSource), df, Option(cfg.partitionPath))
    }

    if (debugEnabled) {
      val logTag = globalLogPrefix + "-" + UUID.randomUUID().toString
      info(s"DF schema, ${dfB.schema}", prefix = s"[$logTag]")
      info(s"Showing intermediate dataframe - after executor code, after partition enrichment", prefix = s"[$logTag]")
      dfB.take(5).foreach(elem => info(s"$elem", prefix = s"[$logTag]"))
    }

    val msg2 = s"Successfully finished transform id [${ds.getId}], registering dataset."
    nx.sendFlowInsight(GeneralUtils.prepareLogNexsetInfo(ds.getOrg.getId, ds.getId, runId, msg2))

    val totalSuccessBytes = successfulBytesAccum.sum
    val totalSuccess = successfulEntriesAccum.sum
    val totalErrors = errorNumAccum.sum + schemaSizeNoMatchAccum.sum
    val totalErrorBytes = errorBytesAccum.sum
    logger.info("Sending transform metrics: total success [{}], total errors [{}]", totalSuccess, totalErrors)
    nx.send(GeneralUtils.prepareMetricsForDataset(ds, totalSuccess, totalSuccessBytes, runId))
    nx.send(GeneralUtils.prepareErrorMetricsForDataset(ds, totalErrors, totalErrorBytes, runId))
    batchRunner.setSuccessBytesReported(ds.getId, totalSuccessBytes)
    batchRunner.setSuccessRowsReported(ds.getId, totalSuccess)
    batchRunner.setErrorBytesReported(ds.getId, totalErrorBytes)
    batchRunner.setErrorRowsReported(ds.getId, totalErrors)

    val targetAlias = s"nexset_${ds.getId}"
    batchRunner.registerDatasetSource(Option.empty, dfB, targetAlias, None)
  }

}
