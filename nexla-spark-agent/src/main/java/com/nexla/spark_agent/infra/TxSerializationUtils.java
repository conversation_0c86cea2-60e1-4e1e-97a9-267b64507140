package com.nexla.spark_agent.infra;

import com.nexla.admin.client.NexlaSchema;
import com.nexla.transform.Transformer;
import com.nexla.transform.TransformerResult;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.RowFactory;
import scala.collection.JavaConverters;
import scala.collection.mutable.ArrayBuffer;

import java.util.*;

/**
 * Utility to make the transformation serializable
 */
public class TxSerializationUtils {
    private TxSerializationUtils() {}

    public static scala.collection.mutable.Buffer<Row> joltTransform(Object specJson, NexlaSchema nexlaSchema, Row input) {
        Transformer tx = new Transformer(specJson);
        Map<String, Object> sparkRow = convertToJavaMap(input);
        List<TransformerResult> actual = tx.transform(sparkRow, nexlaSchema);
        return convertBackToRows(actual, nexlaSchema);
    }

    public static Map<String, Object> convertToJavaMap(Row input) {
        List<String> fieldNames = Arrays.asList(input.schema().fieldNames());
        scala.collection.immutable.Map<String, Object> scalaRes = input.getValuesMap(JavaConverters.collectionAsScalaIterable(fieldNames).toSeq());

        return new HashMap<>(JavaConverters.mapAsJavaMap(scalaRes));
    }

    private static scala.collection.mutable.Buffer<Row> convertBackToRows(List<TransformerResult> actual, NexlaSchema ns) {
        List<Row> rows = new ArrayList<>();

        for (TransformerResult r : actual) {
            Object result = r.getOutput();
            if (result instanceof ArrayList) {
                // several rows?
                ArrayList<TransformerResult> severalRows = (ArrayList<TransformerResult>) result;
                for (TransformerResult singleRow : severalRows) {
                    LinkedHashMap<String, Object> out = (LinkedHashMap<String, Object>) singleRow.getOutput();
                    handleMap(out, rows, ns);
                }
            } else if (result instanceof LinkedHashMap) {
                LinkedHashMap<String, Object> row = (LinkedHashMap<String, Object>) r.getOutput();
                handleMap(row, rows, ns);
            } else {
                // skip row
            }
        }

        return JavaConverters.asScalaBuffer(rows);
    }

    public static void handleMap(Map<String, Object> row, List<Row> rows, NexlaSchema ns) {
        Object[] rawRow = row.values().toArray();
        Object[] fixedRow = new Object[row.values().size()];

        // we have X values in this "row", but X+Y properties in Nexla schema, since it was enriched with the partition columns.
        // the ordering is the same, thanks to LinkedHashMap, so we can just cut off the partition columns from here.
        for (int i = 0; i < row.values().size(); i++) {
            Object colValue = rawRow[i];
            fixedRow[i] = String.valueOf(colValue);
        }

        Row a = RowFactory.create(fixedRow);
        rows.add(a);
    }

    public static void handleMap1(Map<String, Object> row, ArrayBuffer<Row> rows) {
        Object[] rawRow = row.values().toArray();
        Object[] fixedRow = new Object[row.values().size()];

        // we have X values in this "row", but X+Y properties in Nexla schema, since it was enriched with the partition columns.
        // the ordering is the same, thanks to LinkedHashMap, so we can just cut off the partition columns from here.
        for (int i = 0; i < row.values().size(); i++) {
            Object colValue = rawRow[i];
            fixedRow[i] = String.valueOf(colValue);
        }

        Row a = RowFactory.create(fixedRow);
        rows.$plus$eq(a);
    }
}
