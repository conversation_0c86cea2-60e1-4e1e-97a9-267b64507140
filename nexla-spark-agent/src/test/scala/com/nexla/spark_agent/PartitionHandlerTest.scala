package com.nexla.spark_agent

import com.bazaarvoice.jolt.JsonUtils
import com.nexla.admin.client.{AdminApiClient, DataSink}
import com.nexla.spark_agent.batch.BatchRunner
import org.mockito.Mockito
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.verbs.ShouldVerb
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

import java.time.{LocalDate, LocalDateTime}
import scala.collection.JavaConverters._

class PartitionHandlerTest extends AnyFunSuite with ShouldVerb {

  val instance = new BatchRunner(Mockito.mock(classOf[AdminApiClient]))

  //@Test FIXME Deactivating failed tests in release/v3.2.0
  ignore("should not treat path sections as partitions") {
    val cfg1 = Map[String, AnyRef](
      "path" -> "qa-us-west-2.nexla.com/07-10-2023",
      "output.dir.name.pattern" -> "akotliar/none/"
    ).asJava

    val (fixed, parts) = instance.getPartitionColumns(cfg1)
    assert(parts.isEmpty)
    fixed shouldEqual "qa-us-west-2.nexla.com/07-10-2023/akotliar/none/"
  }

  //@Test FIXME Deactivating failed tests in release/v3.2.0
  ignore("should properly extract partitions") {
    val cfg1 = Map[String, AnyRef](
      "path" -> "qa-us-west-2.nexla.com/07-10-2023",
      "output.dir.name.pattern" -> "{yyyy}/{record.arry}/"
    ).asJava

    val (fixed, parts) = instance.getPartitionColumns(cfg1)

    val currentYear = LocalDate.now().getYear
    fixed shouldEqual s"qa-us-west-2.nexla.com/07-10-2023/${currentYear.toString}/"
    parts shouldEqual Seq("arry")
  }

  //@Test FIXME Deactivating failed tests in release/v3.2.0
  ignore("should properly handle mixed cases") {

    val cfg1 = Map[String, AnyRef](
      "path" -> "qa-us-west-2.nexla.com/07-10-2023",
      "output.dir.name.pattern" -> "abcde/{yyyy}/{record.arry}/"
    ).asJava

    val (fixed, parts) = instance.getPartitionColumns(cfg1)

    val currentYear = LocalDate.now().getYear.toString
    fixed shouldEqual s"qa-us-west-2.nexla.com/07-10-2023/abcde/$currentYear/"
    parts shouldEqual Seq("arry")
  }

  //@Test FIXME Deactivating failed tests in release/v3.2.0
  ignore("should properly handle mixed cases with per-file partition output") {

    val exampleSinkWithoutCfg = JsonUtils.classpathToType("/exampleSinkCfgMappingNoOrder.json", classOf[DataSink])
    val sinkCfg = exampleSinkWithoutCfg.getSinkConfig

    val (fixed, parts) = instance.getPartitionColumns(sinkCfg)

    val currentYear = LocalDate.now().getYear.toString
    val currentMonth = LocalDate.now().getMonthValue.toString
    val currentDay = LocalDate.now().getDayOfMonth.toString
    val currentHour = LocalDateTime.now().getHour.toString
    fixed shouldEqual s"/penny-officer/nexla/daily_feed/v1/$currentYear$currentMonth$currentDay/$currentHour/"
    parts shouldEqual Seq.empty[String]
  }

}
