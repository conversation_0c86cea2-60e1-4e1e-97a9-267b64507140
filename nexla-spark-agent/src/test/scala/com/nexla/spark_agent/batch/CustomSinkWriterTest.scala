package com.nexla.spark_agent.batch

import com.nexla.spark_agent.handlers.RDDMultipleTextOutputFormat
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SaveMode, SparkSession}
import org.apache.spark.sql.functions.col
import org.scalatest.Ignore
import org.scalatest.funsuite.AnyFunSuite

import java.time.Instant

@com.nexla.test.ScalaIntegrationTests
@Ignore
// FIXME: add fixtures to the repo
class CustomSinkWriterTest extends AnyFunSuite with LazyLogging {

  test("write and compare 2 different spark writers") {
    val spark = SparkSession.builder().master("local[*]").getOrCreate()
    val df1 = spark.read
      .format("parquet")
      .load("/home/<USER>/testdata/p=1/0b17f88b107b458d8e607f0d594257ea_18_columns.gz.parquet")

    // don't write success in out
    spark.sparkContext.hadoopConfiguration.set("mapreduce.fileoutputcommitter.marksuccessfuljobs", "false")
    spark.sparkContext.hadoopConfiguration.set("parquet.enable.summary-metadata", "false")
    spark.sparkContext.hadoopConfiguration.set("spark.hadoop.validateOutputSpecs", "false")

    // and crc
    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val fs = org.apache.hadoop.fs.FileSystem.get(hadoopConf)
    fs.setWriteChecksum(false)

    // usual writer
    df1.write.format("csv").mode(SaveMode.Overwrite).save(s"/tmp/spark-original-${Instant.now().toString}/")

    val pairRdd = df1.repartition(col("column_5_cat"))
      .rdd.map {
      row =>
        val key = row.getAs[String]("column_5_cat")
        (key, row)
    }

    // new writer
    pairRdd
      .saveAsHadoopFile(s"/tmp/spark-my-output-${Instant.now().toString}/",
        classOf[String], classOf[String], classOf[RDDMultipleTextOutputFormat[String, String]])
  }
}
