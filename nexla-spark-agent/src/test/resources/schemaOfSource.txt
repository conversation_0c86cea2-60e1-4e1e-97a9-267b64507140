root
 |-- transactionid: string (nullable = true)
 |-- eventtime: string (nullable = true)
 |-- eventtime_est: string (nullable = true)
 |-- eventtype: string (nullable = true)
 |-- eventname: string (nullable = true)
 |-- networkid: string (nullable = true)
 |-- contentproviderpartnerid: string (nullable = true)
 |-- distributionpartnerid: string (nullable = true)
 |-- sellingpartnerid: string (nullable = true)
 |-- externalreseller: string (nullable = true)
 |-- valuechainrole: string (nullable = true)
 |-- siteid: string (nullable = true)
 |-- sitesectionid: string (nullable = true)
 |-- requestsitecustomid: string (nullable = true)
 |-- seriesid: string (nullable = true)
 |-- videoassetid: string (nullable = true)
 |-- requestvideocustomid: string (nullable = true)
 |-- timepositionclass: string (nullable = true)
 |-- slotindex: string (nullable = true)
 |-- positioninslot: string (nullable = true)
 |-- timeposition: string (nullable = true)
 |-- adunithash: string (nullable = true)
 |-- placementid: string (nullable = true)
 |-- adunitid: string (nullable = true)
 |-- netvalue: string (nullable = true)
 |-- creativeid: string (nullable = true)
 |-- creativerenditionid: string (nullable = true)
 |-- pricemodel: string (nullable = true)
 |-- uniqueidentifier: string (nullable = true)
 |-- customvisitorid: string (nullable = true)
 |-- pageviewrandom: string (nullable = true)
 |-- videoplayrandom: string (nullable = true)
 |-- visitorcountry: string (nullable = true)
 |-- visitordma: string (nullable = true)
 |-- visitorstateprovince: string (nullable = true)
 |-- visitorpostalcode: string (nullable = true)
 |-- visitortimezoneoffset: string (nullable = true)
 |-- parseduseragent: string (nullable = true)
 |-- allrequestkv: string (nullable = true)
 |-- alleventkv: string (nullable = true)
 |-- billableabstracteventid: string (nullable = true)
 |-- triggeringconcreteeventid: string (nullable = true)
 |-- concreteeventid: string (nullable = true)
 |-- scenarioid: string (nullable = true)
 |-- maxads: string (nullable = true)
 |-- maxduration: string (nullable = true)
 |-- timeunfilled: string (nullable = true)
 |-- adsselected: string (nullable = true)
 |-- adduration: string (nullable = true)
 |-- ipaddress: string (nullable = true)
 |-- addeliverymethod: string (nullable = true)
 |-- channelid: string (nullable = true)
 |-- airingid: string (nullable = true)
 |-- breakid: string (nullable = true)
 |-- requestbreakcustomid: string (nullable = true)
 |-- requestairingcustomid: string (nullable = true)
 |-- requestchannelcustomid: string (nullable = true)
 |-- hyldarequest: string (nullable = true)
 |-- referrerurl: string (nullable = true)
 |-- rawuseragent: string (nullable = true)
 |-- recordidentifier: string (nullable = true)
 |-- mrmruleid: string (nullable = true)
 |-- creativeduration: string (nullable = true)
 |-- adunittypeid: string (nullable = true)
 |-- eventmultiplier: string (nullable = true)
 |-- platformbrowserid: string (nullable = true)
 |-- platformosid: string (nullable = true)
 |-- platformdeviceid: string (nullable = true)
 |-- visitorzone: string (nullable = true)
 |-- audienceitem: string (nullable = true)
 |-- cbpid: string (nullable = true)
 |-- label: string (nullable = true)
 |-- marketunifiedadid: string (nullable = true)
 |-- marketbuyerseatid: string (nullable = true)
 |-- marketdealid: string (nullable = true)
 |-- marketadvertiserid: string (nullable = true)
 |-- clocknumber: string (nullable = true)
 |-- visitorcity: string (nullable = true)
 |-- opportunityid: string (nullable = true)
 |-- streamid: string (nullable = true)
 |-- signalid: string (nullable = true)
 |-- lineardecisiontype: string (nullable = true)
 |-- saleschanneltype: string (nullable = true)
 |-- matchedaudienceitem: string (nullable = true)
 |-- progopenexchangeruleid: string (nullable = true)
 |-- soldorderid: string (nullable = true)
 |-- purchasedorderid: string (nullable = true)
 |-- listingid: string (nullable = true)
 |-- contentbrandid: string (nullable = true)
 |-- platformgroup: string (nullable = true)
 |-- givtdecisionresult: string (nullable = true)
 |-- folderdate: long (nullable = true)
 |-- genreid: string (nullable = true)
 |-- languageid: string (nullable = true)
 |-- endpointownerid: string (nullable = true)
 |-- endpointid: string (nullable = true)
 |-- deviceid: string (nullable = true)
 |-- globalbrandid: string (nullable = true)
 |-- placementopportunityid: string (nullable = true)
 |-- streamtype: string (nullable = true)
 |-- tvratingid: string (nullable = true)
 |-- standardenvironmentid: string (nullable = true)
 |-- standardosid: string (nullable = true)
 |-- standarddevicetypeid: string (nullable = true)
 |-- contentduration: string (nullable = true)
 |-- ipenabledaudience: string (nullable = true)
 |-- programmerid: string (nullable = true)
 |-- xifaid: string (nullable = true)
 |-- contentchannelid: string (nullable = true)
 |-- daypartid: string (nullable = true)
 |-- logsampleamplifier: string (nullable = true)
 |-- globaladvertiserid: string (nullable = true)
 |-- creativevariantid: string (nullable = true)
 |-- creativerenditionvariantid: string (nullable = true)
 |-- matchedinventorypackage: string (nullable = true)
 |-- privacyjurisdiction: string (nullable = true)
 |-- privacychoices: string (nullable = true)
 |-- headerbiddingtransactionid: string (nullable = true)
 |-- headerbiddingkey: string (nullable = true)
 |-- headerbiddingvalue: string (nullable = true)
 |-- trimmedtrackingdomainids: string (nullable = true)
 |-- trimmedtrackingdomains: string (nullable = true)