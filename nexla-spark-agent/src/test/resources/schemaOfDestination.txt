root
 |-- addeliverymethod: string (nullable = true)
 |-- adduration: string (nullable = true)
 |-- adsselected: string (nullable = true)
 |-- adunithash: string (nullable = true)
 |-- adunitid: string (nullable = true)
 |-- adunittypeid: string (nullable = true)
 |-- airingid: string (nullable = true)
 |-- alleventkv: string (nullable = true)
 |-- billableabstracteventid: string (nullable = true)
 |-- breakid: string (nullable = true)
 |-- cbpid: string (nullable = true)
 |-- channelid: string (nullable = true)
 |-- clocknumber: string (nullable = true)
 |-- concreteeventid: string (nullable = true)
 |-- contentbrandid: string (nullable = true)
 |-- contentchannelid: string (nullable = true)
 |-- contentduration: string (nullable = true)
 |-- contentproviderpartnerid: string (nullable = true)
 |-- creativeduration: string (nullable = true)
 |-- creativeid: string (nullable = true)
 |-- creativerenditionid: string (nullable = true)
 |-- creativerenditionvariantid: string (nullable = true)
 |-- creativevariantid: string (nullable = true)
 |-- customvisitorid: string (nullable = true)
 |-- daypartid: string (nullable = true)
 |-- deviceid: string (nullable = true)
 |-- distributionpartnerid: string (nullable = true)
 |-- endpointid: string (nullable = true)
 |-- endpointownerid: string (nullable = true)
 |-- eventmultiplier: string (nullable = true)
 |-- eventname: string (nullable = true)
 |-- eventtime: string (nullable = true)
 |-- eventtime_est: string (nullable = true)
 |-- eventtype: string (nullable = true)
 |-- externalreseller: string (nullable = true)
 |-- folderdate: string (nullable = true)
 |-- genreid: string (nullable = true)
 |-- givtdecisionresult: string (nullable = true)
 |-- globaladvertiserid: string (nullable = true)
 |-- globalbrandid: string (nullable = true)
 |-- headerbiddingkey: string (nullable = true)
 |-- headerbiddingtransactionid: string (nullable = true)
 |-- headerbiddingvalue: string (nullable = true)
 |-- hyldarequest: string (nullable = true)
 |-- ipaddress: string (nullable = true)
 |-- ipenabledaudience: string (nullable = true)
 |-- label: string (nullable = true)
 |-- languageid: string (nullable = true)
 |-- lineardecisiontype: string (nullable = true)
 |-- listingid: string (nullable = true)
 |-- logsampleamplifier: string (nullable = true)
 |-- marketadvertiserid: string (nullable = true)
 |-- marketbuyerseatid: string (nullable = true)
 |-- marketdealid: string (nullable = true)
 |-- marketunifiedadid: string (nullable = true)
 |-- matchedaudienceitem: string (nullable = true)
 |-- matchedinventorypackage: string (nullable = true)
 |-- maxads: string (nullable = true)
 |-- maxduration: string (nullable = true)
 |-- mrmruleid: string (nullable = true)
 |-- netvalue: string (nullable = true)
 |-- networkid: string (nullable = true)
 |-- opportunityid: string (nullable = true)
 |-- pageviewrandom: string (nullable = true)
 |-- parseduseragent: string (nullable = true)
 |-- placementid: string (nullable = true)
 |-- placementopportunityid: string (nullable = true)
 |-- platformbrowserid: string (nullable = true)
 |-- platformdeviceid: string (nullable = true)
 |-- platformgroup: string (nullable = true)
 |-- platformosid: string (nullable = true)
 |-- positioninslot: string (nullable = true)
 |-- pricemodel: string (nullable = true)
 |-- privacychoices: string (nullable = true)
 |-- privacyjurisdiction: string (nullable = true)
 |-- progopenexchangeruleid: string (nullable = true)
 |-- programmerid: string (nullable = true)
 |-- purchasedorderid: string (nullable = true)
 |-- rawuseragent: string (nullable = true)
 |-- recordidentifier: string (nullable = true)
 |-- referrerurl: string (nullable = true)
 |-- requestairingcustomid: string (nullable = true)
 |-- requestbreakcustomid: string (nullable = true)
 |-- requestchannelcustomid: string (nullable = true)
 |-- requestsitecustomid: string (nullable = true)
 |-- requestvideocustomid: string (nullable = true)
 |-- saleschanneltype: string (nullable = true)
 |-- scenarioid: string (nullable = true)
 |-- sellingpartnerid: string (nullable = true)
 |-- seriesid: string (nullable = true)
 |-- signalid: string (nullable = true)
 |-- siteid: string (nullable = true)
 |-- sitesectionid: string (nullable = true)
 |-- slotindex: string (nullable = true)
 |-- soldorderid: string (nullable = true)
 |-- standarddevicetypeid: string (nullable = true)
 |-- standardenvironmentid: string (nullable = true)
 |-- standardosid: string (nullable = true)
 |-- streamid: string (nullable = true)
 |-- streamtype: string (nullable = true)
 |-- timeposition: string (nullable = true)
 |-- timepositionclass: string (nullable = true)
 |-- timeunfilled: string (nullable = true)
 |-- transactionid: string (nullable = true)
 |-- triggeringconcreteeventid: string (nullable = true)
 |-- trimmedtrackingdomainids: string (nullable = true)
 |-- trimmedtrackingdomains: string (nullable = true)
 |-- tvratingid: string (nullable = true)
 |-- uniqueidentifier: string (nullable = true)
 |-- valuechainrole: string (nullable = true)
 |-- videoassetid: string (nullable = true)
 |-- videoplayrandom: string (nullable = true)
 |-- visitorcity: string (nullable = true)
 |-- visitorcountry: string (nullable = true)
 |-- visitordma: string (nullable = true)
 |-- visitorpostalcode: string (nullable = true)
 |-- visitorstateprovince: string (nullable = true)
 |-- visitortimezoneoffset: string (nullable = true)
 |-- visitorzone: string (nullable = true)
 |-- xifaid: string (nullable = true)
 |-- request_date: string (nullable = true)
 |-- time_bucket: string (nullable = true)