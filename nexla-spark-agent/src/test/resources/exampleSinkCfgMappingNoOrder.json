{"id": 9187, "owner": {"id": 438, "full_name": "Oleksii Kotliar", "email": "<EMAIL>"}, "org": {"id": 64, "cluster_id": 3, "new_cluster_id": null, "cluster_status": "ACTIVE", "status": "ACTIVE", "email": null}, "status": "PAUSED", "data_set_id": 16426, "sink_format": null, "sink_config": {"sink.parallelism": 100, "open.files.cache.size": 10000, "mapping": {"mode": "manual", "mapping": {"store_id": ["aliased_store_id"], "sku_id": ["aliased_sku_id"], "approximate_sold_as_quantity": ["aliased_approximate_sold_as_quantity"]}, "tracker_mode": "NONE"}, "max.file.size.mb": 4096, "data_format": "csv", "sink_type": "ftp", "path": "/penny-officer/nexla/daily_feed/v1", "ui.display_path": "/penny-officer/nexla/daily_feed/v1", "output.dir.name.pattern": "{yyyy}{MM}{dd}/{HH}", "file.name.prefix": "store-{record.store_id}", "hc_scale_factor": "64", "replication.mode": false, "node.tag": "dd-file", "tasks.max": 4, "flush.control": "true"}, "sink_schedule": null, "sink_type": "s3", "data_set": {"id": 16426, "output_schema": {"type": "object", "properties": {"assetname": {"type": "string"}, "folderdate": {"type": "string"}, "transactionid_upper": {"type": "string"}, "videometadata": {"type": "string"}, "videoseriesid": {"type": "string"}, "videoseriesname": {"type": "string"}}, "$schema": "http://json-schema.org/draft-04/schema#", "$schema-id": 543824442}, "status": "PAUSED", "version": 1}, "data_credentials": {"id": 7177, "owner": {"id": 438, "full_name": "Oleksii Kotliar", "email": "<EMAIL>"}, "org": {"id": 64, "name": "Nexla", "cluster_id": 3, "new_cluster_id": null, "cluster_status": "ACTIVE", "status": "ACTIVE", "email": null}, "credentials_version": "1", "credentials_type": "s3", "verified_status": "200 Ok"}}