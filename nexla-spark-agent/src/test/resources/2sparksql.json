{"spec": [{"operation": "shift", "spec": {"#__#__NEX_ATTR__#__#": "#__#__NEX_ATTR__#__#", "addeliverymethod": "addeliverymethod", "adduration": "adduration", "adsselected": "adsselected", "adunithash": "<PERSON><PERSON><PERSON><PERSON>", "adunitid": "aduni<PERSON><PERSON>", "adunittypeid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "airingid": "<PERSON>id", "alleventkv": "alleventkv", "billableabstracteventid": "billableabstracteventid", "breakid": "breakid", "cbpid": "cbpid", "channelid": "channelid", "clocknumber": "clocknumber", "concreteeventid": "concreteeventid", "contentbrandid": "contentbrandid", "contentchannelid": "contentchannelid", "contentduration": "contentduration", "contentproviderpartnerid": "contentproviderpartnerid", "creativeduration": "creativeduration", "creativeid": "creativeid", "creativerenditionid": "creativerenditionid", "creativerenditionvariantid": "creativerenditionvariantid", "creativevariantid": "creativevariantid", "customvisitorid": "customvisitorid", "daypartid": "daypartid", "deviceid": "deviceid", "distributionpartnerid": "distributionpartnerid", "endpointid": "endpointid", "endpointownerid": "endpointownerid", "eventmultiplier": "eventmultiplier", "eventname": "eventname", "eventtime": "eventtime", "eventtime_est": "eventtime_est", "eventtype": "eventtype", "externalreseller": "externalreseller", "folderdate": "folderdate", "genreid": "genreid", "givtdecisionresult": "givtdecisionresult", "globaladvertiserid": "globaladvertiserid", "globalbrandid": "globalbrandid", "headerbiddingkey": "headerbiddingkey", "headerbiddingtransactionid": "headerbiddingtransactionid", "headerbiddingvalue": "headerbiddingvalue", "hyldarequest": "hyldarequest", "ipaddress": "ipaddress", "ipenabledaudience": "ipenabledaudience", "label": "label", "languageid": "languageid", "lineardecisiontype": "lineardecisiontype", "listingid": "listingid", "logsampleamplifier": "logsampleamplifier", "marketadvertiserid": "marketadvertiserid", "marketbuyerseatid": "marketbuyerseatid", "marketdealid": "marketdealid", "marketunifiedadid": "marketunifiedadid", "matchedaudienceitem": "matchedaudienceitem", "matchedinventorypackage": "matchedinventorypackage", "maxads": "maxads", "maxduration": "maxduration", "mrmruleid": "m<PERSON><PERSON><PERSON><PERSON>", "netvalue": "netvalue", "networkid": "networkid", "opportunityid": "opportunityid", "pageviewrandom": "pageviewrandom", "parseduseragent": "parseduseragent", "placementid": "placementid", "placementopportunityid": "placementopportunityid", "platformbrowserid": "platformbrowserid", "platformdeviceid": "platformdeviceid", "platformgroup": "platformgroup", "platformosid": "platformosid", "positioninslot": "positioninslot", "pricemodel": "pricemodel", "privacychoices": "privacychoices", "privacyjurisdiction": "privacyjurisdiction", "progopenexchangeruleid": "progopenexchangeruleid", "programmerid": "programmerid", "purchasedorderid": "purchasedorderid", "rawuseragent": "rawuseragent", "recordidentifier": "recordidentifier", "referrerurl": "refer<PERSON><PERSON><PERSON>", "requestairingcustomid": "requestairingcustomid", "requestbreakcustomid": "requestbreakcustomid", "requestchannelcustomid": "requestchannelcustomid", "requestsitecustomid": "requestsitecustomid", "requestvideocustomid": "requestvideocustomid", "saleschanneltype": "saleschanneltype", "scenarioid": "scenarioid", "sellingpartnerid": "sellingpartnerid", "seriesid": "seriesid", "signalid": "signalid", "siteid": "siteid", "sitesectionid": "sitesectionid", "slotindex": "slotindex", "soldorderid": "soldorderid", "standarddevicetypeid": "standarddevicetypeid", "standardenvironmentid": "standardenvironmentid", "standardosid": "standardosid", "streamid": "streamid", "streamtype": "streamtype", "timeposition": "timeposition", "timepositionclass": "timepositionclass", "timeunfilled": "timeunfilled", "transactionid": "transactionid", "triggeringconcreteeventid": "triggeringconcreteeventid", "trimmedtrackingdomainids": "trimmedtrackingdomainids", "trimmedtrackingdomains": "trimmedtrackingdomains", "tvratingid": "tvratingid", "uniqueidentifier": "uniqueidentifier", "valuechainrole": "valuechainrole", "videoassetid": "videoassetid", "videoplayrandom": "videoplayrandom", "visitorcity": "visitorcity", "visitorcountry": "visitorcountry", "visitordma": "visitordma", "visitorpostalcode": "visitorpostalcode", "visitorstateprovince": "visitorstateprovince", "visitortimezoneoffset": "visitortimezoneoffset", "visitorzone": "visitorzone", "xifaid": "xifaid"}}, {"operation": "default", "spec": {"#__#__NEX_ATTR__#__#": 1}}, {"operation": "nexla.modify", "spec": {"addeliverymethod": "=noop(%(1,'addeliverymethod'))", "adduration": "=noop(%(1,'adduration'))", "adsselected": "=noop(%(1,'adsselected'))", "adunithash": "=noop(%(1,'adunithash'))", "adunitid": "=noop(%(1,'adunitid'))", "adunittypeid": "=noop(%(1,'adunittypeid'))", "airingid": "=noop(%(1,'airingid'))", "alleventkv": "=noop(%(1,'alleventkv'))", "billableabstracteventid": "=noop(%(1,'billableabstracteventid'))", "breakid": "=noop(%(1,'breakid'))", "cbpid": "=noop(%(1,'cbpid'))", "channelid": "=noop(%(1,'channelid'))", "clocknumber": "=noop(%(1,'clocknumber'))", "concreteeventid": "=noop(%(1,'concreteeventid'))", "contentbrandid": "=noop(%(1,'contentbrandid'))", "contentchannelid": "=noop(%(1,'contentchannelid'))", "contentduration": "=noop(%(1,'contentduration'))", "contentproviderpartnerid": "=noop(%(1,'contentproviderpartnerid'))", "creativeduration": "=noop(%(1,'creativeduration'))", "creativeid": "=noop(%(1,'creativeid'))", "creativerenditionid": "=noop(%(1,'creativerenditionid'))", "creativerenditionvariantid": "=noop(%(1,'creativerenditionvariantid'))", "creativevariantid": "=noop(%(1,'creativevariantid'))", "customvisitorid": "=noop(%(1,'customvisitorid'))", "daypartid": "=noop(%(1,'daypartid'))", "deviceid": "=noop(%(1,'deviceid'))", "distributionpartnerid": "=noop(%(1,'distributionpartnerid'))", "endpointid": "=noop(%(1,'endpointid'))", "endpointownerid": "=noop(%(1,'endpointownerid'))", "eventmultiplier": "=noop(%(1,'eventmultiplier'))", "eventname": "=noop(%(1,'eventname'))", "eventtime": "=noop(%(1,'eventtime'))", "eventtime_est": "=noop(%(1,'eventtime_est'))", "eventtype": "=noop(%(1,'eventtype'))", "externalreseller": "=noop(%(1,'externalreseller'))", "folderdate": "=noop(%(1,'folderdate'))", "genreid": "=noop(%(1,'genreid'))", "givtdecisionresult": "=noop(%(1,'givtdecisionresult'))", "globaladvertiserid": "=noop(%(1,'globaladvertiserid'))", "globalbrandid": "=noop(%(1,'globalbrandid'))", "headerbiddingkey": "=noop(%(1,'headerbiddingkey'))", "headerbiddingtransactionid": "=noop(%(1,'headerbiddingtransactionid'))", "headerbiddingvalue": "=noop(%(1,'headerbiddingvalue'))", "hyldarequest": "=noop(%(1,'hyldarequest'))", "ipaddress": "=noop(%(1,'ipaddress'))", "ipenabledaudience": "=noop(%(1,'ipenabledaudience'))", "label": "=noop(%(1,'label'))", "languageid": "=noop(%(1,'languageid'))", "lineardecisiontype": "=noop(%(1,'lineardecisiontype'))", "listingid": "=noop(%(1,'listingid'))", "logsampleamplifier": "=noop(%(1,'logsampleamplifier'))", "marketadvertiserid": "=noop(%(1,'marketadvertiserid'))", "marketbuyerseatid": "=noop(%(1,'marketbuyerseatid'))", "marketdealid": "=noop(%(1,'marketdealid'))", "marketunifiedadid": "=noop(%(1,'marketunifiedadid'))", "matchedaudienceitem": "=noop(%(1,'matchedaudienceitem'))", "matchedinventorypackage": "=noop(%(1,'matchedinventorypackage'))", "maxads": "=noop(%(1,'maxads'))", "maxduration": "=noop(%(1,'maxduration'))", "mrmruleid": "=noop(%(1,'mrmruleid'))", "netvalue": "=noop(%(1,'netvalue'))", "networkid": "=noop(%(1,'networkid'))", "opportunityid": "=noop(%(1,'opportunityid'))", "pageviewrandom": "=noop(%(1,'pageviewrandom'))", "parseduseragent": "=noop(%(1,'parseduseragent'))", "placementid": "=noop(%(1,'placementid'))", "placementopportunityid": "=noop(%(1,'placementopportunityid'))", "platformbrowserid": "=noop(%(1,'platformbrowserid'))", "platformdeviceid": "=noop(%(1,'platformdeviceid'))", "platformgroup": "=noop(%(1,'platformgroup'))", "platformosid": "=noop(%(1,'platformosid'))", "positioninslot": "=noop(%(1,'positioninslot'))", "pricemodel": "=noop(%(1,'pricemodel'))", "privacychoices": "=noop(%(1,'privacychoices'))", "privacyjurisdiction": "=noop(%(1,'privacyjurisdiction'))", "progopenexchangeruleid": "=noop(%(1,'progopenexchangeruleid'))", "programmerid": "=noop(%(1,'programmerid'))", "purchasedorderid": "=noop(%(1,'purchasedorderid'))", "rawuseragent": "=noop(%(1,'rawuseragent'))", "recordidentifier": "=noop(%(1,'recordidentifier'))", "referrerurl": "=noop(%(1,'referrerurl'))", "requestairingcustomid": "=noop(%(1,'requestairingcustomid'))", "requestbreakcustomid": "=noop(%(1,'requestbreakcustomid'))", "requestchannelcustomid": "=noop(%(1,'requestchannelcustomid'))", "requestsitecustomid": "=noop(%(1,'requestsitecustomid'))", "requestvideocustomid": "=noop(%(1,'requestvideocustomid'))", "saleschanneltype": "=noop(%(1,'saleschanneltype'))", "scenarioid": "=noop(%(1,'scenarioid'))", "sellingpartnerid": "=noop(%(1,'sellingpartnerid'))", "seriesid": "=noop(%(1,'seriesid'))", "signalid": "=noop(%(1,'signalid'))", "siteid": "=noop(%(1,'siteid'))", "sitesectionid": "=noop(%(1,'sitesectionid'))", "slotindex": "=noop(%(1,'slotindex'))", "soldorderid": "=concat([%(1,'soldorderid'),'',''''])", "standarddevicetypeid": "=noop(%(1,'standarddevicetypeid'))", "standardenvironmentid": "=noop(%(1,'standardenvironmentid'))", "standardosid": "=noop(%(1,'standardosid'))", "streamid": "=noop(%(1,'streamid'))", "streamtype": "=noop(%(1,'streamtype'))", "timeposition": "=noop(%(1,'timeposition'))", "timepositionclass": "=noop(%(1,'timepositionclass'))", "timeunfilled": "=noop(%(1,'timeunfilled'))", "transactionid": "=noop(%(1,'transactionid'))", "triggeringconcreteeventid": "=noop(%(1,'triggeringconcreteeventid'))", "trimmedtrackingdomainids": "=noop(%(1,'trimmedtrackingdomainids'))", "trimmedtrackingdomains": "=noop(%(1,'trimmedtrackingdomains'))", "tvratingid": "=noop(%(1,'tvratingid'))", "uniqueidentifier": "=noop(%(1,'uniqueidentifier'))", "valuechainrole": "=noop(%(1,'valuechainrole'))", "videoassetid": "=noop(%(1,'videoassetid'))", "videoplayrandom": "=noop(%(1,'videoplayrandom'))", "visitorcity": "=noop(%(1,'visitorcity'))", "visitorcountry": "=noop(%(1,'visitorcountry'))", "visitordma": "=noop(%(1,'visitordma'))", "visitorpostalcode": "=noop(%(1,'visitorpostalcode'))", "visitorstateprovince": "=noop(%(1,'visitorstateprovince'))", "visitortimezoneoffset": "=noop(%(1,'visitortimezoneoffset'))", "visitorzone": "=noop(%(1,'visitorzone'))", "xifaid": "=noop(%(1,'xifaid'))"}}, {"operation": "remove", "spec": {"audienceitem": "", "allrequestkv": "", "#__#__NEX_ATTR__#__#": ""}}]}