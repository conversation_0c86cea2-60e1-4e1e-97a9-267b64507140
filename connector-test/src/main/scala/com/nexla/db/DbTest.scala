package com.nexla.db

import com.nexla.connect.common.DbTestUtils._
import com.nexla.sc.util.WithJooq
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfterEach, OneInstancePerTest}

trait DbTest
  extends AnyFlatSpecLike
    with Matchers
    with WithJooq
    with BeforeAndAfterEach
    with OneInstancePerTest {

  val db = getDb

  protected def getDb = newDb(BACKEND_DB.get())

  override val getConnectionFn = () => createConnection(db)

  override def beforeEach {
    recreateTables()
  }

  override def afterEach {
    cleanDb(db)
  }

  def recreateTables() = {
    val connection = createConnection(db)
    val sqlCommands = db.createScript.split("---")
    sqlCommands.map(_.trim).foreach { cmd => connection.createStatement.execute(cmd) }
    connection.commit()
    connection.close()
  }
}
