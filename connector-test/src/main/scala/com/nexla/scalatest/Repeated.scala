package com.nexla.scalatest

import org.scalatest.{Args, Status, TestSuite, TestSuiteMixin}

/**
 * Allows you to repeat the test as many times in auto mode as you see fit.
 * Just add -Dtimes=X to the arguments of the test run command in your IDE or CLI.
 */
trait Repeated extends TestSuiteMixin { this: TestSuite =>
  protected abstract override def runTest(testName: String, args: Args): Status = {
    def run0(times: Int): Status = {
      val status = super.runTest(testName, args)
      if (times <= 1) status else status.thenRun(run0(times - 1))
    }

    run0(args.configMap.getWithDefault("times", "1").toInt)
  }
}
