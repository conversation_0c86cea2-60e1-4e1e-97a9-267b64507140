package com.nexla.jdbc.test

import com.nexla.aws.S3TestUtils
import com.nexla.common.sink.TopicPartition
import com.nexla.common.tracker.Tracker
import com.nexla.common.{NexlaConstants, NexlaMessage, NexlaMetaData}
import com.nexla.connector.ConnectorService.UNIT_TEST
import com.nexla.connector.NexlaMessageContext
import com.nexla.connector.config.file.S3Constants.{ACCESS_KEY_ID, SECRET_KEY}
import com.nexla.connector.config.jdbc.JdbcAuthConfig
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.{PARALLELISM, TEMP_S3_UPLOAD_BUCKET, TEMP_S3_UPLOAD_PREFIX}
import com.nexla.connector.properties.SqlConfigAccessor.{INSERT_MODE, TABLE}

import java.sql.{Connection, DriverManager}
import java.util
import scala.util.{Failure, Random, Success, Try}

trait JdbcWithS3Support extends S3TestUtils {
  val url: String
  val username: String
  val password: String

  val getConnFn = () => {
    val conn = DriverManager.getConnection(url, username, password)
    conn.setAutoCommit(false)
    conn
  }

  def getProperties(table: String) = Map(
    TABLE -> table,
    PARALLELISM -> "1",
    JdbcAuthConfig.URL -> url,
    JdbcAuthConfig.USERNAME -> username,
    JdbcAuthConfig.PASSWORD -> password,
    NexlaConstants.SINK_ID -> "1",
    NexlaConstants.CREDS_ENC -> "1",
    NexlaConstants.CREDS_ENC_IV -> "1",
    UNIT_TEST -> "true",
    INSERT_MODE -> "insert",
    ACCESS_KEY_ID -> accessKeyId,
    SECRET_KEY -> secretKey,
    TEMP_S3_UPLOAD_BUCKET -> "qa.nexla.com",
    TEMP_S3_UPLOAD_PREFIX -> "unit_test",
  )

  def withConn(fn: Connection => Unit) = {
    val connection = getConnFn()
    val result = Try(fn(connection))
    connection.commit()
    connection.close()
    result match {
      case Success(_) =>
      case Failure(exception) => throw exception
    }
  }

  def dropTableIfExists(tableName: String) = withConn(_.prepareStatement(s"DROP TABLE IF EXISTS $tableName").execute())

  def genTableName = s"unit_test_${Random.nextInt(1000)}"

  def createMessage(rawMessage: util.LinkedHashMap[String, AnyRef]) = {
    val message = new NexlaMessage(rawMessage)
    val nexlaMetaData = new NexlaMetaData
    message.setNexlaMetaData(nexlaMetaData)
    nexlaMetaData.setRunId(7L)
    nexlaMetaData.setTrackerId(Tracker.parse("5010:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010"))
    message
  }

  def createMessageContext(rawMessage: util.LinkedHashMap[String, AnyRef]) = {
    val message = createMessage(rawMessage)
    new NexlaMessageContext(message, message, new TopicPartition("1", 1), 1)
  }

}
