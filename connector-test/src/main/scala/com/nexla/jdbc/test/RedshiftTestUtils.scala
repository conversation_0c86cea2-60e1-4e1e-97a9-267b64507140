package com.nexla.jdbc.test

import com.nexla.common.ConnectionType.REDSHIFT
import com.nexla.common.NexlaConstants.CREDENTIALS_TYPE
import com.nexla.connector.config.jdbc.JdbcAuthConfig.SCHEMA_NAME
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.REDSHIFT_TIME_FORMAT
import com.nexla.connector.properties.SqlConfigAccessor._

trait RedshiftTestUtils
  extends JdbcWithS3Support {

  override val url = "*************************************************************************************"
  override val username = "qa_test"
  override val password = "Q@_t3st123"
  val schemaName = "unit_test"

  override def getProperties(table: String) = super.getProperties(table) ++ Map(
    SCHEMA_NAME -> schemaName,
    PRIMARY_KEY -> "id",
    CREDENTIALS_TYPE -> REDSHIFT.name().toLowerCase,
    REDSHIFT_TIME_FORMAT -> "YYYY-MM-DD HH:MI:SS" // allows millis too
  )

}
