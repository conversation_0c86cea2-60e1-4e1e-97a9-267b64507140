package com.nexla.mongo

import com.mongodb.client.{MongoCollection, MongoDatabase}
import java.util.UUID
import com.mongodb.{MongoClient, MongoClientURI}
import com.nexla.common.NexlaConstants._
import com.nexla.connector.ConnectorService.UNIT_TEST
import com.nexla.connector.config.documentdb.mongo.MongoAuthConfig.URL
import com.nexla.connector.properties.DocumentDbConfigAccessor._
import org.bson.BsonDocument

class MongoDbTestUtils(mongoUrl: String) {

  val collectionName: String = "test" + UUID.randomUUID()

  def getCollectionName(): String = {
    collectionName
  }

  def collection(): MongoCollection[BsonDocument] = {
    val mongoConnectionString = new MongoClientURI(mongoUrl)
    val mongoClient = new MongoClient(mongoConnectionString)
    val database = mongoClient.getDatabase(mongoConnectionString.getDatabase)
    database.getCollection(collectionName, classOf[BsonDocument])
  }

  def baseConfig(): Map[String, String] = Map(
    URL -> mongoUrl,
    COLLECTION -> collectionName,
    SOURCE_ID -> "1",
    SINK_ID -> "1",
    CREDS_ENC -> "1",
    CREDS_ENC_IV -> "1",
    CREDENTIALS_DECRYPT_KEY -> "1",
    UNIT_TEST -> "true",
    BOOTSTRAP_SERVERS -> "",
    SINK_TYPE -> "mongo",
    CREDENTIALS_TYPE -> "mongo"
  )

}
