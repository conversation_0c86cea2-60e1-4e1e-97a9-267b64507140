package com.nexla

import com.nexla.admin.client.flownode._
import com.nexla.admin.client.pipeline.{PDataSource, PDataset, PTransform}
import com.nexla.admin.client._
import com.nexla.common.NexlaConstants.CREDENTIALS_DECRYPT_KEY
import com.nexla.common.NexlaFile.FileSourceType
import com.nexla.common.{ConnectionType, NexlaConstants, NexlaFile, Resource, ResourceType}
import com.nexla.connector.config.{FlowType, IngestionMode}
import com.nexla.sc.client.listing.{FileSourceTypes, FileStatuses, ListedFile, ListingAppClient, ListingResult}
import org.joda.time.DateTime
import org.mockito.Mockito.{mock, when}

import java.time.LocalDateTime
import java.util
import java.util.Optional.ofNullable
import java.util.{Collections, Optional}
import scala.concurrent.Future

object Fixtures {

  def nexlaFile(id: Long, p: Option[String]): NexlaFile = {
    val f = new NexlaFile()
    f.setSource(FileSourceType.LISTING)
    f.setId(id)
    f.setLinkedFileId(id)
    f.setSize(id)
    f.setFullPath(p.get)
    f
  }

  def getExampleSourceConfig: util.HashMap[String, Object] = {
    val stubCfgMap = new util.HashMap[String, Object]()
    stubCfgMap.put(NexlaConstants.CREDENTIALS_DECRYPT_KEY, "decrypt-key-for-source")
    stubCfgMap.put("path", "bucketA/someDir/someOtherDir")
    stubCfgMap.put("cloud.region", "my-region")
    stubCfgMap.put("cluster.id", "my-external-cluster")
    stubCfgMap.put("cloud.cluster.vm.subnet", "my-subnet")
    stubCfgMap.put("cloud.role.with.cluster.access", "stub-role-1")
    stubCfgMap.put("cloud.cluster.service.role", "stub-role-2")
    stubCfgMap.put("cloud.cluster.entity.role", "stub-role-3")
    stubCfgMap.put("cloud.jar.location", "s3://foo/bar/baz.jar")
    stubCfgMap
  }
  def getExampleSinkConfig(): util.HashMap[String, Object] = {
    val sinkCfg = new util.HashMap[String, Object]()
    sinkCfg.put(CREDENTIALS_DECRYPT_KEY, "decrypt-key-for-sink")
    sinkCfg
  }

  def adminDataSource(id: Int, config: java.util.Map[String, Object]): DataSource = {
    val stubSource = new DataSource
    stubSource.setId(id)
    stubSource.setConnectionType(ConnectionType.FILE_UPLOAD)
    stubSource.setSourceConfig(config)
    stubSource
  }

  def adminApiWithSourceCfgAndSpecificSinkStatus(srcId: Int, sinkId: Int, status: ResourceStatus, cfg: util.Map[String, Object], srcConType: ConnectionType, dstConType: ConnectionType): AdminApiClient = {
    this.adminApiWithSourceSinkDatasetInternal(srcId, sinkId, cfg, srcConType, dstConType, status)
  }

  def adminApiWithSourceCfgAndSpecificSinkId(srcId: Int, sinkId: Int, cfg: util.Map[String, Object], srcConType: ConnectionType, dstConType: ConnectionType): AdminApiClient = {
    this.adminApiWithSourceSinkDatasetInternal(srcId, sinkId, cfg, srcConType, dstConType)
  }

  def adminApiWithSourceCfgAndSpecificSinkId(srcId: Int, sinkId: Int, cfg: util.Map[String, Object]): AdminApiClient = {
    this.adminApiWithSourceSinkDatasetInternal(srcId, sinkId, cfg, ConnectionType.FILE_UPLOAD, ConnectionType.FTP)
  }

  def adminApiWithSourceSinkDataset(id: Int): AdminApiClient = {
    this.adminApiWithSourceSinkDatasetInternal(id, id, new util.HashMap[String, Object](), ConnectionType.FILE_UPLOAD, ConnectionType.FTP)
  }

  def adminApiClientWithSpecificSourceCfg(id: Int, srcCfg: java.util.Map[String, Object]): AdminApiClient = {
    this.adminApiWithSourceSinkDatasetInternal(id, id, srcCfg, ConnectionType.FILE_UPLOAD, ConnectionType.FTP)
  }

  private def adminApiWithSourceSinkDatasetInternal(srcId: Int, sinkId: Int, srcCfg: java.util.Map[String, Object],
                                                    srcConType: ConnectionType, dstConType: ConnectionType,
                                                    resourceStatus: ResourceStatus = ResourceStatus.ACTIVE): AdminApiClient = {
    adminApiWithSourceSinkDatasetInternal(srcId, sinkId, srcCfg, getExampleSinkConfig(), srcConType, dstConType, resourceStatus)
  }

  def adminApiWithSourceSinkDatasetInternal(srcId: Int, sinkId: Int, srcCfg: java.util.Map[String, Object], sinkCfg: java.util.Map[String, Object],
                                            srcConType: ConnectionType, dstConType: ConnectionType,
                                            resourceStatus: ResourceStatus): AdminApiClient =
    adminApiWithSourceSinkDatasetInternal(1, srcId, sinkId, srcCfg, sinkCfg, srcConType, dstConType, resourceStatus)

  def adminApiWithSourceSinkDatasetInternal(flowId: Int, srcId: Int, sinkId: Int, srcCfg: java.util.Map[String, Object], sinkCfg: java.util.Map[String, Object],
                                            srcConType: ConnectionType, dstConType: ConnectionType,
                                            resourceStatus: ResourceStatus): AdminApiClient = {
    val mockAdminApi = mock(classOf[AdminApiClient])
    val ds: PDataSource = new PDataSource
    ds.setId(srcId)
    ds.setConnectionType(srcConType)
    val stubSink: DataSink = new DataSink
    stubSink.setId(sinkId)
    stubSink.setConnectionType(dstConType)

    stubSink.setSinkConfig(sinkCfg)
    stubSink.setOwner(getOwner(1))
    stubSink.setOrg(getOrg(1))
    stubSink.setDataCredentials(getCreds(1))
    stubSink.setStatus(resourceStatus)

    val stubTx = new PTransform(Collections.emptyList[Object]())
    val stubPDs = new PDataset(srcId, srcId, stubTx)
    val datasets: java.util.List[PDataset] = Collections.singletonList[PDataset](stubPDs)
    val stubDs = new DataSource
    stubDs.setId(srcId)
    stubDs.setConnectionType(srcConType)
    stubDs.setSourceConfig(srcCfg)
    stubDs.setStatus(resourceStatus)
    stubDs.setDatasets(new util.LinkedList())
    stubDs.setOwner(Owner.getOwnerById(srcId))
    stubDs.setOrg(Org.getOrgById(srcId))
    val stubCreds = new DataCredentials

    val credsId = srcId
    stubCreds.setId(credsId)
    stubCreds.setCredentialsEnc("ZHeo/xsK1X85/R/RJtb7ox9r/QhY81aAOevQSulRUNkdfF09DHXV5OCyHrz7q3i/2EBFHaNZ3bOA4zLearHXht2dsN14P+jDBySG6aWOuplVPA==")
    stubCreds.setCredentialsEncIv("dHboqAZ6hcw+cSLr")
    stubDs.setDataCredentials(stubCreds)

    val stubDataset = new DataSet
    stubDataset.setId(srcId)
    val setCfg = new util.HashMap[String, String]()
    stubDataset.setRuntimeConfig(setCfg)
    val txh = new TransformHistory
    txh.setVersion(1)
    txh.setTransforms(java.util.List.of())
    stubDataset.setTransformHistory(txh)
    stubSink.setDataSet(stubDataset)
    stubDataset.setDataSinks(Collections.singletonList(stubSink))
    stubDs.setDatasets(util.Arrays.asList(stubDataset))

    val flowId = 1
    val flowDset = new FlowNodeDataset(
      srcId, 1, 1, flowId,
      1, "src-name", "src-desc", ResourceStatus.ACTIVE, srcId, null,
      util.List.of[String](), null, null, util.List.of[Integer](sinkId))
    val flowDsets: util.List[FlowNodeDataset] = util.List.of[FlowNodeDataset](flowDset)

    val flowDsrc = new FlowNodeDatasource(
      srcId, 1, 1, flowId,
      1, "src-name", "src-desc", ResourceStatus.ACTIVE,
      util.List.of[String](), null, null, sinkId, srcConType, srcConType, srcConType)
    val flowDatasources: util.List[FlowNodeDatasource] = util.List.of[FlowNodeDatasource](flowDsrc)

    val flowDsink = new FlowNodeDatasink(sinkId,
      1, 1, flowId,
      1, "src-name", "src-desc", ResourceStatus.ACTIVE,
      util.List.of[String](), null, null, srcId, srcId, srcConType, srcConType, srcConType)
    val flowDsinks: util.List[FlowNodeDatasink] = util.List.of[FlowNodeDatasink](flowDsink)

    val flow = new AdminApiFlow(
      flowId, 1, 1, srcId, ResourceStatus.ACTIVE,
      FlowType.STREAMING, IngestionMode.FULL_INGESTION, util.List.of[FlowNodeElement](new FlowNodeElement(1, 1, 1, ds.getId, sinkId, util.List.of[FlowNodeElement]()))
    )
    val adminApiFlows: util.List[AdminApiFlow] = util.List.of[AdminApiFlow](flow)

    val nexlaFlow = new NexlaFlow(adminApiFlows, flowDatasources, flowDsets, flowDsinks)

    when(mockAdminApi.getDataCredentials(credsId)).thenReturn(Optional.of(stubCreds))
    when(mockAdminApi.getFlowByResource(new Resource(srcId, ResourceType.SOURCE))).thenReturn(ofNullable(nexlaFlow))
    when(mockAdminApi.getNexlaFlow(flowId)).thenReturn(ofNullable(nexlaFlow))
    when(mockAdminApi.getFlowByResource(new Resource(sinkId, ResourceType.SINK))).thenReturn(ofNullable(nexlaFlow))
    when(mockAdminApi.getPipeline(srcId)).thenReturn(Optional.of(new com.nexla.admin.client.pipeline.Pipeline(datasets, ds, stubSink)))
    when(mockAdminApi.getPipeline(sinkId)).thenReturn(Optional.of(new com.nexla.admin.client.pipeline.Pipeline(datasets, ds, stubSink)))
    when(mockAdminApi.getDataSource(srcId)).thenReturn(Optional.of(stubDs))
    when(mockAdminApi.getDataSet(srcId)).thenReturn(Optional.of(stubDataset))
    when(mockAdminApi.getDataSink(sinkId)).thenReturn(Optional.of(stubSink))
    mockAdminApi
  }

  def getOwner(i: Int): Owner = {
    val o = new Owner(i, "name", "a@a.a", i, i, "12312",
      Optional.empty(), false, DateTime.now(), AccountStatus.ACTIVE, Optional.empty())
    o
  }

  def getOrg(i: Int): Org = {
    val o = new Org(i, "org", "o@o.o", getOwner(i), Optional.empty(), false, DateTime.now(),
      AccountStatus.ACTIVE)
    o
  }

  def getDatasetNoTransforms(id: Int): DataSet = {
    val ds = new DataSet()
    ds.setId(id)
    ds.setOrg(getOrg(id))
    ds.setOwner(getOwner(id))
    ds.setRuntimeConfig(new util.HashMap[String, String]())
    val tx = new TransformHistory()
    tx.setTransforms(new util.ArrayList[AnyRef]())
    ds.setTransformHistory(tx)

    ds
  }

  def getCreds(i: Int): DataCredentials = {
    val c = new DataCredentials
    c.setId(i)
    c
  }

  def getMockListing(sourceId: Int, files: List[String]): ListingAppClient = {
    val instance = mock(classOf[ListingAppClient])

    if (files.isEmpty) {
      when(instance.takeFile(sourceId)).thenReturn(Future.successful(ListingResult(None, false)))
      instance
    } else {
      val futures = files.map { fileName =>
        Future.successful(
          ListingResult(Option(ListedFile(sourceId, fileName, None, None, None, None, FileSourceTypes.Listing, FileStatuses.New, None, None, LocalDateTime.now())), false)
        )
      }

      val empty: Future[ListingResult] = Future.successful(ListingResult(None, false))
      when(instance.takeFile(sourceId)).thenReturn(futures.head, futures.tail ::: List(empty): _*)
      instance
    }
  }

}