package com.nexla.dynamodb

import com.amazonaws.ClientConfiguration

import java.util.UUID
import com.amazonaws.client.builder.AwsClientBuilder
import com.amazonaws.regions.Regions
import com.amazonaws.services.dynamodbv2.{AmazonDynamoDB, AmazonDynamoDBClientBuilder}
import com.amazonaws.retry.RetryPolicy
import com.amazonaws.services.dynamodbv2.{AmazonDynamoDB, AmazonDynamoDBClientBuilder}
import com.nexla.common.NexlaConstants._
import com.nexla.connect.common.TestContainerApi.containerInfo
import com.nexla.connector.ConnectorService.UNIT_TEST
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpecLike

class DynamoDbTestUtils(host: String, mappedPort: Int) {

  val dynamoDbUrl = s"http://$host:$mappedPort"

  val configuration = new AwsClientBuilder.EndpointConfiguration(dynamoDbUrl, Regions.DEFAULT_REGION.toString)
  val dynamoClient: AmazonDynamoDB = AmazonDynamoDBClientBuilder.standard().withEndpointConfiguration(configuration).build()

  val collectionName: String = "test" + UUID.randomUUID()

  def baseConfig: Map[String, String] = Map(
    SOURCE_ID -> "1",
    SINK_ID -> "1",
    CREDS_ENC -> "1",
    CREDS_ENC_IV -> "1",
    CREDENTIALS_DECRYPT_KEY -> "1",
    UNIT_TEST -> "true",
    BOOTSTRAP_SERVERS -> "",
    SINK_TYPE -> "dynamodb",
    CREDENTIALS_TYPE -> "dynamodb",
    "access.key" -> "dummy_username",
    "secret.key" -> "dummy_secret"
  )

}
