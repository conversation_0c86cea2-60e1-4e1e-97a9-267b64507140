package com.nexla.connect.common;

import com.nexla.Fixtures;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AdminApiClientBuilder;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import org.apache.kafka.common.utils.Time;
import org.apache.kafka.connect.connector.policy.ConnectorClientConfigOverridePolicy;
import org.apache.kafka.connect.runtime.Connect;
import org.apache.kafka.connect.runtime.Herder;
import org.apache.kafka.connect.runtime.Worker;
import org.apache.kafka.connect.runtime.isolation.Plugins;
import org.apache.kafka.connect.runtime.rest.RestServer;
import org.apache.kafka.connect.runtime.standalone.StandaloneConfig;
import org.apache.kafka.connect.runtime.standalone.StandaloneHerder;
import org.apache.kafka.connect.storage.FileOffsetBackingStore;
import org.apache.kafka.connect.util.ConnectUtils;
import org.junit.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.containers.KafkaContainer;

/**
 * Harness for executing standalone connectors in junit. Performs similar functionality to
 * {@link org.apache.kafka.connect.cli.ConnectStandalone}
 */
public abstract class BaseConnectorTest extends BaseKafkaTest {

  private static Logger log = LoggerFactory.getLogger(BaseConnectorTest.class);
  protected abstract Map<String, String> getConnectorProps();
  public static KafkaContainer kafka = new KafkaContainer("5.5.1");

  @Before
  public void onBefore() {
    AdminApiClient adminApiClient = Fixtures.adminApiWithSourceSinkDataset(1);
    AdminApiClientBuilder.INSTANCE = adminApiClient;

    Map<String, String> workerProps = new HashMap<>() {{
      put("bootstrap.servers", BOOTSTRAP_SERVERS);
      put("schema.registry.url", "http://localhost:8081");
      put("converter.type", "key");
      put("key.converter", "org.apache.kafka.connect.storage.StringConverter");
      put("value.converter", "org.apache.kafka.connect.storage.StringConverter");
      put("internal.key.converter", "org.apache.kafka.connect.json.JsonConverter");
      put("internal.value.converter", "org.apache.kafka.connect.json.JsonConverter");
      put("internal.key.converter.schemas.enable", "false");
      put("internal.value.converter.schemas.enable", "false");
      put("offset.storage.file.filename", "/tmp/connect.offsets");
      put("offset.flush.interval.ms", "5000");
      put("consumer.metadata.max.age.ms", "10000");
      put("rest.port", "8089");
      put("consumer.interceptor.classes", "io.confluent.monitoring.clients.interceptor.MonitoringConsumerInterceptor");
      put("producer.interceptor.classes", "io.confluent.monitoring.clients.interceptor.MonitoringProducerInterceptor");
    }};

    Plugins plugins = new Plugins(workerProps);
    StandaloneConfig config = new StandaloneConfig(workerProps);
    String kafkaClusterId = ConnectUtils.lookupKafkaClusterId(config);
    ConnectorClientConfigOverridePolicy connectorClientConfigOverridePolicy =
        plugins.newPlugin(
            config.getString("connector.client.config.override.policy"),
            config,
            ConnectorClientConfigOverridePolicy.class
        );
    RestServer rest = new RestServer(config);
    URI advertisedUrl = rest.advertisedUrl();
    String workerId = advertisedUrl.getHost() + ":" + advertisedUrl.getPort();

    Worker worker = new Worker(workerId,
        Time.SYSTEM,
        new Plugins(workerProps),
        new StandaloneConfig(workerProps),
        new FileOffsetBackingStore(),
        connectorClientConfigOverridePolicy);
    Herder herder = new StandaloneHerder(worker, kafkaClusterId, connectorClientConfigOverridePolicy);
    rest.initializeServer();
    Connect connect = new Connect(herder, rest);

    new Thread(connect::start).start();
    herder.putConnectorConfig(getConnectorProps().get("name"), getConnectorProps(), false, (error, info) -> {
      if (error != null) {
        log.error("Failed to start connector", error.getCause());
      }
    });
  }
}
