package com.nexla.connect.common;

import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.nexla.common.io.RedisConnect;
import com.nexla.db.util.DbUtils;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.sync.RedisCommands;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.jooq.DSLContext;
import org.testcontainers.containers.JdbcDatabaseContainer;

import java.sql.Connection;
import java.sql.DriverManager;
import java.time.ZoneId;
import java.util.*;

import static com.nexla.connect.common.TestContainerApi.containerInfo;
import static com.nexla.connector.properties.SqlConfigAccessor.JDBC_PARAMETERS;
import static java.util.Optional.of;

public class DbTestUtils {

	@AllArgsConstructor
	public static class DbData {
		public final String image;

		public final String url;
		public final String username;
		public final String password;
		public final String databaseName;

		public final String createScript;

		public final Optional<DbData> parentDb;
	}

	@AllArgsConstructor
	public static class RedisData {
		public final String host;
		public final int port;
		public final int index;

		public RedisConnect toHostPort() {
			return new RedisConnect(host, port, index);
		}
	}

	public static final Supplier<DbData> POSTGRES;
	public static final Supplier<DbData> SQLSERVER;
	public static final Supplier<DbData> MYSQL_5_5;
	public static final Supplier<DbData> MYSQL_5_7;
	public static final Supplier<DbData> ORACLE;
	public static final Supplier<DbData> SPANNER;
	public static final Supplier<RedisData> REDIS;

	public static final Supplier<DbData> BACKEND_DB;

	static {
		POSTGRES = Suppliers.memoize(() -> db("postgres:latest"));
		MYSQL_5_5 = Suppliers.memoize(() -> db("mysql:5.5"));
		MYSQL_5_7 = Suppliers.memoize(() -> db("mysql:5.7"));
		ORACLE = Suppliers.memoize(() -> db("oracle:latest"));
		REDIS = Suppliers.memoize(() -> parseRedisData(containerInfo("redis:7.0.12")));
		SQLSERVER = Suppliers.memoize(() -> db("mssqlserver:latest"));
		SPANNER = Suppliers.memoize(() -> parseSpannerData("cloud-spanner-emulator:1.4.2"));
		BACKEND_DB = POSTGRES;
	}

	private static DbData db(String image) {
		return parseDbData(containerInfo(image), image);
	}

	@SneakyThrows
	public static DbData newDb(DbData dbData) {
		String newDbName = "test_" + UUID.randomUUID().toString().substring(0, 8);

		if (dbData.image.contains("oracle")) {
			String username = newDbName;
			String password = "123456";
			runQuery(dbData, "CREATE USER " + newDbName + " IDENTIFIED BY " + password);
			runQuery(dbData, "GRANT CONNECT, RESOURCE, DBA TO " + newDbName);

			return new DbData(
				dbData.image,
				dbData.url.replace(dbData.username + "/" + dbData.password, username + "/" + password),
				username,
				password,
				newDbName,
				dbData.createScript,
				of(dbData));

		} else {
			runQuery(dbData, "CREATE DATABASE " + newDbName);
			return new DbData(
				dbData.image,
				dbData.url.replace(dbData.databaseName, newDbName),
				dbData.username,
				dbData.password,
				newDbName,
				dbData.createScript,
				of(dbData));
		}
	}

	public static RedisData newDb(RedisData redisData) {
		int newIndex = new Random().nextInt(16); // 15 is max db index allowed to select in redis
		return new RedisData(redisData.host, redisData.port, newIndex);
	}

	public static void cleanDb(DbData dbData) {
		if (dbData.image.contains("postgres")) {
			// terminating possibly stuck connections
			runQuery(POSTGRES.get(), "SELECT PG_TERMINATE_BACKEND(pid) FROM pg_stat_activity WHERE datname = '" + dbData.databaseName + "'");
			runQuery(POSTGRES.get(), "DROP DATABASE " + dbData.databaseName);
		} else if (dbData.image.contains("oracle")) {
			DbData parentDb = dbData.parentDb.orElse(dbData);
			runQuery(parentDb, "DROP USER " + dbData.databaseName + " CASCADE");
		} else if (dbData.image.contains("spanner")) {
			dropSpannerDb(Integer.parseInt(dbData.username), dbData.databaseName);
		} else {
			runQuery(dbData, "DROP DATABASE " + dbData.databaseName);
		}
	}

	public static void cleanRedisDb(RedisData redisData) {
		RedisCommands<String, String> jedis = getRedis(redisData);
		jedis.flushdb();
		jedis.getStatefulConnection().close();
	}

	public static RedisCommands<String, String> getRedis(RedisData redisData) {
		RedisClient jedis = RedisClient.create(RedisURI.create(redisData.host, redisData.port));
		RedisCommands<String, String> commands = jedis.connect().sync();
		commands.select(redisData.index);
		return commands;
	}

	@SneakyThrows
	public static void runQuery(DbData dbData, String sql) {
		doRunQuery(dbData, sql);
	}

	@SneakyThrows
	private static void doRunQuery(DbData dbData, String sql) {
		Connection connection = createConnection(dbData);
		connection.setAutoCommit(true);
		connection.createStatement().execute(sql);
		connection.close();
	}

	private static RedisData parseRedisData(Map<String, String> containerInfo) {
		return new RedisData(
			containerInfo.get("host"),
			Integer.parseInt(containerInfo.get("port")),
			Integer.parseInt(containerInfo.get("index")));
	}

	private static DbData parseDbData(Map<String, String> info, String image) {
		return new DbData(
			image,
			info.get("url"),
			info.get("username"),
			info.get("password"),
			info.get("database.name"),
			getCreateScript(image),
			null);
	}

	private static DbData parseSpannerData(String image) {
		Map<String, String> containerInfo = containerInfo(image);
		return new DbData(
				image,
				containerInfo.get("url"),
				containerInfo.get("port_rest"),
				"password",
				containerInfo.get("database_id"),
				getCreateScript(image),
				null
		);
	}

	private static String getCreateScript(String image) {
		if (image.contains("oracle")) {
			return DbUtils.getOracleSqlScript();
		} else if (image.contains("mysql")) {
			return DbUtils.getMysqlSqlScriptMetrics();
		} else if (image.contains("postgres")) {
			return DbUtils.getPostgresSqlScript();
		} else {
			return null;
		}
	}

	@SneakyThrows
	public static Connection createConnection(DbData container) {
		if (container.image.contains("oracle")) {
			return createOracleConnection(container);
		} else if (container.image.contains("spanner")) {
			return createSpannerConnection(container);
		} else {
			String sep = container.url.contains("?") ? "&" : "?";
			// everything assumes your local machine runs in UTC - but in fact,
			// you need to configure this directly, otherwise DBs with implied conversion logic
			// such as MySQL will take your correct queries and convert them to other timezone
			String shortServerTzName = TimeZone.getTimeZone(ZoneId.systemDefault()).getDisplayName(false, TimeZone.SHORT);
			String url = container.image.contains("mysql") ? container.url + sep + "useSSL=false" + "&serverTimezone=" + shortServerTzName : container.url;
			Connection connection = DriverManager.getConnection(url, container.username, container.password);
			connection.setAutoCommit(false);
			return connection;
		}
	}

	@SneakyThrows
	public static Connection createOracleConnection(DbData container) {
		Properties properties = new Properties();
		properties.put("username", container.username);
		properties.put("password", container.password);
		properties.put(JDBC_PARAMETERS, "oracle.jdbc.timezoneAsRegion:false");

		Connection connection = DriverManager.getConnection(container.url, properties);
		connection.setAutoCommit(false);
		return connection;
	}

	@SneakyThrows
	public static Connection createOracleConnection(JdbcDatabaseContainer container) {
		Connection connection = DriverManager.getConnection(container.getJdbcUrl(), container.getUsername(), container.getPassword());
		connection.setAutoCommit(false);
		return connection;
	}

	@SneakyThrows
	private static Connection createSpannerConnection(DbData container) {
		initSpannerComponents(container);
		return DriverManager.getConnection(container.url);
	}

	@SneakyThrows
	private static void initSpannerComponents(DbData dbData) {
		createSpannerInstance(dbData);
		createSpannerDB(dbData);
	}

	@SneakyThrows
	private static void createSpannerInstance(DbData dbData) {
		int portRest = Integer.parseInt(dbData.username);
		CloseableHttpClient client = HttpClients.createDefault();
		String hostAndPort = "http://localhost:" + portRest;
		String createTestInstanceUrl = hostAndPort + "/v1/projects/kafka-test-project/instances";
		String createTestInstanceJsonBody = "{\n" +
				"  \"instanceId\": \"kafka-test-instance\",\n" +
				"  \"instance\": {\n" +
				"  \"config\": \"test\",\n" +
				"  \"displayName\": \"kafka-test-instance\"\n" +
				"}\n" +
				"}";

		callSpannerLocalApi(createTestInstanceUrl, createTestInstanceJsonBody);
	}

	@SneakyThrows
	private static void createSpannerDB(DbData dbData) {
		int portRest = Integer.parseInt(dbData.username);
		CloseableHttpClient client = HttpClients.createDefault();
		String hostAndPort = "http://localhost:" + portRest;
		String createTestDatabaseUrl = hostAndPort + "/v1/projects/kafka-test-project/instances/kafka-test-instance/databases";
		String createTestDbJsonBody = String.format("{ \"createStatement\": \"CREATE DATABASE %s\"}", dbData.databaseName);

		callSpannerLocalApi(createTestDatabaseUrl, createTestDbJsonBody);
	}

	@SneakyThrows
	private static int callSpannerLocalApi(String url, String jsonBody) {
		CloseableHttpClient client = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(url);
		StringEntity entity = new StringEntity(jsonBody);
		httpPost.setEntity(entity);
		httpPost.setHeader("Accept", "application/json");
		httpPost.setHeader("Content-type", "application/json");

		CloseableHttpResponse response = client.execute(httpPost);
		client.close();
		return response.getStatusLine().getStatusCode();
	}

	private static void dropSpannerDb(int portRest, String databaseName) {
		String hostAndPort = "http://localhost:" + portRest;
		String deleteEndpoint = String.format(
				"%s/v1/projects/kafka-test-project/instances/kafka-test-instance/databases/%s",
				hostAndPort,
				databaseName
		);

		CloseableHttpClient httpclient = HttpClients.createDefault();
		HttpDelete httpDelete = new HttpDelete(deleteEndpoint);
	}


	public static void recreateTablesByContains(DSLContext jooq, DbData db, String... tables) {
		Set<String> tableSet = StreamEx.of(tables).toSet();
		StreamEx
			.of(db.createScript.split("---"))
			.filter(cmd -> {
				String command = cmd.toLowerCase();
				return tableSet.stream().anyMatch(command::contains);
			})
			.forEach(jooq::execute);
	}

	public static void recreateTablesByContainsMySqlMetrics(DSLContext jooq, String... tables) {
		Set<String> tableSet = StreamEx.of(tables).toSet();
		StreamEx
				.of(tableSet)
				.forEach(tableName ->
						jooq.execute("DROP TABLE IF EXISTS " + tableName + ";"));

		StreamEx
				.of(DbUtils.getMysqlSqlScriptMetrics().split("---"))
				.filter(cmd -> {
					String command = cmd.toLowerCase();
					return tableSet.stream().anyMatch(command::contains);
				})
				.forEach(jooq::execute);
	}
}
