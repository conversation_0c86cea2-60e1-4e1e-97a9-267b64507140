package com.nexla.connect.common;

import com.google.common.collect.Lists;
import com.nexla.common.NexlaConstants;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.admin.RecordsToDelete;
import org.apache.kafka.common.errors.UnknownTopicOrPartitionException;
import org.slf4j.MDC;
import org.testcontainers.containers.KafkaContainer;
import com.nexla.common.exception.NexlaQuarantineMessage;
import com.nexla.common.metrics.NexlaRawMetric;
import com.nexla.common.notify.NexlaNotificationEvent;
import com.nexla.common.notify.error.NexlaInternalNotificationEvent;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogEvent;
import com.nexla.test.IntegrationTests;
import lombok.SneakyThrows;
import one.util.streamex.IntStreamEx;
import one.util.streamex.StreamEx;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.source.SourceTask;
import org.junit.contrib.java.lang.system.EnvironmentVariables;
import org.junit.experimental.categories.Category;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import static com.bazaarvoice.jolt.JsonUtils.stringToType;
import static com.nexla.common.NexlaConstants.CONNECT_BOOTSTRAP_SERVERS;
import static com.nexla.common.TraceMessage.NX_RUN_TRACE;
import static com.nexla.connect.common.TestContainerApi.exec;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.toList;

@Category(IntegrationTests.class)
public abstract class BaseKafkaTest {

	private static final Logger logger = LoggerFactory.getLogger(BaseKafkaTest.class);
	private static EnvironmentVariables environmentVariables = new EnvironmentVariables();

	protected Optional<Runnable> runBeforeTopicReading = Optional.empty();
	protected static int ZOOKEEPER_PORT;
	protected static String BOOTSTRAP_SERVERS;
	protected static String ZOOKEEPER_CONNECT;
	protected static String CONTAINER_NAME;

	protected final AtomicReference<KafkaProducer<String, String>> producer = new AtomicReference<>();
	protected final ConcurrentMap<String, KafkaConsumer<String, String>> consumers = new ConcurrentHashMap<>();

	public int getZkPort() {
		return ZOOKEEPER_PORT;
	}

	public String getBsServers() {
		return BOOTSTRAP_SERVERS;
	}

	public String getZkConnect() {
		return ZOOKEEPER_CONNECT;
	}

	public String getContName() {
		return CONTAINER_NAME;
	}

	protected static void init(KafkaContainer kc) {
		BOOTSTRAP_SERVERS = kc.getBootstrapServers();
		ZOOKEEPER_PORT = kc.getMappedPort(2181);
		ZOOKEEPER_CONNECT = "localhost:" + ZOOKEEPER_PORT;
		environmentVariables.set(CONNECT_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
		environmentVariables.set(NexlaConstants.BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
		environmentVariables.set("NODE_ID", "nodeId");
		MDC.put("test.name", "Base kafka test insides - static");
	}

	public void initDynamic(KafkaContainer kc) {
		BOOTSTRAP_SERVERS = kc.getBootstrapServers();
		ZOOKEEPER_PORT = kc.getMappedPort(2181);
		ZOOKEEPER_CONNECT = "localhost:" + ZOOKEEPER_PORT;
		environmentVariables.set(CONNECT_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
		environmentVariables.set("NODE_ID", "nodeId");
		MDC.put("test.name", "Base kafka test insides - dynamic");
	}

	protected void closeConsumers() {
		for (Map.Entry<String, KafkaConsumer<String, String>> kc : this.consumers.entrySet()) {
			kc.getValue().wakeup();
			kc.getValue().close();
		}
	}

	protected void closeProducer() {
		if (this.producer.get() != null) {
			this.producer.get().flush();
			this.producer.get().close();
		}
	}

	protected static void createTopics(String... topics) {
		Properties props = new Properties();
		props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);

		try (AdminClient adminClient = AdminClient.create(props)) {
			for (String topic : topics) {
				NewTopic newTopic = new NewTopic(topic, 1, (short) 1);
				adminClient.createTopics(Collections.singletonList(newTopic)).all().get();
			}
		} catch (ExecutionException | InterruptedException e) {
      throw new RuntimeException(e);
    }
	}

	protected static void deleteTopics(String... topics) {
		Properties props = new Properties();
		props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);

		try (AdminClient adminClient = AdminClient.create(props)) {
			for (String topicName : topics) {
				adminClient.deleteRecords(Collections.singletonMap(
					new org.apache.kafka.common.TopicPartition(topicName, 0),
					RecordsToDelete.beforeOffset(Long.MAX_VALUE)
				)).all().get();
			}
			adminClient.deleteTopics(List.of(topics)).all().get();
		} catch (ExecutionException | InterruptedException e) {
			if (e.getCause() instanceof UnknownTopicOrPartitionException) {
				//skip it
				return;
			}
			throw new RuntimeException(e);
		}
	}

	private static void logExecResult(Map<String, String> execResult) {
		logger.info("DOCKER EXEC=" + execResult.toString());
	}

	protected List<NexlaRawMetric> readMetrics(KafkaConsumer<String, String> consumer, int sinkId) {
		return readTopic(consumer, NexlaRawMetric.class).stream()
			.filter(x -> !NX_RUN_TRACE.equalsIgnoreCase(x.getTags().get("name")) && x.getResourceId().equals(sinkId))
			.collect(toList());
	}

	protected List<NexlaRawMetric> readMetrics(KafkaConsumer<String, String> consumer) {
		return readTopic(consumer, NexlaRawMetric.class).stream()
			.filter(x -> !NX_RUN_TRACE.equalsIgnoreCase(x.getTags().get("name")))
			.collect(toList());
	}

	protected <T> List<T> readTopic(KafkaConsumer<String, String> consumer, Class<T> recordClass) {
		return read(consumer, 2).stream()
			.map(record -> stringToType(record.value(), recordClass))
			.collect(toList());
	}

	protected void withConsumer(BiConsumer<String, KafkaConsumer<String, String>> fn) {
		withConsumer(fn, null);
	}

	protected void withConsumer(String topic, Consumer<KafkaConsumer<String, String>> fn) {
		CompletableFuture.runAsync(() -> createTopics(topic));
		try (KafkaConsumer<String, String> consumer = getOrCreateConsumer(topic)) {
			fn.accept(consumer);
		} finally {
			CompletableFuture.runAsync(() -> deleteTopics(topic));
		}
	}

	protected void withConsumer(BiConsumer<String, KafkaConsumer<String, String>> fn, String topicName) {
		String topic = topicName == null ? genTopic() : topicName;
		CompletableFuture.runAsync(() -> createTopics(topic));
		try (KafkaConsumer<String, String> consumer = getOrCreateConsumer(topic)) {
			fn.accept(topic, consumer);
		}
	}

	private static String genTopic() {
		return UUID.randomUUID().toString();
	}

	protected void withProducer(Consumer<KafkaProducer<String, String>> fn) {
		try (KafkaProducer<String, String> producer = getOrCreateProducer()) {
			fn.accept(producer);
			producer.flush();
			producer.abortTransaction();
			producer.close();
		}
	}

	protected static void withTopic(Consumer<String> fn) {
		String topic = genTopic();
		CompletableFuture.runAsync(() -> createTopics(topic));
		try {
			fn.accept(topic);
		} finally {
			CompletableFuture.runAsync(() -> deleteTopics(topic));
		}
	}

	protected List<NexlaQuarantineMessage> readQuarantine(KafkaConsumer<String, String> consumer) {
		return read(consumer, 2).stream()
			.map(record -> stringToType(record.value(), NexlaQuarantineMessage.class))
			.collect(toList());
	}

	protected List<NexlaNotificationEvent> readNotifications(KafkaConsumer<String, String> consumer) {
		return read(consumer, 2).stream()
			.map(record -> stringToType(record.value(), NexlaNotificationEvent.class)).collect(toList());
	}

	protected List<NexlaMonitoringLogEvent> readMonitoringLogs(KafkaConsumer<String, String> consumer) {
		return read(consumer, 2).stream()
				.map(record -> stringToType(record.value(), NexlaMonitoringLogEvent.class)).collect(toList());
	}

	protected List<NexlaInternalNotificationEvent> readInternalNotifications(KafkaConsumer<String, String> consumer) {
		return read(consumer, 2).stream()
				.map(record -> stringToType(record.value(), NexlaInternalNotificationEvent.class)).collect(toList());
	}

	protected List<ConsumerRecord<String, String>> read(KafkaConsumer<String, String> consumer) {
		return read(consumer, 2);
	}

	protected List<ConsumerRecord<String, String>> read(KafkaConsumer<String, String> consumer, int attempts) {
		runBeforeTopicReading.ifPresent	(Runnable::run);
		return IntStreamEx.constant(1, attempts).boxed()
			.flatMap(i -> StreamEx.of(consumer.poll(1000).iterator()))
			.collect(toList());
	}

	protected KafkaConsumer<String, String> getOrCreateConsumer(String topic) {
		if (this.consumers.get(topic) != null) {
			return this.consumers.get(topic);
		} else {
			Properties props = new Properties();
			props.put("bootstrap.servers", BOOTSTRAP_SERVERS);
			props.put(ConsumerConfig.GROUP_ID_CONFIG, UUID.randomUUID().toString());
			props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
			props.put(ConsumerConfig.METADATA_MAX_AGE_CONFIG, "10000");
			props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
			props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
			props.put("poll.ms", 10000);
			props.put("session.timeout.ms", 10000);
			props.put("request.timeout.ms", 10000);
			KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
			consumer.subscribe(singletonList(topic));
			this.consumers.putIfAbsent(topic, consumer);
			return this.consumers.get(topic);
		}
	}

	protected KafkaProducer<String, String> getOrCreateProducer() {
		if (this.producer.get() == null) {
			Map<String, Object> props = new HashMap<>();
			props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
			props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
			props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
			props.put(ProducerConfig.METADATA_MAX_AGE_CONFIG, "10000");
			props.put("poll.ms", 1000);
			props.put("session.timeout.ms", 1000);
			props.put("request.timeout.ms", 1000);
			this.producer.set(new KafkaProducer<>(props));
			return this.producer.get();
		} else {
			return this.producer.get();
		}
	}

	@SneakyThrows
	public List<SourceRecord> pollNoTrace(SourceTask task) {
		List<SourceRecord> result = Lists.newArrayList();
		List<SourceRecord> poll = Optional.ofNullable(task.poll()).orElse(emptyList());
		for (SourceRecord x: poll) {
			if (!x.value().toString().contains(NX_RUN_TRACE)) {
				result.add(x);
			}
		}
		return result;
	}

}
