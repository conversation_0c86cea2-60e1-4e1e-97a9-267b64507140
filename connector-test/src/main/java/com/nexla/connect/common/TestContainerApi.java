package com.nexla.connect.common;

import com.nexla.common.RestTemplateBuilder;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

import static java.util.Optional.empty;

public class TestContainerApi {

	private static final RestTemplate REST_TEMPLATE = new RestTemplateBuilder().build();

	private static final String BASE_URL = "http://localhost:8181";

	public static Map<String, String> containerInfo(String containerName) {
		return REST_TEMPLATE.getForObject(BASE_URL + "/container/" + containerName, Map.class);
	}

	public static Map<String, String> exec(String containerName, String cmd) {
		return REST_TEMPLATE.postForObject(BASE_URL + "/container/" + containerName + "/exec", cmd, Map.class);
	}

}
