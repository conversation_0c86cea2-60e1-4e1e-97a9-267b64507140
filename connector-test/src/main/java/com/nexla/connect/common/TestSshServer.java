package com.nexla.connect.common;

import com.nexla.common.io.NexlaIO;
import lombok.SneakyThrows;
import org.apache.sshd.common.file.virtualfs.VirtualFileSystemFactory;
import org.apache.sshd.server.SshServer;
import org.apache.sshd.server.keyprovider.SimpleGeneratorHostKeyProvider;
import org.apache.sshd.sftp.server.SftpSubsystemFactory;
import org.junit.rules.ExternalResource;

import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.util.List;

public class TestSshServer extends ExternalResource {

	public static final String FTP_USERNAME = "username";
	public static final String FTP_PASSWORD = "password";

	private final Path dir;

	public SshServer ftpServer;

	public TestSshServer(Path dir) {
		this.dir = dir;
	}

	@Override
	public void before() {
		this.ftpServer = createSftpServer();
	}

	@SneakyThrows
	@Override
	public void after() {
		ftpServer.stop();
	}

	@SneakyThrows
	private SshServer createSftpServer() {
		SshServer sshd = SshServer.setUpDefaultServer();

		NexlaIO.Port freePort = NexlaIO.findFreePort(2221, 2300);

		sshd.setPort(freePort.port);
		sshd.setKeyPairProvider(new SimpleGeneratorHostKeyProvider());
		sshd.setPasswordAuthenticator((username, password, session)
			-> FTP_USERNAME.equals(username) && FTP_PASSWORD.equals(password));
		sshd.setSubsystemFactories(List.of(new SftpSubsystemFactory()));
		sshd.setFileSystemFactory(new VirtualFileSystemFactory(FileSystems.getDefault().getPath(dir.toString())));

		freePort.socket.close();
		sshd.start();
		return sshd;
	}
}
