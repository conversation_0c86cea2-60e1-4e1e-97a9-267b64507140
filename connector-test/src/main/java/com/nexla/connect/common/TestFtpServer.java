package com.nexla.connect.common;

import com.nexla.common.io.NexlaIO;
import com.nexla.common.io.NexlaIO.Port;
import lombok.SneakyThrows;
import org.apache.ftpserver.FtpServer;
import org.apache.ftpserver.FtpServerFactory;
import org.apache.ftpserver.ftplet.Authority;
import org.apache.ftpserver.ftplet.UserManager;
import org.apache.ftpserver.listener.ListenerFactory;
import org.apache.ftpserver.usermanager.PropertiesUserManagerFactory;
import org.apache.ftpserver.usermanager.impl.BaseUser;
import org.junit.rules.ExternalResource;

import java.nio.file.Path;
import java.util.List;

import static java.util.Collections.emptyList;

public class TestFtpServer extends ExternalResource {

	public static final String FTP_USERNAME = "username";
	public static final String FTP_PASSWORD = "password";

	private final Path dir;
	private List<Authority> permissions = emptyList();

	private FtpServer ftpServer;
	public int port;

	public TestFtpServer(Path dir) {
		this.dir = dir;
	}

	public TestFtpServer(Path dir, List<Authority> permissions) {
		this.dir = dir;
		this.permissions = permissions;
	}

	@SneakyThrows
	@Override
	public void before() {
		this.ftpServer = createFtpServer();
	}

	@Override
	public void after() {
		ftpServer.stop();
	}

	public String getUserName() {
		return FTP_USERNAME;
	}

	public String getPassword() {
		return FTP_PASSWORD;
	}

	@SneakyThrows
	private FtpServer createFtpServer() {
		PropertiesUserManagerFactory userManagerFactory = new PropertiesUserManagerFactory();
		UserManager userManager = userManagerFactory.createUserManager();
		BaseUser user = new BaseUser();
		user.setName(FTP_USERNAME);
		user.setPassword(FTP_PASSWORD);

		user.setAuthorities(permissions);

		user.setHomeDirectory(dir.toString());
		userManager.save(user);

		Port freePort = NexlaIO.findFreePort(2221, 2300);
		this.port = freePort.port;

		ListenerFactory listenerFactory = new ListenerFactory();
		listenerFactory.setPort(freePort.port);

		FtpServerFactory factory = new FtpServerFactory();
		factory.setUserManager(userManager);
		factory.addListener("default", listenerFactory.createListener());

		FtpServer server = factory.createServer();
		freePort.socket.close();
		server.start();
		return server;
	}
}
