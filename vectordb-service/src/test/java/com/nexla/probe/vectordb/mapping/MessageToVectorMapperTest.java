package com.nexla.probe.vectordb.mapping;

import com.nexla.common.NexlaMessage;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.vectordb.mapping.VectorMappingConfig;
import com.nexla.probe.pinecone.PineconeMetadataConverter;
import com.nexla.test.UnitTests;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Assert;
import org.junit.Rule;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.junit.rules.ExpectedException;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import static com.nexla.connector.config.vectordb.mapping.MetadataMappingType.DELETE;
import static com.nexla.connector.config.vectordb.mapping.MetadataMappingType.SELECT;
import static com.nexla.connector.config.vectordb.mapping.VectorMappingConfig.MODE_AUTO;
import static com.nexla.connector.config.vectordb.mapping.VectorMappingConfig.MODE_MANUAL;

@Category(UnitTests.class)
public class MessageToVectorMapperTest {
    private PineconeMetadataConverter defaultMetadataConverter = new PineconeMetadataConverter();
    private TopicPartition testTopic = new TopicPartition("test", 0);

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Test
    public void testDefaultMappingOfValidStructure() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
        }});

        var vector = messageToVectorMapper.extractVector(message);

        Assert.assertEquals("some_id", vector.getVectorID().get().getId());
        Assert.assertEquals(2, vector.getDenseValues().get().size());
        Assert.assertEquals(true, vector.getMetadata().isEmpty());
    }

    @Test
    public void testDefaultMetadataFieldDiscoveredInAutoMode() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_AUTO);

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
            put("metadata", "{\"tag\": \"value1\"}");
        }});

        var vector = messageToVectorMapper.extractVector(message);

        Assert.assertEquals("some_id", vector.getVectorID().get().getId());
        Assert.assertEquals(2, vector.getDenseValues().get().size());
        Assert.assertEquals("value1", vector.getMetadata().get().get("tag"));
    }

    @Test
    public void testNoIdField() {
        exceptionRule.expect(MappingException.class);
        exceptionRule.expectMessage("Vector id field 'id' is required");

        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
        }});

        messageToVectorMapper.extractVector(message);
    }

    @Test
    public void testNoIdFieldRemapped() {
        exceptionRule.expect(MappingException.class);
        exceptionRule.expectMessage("Vector id field 'my_id' is required");

        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);
        vectorMappingConfig.setIdFieldPath("my_id");

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
        }});

        messageToVectorMapper.extractVector(message);
    }

    @Test
    public void testSimpleRemappingIdField() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);
        vectorMappingConfig.setIdFieldPath("my_id");

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("my_id", "some_id");
            put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
        }});

        var vector = messageToVectorMapper.extractVector(message);

        Assert.assertEquals("some_id", vector.getVectorID().get().getId());
    }

    @Test
    public void testJsonPathRemappingIdField() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);
        vectorMappingConfig.setIdFieldPath("$.parent.id");

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("parent", new HashMap<>() {{
                put("id", "some_id");
            }});
            put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
        }});

        var vector = messageToVectorMapper.extractVector(message);

        Assert.assertEquals("some_id", vector.getVectorID().get().getId());
    }

    @Test
    public void testIdFieldAsNumber() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", 123);
            put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
        }});

        var vector = messageToVectorMapper.extractVector(message);

        Assert.assertEquals("123", vector.getVectorID().get().getId());
    }

    @Test
    public void testDenseVectorRemapping() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);
        vectorMappingConfig.setDenseValuesFieldPath("my_vector");

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("my_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
        }});

        var vector = messageToVectorMapper.extractVector(message);
        Assert.assertEquals(2, vector.getDenseValues().get().size());
    }

    @Test
    public void testDenseVectorDoubles() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("dense_vector", List.of(RandomUtils.nextDouble(), RandomUtils.nextDouble()));
        }});

        var vector = messageToVectorMapper.extractVector(message);
        Assert.assertEquals(2, vector.getDenseValues().get().size());
    }

    @Test
    public void testDenseVectorValueAsStrings() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("dense_vector", List.of("0.1", "0.2"));
        }});

        var vector = messageToVectorMapper.extractVector(message);
        Assert.assertEquals(2, vector.getDenseValues().get().size());
    }

    @Test
    public void testDenseVectorWrongTypes() {
        exceptionRule.expect(MappingException.class);
        exceptionRule.expectMessage("Vector value must be a number or string that can be parsed as number");

        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("dense_vector", List.of("a", "b"));
        }});

        messageToVectorMapper.extractVector(message);
    }

    @Test
    public void testDenseVectorWrongTypes2() {
        exceptionRule.expect(MappingException.class);
        exceptionRule.expectMessage("Vector value must be a number or string that can be parsed as number");

        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("dense_vector", List.of(true, false));
        }});

        messageToVectorMapper.extractVector(message);
    }

    @Test
    public void testDenseVectorRemappingJsonPath() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);
        vectorMappingConfig.setDenseValuesFieldPath("$.nested.vector_data");

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("nested", new HashMap<>() {{
                put("vector_data", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
            }});
        }});

        var vector = messageToVectorMapper.extractVector(message);
        Assert.assertEquals(2, vector.getDenseValues().get().size());
    }

    @Test
    public void testSparseVector() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, false, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("sparse_values", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
            put("sparse_indices", List.of(2, 5));
        }});

        var vector = messageToVectorMapper.extractVector(message);

        Assert.assertEquals("some_id", vector.getVectorID().get().getId());
        Assert.assertEquals(2, vector.getSparseValues().get().size());
        Assert.assertEquals(2, vector.getSparseIndices().get().size());
        Assert.assertEquals(true, vector.getMetadata().isEmpty());
    }

    @Test
    public void testVectorData() {
        exceptionRule.expect(MappingException.class);
        exceptionRule.expectMessage("Vector field 'dense_vector' is required");

        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
        }});

        messageToVectorMapper.extractVector(message);
    }

    @Test
    public void testSparseVectorRemapping() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);
        vectorMappingConfig.setSparseValuesFieldPath("my_values");
        vectorMappingConfig.setSparseIndexesFieldPath("my_indices");

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, false, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("my_values", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
            put("my_indices", List.of(2, 5));
        }});

        var vector = messageToVectorMapper.extractVector(message);

        Assert.assertEquals("some_id", vector.getVectorID().get().getId());
        Assert.assertEquals(2, vector.getSparseValues().get().size());
        Assert.assertEquals(2, vector.getSparseIndices().get().size());
        Assert.assertEquals(true, vector.getMetadata().isEmpty());
    }

    @Test
    public void testRemappingWholeMetadata() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);
        vectorMappingConfig.setMetadataMapping(List.of(
            new VectorMappingConfig.MetadataMapping(SELECT, "", "custom_metadata")
        ));

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
            put("custom_metadata", new HashMap<>() {{
                put("tag", "value1");
            }});
        }});

        var vector = messageToVectorMapper.extractVector(message);

        Assert.assertEquals("value1", vector.getMetadata().get().get("tag"));
    }

    @Test
    public void testRemappingWholeMetadataAsNested() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);
        vectorMappingConfig.setMetadataMapping(List.of(
            new VectorMappingConfig.MetadataMapping(SELECT, "metadata", "custom")
        ));

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
            put("custom", new HashMap<>() {{
                put("tag", "value1");
            }});
        }});

        var vector = messageToVectorMapper.extractVector(message);

        Assert.assertEquals("value1", vector.getMetadata().get().get("metadata_tag"));
    }

    @Test
    public void testManuallySelectMetadataFields() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);
        vectorMappingConfig.setMetadataMapping(List.of(
            new VectorMappingConfig.MetadataMapping(SELECT, "color", "some_color"),
            new VectorMappingConfig.MetadataMapping(SELECT, "age", "some_age"),
            new VectorMappingConfig.MetadataMapping(SELECT, "tag", "$.nested_filed.tag")
        ));

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
            put("some_color", "red");
            put("some_age", "30");
            put("nested_filed", new HashMap<>() {{
                put("tag", "value1");
            }});
        }});

        var vector = messageToVectorMapper.extractVector(message);

        Assert.assertEquals("red", vector.getMetadata().get().get("color"));
        Assert.assertEquals("30", vector.getMetadata().get().get("age"));
        Assert.assertEquals("value1", vector.getMetadata().get().get("tag"));
    }

    @Test
    public void testJoinComplexMetadataFields() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_MANUAL);
        vectorMappingConfig.setMetadataMapping(List.of(
            new VectorMappingConfig.MetadataMapping(SELECT, "", "complex1"),
            new VectorMappingConfig.MetadataMapping(SELECT, "", "complex2")
        ));

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));

            put("complex1", new HashMap<>() {{
                put("tag1", "value1");
                put("tag2", "value2");
            }});
            put("complex2", new HashMap<>() {{
                put("tag3", "value3");
            }});
        }});

        var vector = messageToVectorMapper.extractVector(message);

        Assert.assertEquals("value1", vector.getMetadata().get().get("tag1"));
        Assert.assertEquals("value2", vector.getMetadata().get().get("tag2"));
        Assert.assertEquals("value3", vector.getMetadata().get().get("tag3"));
    }

    @Test
    public void testRemoveTagFromAutoDiscovery() {
        VectorMappingConfig vectorMappingConfig = new VectorMappingConfig();
        vectorMappingConfig.setMode(MODE_AUTO);
        vectorMappingConfig.setMetadataMapping(List.of(
            new VectorMappingConfig.MetadataMapping(DELETE, "some_age", null)
        ));

        MessageToVectorMapper messageToVectorMapper = new MessageToVectorMapper(vectorMappingConfig, defaultMetadataConverter, true, false);

        var message = makeMessage(new LinkedHashMap<>(){{
            put("id", "some_id");
            put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat()));
            put("some_color", "red");
            put("some_age", "30");
        }});

        var vector = messageToVectorMapper.extractVector(message);

        Assert.assertEquals(1, vector.getMetadata().get().size());
        Assert.assertEquals("red", vector.getMetadata().get().get("some_color"));
    }

    private NexlaMessageContext makeMessage(LinkedHashMap<String, Object> rawMessage) {
        NexlaMessage nexlaMessage = new NexlaMessage(rawMessage);
        return new NexlaMessageContext(nexlaMessage, nexlaMessage, testTopic, 0L);
    }
}
