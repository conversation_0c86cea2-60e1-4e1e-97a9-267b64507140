package com.nexla.probe.pinecone;

import com.nexla.test.UnitTests;
import org.junit.Assert;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import java.util.HashMap;
import java.util.List;

@Category(UnitTests.class)
public class PineconeMetadataConverterTest {

    PineconeMetadataConverter converter = new PineconeMetadataConverter();

    @Test
    public void testValidTypes() {
        var result = converter.convert(new HashMap<>() {{
            put("string", "string");
            put("number", 1);
            put("float", 1.2);
            put("boolean", true);
            put("list", List.of("a", "b"));
        }});
        Assert.assertEquals(5, result.size());
        Assert.assertEquals("string", result.get("string"));
        Assert.assertEquals(1, result.get("number"));
        Assert.assertEquals(1.2, result.get("float"));
        Assert.assertEquals(true, result.get("boolean"));
        Assert.assertEquals(List.of("a", "b"), result.get("list"));
    }

    @Test
    public void testNestedMaps() {
        var result = converter.convert(new HashMap<>() {{
            put("string", "string");
            put("nested", new HashMap<>() {{
                put("number", 1);
                put("float", 1.2);
                put("boolean", true);
                put("list", List.of("a", "b"));
            }});
        }});
        Assert.assertEquals(5, result.size());
        Assert.assertEquals("string", result.get("string"));
        Assert.assertEquals(1, result.get("nested_number"));
        Assert.assertEquals(1.2, result.get("nested_float"));
        Assert.assertEquals(true, result.get("nested_boolean"));
        Assert.assertEquals(List.of("a", "b"), result.get("nested_list"));
    }

    @Test
    public void testSkipUnsupportedTypes() {
        var result = converter.convert(new HashMap<>() {{
            put("string", "string");
            put("list", List.of(1, 2));
            put("nulls", null);
        }});
        Assert.assertEquals(1, result.size());
        Assert.assertEquals("string", result.get("string"));
    }
}
