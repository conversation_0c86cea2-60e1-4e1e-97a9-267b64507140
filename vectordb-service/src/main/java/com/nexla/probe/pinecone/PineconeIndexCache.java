package com.nexla.probe.pinecone;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.pinecone.clients.Index;
import io.pinecone.clients.Pinecone;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class PineconeIndexCache {

    private static final Logger logger = LoggerFactory.getLogger(PineconeIndexCache.class);

    @AllArgsConstructor
    @Data
    public static class PineconeCacheKey {
        private Integer credsId;
        private String apiKey;
        private String indexName;
    }

    private final LoadingCache<PineconeCacheKey, Index> indexCache;
    private final PineconeClientProvider pineconeClientProvider = new PineconeClientProvider();
    private final ScheduledExecutorService cleanupExecutor =
            Executors.newSingleThreadScheduledExecutor(
                    new ThreadFactoryBuilder().setNameFormat("PineconeIndexCache-%d").setDaemon(true).build());

    private static Index testInstance = null;

    public PineconeIndexCache() {
        this.indexCache = CacheBuilder
                .newBuilder()
                .expireAfterAccess(15, TimeUnit.MINUTES)
                .removalListener(notification -> {
                    PineconeCacheKey cacheKey = (PineconeCacheKey) notification.getKey();
                    logger.info("Pinecone: Close index for credsId: {}, indexName: {}", cacheKey.getCredsId(), cacheKey.getIndexName());
                    Index index = (Index) notification.getValue();
                    index.close();
                })
                .build(new CacheLoader() {
                    @Override
                    public Object load(Object key) throws Exception {
                        PineconeCacheKey cacheKey = (PineconeCacheKey) key;
                        logger.info("Pinecone: Loading index for credsId: {}, indexName: {}", cacheKey.getCredsId(), cacheKey.getIndexName());
                        Pinecone client = pineconeClientProvider.createPineconeClient(cacheKey.getApiKey());
                        Index index = client.getIndexConnection(cacheKey.getIndexName());
                        return index;
                    }
                });
        cleanupExecutor.scheduleAtFixedRate(() -> {
            if (indexCache.size() > 0) {
                logger.debug("Pinecone: Check cache for cleanup");
                indexCache.cleanUp();
            }
        }, 1, 1, TimeUnit.MINUTES);
    }

    @VisibleForTesting
    public static void setTestIndex(Index index) {
        testInstance = index;
    }

    @SneakyThrows
    public Index getIndex(Integer credsId, String apiKey, String indexName) {
        if (testInstance != null) {
            return testInstance;
        }
        return indexCache.get(new PineconeCacheKey(credsId, apiKey, indexName));
    }
}
