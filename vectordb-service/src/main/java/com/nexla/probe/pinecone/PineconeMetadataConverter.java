package com.nexla.probe.pinecone;

import com.nexla.probe.vectordb.mapping.MetadataConverter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class PineconeMetadataConverter implements MetadataConverter {

    @Override
    public Map<String, Object> convert(Map<String, Object> metadata) {
        var finalMetadata = new LinkedHashMap<String, Object>();
        collectSupportedFields(metadata, finalMetadata, "");
        return finalMetadata;
    }

    private void collectSupportedFields(Map<String, Object> originalMetadata, Map<String, Object> finalMetadata, String path) {
        originalMetadata.forEach((key, value) -> {
            // convert nested maps to flat map and only keep supported types
            var newKey = "".equals(path) ? key : path + "_" + key;
            if (value instanceof Map) {
                collectSupportedFields((Map<String, Object>) value, finalMetadata, newKey);
            } else {
                var supportedSimpleTypes = value instanceof String || value instanceof Number || value instanceof Boolean;
                if (supportedSimpleTypes) {
                    finalMetadata.put(newKey, value);
                } else
                if (value instanceof List) {
                    var isListOfStrings = ((List) value).stream().allMatch(x -> x instanceof String);
                    if (isListOfStrings) {
                        finalMetadata.put(newKey, value);
                    }
                }
            }
        });
    }
}
