package com.nexla.probe.vectordb.mapping;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.api.client.util.Lists;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.vectordb.mapping.MetadataMappingType;
import com.nexla.connector.config.vectordb.mapping.VectorMappingConfig;
import com.nexla.probe.vectordb.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.bazaarvoice.jolt.JsonUtils.jsonToMap;

public class MessageToVectorMapper {
    private static final Logger LOGGER = LoggerFactory.getLogger(MessageToVectorMapper.class);

    public static final String DEFAULT_METADATA_PATH = "metadata";

    public static final List<VectorMappingConfig.MetadataMapping> DEFAULT_MAPPING_METADATA =
            List.of(new VectorMappingConfig.MetadataMapping(MetadataMappingType.SELECT,"", DEFAULT_METADATA_PATH));

    public static final List<VectorMappingConfig.MetadataMapping> DEFAULT_MAPPING_ALL =
            List.of(new VectorMappingConfig.MetadataMapping(MetadataMappingType.SELECT,"","*"));

    private final VectorMappingConfig mapping;
    private final MetadataConverter metadataConverter;
    private final boolean denseFieldRequired;
    private final boolean sparseFieldRequired;

    public MessageToVectorMapper(VectorMappingConfig mapping, MetadataConverter metadataConverter,
                                 boolean denseFieldRequired, boolean sparseFieldRequired) {
        this.mapping = mapping;
        this.metadataConverter = metadataConverter;
        this.denseFieldRequired = denseFieldRequired;
        this.sparseFieldRequired = sparseFieldRequired;
    }

    public VectorData extractVector(NexlaMessageContext messageContext) {
        var message = messageContext.mapped;
        var rawMessage = message.getRawMessage();

        var id = extractIdField(rawMessage);
        if (id.isEmpty()) {
            var idFieldPath = this.mapping.getIdFieldPath();
            throw new MappingException("Vector id field '" + idFieldPath + "' is required");
        }

        var metadata = extractMetadata(rawMessage).map(metadataConverter::convert);

        var denseFieldPath = this.mapping.getDenseValuesFieldPath();
        var denseVectorField = findFieldByPath(denseFieldPath, rawMessage);

        var sparseValuesFieldPath = this.mapping.getSparseValuesFieldPath();
        var sparseValuesVectorField = findFieldByPath(sparseValuesFieldPath, rawMessage);

        var sparseIndexesFieldPath = this.mapping.getSparseIndexesFieldPath();
        var sparseIndexesVectorField = findFieldByPath(sparseIndexesFieldPath, rawMessage);

        if (denseFieldRequired && denseVectorField.isEmpty()) {
            throw new MappingException("Vector field '" + denseFieldPath + "' is required");
        }

        if (sparseFieldRequired && sparseValuesVectorField.isEmpty()) {
            throw new MappingException("Sparse value field '" + sparseValuesFieldPath + "' is required");
        }

        if (sparseFieldRequired && sparseIndexesVectorField.isEmpty()) {
            throw new MappingException("Sparse index field '" + sparseIndexesFieldPath + "' is required");
        }

        var denseVector = denseVectorField.isPresent() ?
                extractDenseVectorField(rawMessage) : Optional.<List<Number>>empty();

        var sparseValuesVector = sparseValuesVectorField.isPresent() ?
                extractSparseValuesVectorField(rawMessage) : Optional.<List<Number>>empty();

        var sparseIndexesVector = sparseIndexesVectorField.isPresent() ?
                extractSparseIndexesVectorField(rawMessage) : Optional.<List<Long>>empty();

        return new VectorData(id, denseVector, sparseIndexesVector, sparseValuesVector, metadata, Optional.empty());
    }

    private Optional<Map<String, Object>> extractMetadata(Map<String, Object> rawMessage) {
        // JsonPath will modify the original object, so we need to make writable copy
        var rawMessageCopy = JsonUtils.cloneJson(rawMessage);

        // Default metadata template to all fields
        var metadataTemplate = JsonPath.parse(rawMessageCopy);
        var metadataTemplateJson = ((Map<String, Object>)metadataTemplate.json());

        // Cleanup mapping from fields that we know are not metadata
        removeMetadataByJsonPath(metadataTemplate, this.mapping.getIdFieldPath());
        removeMetadataByJsonPath(metadataTemplate, this.mapping.getDenseValuesFieldPath());
        removeMetadataByJsonPath(metadataTemplate, this.mapping.getSparseIndexesFieldPath());
        removeMetadataByJsonPath(metadataTemplate, this.mapping.getSparseValuesFieldPath());

        // Final metadata mapping
        List<VectorMappingConfig.MetadataMapping> mappings = Lists.newArrayList();
        if (!this.mapping.isManual()) {
            if (metadataTemplateJson.containsKey(DEFAULT_METADATA_PATH)) {
                mappings.addAll(DEFAULT_MAPPING_METADATA);
            } else {
                mappings.addAll(DEFAULT_MAPPING_ALL);
            }
        }
        mappings.addAll(this.mapping.getMetadataMapping());


        var selectedMetadata = JsonPath.parse("{}");
        var selectedMetadataJson = ((Map<String, Object>)selectedMetadata.json());

        mappings.stream().forEach(m -> {
            switch (m.getType()) {
                case SELECT:
                    if (m.getFrom() == null || m.getFrom().isEmpty()) {
                        throw new MappingException("Metadata mapping 'from' field is required for SELECT type");
                    }
                    var selectedTemplateDataOpt = findFieldByPath(m.getFrom(), metadataTemplateJson);
                    selectedTemplateDataOpt.ifPresent(selectedTemplateData -> {
                        if ((m.getField() == null || m.getField().isEmpty())) {
                            if (selectedTemplateData instanceof Map) {
                                selectedMetadataJson.putAll((Map) selectedTemplateData);
                            } else if (selectedTemplateData instanceof String) {
                                selectedMetadataJson.putAll(jsonToMap(selectedTemplateData.toString()));
                            } else {
                                LOGGER.warn("Metadata is not a not a hash map: {}", selectedTemplateData);
                            }
                        } else {
                            selectedMetadataJson.put(m.getField(), selectedTemplateData);
                        }
                    });
                    break;
                case DELETE:
                    if (m.getField() == null || m.getField().isEmpty()) {
                        throw new MappingException("'field' is required for DELETE type");
                    }
                    removeMetadataByJsonPath(selectedMetadata, m.getField());
                    break;
                default:
                    throw new MappingException("Unsupported metadata mapping type: " + m.getType());
            }
        });

        return Optional.ofNullable(selectedMetadataJson).filter(m -> !m.isEmpty());
    }

    private void removeMetadataByJsonPath(DocumentContext context, String path) {
        if (path == null || path.isEmpty()) {
            return;
        }
        try {
            if (path.equals("*")) {
                context.delete("$..*");
            }
            if (path.startsWith("$")) {
                context.delete(path);
            }
            // TODO: escape special characters
            context.delete("$.['" + path + "']");
        } catch (PathNotFoundException e) {
            // Ignore missing fields
        }
    }

    private Optional<List<Number>> extractDenseVectorField(Map<String, Object> rawMessage) {
        var path = this.mapping.getDenseValuesFieldPath();
        var denseVectorField = findFieldByPath(path, rawMessage);
        return denseVectorField.map(field -> {
            if (field instanceof List) {
                return ((List<Object>)field).stream().map(this::extractVectorValue).collect(Collectors.toList());
            }
            throw new MappingException("Dense vector field '" + path + "' must be an array of numbers");
        });
    }

    private Optional<List<Number>> extractSparseValuesVectorField(Map<String, Object> rawMessage) {
        var path = this.mapping.getSparseValuesFieldPath();
        var sparseValuesVectorField = findFieldByPath(path, rawMessage);
        return sparseValuesVectorField.map(field -> {
            if (field instanceof List) {
                return ((List<Object>)field).stream().map(this::extractVectorValue).collect(Collectors.toList());
            }
            throw new MappingException("Sparse value field '" + path + "' must be an array of float numbers");
        });
    }

    private Optional<List<Long>> extractSparseIndexesVectorField(Map<String, Object> rawMessage) {
        var path = this.mapping.getSparseIndexesFieldPath();
        var sparseIndexesVectorField = findFieldByPath(path, rawMessage);
        return sparseIndexesVectorField.map(field -> {
            if (field instanceof List) {
                return ((List<Object>)field).stream().map(this::extractIndexValue).collect(Collectors.toList());
            }
            throw new MappingException("Sparse index field '" + path + "'  must be an array of integer numbers");
        });
    }

    private Optional<VectorID> extractIdField(Map<String, Object> rawMessage) {
        var path = this.mapping.getIdFieldPath();
        var idField = findFieldByPath(path, rawMessage);
        return idField.map(field -> {
            if (field instanceof String) {
                return new StringVectorID((String)field);
            }
            if (field instanceof Number) {
                return new LongVectorID(((Number)field).longValue());
            }
            throw new MappingException("ID field '" + path + "' must be a string or a number");
        });
    }

    private Optional<Object> findFieldByPath(String fieldPath, Map<String, Object> rawMessage) {
        if (fieldPath == null || fieldPath.isEmpty()) {
            return Optional.empty();
        }
        if (fieldPath.startsWith("$")) {
            try {
                return Optional.ofNullable(JsonPath.read(rawMessage, fieldPath));
            } catch (PathNotFoundException e) {
                // Ignore missing fields
                return Optional.empty();
            }
        }
        if ("*".equals(fieldPath)) {
            return Optional.ofNullable(rawMessage);
        }
        return Optional.ofNullable(rawMessage.get(fieldPath));
    }

    private Number extractVectorValue(Object value) {
        if (value instanceof Number) {
            return (Number)value;
        }
        if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                throw new MappingException("Vector value must be a number or string that can be parsed as number");
            }
        }
        throw new MappingException("Vector value must be a number or string that can be parsed as number");
    }

    private long extractIndexValue(Object value) {
        if (value instanceof Number) {
            return ((Number)value).longValue();
        }
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                throw new MappingException("Vector index must be a number or string that can be parsed as number");
            }
        }
        throw new MappingException("Vector index must be a number or string that can be parsed as number");
    }
}
