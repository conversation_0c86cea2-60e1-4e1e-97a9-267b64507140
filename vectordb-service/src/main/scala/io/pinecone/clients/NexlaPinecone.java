package io.pinecone.clients;

import io.pinecone.configs.PineconeConfig;
import io.pinecone.configs.PineconeConnection;
import io.pinecone.configs.ProxyConfig;
import io.pinecone.exceptions.PineconeConfigurationException;
import okhttp3.OkHttpClient;
import org.openapitools.control.client.ApiClient;
import org.openapitools.control.client.api.ManageIndexesApi;

import java.net.InetSocketAddress;
import java.net.Proxy;

/* Wrapper for Pinecone API to fix some concurrency and cache issues */
public class NexlaPinecone extends Pinecone {
    private final PineconeConfig config;

    public NexlaPinecone(PineconeConfig config, ManageIndexesApi manageIndexesApi) {
        super(config, manageIndexesApi);
        this.config = config;
    }

    /*
     * We don't cache connections in the NexlaPinecone
     * Connections are heavy objects and should be closed after use (grpc connections, etc)
     */
    @Override PineconeConnection getConnection(String indexName) {
        return new PineconeConnection(this.config);
    }

    public static class Builder {
        private final String apiKey;
        private String sourceTag;
        private ProxyConfig proxyConfig;
        private OkHttpClient customOkHttpClient;

        public Builder(String apiKey) {
            this.apiKey = apiKey;
        }

        public NexlaPinecone.Builder withSourceTag(String sourceTag) {
            this.sourceTag = sourceTag;
            return this;
        }

        public NexlaPinecone.Builder withOkHttpClient(OkHttpClient okHttpClient) {
            this.customOkHttpClient = okHttpClient;
            return this;
        }

        public NexlaPinecone.Builder withProxy(String proxyHost, int proxyPort) {
            this.proxyConfig = new ProxyConfig(proxyHost, proxyPort);
            return this;
        }

        public NexlaPinecone build() {
            PineconeConfig config = new PineconeConfig(this.apiKey, this.sourceTag, this.proxyConfig);
            config.validate();
            if (this.proxyConfig != null && this.customOkHttpClient != null) {
                throw new PineconeConfigurationException("Invalid configuration: Both Custom OkHttpClient and Proxy are set. Please configure only one of these options.");
            } else {
                ApiClient apiClient = this.customOkHttpClient != null ? new ApiClient(this.customOkHttpClient) : new ApiClient(this.buildOkHttpClient());
                apiClient.setApiKey(config.getApiKey());
                apiClient.setUserAgent(config.getUserAgent());
                apiClient.addDefaultHeader("X-Pinecone-Api-Version", "2024-07");
                if (Boolean.parseBoolean(System.getenv("PINECONE_DEBUG"))) {
                    apiClient.setDebugging(true);
                }

                ManageIndexesApi manageIndexesApi = new ManageIndexesApi();
                manageIndexesApi.setApiClient(apiClient);
                return new NexlaPinecone(config, manageIndexesApi);
            }
        }

        private OkHttpClient buildOkHttpClient() {
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            if (this.proxyConfig != null) {
                Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(this.proxyConfig.getHost(), this.proxyConfig.getPort()));
                builder.proxy(proxy);
            }

            return builder.build();
        }
    }
}
