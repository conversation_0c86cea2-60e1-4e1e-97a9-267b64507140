package com.nexla.probe.vectordb

import com.bazaarvoice.jolt.JsonUtils
import com.nexla.common.exception.ProbeRetriableException
import com.nexla.common.metrics.RecordMetric
import com.nexla.common.probe.{ProbeSampleResultEntry, StringSampleResult}
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.connector.config.vectordb.{VectorSinkConnectorConfig, VectorSourceConnectorConfig, VectorTreeService}
import com.nexla.connector.properties.VectorDbConfigAccessor
import com.nexla.connector.{ConnectorService, NexlaMessageContext}
import com.nexla.probe.vectordb.VectorDbConnectorService.defaultSampleRows
import one.util.streamex.StreamEx
import org.apache.kafka.common.config.AbstractConfig

import java.util
import java.util.Optional
import scala.util.{Try, Using}

abstract class VectorDbConnectorService[T <: BaseAuthConfig] extends ConnectorService[T] with VectorTreeService[VectorSourceConnectorConfig]{

  def bulkWrite(config: VectorSinkConnectorConfig, messageList: Seq[NexlaMessageContext])(implicit recordMetric: RecordMetric): Try[Unit]

  override def readSample(c: AbstractConfig, rawNotUsed: Boolean): StringSampleResult = {
    val config: VectorSourceConnectorConfig = c.asInstanceOf[VectorSourceConnectorConfig]

    val stringSampleResult = Using(stream(config, Some(defaultSampleRows))) { closeableStream =>
      val samples: StreamEx[ProbeSampleResultEntry[String]] = closeableStream
        .limit(defaultSampleRows)
        .map[util.LinkedHashMap[String, AnyRef]](_.toNexlaMessage().getRawMessage)
        .map[String](JsonUtils.toJsonString)
        .map(s => new ProbeSampleResultEntry[String](s))

      new StringSampleResult(samples.toList, Optional.empty[String], true)
    }.get
    stringSampleResult
  }

  final def stream(config: VectorSourceConnectorConfig, sampleLimitHint: Option[Int]): StreamEx[VectorData] = {
    config.getQueryType match {
      case VectorDbConfigAccessor.FETCH_VECTORS => fetchVectors(config, sampleLimitHint)
      case VectorDbConfigAccessor.FETCH_IDS => fetchIDs(config, sampleLimitHint)
      case VectorDbConfigAccessor.SIMILARITY_SEARCH => similaritySearch(config, sampleLimitHint)
      case _ => throw new ProbeRetriableException(s"Unknown query type: ${config.getQueryType}")
    }
  }

  def fetchVectors(config: VectorSourceConnectorConfig, sampleLimitHint: Option[Int]): StreamEx[VectorData]
  def fetchIDs(config: VectorSourceConnectorConfig, sampleLimitHint: Option[Int]): StreamEx[VectorData]
  def similaritySearch(config: VectorSourceConnectorConfig, sampleLimitHint: Option[Int]): StreamEx[VectorData]
}

object VectorDbConnectorService {
  val defaultSampleRows = 10
}