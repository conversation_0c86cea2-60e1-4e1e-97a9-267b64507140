package com.nexla.probe.pinecone

import com.nexla.common.logging.{<PERSON><PERSON>la<PERSON><PERSON><PERSON><PERSON>, NexlaLogger}
import com.nexla.common.metrics.RecordMetric
import com.nexla.common.metrics.RecordMetric.quarantineMessage
import com.nexla.common.{NexlaBucket, NexlaFile, ResourceType}
import com.nexla.connector.ConnectorService.AuthResponse
import com.nexla.connector.ConnectorService.AuthResponse.{SUCCESS, authError}
import com.nexla.connector.NexlaMessageContext
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.connector.config.vectordb.pinecone.{PineconeAuthConfig, PineconeSinkConfig, PineconeSourceConfig}
import com.nexla.connector.config.vectordb.{VectorSinkConnectorConfig, VectorSourceConnectorConfig}
import com.nexla.probe.vectordb.{StringVectorID, VectorData, VectorDbConnectorService, VectorID}
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import io.pinecone.clients.{Index, Pinecone}
import io.pinecone.proto.{ListRequest, VectorServiceGrpc, Vector => PineconeVector}
import com.nexla.probe.pinecone.PineconeHelper.{convertMap, createFilterFromJsonString, extractMetadata, extractMetadataStruct, extractMetadataValue}
import io.pinecone.unsigned_indices_model.{ScoredVectorWithUnsignedIndices, SparseValuesWithUnsignedIndices, VectorWithUnsignedIndices}
import one.util.streamex.StreamEx
import org.apache.kafka.common.config.AbstractConfig
import org.slf4j.LoggerFactory

import java.util.function.Consumer
import scala.util._
import scala.jdk.CollectionConverters._
import com.nexla.probe.vectordb.VectorUtils._
import PineconeService.MAX_BATCH_SIZE
import com.github.rholder.retry.{Attempt, RetryException, RetryListener, Retryer, RetryerBuilder, StopStrategies, WaitStrategies}
import com.google.common.annotations.VisibleForTesting
import com.google.protobuf.Struct
import com.nexla.common.MetricUtils.calcBytes
import com.nexla.common.probe.ProbeException
import com.nexla.connector.config.vectordb.VectorSourceConnectorConfig.SearchBy
import com.nexla.probe.http.SenderHttpException
import com.nexla.probe.vectordb.mapping.MessageToVectorMapper
import io.grpc.StatusRuntimeException

import java.util
import java.util.Optional
import java.util.Optional.{empty, ofNullable}
import java.util.concurrent.{Callable, Executors, Future, TimeUnit}
import org.joor.Reflect.on
import org.springframework.http.{HttpHeaders, HttpStatus}
import org.springframework.web.client.HttpClientErrorException

import java.nio.charset.Charset
import scala.concurrent.ExecutionException;

class PineconeService
  extends VectorDbConnectorService[BaseAuthConfig]
    with StrictNexlaLogging
    with WithLogging {

  type ErrorMessage = String

  private val DEFAULT_NAMESPACE = "(default)"

  private var nexlaLogger = LoggerFactory.getLogger(classOf[PineconeService])
  private var pineconeClientProvider = new PineconeClientProvider()
  private var pineconeMetadataConverter = new PineconeMetadataConverter()

  private val retryableStatusCodes = Set(
    io.grpc.Status.Code.DEADLINE_EXCEEDED,
    io.grpc.Status.Code.RESOURCE_EXHAUSTED,
    io.grpc.Status.Code.FAILED_PRECONDITION,
    io.grpc.Status.Code.UNAVAILABLE,
    io.grpc.Status.Code.UNKNOWN,
    io.grpc.Status.Code.INTERNAL
  )

  override def initLogger(resourceType: ResourceType, resourceId: Integer, taskId: Optional[Integer]): Unit = {
    this.nexlaLogger = new NexlaLogger(nexlaLogger, new NexlaLogKey(resourceType, resourceId, taskId))
  }

  @VisibleForTesting
  def setPinconeClientProvider(pineconeClientProvider: PineconeClientProvider) = {
    this.pineconeClientProvider = pineconeClientProvider
  }

  private def createPineconeClient(config: PineconeAuthConfig): Try[Pinecone] = {
    Try(pineconeClientProvider.createPineconeClient(config.getApiKey()))
  }

  private def withClient[T](authConfig: BaseAuthConfig)
                           (fn: (Pinecone) => T): Try[T] = tryOp(s"creds-${authConfig.getCredsId}") { () =>
    createPineconeClient(authConfig.asInstanceOf[PineconeAuthConfig]).map(fn)
  }

  @VisibleForTesting
  def getCachedIndex(authConfig: BaseAuthConfig, database: String): Index = {
    val pineconeAuthConfig = authConfig.asInstanceOf[PineconeAuthConfig]
    executeWithRetry("getIndex") {
      PineconeService.indexCache.getIndex(authConfig.getCredsId, pineconeAuthConfig.getApiKey(), database)
    }
  }

  override def authenticate(authConfig: BaseAuthConfig): AuthResponse = {
    withClient(authConfig) { client =>
      executeWithRetry("listIndexes") {
        client.listIndexes()
      }
    } match {
      case Success(_) => SUCCESS
      case Failure(e) =>
        nexlaLogger.error(s"[creds-${authConfig.getCredsId}] Exception while authenticating", e)
        authError(e)
    }
  }

  override def listCollections(database: String, config: VectorSourceConnectorConfig): StreamEx[String] = {
    val index = getCachedIndex(config.getAuthConfig, database)

    val indexStatsResponse = executeWithRetry("describeIndexStats") {
      index.describeIndexStats()
    }
    val namespacesMap = indexStatsResponse.getNamespacesMap().asScala
    val nonEmptyNamespaces = namespacesMap
      .filter { case (_, stats) => stats.isInitialized && stats.getVectorCount > 0 }
      .map { case (name, _) => name }.toList
    val collections = nonEmptyNamespaces.map(namespaceToCollectionName)
    StreamEx.of(collections: _*)
  }

  override def listDatabases(config: VectorSourceConnectorConfig): StreamEx[String] = {
    withClient(config.getAuthConfig) { client =>
      val indexes = executeWithRetry("listIndexes") {
         client.listIndexes();
      }
      StreamEx.of(indexes.getIndexes.asScala.map(_.getName): _*)
    }.get
  }

  override def listBuckets(config: AbstractConfig): StreamEx[NexlaBucket] = throw new UnsupportedOperationException("not supported")

  override def listBucketContents(config: AbstractConfig): StreamEx[NexlaFile] = throw new UnsupportedOperationException("not supported")

  override def checkWriteAccess(config: AbstractConfig): Boolean = throw new UnsupportedOperationException("not supported")

  override def fetchVectors(config: VectorSourceConnectorConfig, sampleLimitHint: Option[Int]): StreamEx[VectorData] = {
    fetchIDsInternal(config, sampleLimitHint, (index, vectorsIds, action) => {
      val namespace = targetNamespace(config)
      val vectorsFetchResponse = executeWithRetry("fetch") {
        index.fetch(vectorsIds.asJava, namespace)
      }
      val vectorMap = vectorsFetchResponse.getVectorsMap
      vectorMap.asScala.values.map(extractVector).foreach(v => action.accept(v))
    })
  }

  override def fetchIDs(config: VectorSourceConnectorConfig, sampleLimitHint: Option[Int]): StreamEx[VectorData] = {
    fetchIDsInternal(config, sampleLimitHint, (_, vectorsIds, action) => {
       vectorsIds.foreach { id =>
         val vector = VectorData.buildIds(toVectorId(id), empty(), empty())
         action.accept(vector)
       }
    })
  }

  override def similaritySearch(config: VectorSourceConnectorConfig, sampleLimitHint: Option[Int]): StreamEx[VectorData] = {
    config.searchBy match {
      case SearchBy.VECTOR_ID =>
        if (config.getVectorId.isEmpty) {
          throw new ProbeException("vector_id is required for search by vector ID")
        }
      case SearchBy.DENSE_VECTOR =>
        if (config.getDenseVector.isEmpty) {
          throw new RuntimeException("dense_vector is required for search by dense vector")
        }
      case _ => throw new ProbeException("Unsupported search by type " + config.getSearchBy)
    }

    val vectorDbQueryConfig = config.getProviderSourceConfig.asInstanceOf[PineconeSourceConfig]
    val index = getCachedIndex(config.getAuthConfig, config.getDatabase)

    val response = config.searchBy match {
      case SearchBy.VECTOR_ID =>
        executeWithRetry("query") {
          index.query(
            config.getTopK.orElse(20),
            null,
            null,
            null,
            config.getVectorId.orElse(null),
            targetNamespace(config),
            vectorDbQueryConfig.getFilter.map[Struct](createFilterFromJsonString).orElse(null),
            vectorDbQueryConfig.isIncludeValues,
            vectorDbQueryConfig.isIncludeMetadata
          )
      }
      case SearchBy.DENSE_VECTOR =>
        executeWithRetry("query") {
          index.query(
            config.getTopK.orElse(20),
            listDoublesToListFloats(config.getDenseVector),
            Option(config.getSparseVectorIndices).filter(!_.isEmpty).getOrElse(null),
            Option(listDoublesToListFloats(config.getSparseVectorValues)).filter(!_.isEmpty).getOrElse(null),
            null,
            targetNamespace(config),
            vectorDbQueryConfig.getFilter.map[Struct](createFilterFromJsonString).orElse(null),
            vectorDbQueryConfig.isIncludeValues,
            vectorDbQueryConfig.isIncludeMetadata
          )
        }
      case _ => throw new ProbeException("Unsupported search by type " + config.getSearchBy)
    }

    val matches = response.getMatchesList.asScala.map(extractSearchResults)
    StreamEx.of(matches: _*)
  }


  private def fetchIDsInternal(
    config: VectorSourceConnectorConfig,
    sampleLimitHint: Option[Int],
    idsToVector: (Index, Seq[String], Consumer[_ >: VectorData]) => Unit
  ): StreamEx[VectorData] = {

    val vectorDbQueryConfig = config.getProviderSourceConfig.asInstanceOf[PineconeSourceConfig]
    val namespace = targetNamespace(config)
    val limit = sampleLimitHint.getOrElse(vectorDbQueryConfig.getFetchLimit)
    val prefix = vectorDbQueryConfig.getPrefix.orElse("")
    var paginationToken: Option[String] = None

    StreamEx.produce[VectorData] { action => {

      val index = getCachedIndex(config.getAuthConfig, config.getDatabase)

      val idsListResponse = if (config.unitTest) {
        if (prefix.isEmpty)
          paginationToken.fold(index.list(namespace, limit)) { token =>
            index.list(namespace, limit, token)
          }
        else
          paginationToken.fold(index.list(namespace, prefix, limit)) { token =>
            index.list(namespace, prefix, token, limit)
          }
      } else {
        // index list method is buggy and doesn't work with default/empty namespace. We have to do call manually
        // with low-level code
        val blockingStub: VectorServiceGrpc.VectorServiceBlockingStub = on(index).get("blockingStub")

        val builder = ListRequest.newBuilder();
        if (namespace.nonEmpty) builder.setNamespace(namespace)
        if (limit > 0) builder.setLimit(limit)
        if (prefix.nonEmpty) builder.setPrefix(prefix)
        paginationToken.map(builder.setPaginationToken)

        val request = builder.build()

        executeWithRetry("fetch") {
          blockingStub.list(request)
        }
    }

      val vectorsIds = idsListResponse.getVectorsList.asScala.map(_.getId)
      idsToVector(index, vectorsIds, action)
      paginationToken = Option(idsListResponse.getPagination.getNext).filter(_.nonEmpty)
      paginationToken.isDefined && sampleLimitHint.isEmpty
    }}
  }

  private def namespaceToCollectionName(namespace: String): String =
    if (namespace.trim.isEmpty) DEFAULT_NAMESPACE else namespace

  private def collectionNameToNamespace(collection: String): String =
    if (collection == DEFAULT_NAMESPACE) "" else collection

  private def extractSearchResults(results: ScoredVectorWithUnsignedIndices): VectorData = {
    val sparseValues = results.getSparseValuesWithUnsignedIndices.getValuesList
    val sparseIndexes = results.getSparseValuesWithUnsignedIndices.getIndicesWithUnsigned32IntList

    val metadataStruct = results.getMetadata
    val metadata = ofNullable(extractMetadataStruct(metadataStruct)).filter(_.size() > 0)

    val other = new util.HashMap[String, AnyRef]()
    other.put("score", java.lang.Float.valueOf(results.getScore))

    new VectorData(
      toVectorId(results.getId),
      ofNullable(listFloatsToListOfNumbers(results.getValuesList)).filter(!_.isEmpty),
      ofNullable(sparseIndexes).filter(!_.isEmpty),
      ofNullable(listFloatsToListOfNumbers(sparseValues)).filter(!_.isEmpty),
      metadata,
      Optional.of(other)
    )
  }

  private def extractVector(vector: PineconeVector): VectorData = {
    new VectorData(
      toVectorId(vector.getId),
      ofNullable(listFloatsToListOfNumbers(vector.getValuesList)).filter(!_.isEmpty),
      ofNullable(listIntegerToListOfLongs(vector.getSparseValues.getIndicesList)).filter(!_.isEmpty),
      ofNullable(listFloatsToListOfNumbers(vector.getSparseValues.getValuesList)).filter(!_.isEmpty),
      extractMetadata(vector),
      empty()
    )
  }

  override def bulkWrite(config: VectorSinkConnectorConfig, messageList: Seq[NexlaMessageContext])(implicit recordMetric: RecordMetric): Try[Unit] = {
    val mapper = new MessageToVectorMapper(config.getVectorMappingConfig, pineconeMetadataConverter, true, false)

    val (successes, failures) = messageList
      .map(m => m -> toPineconeMessage(m, mapper))
      .toList
      .partition(_._2.isSuccess)

    val successfulItems = successes.map { case (c, success) => c -> success.get }
    val failedItems = failures.map { case (c, failure) => c -> failure.failed.get.getMessage }
    updateFailureMetrics(failedItems)

    val namespace = targetNamespace(config)
    val groupedMessages = successfulItems.grouped(MAX_BATCH_SIZE).toVector
    val upsertParallelism = config.getProviderSinkConfig.asInstanceOf[PineconeSinkConfig].getUpsertParallelism

    Try(executeUpsertParallel(config.getAuthConfig, config.getDatabase, namespace, groupedMessages, upsertParallelism))
  }

  private def targetNamespace(config: VectorSinkConnectorConfig): String = {
    config.getCollection.map[String](collectionNameToNamespace).orElse("")
  }

  private def targetNamespace(config: VectorSourceConnectorConfig): String = {
    config.getCollection.map[String](collectionNameToNamespace).orElse("")
  }

  private def executeUpsertParallel(authConfig: BaseAuthConfig, database: String, namespace: String, groups: Vector[List[(NexlaMessageContext, VectorWithUnsignedIndices)]], upsertParallelism: Int)(implicit recordMetric: RecordMetric): Unit = {
    val tasks = groups.map(group => new Callable[Unit]() {
      override def call(): Unit = {
        // TODO: Do we need rate limiting?
        val index = getCachedIndex(authConfig, database)
        val response = executeWithRetry("upsert") {
          index.upsert(group.map(_._2).asJava, namespace)
        }
        if (response == null) {
          throw new ProbeException(s"Failed to upsert vectors")
        }
      }
    })

    val threadPool = Executors.newFixedThreadPool(upsertParallelism)
    val results = threadPool.invokeAll(tasks.asJava)
    threadPool.shutdown()

    val executionResult = groups.zip(results.asScala)
    updateMetricsFromExecution(executionResult)
    // collect the results to ensure that any exceptions are thrown
    executionResult.foreach(_._2.get())
  }

  private def toPineconeMessage(messageContext: NexlaMessageContext, mapper: MessageToVectorMapper): Try[VectorWithUnsignedIndices] = Try {
    val vector = mapper.extractVector(messageContext)

    val id: Optional[String] = vector.getVectorID().map(x => x.getId())
    val dense_vector: util.List[java.lang.Float] = listNumbersToListOfFloats(vector.getDenseValues().orElse(null))
    val metadata: Optional[Struct] = vector.getMetadata().map(convertMap)
    val sparseIndexes = vector.getSparseIndices().orElse(null)
    val sparseValues = listNumbersToListOfFloats(vector.getSparseValues().orElse(null))
    val sparseVector = new SparseValuesWithUnsignedIndices(sparseIndexes, sparseValues)
    new VectorWithUnsignedIndices(id.orElse(null), dense_vector, metadata.orElse(null), sparseVector)
  }

  private def updateMetricsFromExecution(executionResult: Vector[(List[(NexlaMessageContext, VectorWithUnsignedIndices)], Future[Unit])])
                                        (implicit metric: RecordMetric) = {
    executionResult.foreach { case (group, result) =>
      Try(result.get()) match {
        case Success(_) => updateSuccessMetrics(group)
        case Failure(e) => processFailure(e, group.map(_._1))
      }
    }
  }

  private def updateFailureMetrics(failures: List[(NexlaMessageContext, ErrorMessage)])
                                  (implicit metric: RecordMetric) = {
    failures.foreach { case (message, errorMessage) =>
      metric.errorRecords.incrementAndGet()
      metric.quarantineMessages.add(quarantineMessage(message.original, errorMessage))
    }
  }

  private def updateSuccessMetrics(successes: List[(NexlaMessageContext, VectorWithUnsignedIndices)])
                                  (implicit metric: RecordMetric) = {
    successes.foreach { success =>
      metric.sentRecordsTotal.incrementAndGet()
      metric.sentBytesTotal.addAndGet(calcBytes(success._1.mapped.toJsonString))
    }
  }

  private def processFailure(e: Throwable,
                             sentMessages: Seq[NexlaMessageContext])
                            (implicit metric: RecordMetric) = e match {
    case ex => Try {
      nexlaLogger.error("Pinecone bulk write failed", ex)
      val errorsFromDynamoDb = sentMessages
        .map(m => m -> e.getMessage)
        .toList
      updateFailureMetrics(errorsFromDynamoDb)
    }
  }

  private def toVectorId(id: String): Optional[VectorID] = ofNullable(new StringVectorID(id))

  private def executeWithRetry[T](operation: String)(fn: => T): T = {
    val retryer = constructRetryer[T](operation)
    try {
      retryer.call(() => fn)
    } catch {
      case e: ExecutionException => {
        nexlaLogger.error("Error executing Pinecone operation: " + operation, e)
        throw e.getCause
      }
      case e: RetryException => {
        nexlaLogger.error("Unable to retry Pinecone operation: " + operation, e)
        // Special case for propagating rate limiting error to sync-api, we need to construct SenderHttpException
        if (e.getCause.isInstanceOf[StatusRuntimeException]) {
          val status = e.getCause.asInstanceOf[StatusRuntimeException].getStatus
          if (status.getCode == io.grpc.Status.Code.RESOURCE_EXHAUSTED) {
            val clientError = HttpClientErrorException.create(e.getMessage, HttpStatus.TOO_MANY_REQUESTS, "Too many requests", new HttpHeaders(), Array.empty, Charset.forName("UTF-8"))
            throw new SenderHttpException("pinecone.io", clientError)
          } else {
            throw e.getCause
          }
        }
        throw e.getCause
      }
    }
  }

  private def constructRetryer[T](operation: String) = {
    RetryerBuilder.newBuilder[T]()
      .retryIfException(x => {
        if (x.isInstanceOf[StatusRuntimeException]) {
          val status = x.asInstanceOf[StatusRuntimeException].getStatus
          retryableStatusCodes.contains(status.getCode)
        } else {
          false
        }
      })
      .withWaitStrategy(WaitStrategies.exponentialWait(500, 30, TimeUnit.SECONDS))
      .withStopStrategy(StopStrategies.stopAfterAttempt(5))
      .withRetryListener(new RetryListener() {
        override def onRetry[V](attempt: Attempt[V]): Unit = {
          if (attempt.hasException) {
            nexlaLogger.error("Pinecone operation: " + operation + ", attempt: " + attempt.getAttemptNumber + ", failed due to error: " + attempt.getExceptionCause.getMessage)
          }
        }
      })
      .build();
  }
}

object PineconeService {
  val MAX_BATCH_SIZE = 1000

  val indexCache = new PineconeIndexCache()
}