<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.nexla</groupId>
        <artifactId>backend-connectors</artifactId>
        <version>3.2.1-SNAPSHOT</version>
    </parent>

    <groupId>com.nexla.probe</groupId>
    <artifactId>vectordb-service</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>connector-properties</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>common-sc</artifactId>
            <version>${nexla-backend-common.version}</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>${guava-retrying.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>http-probe</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sun.jersey</groupId>
                    <artifactId>jersey-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sun.jersey</groupId>
                    <artifactId>jersey-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sun.jersey</groupId>
                    <artifactId>jersey-server</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-handler</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Pinecone API with grpc dependencies will be shaded, so it's ok to have explicit versions here -->
        <dependency>
            <groupId>io.pinecone</groupId>
            <artifactId>pinecone-client</artifactId>
            <version>2.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-http2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-handler-proxy</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>${json-path.vestion}</version>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>${netty-all.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jooq</groupId>
            <artifactId>joor</artifactId>
            <version>${joor.version}</version>
        </dependency>

        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-api</artifactId>
            <version>1.60.2</version>
        </dependency>

        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-auth</artifactId>
            <version>1.60.2</version>
        </dependency>

        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-core</artifactId>
            <version>1.60.2</version>
        </dependency>

        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-protobuf</artifactId>
            <version>1.60.2</version>
        </dependency>

        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-stub</artifactId>
            <version>1.60.2</version>
        </dependency>

        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-context</artifactId>
            <version>1.60.2</version>
        </dependency>

        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-netty</artifactId>
            <version>1.60.2</version>
        </dependency>

        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-protobuf</artifactId>
            <version>1.60.2</version>
        </dependency>

        <dependency>
            <groupId>com.google.api.grpc</groupId>
            <artifactId>proto-google-common-protos</artifactId>
            <version>2.23.0</version>
        </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.25.2</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>

            <!--  shading all grpc dependencies that are necessary for pinecone to work. Otherwise global pom declaration cause conflicts with other google services we use -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>${maven-shade-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <artifactSet>
                                <includes>
                                    <include>io.grpc:grpc-api:*</include>
                                    <include>io.grpc:grpc-auth:*</include>
                                    <include>io.grpc:grpc-netty:*</include>
                                    <include>io.grpc:grpc-core:*</include>
                                    <include>io.grpc:grpc-protobuf:*</include>
                                    <include>io.grpc:grpc-stub:*</include>
                                    <include>io.grpc:grpc-context:*</include>
                                    <include>io.grpc:grpc-protobuf:*</include>
                                    <include>io.grpc:grpc-protobuf-lite:*</include>
                                    <include>com.google.api.grpc:proto-google-common-protos:*</include>
                                    <include>com.google.http-client:google-http-client:*</include>
                                    <include>com.google.protobuf:protobuf-java:*</include>
                                    <include>io.pinecone:pinecone-client:*</include>
                                </includes>
                            </artifactSet>
                            <relocations>
                                <relocation>
                                    <pattern>io.grpc.</pattern>
                                    <shadedPattern>shaded.vectordb.nexla.io.grpc.</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>io.pinecone.</pattern>
                                    <shadedPattern>shaded.vectordb.nexla.io.pinecone.</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>com.google.api.</pattern>
                                    <shadedPattern>shaded.vectordb.nexla.com.google.api.</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>com.google.protobuf.</pattern>
                                    <shadedPattern>shaded.vectordb.nexla.com.google.protobuf.</shadedPattern>
                                </relocation>
                            </relocations>

                            <!--this shades META-INF services that google libraries need -->
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                            </transformers>

                            <shadeSourcesContent>true</shadeSourcesContent>

                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
