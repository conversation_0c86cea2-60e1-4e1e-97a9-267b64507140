package com.nexla.connector.documentdb.sink

import com.nexla.common.{ConnectionType, ResourceType}
import com.nexla.common.metrics.RecordMetric
import com.nexla.connect.common.BaseSinkTask
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter
import com.nexla.connector.NexlaMessageContext
import com.nexla.connector.config.documentdb.DocumentDbSinkConnectorConfig
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.probe.documentdb.DocumentDbConnectorService
import com.nexla.probe.dynamodb.DynamoDbService
import com.nexla.probe.firebase.FirebaseService
import com.nexla.probe.mongodb.MongoDbService
import one.util.streamex.StreamEx
import org.apache.kafka.common.config.ConfigDef

import java.util
import java.util.Optional
import scala.jdk.CollectionConverters._

class DocumentDbSinkTask extends BaseSinkTask[DocumentDbSinkConnectorConfig] {

  override protected def doStart(): Unit = {
    this.sinkTelemetryReporter = Optional.of(new ConnectorTelemetryReporter(config.sinkId, config.connectionType.name,
      ResourceType.SINK, isDedicatedNode))
  }


  override protected def configDef(): ConfigDef = DocumentDbSinkConnectorConfig.configDef()

  override def doPut(messages: StreamEx[NexlaMessageContext],
                     streamSize: Int): Unit = {

    implicit val recordMetric = new RecordMetric

    val messageList = messages.toList.asScala

    if (messageList.nonEmpty) {

      val result = getConnectorService().bulkWrite(config, messageList)

      sendQuarantineMessage(config.collection, recordMetric.getQuarantineMessages)
      sendMetric(config.collection, Optional.empty(), recordMetric.sentRecordsTotal.get, recordMetric.sentBytesTotal.get(), recordMetric.errorRecords.get())

      offsetsSender.ifPresent(os => {
        val offsets = messageList
          .map(x => x.topicPartition -> x.kafkaOffset)
          .toMap
          .asJava

        os.updateSinkOffsets(config.sinkId, offsets)
        logger.info("Updated offsets: {}", offsets)
      })

      // rethrowing exception
      result.get
    }
  }

  override protected def parseConfig(props: util.Map[String, String]) = new DocumentDbSinkConnectorConfig(props)

  override def stop(): Unit = {
  }

  private def getConnectorService(): DocumentDbConnectorService[BaseAuthConfig] = {
    config.connectionType match {
      case e if e == ConnectionType.MONGO => new MongoDbService
      case e if e == ConnectionType.FIREBASE => new FirebaseService
      case e if e == ConnectionType.DYNAMODB => new DynamoDbService
      case other => throw new IllegalArgumentException(s"Not a document based database: $other")
    }
  }

}
