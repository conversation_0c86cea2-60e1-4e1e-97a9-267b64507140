package com.nexla.connector.documentdb.sink

import com.dimafeng.testcontainers.lifecycle.and
import com.dimafeng.testcontainers.scalatest.TestContainersForAll
import com.dimafeng.testcontainers.{<PERSON><PERSON><PERSON><PERSON><PERSON>r, MongoDBContainer}
import com.mongodb.client.MongoCollection
import com.nexla.admin.client.flownode.{FlowNodeDatasource, NexlaFlow}
import com.nexla.admin.client.pipeline.{PDataSource, PDataset, Pipeline}
import com.nexla.admin.client.{AdminApiClient, AdminApiClientBuilder, DataSink, ResourceStatus}
import com.nexla.common.StreamUtils._
import com.nexla.common.notify.transport.{NexlaMessageProducer, NexlaMessageTransport}
import com.nexla.common.sink.TopicPartition
import com.nexla.common.{ConnectionType, NexlaMessage, NexlaSslContext}
import com.nexla.connect.common.offsets.OffsetsTracker
import com.nexla.connect.common.BaseKafkaTest
import com.nexla.connector.NexlaMessageContext
import com.nexla.mongo.MongoDbTestUtils
import one.util.streamex.StreamEx
import org.bson.{BsonDocument, BsonString}
import org.mockito.ArgumentMatchers.{any, anyInt}
import org.mockito.Mockito._
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfterEach, OneInstancePerTest}

import java.util.{Collections, Optional}
import scala.collection.JavaConverters._
import scala.collection.mutable

@com.nexla.test.ScalaIntegrationTests
class DocumentDbSinkTaskTest
  extends BaseKafkaTest with AnyFlatSpecLike
    with Matchers
    with BeforeAndAfterEach
    with OneInstancePerTest
    with TestContainersForAll {

  override type Containers = MongoDBContainer and KafkaContainer

  override def startContainers(): Containers = {
    val mongo = MongoDBContainer("mongo:4.0.6")
    mongo.start()

    val kafka = KafkaContainer.Def(
      dockerImageName = "confluentinc/cp-kafka:7.2.11"
    ).start()

    mongo and kafka
  }

  val task: DocumentDbSinkTask = new DocumentDbSinkTask {
    override protected def messageProducer(nexlaSslContext: NexlaSslContext) =
      new NexlaMessageProducer(mock(classOf[NexlaMessageTransport]))
  }

  override protected def beforeEach(): Unit = {
    val adminApiClient = mock(classOf[AdminApiClient])
    AdminApiClientBuilder.INSTANCE = adminApiClient
    val dataSink = new DataSink
    dataSink.setId(1)
    dataSink.setSinkConfig(java.util.Collections.emptyMap[String, Object]())
    dataSink.setConnectionType(ConnectionType.MONGO)
    dataSink.setStatus(ResourceStatus.ACTIVE)
    when(adminApiClient.getPipeline(any(classOf[Integer]))).thenReturn(Optional.of(new Pipeline(null, null, dataSink)))
    when(adminApiClient.getDataSink(any(classOf[Integer]))).thenReturn(Optional.of(dataSink))

    val dataSets = List(new PDataset(24, 32, null)).asJava
    val ds: PDataSource = new PDataSource
    val sink: DataSink = new DataSink
    when(adminApiClient.getPipeline(anyInt())).thenReturn(Optional.of(new Pipeline(dataSets, ds, sink)))
    when(adminApiClient.getFlowByResource(any())).thenReturn(Optional.of(new NexlaFlow(Collections.emptyList(),
      Collections.singletonList(new FlowNodeDatasource(1, 2, 3, 4, 5, "", "", ResourceStatus.ACTIVE, Collections.emptyList(),
        7, 8, 9, ConnectionType.MONGO, ConnectionType.MONGO, ConnectionType.MONGO)), Collections.emptyList(), Collections.emptyList())))
  }

  it should "upsert composite" in {
    withContainers {
      case mongo and kafka =>
        BaseKafkaTest.init(kafka.container)
        val testUtils = new MongoDbTestUtils(mongo.container.getReplicaSetUrl)
        val baseConfig = testUtils.baseConfig()
        val collection = testUtils.collection()

        val cfg = new mutable.HashMap[String, String]()
        baseConfig.foreach(e => cfg.put(e._1, e._2))
        cfg.put("sink_type", "mongo")
        cfg.put("insert.mode", "upsert")
        cfg.put("id.fields", "key1,key2")
        cfg.put("bootstrap.servers", BaseKafkaTest.BOOTSTRAP_SERVERS)
        cfg.put("bootstrap.server", BaseKafkaTest.BOOTSTRAP_SERVERS)

        task.start(cfg.asJava)

        task.offsetsSender = java.util.Optional.of(mock(classOf[OffsetsTracker]))

        task.doPut(m(lhm("key1", "1", "key2", "2", "value", "a")), 1)

        select(List("_id", "value"), collection).toSet.size shouldBe 1

        task.doPut(m(lhm("key1", "1", "key2", "2", "value", "b")), 1)

        select(List("_id", "value"), collection).toSet.size shouldBe 1
    }
  }

  it should "insert all as new" in {
    withContainers {
      case mongo and kafka =>
        BaseKafkaTest.init(kafka.container)
        val testUtils = new MongoDbTestUtils(mongo.container.getReplicaSetUrl)
        val baseConfig = testUtils.baseConfig()
        val collection = testUtils.collection()

        val cfg = new mutable.HashMap[String, String]()
        baseConfig.foreach(e => cfg.put(e._1, e._2))
        cfg.put("sink_type", "mongo")
        cfg.put("insert.mode", "insert")
        cfg.put("bootstrap.servers", BaseKafkaTest.BOOTSTRAP_SERVERS)
        cfg.put("bootstrap.server", BaseKafkaTest.BOOTSTRAP_SERVERS)

        task.start(cfg.asJava)

        task.offsetsSender = java.util.Optional.of(mock(classOf[OffsetsTracker]))

        task.doPut(m(lhm("key", "1", "value", "a")), 1)
        select(List("_id", "value"), collection).toSet.size shouldBe 1

        task.doPut(m(lhm("key", "1", "value", "b")), 1)
        select(List("_id", "value"), collection).toSet.size shouldBe 2
    }
  }

  private def select(fields: List[String], collection: MongoCollection[BsonDocument]) = {
    collection.find().asScala.toList
      .map(record => fields.map(record.get).map {
        case value: BsonString => value.getValue
        case other => other.toString
      })
  }

  private def m(data: java.util.LinkedHashMap[String, AnyRef]) = {
    StreamEx.of(
      List(
        new NexlaMessage(data))
        .map(r => new NexlaMessageContext(r, r, new TopicPartition("topic", 1), -1))
        .asJava)
  }
}
