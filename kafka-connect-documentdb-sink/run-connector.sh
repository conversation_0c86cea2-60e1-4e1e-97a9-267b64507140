#!/bin/sh

export JAVA_DEBUG_PORT=5005

export KAFKA_DEBUG=y
export DEBUG_SUSPEND_FLAG=n

export NEXLA_BACKEND_BASE=/Users/<USER>/code/nexla
export KAFKA_PATH=/Users/<USER>/tools/kafka-2.6.0

export PROJECT_PATH=$NEXLA_BACKEND_BASE/kafka-connect-documentdb-sink
export CLASSPATH=$PROJECT_PATH/target/kafka-connect-documentdb-sink-0.0.1-SNAPSHOT.jar

$KAFKA_PATH/bin/connect-standalone.sh \
    $PROJECT_PATH/example-connect-worker.properties \
    $PROJECT_PATH/example-connect-mongodb-sink.properties

