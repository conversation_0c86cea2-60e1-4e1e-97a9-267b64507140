# Extra logging related to initialization of Log4j
# Set to debug or trace if log4j initialization is failing
status = info
# Name of the configuration
name = NexlaConnectorLogConfig

# Console appender configuration
appender.console.type = Console
appender.console.name = consoleLogger
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = [%date{yyyy-MM-dd HH:mm:ss.SSS}] [${sys:git_hash}] %highlight{%-5level} [%20.20thread] %style{%-40logger}{cyan} - %msg%n%throwable

# File appender configuration
appender.rolling.type = RollingFile
appender.rolling.name = fileLogger
appender.rolling.fileName= /tmp/logs/kafka-connect-documentdb-sink.log
appender.rolling.filePattern= /tmp/logs/kafka-connect-documentdb-sink_backup%i.log.gz
appender.rolling.layout.type = PatternLayout
appender.rolling.layout.pattern = [%d] %p %m (%c)%n
appender.rolling.policies.type = Policies
appender.rolling.policies.size.type = SizeBasedTriggeringPolicy
appender.rolling.policies.size.size=100MB
appender.rolling.strategy.type = DefaultRolloverStrategy
appender.rolling.strategy.max = 1000

# Root logger
rootLogger.level = info
rootLogger.appenderRef.rolling.ref = fileLogger
rootLogger.appenderRef.console.ref = consoleLogger

logger.nexla.name = com.nexla
logger.nexla.additivity = false
logger.nexla.level = debug
logger.nexla.appenderRef.rolling.ref = fileLogger
logger.nexla.appenderRef.console.ref = consoleLogger

logger.joestelmach.name = com.joestelmach.natty
logger.joestelmach.additivity = false
logger.joestelmach.level = warn
logger.joestelmach.appenderRef.rolling.ref = fileLogger
logger.joestelmach.appenderRef.console.ref = consoleLogger

logger.kafka.name = org.apache.kafka
logger.kafka.additivity = false
logger.kafka.level = info
logger.kafka.appenderRef.rolling.ref = fileLogger
logger.kafka.appenderRef.console.ref = consoleLogger

logger.kafkaconnect.name = org.apache.kafka.connect
logger.kafkaconnect.additivity = false
logger.kafkaconnect.level = info
logger.kafkaconnect.appenderRef.rolling.ref = fileLogger
logger.kafkaconnect.appenderRef.console.ref = consoleLogger

logger.reflections.name = org.reflections
logger.reflections.additivity = false
logger.reflections.level = error
logger.reflections.appenderRef.rolling.ref = fileLogger
logger.reflections.appenderRef.console.ref = consoleLogger
